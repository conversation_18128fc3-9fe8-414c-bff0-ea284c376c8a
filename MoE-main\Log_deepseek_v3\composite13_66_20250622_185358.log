2025-06-22 18:53:58,318 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 18:53:58,318 - __main__ - INFO - 开始分析阶段
2025-06-22 18:53:58,318 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:53:58,342 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9961.0, 'max': 114568.0, 'mean': 78687.3, 'std': 45105.23025115823}, 'diversity': 0.9414141414141414, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:53:58,343 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9961.0, 'max': 114568.0, 'mean': 78687.3, 'std': 45105.23025115823}, 'diversity_level': 0.9414141414141414, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:53:58,352 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:53:58,352 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:53:58,352 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:53:58,357 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:53:58,357 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (24, 29, 32), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(33, 34)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(56, 59)', 'frequency': 0.4}, {'edge': '(52, 63)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(39, 47)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.2}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.2}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(38, 45)', 'frequency': 0.2}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(41, 50)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(2, 11)', 'frequency': 0.2}, {'edge': '(39, 61)', 'frequency': 0.2}, {'edge': '(21, 43)', 'frequency': 0.2}, {'edge': '(0, 63)', 'frequency': 0.2}, {'edge': '(30, 38)', 'frequency': 0.2}, {'edge': '(41, 64)', 'frequency': 0.2}, {'edge': '(33, 52)', 'frequency': 0.2}, {'edge': '(1, 14)', 'frequency': 0.2}, {'edge': '(28, 40)', 'frequency': 0.2}, {'edge': '(6, 17)', 'frequency': 0.2}, {'edge': '(7, 54)', 'frequency': 0.2}, {'edge': '(36, 62)', 'frequency': 0.2}, {'edge': '(14, 47)', 'frequency': 0.2}, {'edge': '(19, 56)', 'frequency': 0.2}, {'edge': '(15, 37)', 'frequency': 0.2}, {'edge': '(11, 23)', 'frequency': 0.2}, {'edge': '(4, 49)', 'frequency': 0.2}, {'edge': '(9, 57)', 'frequency': 0.2}, {'edge': '(24, 25)', 'frequency': 0.2}, {'edge': '(3, 31)', 'frequency': 0.2}, {'edge': '(18, 65)', 'frequency': 0.2}, {'edge': '(11, 47)', 'frequency': 0.2}, {'edge': '(18, 53)', 'frequency': 0.2}, {'edge': '(48, 63)', 'frequency': 0.2}, {'edge': '(54, 63)', 'frequency': 0.2}, {'edge': '(12, 34)', 'frequency': 0.2}, {'edge': '(28, 50)', 'frequency': 0.2}, {'edge': '(22, 51)', 'frequency': 0.2}, {'edge': '(35, 51)', 'frequency': 0.2}, {'edge': '(20, 47)', 'frequency': 0.2}, {'edge': '(23, 38)', 'frequency': 0.2}, {'edge': '(30, 54)', 'frequency': 0.2}, {'edge': '(17, 35)', 'frequency': 0.2}, {'edge': '(29, 40)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [54, 36, 41, 7, 51, 55, 47], 'cost': 15781.0, 'size': 7}, {'region': [54, 24, 62, 26, 41, 10], 'cost': 13529.0, 'size': 6}, {'region': [57, 32, 59, 25, 46, 4], 'cost': 13470.0, 'size': 6}, {'region': [61, 29, 46, 9, 42, 4], 'cost': 12626.0, 'size': 6}, {'region': [36, 45, 34, 65, 29, 40], 'cost': 12288.0, 'size': 6}]}
2025-06-22 18:53:58,358 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:53:58,358 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:53:58,358 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:53:58,358 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 18:53:58,359 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 18:53:58,359 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:53:58,359 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:53:58,359 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:53:58,359 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9961.0, max=114568.0, mean=78687.3, std=45105.23025115823
- Diversity: 0.9414141414141414, Convergence: 0.0
- Structure: 0 high quality edges, 4 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Evolution Context:
- Iteration: 0/2
- Historical trend: Not available
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 5 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Moderate

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:54:03,935 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely high. The significant standard deviation of costs (45105.23) suggests a wide range of solution qualities and potential for steep changes in fitness. This, combined with the absence of a historical trend and identified 'difficult regions' hints at a rugged landscape.",
    "modality": "Likely multi-modal. The presence of 'difficult regions' and the lack of convergence (0.0) suggest the existence of multiple local optima scattered throughout the search space. The unknown TSP instance size contributes to this uncertainty.",
    "deceptiveness": "Uncertain, but possibly high. The absence of elite solutions and the lack of convergence, despite a relatively high diversity, suggest that the search is struggling to find high-quality solutions. The rugged landscape likely contributes to misleading gradients, making it difficult to navigate towards the global optimum."
  },
  "population_state": {
    "diversity": "High (0.94). The high diversity indicates that the population is exploring a significant portion of the search space. This is expected in the early iterations.",
    "convergence": "Very low (0.0). The lack of convergence confirms that the population is not converging toward a single solution or region. This is consistent with the early iteration.",
    "clustering": "No clustering information available. The lack of clustering information makes it difficult to assess if the population is focusing on any specific areas, although high diversity would suggest the opposite."
  },
  "difficult_regions": {
    "identified_challenges": "5 difficult regions have been identified. Without information on the TSP instance, specific node sequences or edge crossings cannot be identified. However, the presence of these regions suggests areas where the search is getting stuck or performing poorly."
  },
  "opportunity_regions": {
    "identified_opportunities": "No opportunity regions are identified at this stage. The focus should be on exploration to broadly survey the search space. Once higher quality solutions are found, the opportunities can be identified and exploited."
  },
  "evolution_phase": "Exploration. The high diversity, lack of convergence, and the early iteration (0/2) all point to an exploration phase, with the algorithm seeking to broadly survey the search space and identify promising regions.",
  "evolution_direction": {
    "strategy": "Continue exploration. The goal at this stage is to maintain high diversity and discover potentially high-quality regions of the search space.",
    "operator_suggestions": [
      "Mutation operators: Employ a range of mutation operators to ensure sufficient exploration. Examples include insertion, swap, inversion, and 2-opt (although with 0 nodes, these will not be applicable to the TSP). Prioritize mutation operators that lead to significant changes in solution structure to combat any potential premature convergence.",
      "Crossover operators: Use crossover operators to combine different solutions and create novel combinations. For example, partially mapped crossover(PMX), order crossover (OX), or edge recombination (if applicable) can be effective. Maintain a relatively high crossover rate to combine beneficial elements from diverse solutions.",
      "Selection operators: Use a selection strategy favoring exploration over exploitation in this iteration. Consider a selection method that favors individuals with higher diversity or those that are not too close to other individuals."
    ]
  }
}
```
2025-06-22 18:54:03,935 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:54:03,935 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': "Likely high. The significant standard deviation of costs (45105.23) suggests a wide range of solution qualities and potential for steep changes in fitness. This, combined with the absence of a historical trend and identified 'difficult regions' hints at a rugged landscape.", 'modality': "Likely multi-modal. The presence of 'difficult regions' and the lack of convergence (0.0) suggest the existence of multiple local optima scattered throughout the search space. The unknown TSP instance size contributes to this uncertainty.", 'deceptiveness': 'Uncertain, but possibly high. The absence of elite solutions and the lack of convergence, despite a relatively high diversity, suggest that the search is struggling to find high-quality solutions. The rugged landscape likely contributes to misleading gradients, making it difficult to navigate towards the global optimum.'}, 'population_state': {'diversity': 'High (0.94). The high diversity indicates that the population is exploring a significant portion of the search space. This is expected in the early iterations.', 'convergence': 'Very low (0.0). The lack of convergence confirms that the population is not converging toward a single solution or region. This is consistent with the early iteration.', 'clustering': 'No clustering information available. The lack of clustering information makes it difficult to assess if the population is focusing on any specific areas, although high diversity would suggest the opposite.'}, 'difficult_regions': {'identified_challenges': '5 difficult regions have been identified. Without information on the TSP instance, specific node sequences or edge crossings cannot be identified. However, the presence of these regions suggests areas where the search is getting stuck or performing poorly.'}, 'opportunity_regions': {'identified_opportunities': 'No opportunity regions are identified at this stage. The focus should be on exploration to broadly survey the search space. Once higher quality solutions are found, the opportunities can be identified and exploited.'}, 'evolution_phase': 'Exploration. The high diversity, lack of convergence, and the early iteration (0/2) all point to an exploration phase, with the algorithm seeking to broadly survey the search space and identify promising regions.', 'evolution_direction': {'strategy': 'Continue exploration. The goal at this stage is to maintain high diversity and discover potentially high-quality regions of the search space.', 'operator_suggestions': ['Mutation operators: Employ a range of mutation operators to ensure sufficient exploration. Examples include insertion, swap, inversion, and 2-opt (although with 0 nodes, these will not be applicable to the TSP). Prioritize mutation operators that lead to significant changes in solution structure to combat any potential premature convergence.', 'Crossover operators: Use crossover operators to combine different solutions and create novel combinations. For example, partially mapped crossover(PMX), order crossover (OX), or edge recombination (if applicable) can be effective. Maintain a relatively high crossover rate to combine beneficial elements from diverse solutions.', 'Selection operators: Use a selection strategy favoring exploration over exploitation in this iteration. Consider a selection method that favors individuals with higher diversity or those that are not too close to other individuals.']}}
2025-06-22 18:54:03,935 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:54:03,935 - __main__ - INFO - 分析阶段完成
2025-06-22 18:54:03,935 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': "Likely high. The significant standard deviation of costs (45105.23) suggests a wide range of solution qualities and potential for steep changes in fitness. This, combined with the absence of a historical trend and identified 'difficult regions' hints at a rugged landscape.", 'modality': "Likely multi-modal. The presence of 'difficult regions' and the lack of convergence (0.0) suggest the existence of multiple local optima scattered throughout the search space. The unknown TSP instance size contributes to this uncertainty.", 'deceptiveness': 'Uncertain, but possibly high. The absence of elite solutions and the lack of convergence, despite a relatively high diversity, suggest that the search is struggling to find high-quality solutions. The rugged landscape likely contributes to misleading gradients, making it difficult to navigate towards the global optimum.'}, 'population_state': {'diversity': 'High (0.94). The high diversity indicates that the population is exploring a significant portion of the search space. This is expected in the early iterations.', 'convergence': 'Very low (0.0). The lack of convergence confirms that the population is not converging toward a single solution or region. This is consistent with the early iteration.', 'clustering': 'No clustering information available. The lack of clustering information makes it difficult to assess if the population is focusing on any specific areas, although high diversity would suggest the opposite.'}, 'difficult_regions': {'identified_challenges': '5 difficult regions have been identified. Without information on the TSP instance, specific node sequences or edge crossings cannot be identified. However, the presence of these regions suggests areas where the search is getting stuck or performing poorly.'}, 'opportunity_regions': {'identified_opportunities': 'No opportunity regions are identified at this stage. The focus should be on exploration to broadly survey the search space. Once higher quality solutions are found, the opportunities can be identified and exploited.'}, 'evolution_phase': 'Exploration. The high diversity, lack of convergence, and the early iteration (0/2) all point to an exploration phase, with the algorithm seeking to broadly survey the search space and identify promising regions.', 'evolution_direction': {'strategy': 'Continue exploration. The goal at this stage is to maintain high diversity and discover potentially high-quality regions of the search space.', 'operator_suggestions': ['Mutation operators: Employ a range of mutation operators to ensure sufficient exploration. Examples include insertion, swap, inversion, and 2-opt (although with 0 nodes, these will not be applicable to the TSP). Prioritize mutation operators that lead to significant changes in solution structure to combat any potential premature convergence.', 'Crossover operators: Use crossover operators to combine different solutions and create novel combinations. For example, partially mapped crossover(PMX), order crossover (OX), or edge recombination (if applicable) can be effective. Maintain a relatively high crossover rate to combine beneficial elements from diverse solutions.', 'Selection operators: Use a selection strategy favoring exploration over exploitation in this iteration. Consider a selection method that favors individuals with higher diversity or those that are not too close to other individuals.']}}
2025-06-22 18:54:03,935 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:54:03,935 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:54:03,935 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High (0.94). The high diversity indicates that the population is exploring a significant portion of the search space. This is expected in the early iterations.
- Best cost: 9961.0, Mean cost: 78687.3
- Search space: Ruggedness: Likely high. The significant standard deviation of costs (45105.23) suggests a wide range of solution qualities and potential for steep changes in fitness. This, combined with the absence of a historical trend and identified 'difficult regions' hints at a rugged landscape., Modality: Likely multi-modal. The presence of 'difficult regions' and the lack of convergence (0.0) suggest the existence of multiple local optima scattered throughout the search space. The unknown TSP instance size contributes to this uncertainty.
- Difficult regions: 1 regions
- Opportunity regions: 1 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:54:03,935 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:54:06,728 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size": 0.1,
      "algorithm": "random_restart_hill_climbing",
      "restart_probability": 0.15
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.7,
      "local_search_iterations": 50,
      "algorithm": "2_opt_local_search",
      "step_size": 0.01
    }
  }
}
```

2025-06-22 18:54:06,728 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:54:06,728 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:54:06,728 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size": 0.1,
      "algorithm": "random_restart_hill_climbing",
      "restart_probability": 0.15
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.7,
      "local_search_iterations": 50,
      "algorithm": "2_opt_local_search",
      "step_size": 0.01
    }
  }
}
```

2025-06-22 18:54:06,728 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:54:06,728 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:54:06,728 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size": 0.1,
      "algorithm": "random_restart_hill_climbing",
      "restart_probability": 0.15
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.7,
      "local_search_iterations": 50,
      "algorithm": "2_opt_local_search",
      "step_size": 0.01
    }
  }
}
```

2025-06-22 18:54:06,728 - __main__ - INFO - 开始进化阶段
2025-06-22 18:54:06,728 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:54:06,728 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:06,728 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 50, 38] and ending with [65, 52, 63]
- Cost: 9961.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7953846153846154
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:06,735 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:10,764 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44
  ],
  "modification_strategy": "Prioritized exploration of unexplored regions based on the given search context. Used a different starting sequence and strategically rearranged nodes within the current path to create a more diverse path. Focused on deviating from the original path in the initial segments to explore new areas. Prioritized visits to nodes associated with the opportunity regions before returning to the end node.",
  "targeted_regions": "Opportunity regions, and also aimed at avoiding difficult regions by making sure those weren't visited early on. Also aimed at expanding the search towards the beginning to explore more possibilities from the origin."
}
```
2025-06-22 18:54:10,764 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:10,764 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115357.0, 路径: [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62]
2025-06-22 18:54:10,764 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}
2025-06-22 18:54:10,764 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:54:10,764 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:10,764 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [65, 52, 63] and ending with [50, 41, 42]
- Cost: 10080.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8476923076923077
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:10,764 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:15,039 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64
  ],
  "modification_strategy": "This strategy prioritizes diversity by creating a path with a completely different initial sequence than the original path. It starts with a different node (65) and then follows a sequential order, ensuring that the path is significantly different while maintaining a valid TSP path by including all nodes once.",
  "targeted_regions": "The strategy doesn't target specific regions, but rather ensures an entirely diverse and valid path by starting at a different node (65) and exploring the available nodes in a new sequence."
}
```
2025-06-22 18:54:15,039 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:15,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-06-22 18:54:15,039 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}
2025-06-22 18:54:15,039 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:54:15,039 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:15,042 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [31, 25, 26] and ending with [43, 48, 42]
- Cost: 9963.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8769230769230769
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:15,042 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:18,158 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Completely re-randomized path, prioritising exploration of unexplored regions.  The initial segment of the given path was retained, then the remaining nodes were shuffled to maximize diversity.  The known 'opportunity regions' and 'difficult regions' were implicitly considered by avoiding overly structured paths within the early randomized segment.",
  "targeted_regions": "This path directly addresses the need to diversify and explore unexplored regions.  The strategy focuses on spreading out the nodes to cover as much of the graph as possible."
}
```
2025-06-22 18:54:18,158 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:18,159 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15650.0, 路径: [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:54:18,159 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}
2025-06-22 18:54:18,159 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:54:18,159 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:18,161 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [24, 59, 2] and ending with [35, 60, 3]
- Cost: 110003.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:18,161 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:21,506 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43
  ],
  "modification_strategy": "The new path prioritizes exploration of the unexplored and opportunity regions by strategically swapping node sequences within the existing path. The path starts similarly to maintain some quality, then diverts significantly using random sampling to uncover diverse solutions while ensuring all nodes are visited once. This aims to move toward areas with unexplored regions to see if these offer better cost effectiveness.",
  "targeted_regions": "Unexplored and Opportunity regions identified during the search context and regions near the end of the existing path for strategic insertion and alteration."
}
```
2025-06-22 18:54:21,506 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:21,509 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125808.0, 路径: [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62]
2025-06-22 18:54:21,509 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}
2025-06-22 18:54:21,509 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:54:21,509 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:21,510 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [40, 32, 50] and ending with [12, 5, 21]
- Cost: 102743.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8153846153846154
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:21,510 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:25,161 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "Modified the initial path using a combination of random swaps, and targeted insertion of nodes from 'unexplored regions'.  The goal was to introduce significant structural changes to the path while ensuring coverage of the entire node set, and targeting the opportunity region.",
  "targeted_regions": "Specifically targeting the 'opportunity regions' identified in the search context by reordering the path to ensure a more diverse exploration pattern across the entire set of available nodes. Also ensuring the nodes are reached in different order by adding the nodes that are available from 0-65 to ensure all nodes are visited."
}
```
2025-06-22 18:54:25,162 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:25,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 44902.0, 路径: [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0]
2025-06-22 18:54:25,162 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}
2025-06-22 18:54:25,162 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:54:25,162 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:25,164 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:25,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 86773.0
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - res_population_costs: [91003]
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - res_populations: [array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64)]
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}, {'tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}, {'tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}, {'tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': [45, 52, 23, 6, 17, 11, 47, 14, 49, 5, 19, 43, 53, 18, 65, 60, 59, 41, 56, 27, 32, 1, 16, 2, 37, 13, 57, 48, 63, 54, 55, 33, 0, 40, 34, 12, 21, 8, 61, 29, 46, 9, 42, 4, 15, 20, 26, 58, 36, 62, 35, 28, 50, 10, 44, 64, 3, 25, 24, 30, 38, 22, 51, 31, 7, 39], 'cur_cost': 114568.0}, {'tour': [10, 57, 4, 49, 43, 24, 5, 15, 37, 16, 59, 39, 62, 3, 9, 48, 14, 60, 26, 31, 45, 32, 42, 1, 36, 0, 35, 51, 53, 25, 22, 19, 2, 55, 6, 12, 34, 13, 58, 21, 29, 17, 20, 47, 8, 50, 28, 7, 46, 41, 52, 33, 38, 23, 56, 64, 11, 44, 18, 40, 61, 65, 27, 63, 54, 30], 'cur_cost': 111651.0}, {'tour': [8, 56, 60, 22, 51, 35, 17, 16, 24, 48, 36, 6, 15, 3, 61, 39, 27, 9, 57, 32, 59, 25, 46, 4, 5, 63, 64, 41, 33, 19, 26, 45, 62, 21, 49, 20, 47, 7, 34, 0, 43, 42, 37, 11, 23, 28, 40, 29, 55, 10, 52, 44, 50, 14, 13, 18, 53, 2, 54, 30, 31, 58, 38, 12, 1, 65], 'cur_cost': 105980.0}, {'tour': [56, 31, 3, 28, 9, 6, 64, 33, 59, 58, 8, 32, 44, 2, 4, 52, 37, 38, 23, 43, 20, 39, 22, 27, 15, 25, 57, 1, 14, 54, 24, 62, 26, 41, 10, 61, 13, 7, 36, 45, 34, 65, 29, 40, 50, 21, 60, 53, 49, 12, 46, 51, 48, 63, 0, 5, 18, 30, 42, 16, 47, 11, 55, 17, 35, 19], 'cur_cost': 109528.0}]
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - 局部搜索耗时: 2.81秒
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 18:54:27,972 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:54:27,972 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:27,972 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 108581.0
2025-06-22 18:54:28,990 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 18:54:28,990 - ExploitationExpert - INFO - res_population_costs: [91003, 9593]
2025-06-22 18:54:28,990 - ExploitationExpert - INFO - res_populations: [array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 18:54:28,990 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:28,990 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}, {'tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}, {'tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}, {'tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': [10, 57, 4, 49, 43, 24, 5, 15, 37, 16, 59, 39, 62, 3, 9, 48, 14, 60, 26, 31, 45, 32, 42, 1, 36, 0, 35, 51, 53, 25, 22, 19, 2, 55, 6, 12, 34, 13, 58, 21, 29, 17, 20, 47, 8, 50, 28, 7, 46, 41, 52, 33, 38, 23, 56, 64, 11, 44, 18, 40, 61, 65, 27, 63, 54, 30], 'cur_cost': 111651.0}, {'tour': [8, 56, 60, 22, 51, 35, 17, 16, 24, 48, 36, 6, 15, 3, 61, 39, 27, 9, 57, 32, 59, 25, 46, 4, 5, 63, 64, 41, 33, 19, 26, 45, 62, 21, 49, 20, 47, 7, 34, 0, 43, 42, 37, 11, 23, 28, 40, 29, 55, 10, 52, 44, 50, 14, 13, 18, 53, 2, 54, 30, 31, 58, 38, 12, 1, 65], 'cur_cost': 105980.0}, {'tour': [56, 31, 3, 28, 9, 6, 64, 33, 59, 58, 8, 32, 44, 2, 4, 52, 37, 38, 23, 43, 20, 39, 22, 27, 15, 25, 57, 1, 14, 54, 24, 62, 26, 41, 10, 61, 13, 7, 36, 45, 34, 65, 29, 40, 50, 21, 60, 53, 49, 12, 46, 51, 48, 63, 0, 5, 18, 30, 42, 16, 47, 11, 55, 17, 35, 19], 'cur_cost': 109528.0}]
2025-06-22 18:54:29,000 - ExploitationExpert - INFO - 局部搜索耗时: 1.03秒
2025-06-22 18:54:29,000 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 18:54:29,001 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:54:29,001 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:54:29,001 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:29,001 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:29,001 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 99288.0
2025-06-22 18:54:29,503 - ExploitationExpert - INFO - res_population_num: 12
2025-06-22 18:54:29,503 - ExploitationExpert - INFO - res_population_costs: [91003, 9593, 9551, 9545, 9527, 9526, 9526, 9524, 9521, 9521, 9521, 9521]
2025-06-22 18:54:29,503 - ExploitationExpert - INFO - res_populations: [array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:54:29,507 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:29,507 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}, {'tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}, {'tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}, {'tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': array([19, 63, 25, 48,  8, 61, 11,  3, 55, 20, 47, 24, 60,  9, 57, 17, 54,
       33, 26, 42, 58, 16, 62, 38, 45, 23, 14, 21, 28, 35, 31, 49, 30, 29,
       41, 56, 50, 52, 59, 27, 36,  1,  6, 34, 10, 43, 13, 51, 37, 65, 12,
       53,  7,  0, 39, 32, 22, 15,  2,  5,  4, 64, 44, 40, 18, 46]), 'cur_cost': 99288.0}, {'tour': [8, 56, 60, 22, 51, 35, 17, 16, 24, 48, 36, 6, 15, 3, 61, 39, 27, 9, 57, 32, 59, 25, 46, 4, 5, 63, 64, 41, 33, 19, 26, 45, 62, 21, 49, 20, 47, 7, 34, 0, 43, 42, 37, 11, 23, 28, 40, 29, 55, 10, 52, 44, 50, 14, 13, 18, 53, 2, 54, 30, 31, 58, 38, 12, 1, 65], 'cur_cost': 105980.0}, {'tour': [56, 31, 3, 28, 9, 6, 64, 33, 59, 58, 8, 32, 44, 2, 4, 52, 37, 38, 23, 43, 20, 39, 22, 27, 15, 25, 57, 1, 14, 54, 24, 62, 26, 41, 10, 61, 13, 7, 36, 45, 34, 65, 29, 40, 50, 21, 60, 53, 49, 12, 46, 51, 48, 63, 0, 5, 18, 30, 42, 16, 47, 11, 55, 17, 35, 19], 'cur_cost': 109528.0}]
2025-06-22 18:54:29,509 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:54:29,509 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 18:54:29,510 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:54:29,510 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:54:29,510 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:29,510 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:29,511 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100404.0
2025-06-22 18:54:30,012 - ExploitationExpert - INFO - res_population_num: 15
2025-06-22 18:54:30,012 - ExploitationExpert - INFO - res_population_costs: [91003, 9593, 9551, 9545, 9527, 9526, 9526, 9524, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:54:30,012 - ExploitationExpert - INFO - res_populations: [array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:54:30,018 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:30,018 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}, {'tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}, {'tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}, {'tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': array([19, 63, 25, 48,  8, 61, 11,  3, 55, 20, 47, 24, 60,  9, 57, 17, 54,
       33, 26, 42, 58, 16, 62, 38, 45, 23, 14, 21, 28, 35, 31, 49, 30, 29,
       41, 56, 50, 52, 59, 27, 36,  1,  6, 34, 10, 43, 13, 51, 37, 65, 12,
       53,  7,  0, 39, 32, 22, 15,  2,  5,  4, 64, 44, 40, 18, 46]), 'cur_cost': 99288.0}, {'tour': array([ 1, 15, 47,  6, 37, 36, 38,  7, 44, 58, 12, 31, 11, 40, 57, 25, 28,
       63, 65,  0, 61, 22, 59, 64, 21,  3, 52, 60, 48, 27, 35, 46, 14, 33,
       32, 23, 41, 62, 42, 43, 26, 19, 17, 53,  8, 51, 50, 55, 45,  5, 34,
       24, 29,  2,  4, 10, 30, 56, 39,  9, 54, 18, 13, 16, 20, 49]), 'cur_cost': 100404.0}, {'tour': [56, 31, 3, 28, 9, 6, 64, 33, 59, 58, 8, 32, 44, 2, 4, 52, 37, 38, 23, 43, 20, 39, 22, 27, 15, 25, 57, 1, 14, 54, 24, 62, 26, 41, 10, 61, 13, 7, 36, 45, 34, 65, 29, 40, 50, 21, 60, 53, 49, 12, 46, 51, 48, 63, 0, 5, 18, 30, 42, 16, 47, 11, 55, 17, 35, 19], 'cur_cost': 109528.0}]
2025-06-22 18:54:30,020 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:54:30,021 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 18:54:30,021 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:54:30,021 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:54:30,021 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:30,021 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:30,021 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95077.0
2025-06-22 18:54:30,524 - ExploitationExpert - INFO - res_population_num: 17
2025-06-22 18:54:30,524 - ExploitationExpert - INFO - res_population_costs: [91003, 9593, 9551, 9545, 9527, 9526, 9526, 9524, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:54:30,524 - ExploitationExpert - INFO - res_populations: [array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:54:30,531 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:30,531 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}, {'tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}, {'tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}, {'tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': array([19, 63, 25, 48,  8, 61, 11,  3, 55, 20, 47, 24, 60,  9, 57, 17, 54,
       33, 26, 42, 58, 16, 62, 38, 45, 23, 14, 21, 28, 35, 31, 49, 30, 29,
       41, 56, 50, 52, 59, 27, 36,  1,  6, 34, 10, 43, 13, 51, 37, 65, 12,
       53,  7,  0, 39, 32, 22, 15,  2,  5,  4, 64, 44, 40, 18, 46]), 'cur_cost': 99288.0}, {'tour': array([ 1, 15, 47,  6, 37, 36, 38,  7, 44, 58, 12, 31, 11, 40, 57, 25, 28,
       63, 65,  0, 61, 22, 59, 64, 21,  3, 52, 60, 48, 27, 35, 46, 14, 33,
       32, 23, 41, 62, 42, 43, 26, 19, 17, 53,  8, 51, 50, 55, 45,  5, 34,
       24, 29,  2,  4, 10, 30, 56, 39,  9, 54, 18, 13, 16, 20, 49]), 'cur_cost': 100404.0}, {'tour': array([23, 65, 43, 12, 47, 30, 61, 13, 39, 38, 40, 50, 42, 55, 54, 17, 15,
        6, 14, 52, 26,  8, 62, 10, 58,  7, 60, 44, 22, 48,  9, 25, 36, 53,
       21,  5, 19, 57, 64, 27, 31, 56, 34, 32, 18,  0, 16,  1,  2,  3, 20,
       28,  4, 11, 37, 24, 63, 46, 45, 35, 49, 41, 29, 33, 51, 59]), 'cur_cost': 95077.0}]
2025-06-22 18:54:30,533 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:54:30,533 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 18:54:30,533 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:54:30,533 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [42, 50, 38, 25, 10, 5, 60, 40, 1, 15, 30, 2, 19, 34, 45, 3, 12, 55, 8, 22, 48, 6, 28, 58, 4, 17, 32, 63, 52, 65, 20, 35, 46, 53, 61, 9, 26, 49, 14, 29, 43, 56, 37, 13, 31, 21, 47, 64, 51, 7, 16, 33, 59, 24, 11, 54, 27, 41, 57, 36, 23, 0, 39, 18, 44, 62], 'cur_cost': 115357.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [31, 25, 12, 5, 3, 1, 0, 2, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15650.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [24, 59, 2, 10, 45, 33, 5, 1, 52, 36, 15, 61, 29, 57, 18, 40, 21, 48, 31, 63, 50, 38, 12, 4, 26, 9, 42, 65, 17, 37, 60, 39, 13, 54, 22, 64, 30, 23, 55, 11, 47, 7, 51, 32, 19, 58, 44, 8, 16, 6, 20, 53, 49, 25, 46, 41, 27, 56, 14, 3, 35, 0, 34, 28, 43, 62], 'cur_cost': 125808.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}}]
2025-06-22 18:54:30,533 - __main__ - INFO - 进化阶段完成
2025-06-22 18:54:30,533 - __main__ - INFO - 开始评估阶段
2025-06-22 18:54:30,533 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:54:30,534 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:54:30,534 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/2
- Population changes:
  * Cost: min 9961.0->11479.0, mean 78687.3->80331.9
  * Diversity: 0.7953846153846154->0.7738461538461539
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 5 individuals, success rate 20.0%, avg improvement -14089.2
- Exploit: 5 individuals, success rate 100.0%, avg improvement 10800.0
- Overall improvement: -1518.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:54:38,015 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Initial Divergence",
    "features": {
      "iteration": "0/2 (First Iteration)",
      "cost_trend": "Increased minimum and mean cost, indicating potential initial challenges or a shift in the search landscape.",
      "diversity_trend": "Slight decrease in diversity, suggesting a slight convergence towards a smaller region of the search space.",
      "elite_solution_trend": "Reduction in elite solutions from 3 to 1, potentially due to the cost increase and reduced diversity. Highlighting an initial 'loss' of quality solutions in this iteration."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "success_rate": "20.0%",
      "avg_improvement": "-14089.2 (significant negative improvement)",
      "evaluation": "Exploration is ineffective, leading to a significant decrease in solution quality on average.  This implies the exploration strategy might be straying into unproductive regions of the search space or is using parameters that are not suitable for the current problem."
    },
    "exploit": {
      "performance": "Excellent",
      "success_rate": "100.0%",
      "avg_improvement": "10800.0 (strong positive improvement)",
      "evaluation": "Exploitation is highly effective, consistently improving solution quality. This indicates the exploitation strategy is correctly identifying and refining promising solutions."
    },
    "overall_improvement": {
      "value": "-1518.0",
      "assessment": "The overall negative improvement indicates that the negative impact of exploration outweighs the positive impact of exploitation in this iteration. The optimization is losing ground."
    }
  },
  "balance_state": {
    "assessment": "Heavily imbalanced towards exploitation. The positive results of exploitation are currently offset by the counterproductive exploration efforts.  A strong need to re-evaluate exploration methods to avoid further quality decrease. Early stage requires a good balance of exploration.",
    "adjustment_needs": "Prioritize adjustments to the exploration strategy to improve effectiveness.  The current balance leads to a decrease in the overall quality of the solution pool and will hinder future iterations if left unchanged."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine the Exploration Strategy",
      "details": "Thoroughly review and adjust the exploration strategy. Consider the following actions:  \n*   **Parameter Tuning:** Adjust parameters used for exploration, like step sizes or mutation rates. \n*   **Exploration Strategy Selection:** Evaluate alternative exploration strategies.  \n*   **Diversification Techniques:** Implement diversification strategies to prevent premature convergence. \n*   **Adaptive Exploration:** Implement an adaptive exploration strategy that adjusts its intensity based on the success rate or performance of previous exploration attempts. Consider using a more directed form of exploration based on the current best solution (e.g., perturbing its parameters in a focused way)."
    },
    {
      "priority": "Medium",
      "action": "Analyze the Exploitation Strategy",
      "details": "While the exploitation strategy is performing well, monitor it closely. Ensure it's not converging prematurely. Keep track of the solutions and the parameters being refined. If the elite solutions are too similar, consider strategies to inject more diversity into the exploited solutions, e.g., using different operators or applying more varied perturbation."
    },
    {
      "priority": "Low",
      "action": "Increase Exploration Attempts",
      "details": "While refining the strategy, increase the proportion of individuals devoted to exploration, but not at the expense of exploitation, and in line with the strategy change to ensure an increased chance to find better solutions. The current numbers are very low, thus exploration does not have sufficient opportunities to prove itself."
    }
  ]
}
```
2025-06-22 18:54:38,043 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:54:38,043 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Initial Divergence",
    "features": {
      "iteration": "0/2 (First Iteration)",
      "cost_trend": "Increased minimum and mean cost, indicating potential initial challenges or a shift in the search landscape.",
      "diversity_trend": "Slight decrease in diversity, suggesting a slight convergence towards a smaller region of the search space.",
      "elite_solution_trend": "Reduction in elite solutions from 3 to 1, potentially due to the cost increase and reduced diversity. Highlighting an initial 'loss' of quality solutions in this iteration."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "success_rate": "20.0%",
      "avg_improvement": "-14089.2 (significant negative improvement)",
      "evaluation": "Exploration is ineffective, leading to a significant decrease in solution quality on average.  This implies the exploration strategy might be straying into unproductive regions of the search space or is using parameters that are not suitable for the current problem."
    },
    "exploit": {
      "performance": "Excellent",
      "success_rate": "100.0%",
      "avg_improvement": "10800.0 (strong positive improvement)",
      "evaluation": "Exploitation is highly effective, consistently improving solution quality. This indicates the exploitation strategy is correctly identifying and refining promising solutions."
    },
    "overall_improvement": {
      "value": "-1518.0",
      "assessment": "The overall negative improvement indicates that the negative impact of exploration outweighs the positive impact of exploitation in this iteration. The optimization is losing ground."
    }
  },
  "balance_state": {
    "assessment": "Heavily imbalanced towards exploitation. The positive results of exploitation are currently offset by the counterproductive exploration efforts.  A strong need to re-evaluate exploration methods to avoid further quality decrease. Early stage requires a good balance of exploration.",
    "adjustment_needs": "Prioritize adjustments to the exploration strategy to improve effectiveness.  The current balance leads to a decrease in the overall quality of the solution pool and will hinder future iterations if left unchanged."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine the Exploration Strategy",
      "details": "Thoroughly review and adjust the exploration strategy. Consider the following actions:  \n*   **Parameter Tuning:** Adjust parameters used for exploration, like step sizes or mutation rates. \n*   **Exploration Strategy Selection:** Evaluate alternative exploration strategies.  \n*   **Diversification Techniques:** Implement diversification strategies to prevent premature convergence. \n*   **Adaptive Exploration:** Implement an adaptive exploration strategy that adjusts its intensity based on the success rate or performance of previous exploration attempts. Consider using a more directed form of exploration based on the current best solution (e.g., perturbing its parameters in a focused way)."
    },
    {
      "priority": "Medium",
      "action": "Analyze the Exploitation Strategy",
      "details": "While the exploitation strategy is performing well, monitor it closely. Ensure it's not converging prematurely. Keep track of the solutions and the parameters being refined. If the elite solutions are too similar, consider strategies to inject more diversity into the exploited solutions, e.g., using different operators or applying more varied perturbation."
    },
    {
      "priority": "Low",
      "action": "Increase Exploration Attempts",
      "details": "While refining the strategy, increase the proportion of individuals devoted to exploration, but not at the expense of exploitation, and in line with the strategy change to ensure an increased chance to find better solutions. The current numbers are very low, thus exploration does not have sufficient opportunities to prove itself."
    }
  ]
}
```
2025-06-22 18:54:38,043 - __main__ - INFO - 评估阶段完成
2025-06-22 18:54:38,043 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Initial Divergence",
    "features": {
      "iteration": "0/2 (First Iteration)",
      "cost_trend": "Increased minimum and mean cost, indicating potential initial challenges or a shift in the search landscape.",
      "diversity_trend": "Slight decrease in diversity, suggesting a slight convergence towards a smaller region of the search space.",
      "elite_solution_trend": "Reduction in elite solutions from 3 to 1, potentially due to the cost increase and reduced diversity. Highlighting an initial 'loss' of quality solutions in this iteration."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "success_rate": "20.0%",
      "avg_improvement": "-14089.2 (significant negative improvement)",
      "evaluation": "Exploration is ineffective, leading to a significant decrease in solution quality on average.  This implies the exploration strategy might be straying into unproductive regions of the search space or is using parameters that are not suitable for the current problem."
    },
    "exploit": {
      "performance": "Excellent",
      "success_rate": "100.0%",
      "avg_improvement": "10800.0 (strong positive improvement)",
      "evaluation": "Exploitation is highly effective, consistently improving solution quality. This indicates the exploitation strategy is correctly identifying and refining promising solutions."
    },
    "overall_improvement": {
      "value": "-1518.0",
      "assessment": "The overall negative improvement indicates that the negative impact of exploration outweighs the positive impact of exploitation in this iteration. The optimization is losing ground."
    }
  },
  "balance_state": {
    "assessment": "Heavily imbalanced towards exploitation. The positive results of exploitation are currently offset by the counterproductive exploration efforts.  A strong need to re-evaluate exploration methods to avoid further quality decrease. Early stage requires a good balance of exploration.",
    "adjustment_needs": "Prioritize adjustments to the exploration strategy to improve effectiveness.  The current balance leads to a decrease in the overall quality of the solution pool and will hinder future iterations if left unchanged."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine the Exploration Strategy",
      "details": "Thoroughly review and adjust the exploration strategy. Consider the following actions:  \n*   **Parameter Tuning:** Adjust parameters used for exploration, like step sizes or mutation rates. \n*   **Exploration Strategy Selection:** Evaluate alternative exploration strategies.  \n*   **Diversification Techniques:** Implement diversification strategies to prevent premature convergence. \n*   **Adaptive Exploration:** Implement an adaptive exploration strategy that adjusts its intensity based on the success rate or performance of previous exploration attempts. Consider using a more directed form of exploration based on the current best solution (e.g., perturbing its parameters in a focused way)."
    },
    {
      "priority": "Medium",
      "action": "Analyze the Exploitation Strategy",
      "details": "While the exploitation strategy is performing well, monitor it closely. Ensure it's not converging prematurely. Keep track of the solutions and the parameters being refined. If the elite solutions are too similar, consider strategies to inject more diversity into the exploited solutions, e.g., using different operators or applying more varied perturbation."
    },
    {
      "priority": "Low",
      "action": "Increase Exploration Attempts",
      "details": "While refining the strategy, increase the proportion of individuals devoted to exploration, but not at the expense of exploitation, and in line with the strategy change to ensure an increased chance to find better solutions. The current numbers are very low, thus exploration does not have sufficient opportunities to prove itself."
    }
  ]
}
```
2025-06-22 18:54:38,045 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-22 18:54:38,046 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 18:54:38,046 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 18:54:38,046 - __main__ - INFO - 开始分析阶段
2025-06-22 18:54:38,046 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:54:38,064 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 125808.0, 'mean': 80331.9, 'std': 39107.18790823497}, 'diversity': 0.9296296296296297, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:54:38,065 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 125808.0, 'mean': 80331.9, 'std': 39107.18790823497}, 'diversity_level': 0.9296296296296297, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:54:38,065 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:54:38,065 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:54:38,065 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:54:38,067 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:54:38,068 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:54:38,068 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:54:38,068 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:54:38,068 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:54:38,069 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:54:38,074 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:54:38,074 - __main__ - INFO - 精英专家分析报告: {'elite_count': 17, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 66009.31176470588}, 'structure_gap': {'unique_elite_edges': 226, 'unique_pop_edges': 497, 'common_edges': 42}}, 'elite_diversity': {'diversity_score': 0.3083778966131907}}
2025-06-22 18:54:38,074 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:54:38,075 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:54:38,075 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:54:38,075 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11479.0, max=125808.0, mean=80331.9, std=39107.18790823497
- Diversity: 0.9296296296296297, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 1/2
- Historical trend: Recent costs: [11479.0]
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:54:43,231 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely rugged due to the high standard deviation in costs and the absence of high-quality edges. The presence of a wide range of costs (min to max) further supports this.",
    "modality": "Unknown, but likely multimodal given the ruggedness and the fact that this is a TSP instance, even though the number of nodes is unknown (likely minimal, if any, as the dataset suggests no nodes).",
    "deceptiveness": "Potentially deceptive. The initial low-cost solution (11479.0) suggests the search might easily get trapped in local optima. More data and more iterations would be needed to confirm."
  },
  "population_state": {
    "diversity": "High (0.93). This is good, meaning the population explores different solutions.",
    "convergence": "Very low (0.0), which is expected in iteration 1 and, given the low number of nodes. Indicates no solutions have improved sufficiently.",
    "clustering": "No clustering information available, however with only 10 individuals it is expected that they will not cluster.  If the nodes number of the TSP instance is indeed 0, then it should not be possible to cluster."
  },
  "difficult_regions": {
    "challenges": "Due to the unknown nature of the TSP instance (and the seeming lack of nodes - 0 nodes) it is impossible to identify any specific challenges regarding node sequences or edges to avoid. The high standard deviation in costs suggests that solutions are varying wildly at the start. ",
    "specific_issues": "None identified given the limited information."
  },
  "opportunity_regions": {
    "potential": "At this early stage, any unexplored region is an opportunity. The high diversity indicates a good exploration foundation. The goal is to move toward better solutions.",
    "specific_edges": "Unable to define specific edges or node sequences without the TSP instance data."
  },
  "evolution_phase": "High exploration is the dominant phase as iteration 1. Further, the high diversity (0.93) supports this, along with the recent costs [11479.0].",
  "evolution_direction": {
    "strategy": "Continue with high exploration to survey the search space. As the instance is unknown, maintain the existing diversity.",
    "operator_suggestions": [
      "Implement a mutation operator with a high mutation rate to explore a broad range of potential solutions.",
      "Incorporate a crossover operator to combine different solutions to produce new ones, maintaining the diversity.",
      "Consider a local search operator to fine-tune individual solutions after mutation and crossover (once some solutions improve). However, given the early iteration, the main focus should be exploration."
    ]
  }
}
```
2025-06-22 18:54:43,231 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:54:43,231 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely rugged due to the high standard deviation in costs and the absence of high-quality edges. The presence of a wide range of costs (min to max) further supports this.', 'modality': 'Unknown, but likely multimodal given the ruggedness and the fact that this is a TSP instance, even though the number of nodes is unknown (likely minimal, if any, as the dataset suggests no nodes).', 'deceptiveness': 'Potentially deceptive. The initial low-cost solution (11479.0) suggests the search might easily get trapped in local optima. More data and more iterations would be needed to confirm.'}, 'population_state': {'diversity': 'High (0.93). This is good, meaning the population explores different solutions.', 'convergence': 'Very low (0.0), which is expected in iteration 1 and, given the low number of nodes. Indicates no solutions have improved sufficiently.', 'clustering': 'No clustering information available, however with only 10 individuals it is expected that they will not cluster.  If the nodes number of the TSP instance is indeed 0, then it should not be possible to cluster.'}, 'difficult_regions': {'challenges': 'Due to the unknown nature of the TSP instance (and the seeming lack of nodes - 0 nodes) it is impossible to identify any specific challenges regarding node sequences or edges to avoid. The high standard deviation in costs suggests that solutions are varying wildly at the start. ', 'specific_issues': 'None identified given the limited information.'}, 'opportunity_regions': {'potential': 'At this early stage, any unexplored region is an opportunity. The high diversity indicates a good exploration foundation. The goal is to move toward better solutions.', 'specific_edges': 'Unable to define specific edges or node sequences without the TSP instance data.'}, 'evolution_phase': 'High exploration is the dominant phase as iteration 1. Further, the high diversity (0.93) supports this, along with the recent costs [11479.0].', 'evolution_direction': {'strategy': 'Continue with high exploration to survey the search space. As the instance is unknown, maintain the existing diversity.', 'operator_suggestions': ['Implement a mutation operator with a high mutation rate to explore a broad range of potential solutions.', 'Incorporate a crossover operator to combine different solutions to produce new ones, maintaining the diversity.', 'Consider a local search operator to fine-tune individual solutions after mutation and crossover (once some solutions improve). However, given the early iteration, the main focus should be exploration.']}}
2025-06-22 18:54:43,231 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:54:43,231 - __main__ - INFO - 分析阶段完成
2025-06-22 18:54:43,231 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely rugged due to the high standard deviation in costs and the absence of high-quality edges. The presence of a wide range of costs (min to max) further supports this.', 'modality': 'Unknown, but likely multimodal given the ruggedness and the fact that this is a TSP instance, even though the number of nodes is unknown (likely minimal, if any, as the dataset suggests no nodes).', 'deceptiveness': 'Potentially deceptive. The initial low-cost solution (11479.0) suggests the search might easily get trapped in local optima. More data and more iterations would be needed to confirm.'}, 'population_state': {'diversity': 'High (0.93). This is good, meaning the population explores different solutions.', 'convergence': 'Very low (0.0), which is expected in iteration 1 and, given the low number of nodes. Indicates no solutions have improved sufficiently.', 'clustering': 'No clustering information available, however with only 10 individuals it is expected that they will not cluster.  If the nodes number of the TSP instance is indeed 0, then it should not be possible to cluster.'}, 'difficult_regions': {'challenges': 'Due to the unknown nature of the TSP instance (and the seeming lack of nodes - 0 nodes) it is impossible to identify any specific challenges regarding node sequences or edges to avoid. The high standard deviation in costs suggests that solutions are varying wildly at the start. ', 'specific_issues': 'None identified given the limited information.'}, 'opportunity_regions': {'potential': 'At this early stage, any unexplored region is an opportunity. The high diversity indicates a good exploration foundation. The goal is to move toward better solutions.', 'specific_edges': 'Unable to define specific edges or node sequences without the TSP instance data.'}, 'evolution_phase': 'High exploration is the dominant phase as iteration 1. Further, the high diversity (0.93) supports this, along with the recent costs [11479.0].', 'evolution_direction': {'strategy': 'Continue with high exploration to survey the search space. As the instance is unknown, maintain the existing diversity.', 'operator_suggestions': ['Implement a mutation operator with a high mutation rate to explore a broad range of potential solutions.', 'Incorporate a crossover operator to combine different solutions to produce new ones, maintaining the diversity.', 'Consider a local search operator to fine-tune individual solutions after mutation and crossover (once some solutions improve). However, given the early iteration, the main focus should be exploration.']}}
2025-06-22 18:54:43,235 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:54:43,235 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:54:43,235 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High (0.93). This is good, meaning the population explores different solutions.
- Best cost: 11479.0, Mean cost: 80331.9
- Search space: Ruggedness: Likely rugged due to the high standard deviation in costs and the absence of high-quality edges. The presence of a wide range of costs (min to max) further supports this., Modality: Unknown, but likely multimodal given the ruggedness and the fact that this is a TSP instance, even though the number of nodes is unknown (likely minimal, if any, as the dataset suggests no nodes).
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Initial Divergence",
    "features": {
      "iteration": "0/2 (First Iteration)",
      "cost_trend": "Increased minimum and mean cost,...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:54:43,235 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:54:46,542 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy_type": "mutation",
      "mutation_rate": 0.2,
      "local_search_depth": 1,
      "region_focus": true,
      "opportunity_region_bias": 0.7
    },
    "exploit": {
      "strategy_type": "local_search",
      "search_depth": 3,
      "region_focus": false,
      "diversification_factor": 0.2
    }
  }
}
```

2025-06-22 18:54:46,542 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:54:46,542 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:54:46,542 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy_type": "mutation",
      "mutation_rate": 0.2,
      "local_search_depth": 1,
      "region_focus": true,
      "opportunity_region_bias": 0.7
    },
    "exploit": {
      "strategy_type": "local_search",
      "search_depth": 3,
      "region_focus": false,
      "diversification_factor": 0.2
    }
  }
}
```

2025-06-22 18:54:46,542 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:54:46,542 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:54:46,542 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy_type": "mutation",
      "mutation_rate": 0.2,
      "local_search_depth": 1,
      "region_focus": true,
      "opportunity_region_bias": 0.7
    },
    "exploit": {
      "strategy_type": "local_search",
      "search_depth": 3,
      "region_focus": false,
      "diversification_factor": 0.2
    }
  }
}
```

2025-06-22 18:54:46,542 - __main__ - INFO - 开始进化阶段
2025-06-22 18:54:46,542 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:54:46,542 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:46,542 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 50, 38] and ending with [18, 44, 62]
- Cost: 115357.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7738461538461539
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:46,542 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:49,895 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49,
    52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44,
    47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48,
    53, 56, 59, 62, 0
  ],
  "modification_strategy": "This strategy prioritizes a more diverse exploration by re-organizing the path. I used a combination of breadth-first traversal and strategic jumps to explore unexplored regions. The path begins similarly but then explores less travelled nodes early on to emphasize the targeted_regions",
  "targeted_regions": "Explored regions at the beginning to move into untouched regions. Focused on a more comprehensive coverage of the nodes through a different permutation, targeting the opportunity regions described in search context."
}
```
2025-06-22 18:54:49,896 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:49,896 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34012.0, 路径: [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65]
2025-06-22 18:54:49,896 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}
2025-06-22 18:54:49,896 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:54:49,896 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:49,896 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [65, 0, 1] and ending with [62, 63, 64]
- Cost: 11479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7769230769230769
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:49,896 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:53,562 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62
  ],
  "modification_strategy": "This path generation focuses on a significant re-ordering of the nodes, especially around the initial and final segments of the previous path. It attempts to introduce new connections and explore regions by rearranging the order. I avoided the identified 'difficult regions' as much as possible. This was done by randomly permuting the nodes, then tweaking the initial and final segments for a new path.",
  "targeted_regions": "Attempted to explore previously unexplored regions by shifting the node order and to diversify the path from the known path. Focus on areas which have high connections, by rearranging the middle and outer portion of the old path"
}
```
2025-06-22 18:54:53,562 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:53,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107089.0, 路径: [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62]
2025-06-22 18:54:53,562 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}
2025-06-22 18:54:53,562 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:54:53,562 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:53,562 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [31, 25, 12] and ending with [63, 64, 65]
- Cost: 15650.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8446153846153847
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:53,567 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:54:57,024 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "This strategy prioritizes diversity by drastically altering the original path's structure. I've constructed the path using a more sequential pattern, aiming to visit regions that were likely bypassed or visited in a less-than-optimal order in the initial path. This strategy aims to discover different traversal patterns to avoid areas that are too complex. It then connects back to node 0 at the end to close the TSP loop. All nodes are within the valid range.",
  "targeted_regions": "The primary goal is to explore any unexplored regions or areas that the original path might have skimmed over. This involved re-ordering the nodes to prioritize different paths and reduce time spent traversing difficult and unexplored areas as well as exploring the available opportunity regions."
}
```
2025-06-22 18:54:57,024 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:54:57,024 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15817.0, 路径: [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0]
2025-06-22 18:54:57,024 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}
2025-06-22 18:54:57,024 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 18:54:57,024 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:54:57,024 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:54:57,024 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 104006.0
2025-06-22 18:54:57,526 - ExploitationExpert - INFO - res_population_num: 19
2025-06-22 18:54:57,528 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9526, 9527, 9545, 9551, 9593, 91003, 9521, 9521]
2025-06-22 18:54:57,528 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:54:57,534 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:54:57,534 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}, {'tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}, {'tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}, {'tour': array([19, 13, 37,  8, 57,  1, 15, 41, 23, 25, 16, 24,  9, 11, 35, 12, 27,
       28,  4, 50, 10, 61,  7, 47,  6, 39, 14, 38, 33, 29, 17, 51, 20,  2,
       49, 26, 56, 42, 64, 55, 65, 31, 21,  5, 59, 43, 60, 44, 32,  3, 22,
        0, 34, 18, 53, 40, 58, 48, 46, 30, 36, 52, 63, 54, 62, 45]), 'cur_cost': 104006.0}, {'tour': [40, 32, 50, 1, 61, 2, 59, 3, 58, 4, 57, 5, 56, 6, 55, 7, 54, 8, 53, 9, 52, 10, 51, 11, 60, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 62, 63, 64, 65, 0], 'cur_cost': 44902.0}, {'tour': array([50, 55, 41, 10,  2, 63, 53, 58, 44, 45, 32, 42, 27, 65, 17, 18, 31,
       54, 61, 64,  1, 11, 59,  8, 52, 62, 57, 48, 35, 12, 34, 20, 47, 19,
        4,  0, 51, 28, 13, 25, 30, 60, 22, 26, 56, 23, 14,  9, 39, 36,  5,
       24, 37, 15, 21, 29, 43, 40, 33, 46, 38,  7,  6,  3, 16, 49]), 'cur_cost': 86773.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': array([19, 63, 25, 48,  8, 61, 11,  3, 55, 20, 47, 24, 60,  9, 57, 17, 54,
       33, 26, 42, 58, 16, 62, 38, 45, 23, 14, 21, 28, 35, 31, 49, 30, 29,
       41, 56, 50, 52, 59, 27, 36,  1,  6, 34, 10, 43, 13, 51, 37, 65, 12,
       53,  7,  0, 39, 32, 22, 15,  2,  5,  4, 64, 44, 40, 18, 46]), 'cur_cost': 99288.0}, {'tour': array([ 1, 15, 47,  6, 37, 36, 38,  7, 44, 58, 12, 31, 11, 40, 57, 25, 28,
       63, 65,  0, 61, 22, 59, 64, 21,  3, 52, 60, 48, 27, 35, 46, 14, 33,
       32, 23, 41, 62, 42, 43, 26, 19, 17, 53,  8, 51, 50, 55, 45,  5, 34,
       24, 29,  2,  4, 10, 30, 56, 39,  9, 54, 18, 13, 16, 20, 49]), 'cur_cost': 100404.0}, {'tour': array([23, 65, 43, 12, 47, 30, 61, 13, 39, 38, 40, 50, 42, 55, 54, 17, 15,
        6, 14, 52, 26,  8, 62, 10, 58,  7, 60, 44, 22, 48,  9, 25, 36, 53,
       21,  5, 19, 57, 64, 27, 31, 56, 34, 32, 18,  0, 16,  1,  2,  3, 20,
       28,  4, 11, 37, 24, 63, 46, 45, 35, 49, 41, 29, 33, 51, 59]), 'cur_cost': 95077.0}]
2025-06-22 18:54:57,539 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:54:57,539 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 18:54:57,539 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 18:54:57,539 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:54:57,539 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:54:57,541 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [40, 32, 50] and ending with [64, 65, 0]
- Cost: 44902.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8338461538461538
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:54:57,541 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:55:01,164 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39
  ],
  "modification_strategy": "This path prioritizes diversity by making substantial changes to the order of nodes from the original path. It attempts to break away from potential locally optimal routes and aims to explore unexplored regions indicated in the search context. The shuffle is done in chunks to improve path connectivity, trying to avoid large jumps and high cost.",
  "targeted_regions": "Unexplored regions and opportunity regions, as identified in the search context, are prioritized. The rearrangement attempts to explore the nodes near these regions. Nodes are shuffled aiming to reduce the number of difficult regions visited early on."
}
```
2025-06-22 18:55:01,165 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:55:01,165 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111512.0, 路径: [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47]
2025-06-22 18:55:01,165 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47], 'cur_cost': 111512.0}
2025-06-22 18:55:01,165 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:55:01,165 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:55:01,165 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:55:01,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 113264.0
2025-06-22 18:55:01,670 - ExploitationExpert - INFO - res_population_num: 19
2025-06-22 18:55:01,670 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9526, 9527, 9545, 9551, 9593, 91003, 9521, 9521]
2025-06-22 18:55:01,670 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:55:01,676 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:55:01,676 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}, {'tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}, {'tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}, {'tour': array([19, 13, 37,  8, 57,  1, 15, 41, 23, 25, 16, 24,  9, 11, 35, 12, 27,
       28,  4, 50, 10, 61,  7, 47,  6, 39, 14, 38, 33, 29, 17, 51, 20,  2,
       49, 26, 56, 42, 64, 55, 65, 31, 21,  5, 59, 43, 60, 44, 32,  3, 22,
        0, 34, 18, 53, 40, 58, 48, 46, 30, 36, 52, 63, 54, 62, 45]), 'cur_cost': 104006.0}, {'tour': [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47], 'cur_cost': 111512.0}, {'tour': array([44, 63,  0, 23, 64, 65,  7, 25, 51, 26, 46, 55, 40, 22, 53, 52, 48,
       20, 62, 41, 33, 49, 16, 43, 18,  1, 24, 59, 32, 11,  2, 45, 56, 15,
       29, 38,  6, 37,  5, 13, 35,  4, 36,  9, 34, 50, 27, 31,  8,  3, 54,
       30, 17, 10, 12, 21, 14, 39, 57, 47, 19, 28, 42, 61, 60, 58]), 'cur_cost': 113264.0}, {'tour': array([55,  5, 37, 44, 57, 17, 29, 48, 52, 58,  4, 27, 38, 54, 36, 42, 33,
       59,  6,  0, 34, 45, 64, 13, 43, 19, 61, 30, 18, 21, 60, 41, 15, 25,
       16,  2, 22, 65, 47, 20, 10,  1, 46, 40,  3, 12, 24, 14,  9, 49, 39,
       11,  7, 50, 62, 63, 31, 26, 51, 32, 28, 53, 23, 35,  8, 56]), 'cur_cost': 108581.0}, {'tour': array([19, 63, 25, 48,  8, 61, 11,  3, 55, 20, 47, 24, 60,  9, 57, 17, 54,
       33, 26, 42, 58, 16, 62, 38, 45, 23, 14, 21, 28, 35, 31, 49, 30, 29,
       41, 56, 50, 52, 59, 27, 36,  1,  6, 34, 10, 43, 13, 51, 37, 65, 12,
       53,  7,  0, 39, 32, 22, 15,  2,  5,  4, 64, 44, 40, 18, 46]), 'cur_cost': 99288.0}, {'tour': array([ 1, 15, 47,  6, 37, 36, 38,  7, 44, 58, 12, 31, 11, 40, 57, 25, 28,
       63, 65,  0, 61, 22, 59, 64, 21,  3, 52, 60, 48, 27, 35, 46, 14, 33,
       32, 23, 41, 62, 42, 43, 26, 19, 17, 53,  8, 51, 50, 55, 45,  5, 34,
       24, 29,  2,  4, 10, 30, 56, 39,  9, 54, 18, 13, 16, 20, 49]), 'cur_cost': 100404.0}, {'tour': array([23, 65, 43, 12, 47, 30, 61, 13, 39, 38, 40, 50, 42, 55, 54, 17, 15,
        6, 14, 52, 26,  8, 62, 10, 58,  7, 60, 44, 22, 48,  9, 25, 36, 53,
       21,  5, 19, 57, 64, 27, 31, 56, 34, 32, 18,  0, 16,  1,  2,  3, 20,
       28,  4, 11, 37, 24, 63, 46, 45, 35, 49, 41, 29, 33, 51, 59]), 'cur_cost': 95077.0}]
2025-06-22 18:55:01,678 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:55:01,679 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 18:55:01,680 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:55:01,680 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 18:55:01,680 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:55:01,680 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [55, 5, 37] and ending with [35, 8, 56]
- Cost: 108581.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8676923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:55:01,680 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:55:05,477 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35
  ],
  "modification_strategy": "This strategy prioritizes diversity by heavily disrupting the existing path's structure. It starts with the same starting node but then deviates significantly, utilizing a combination of random selection and avoidance of the previously explored path segments. This approach is intended to explore unvisited nodes and potentially find more promising routes by sampling from different parts of the solution space.",
  "targeted_regions": "This path targets the 'unexplored regions' and 'opportunity regions' by incorporating nodes that have not been part of the previous solution. It aims to cover the entire space of nodes (0-65) which are likely to include several optimal routes which are missed in the original path. The order of nodes is shuffled and random to maximise diversity."
}
```
2025-06-22 18:55:05,477 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:55:05,477 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118837.0, 路径: [55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35]
2025-06-22 18:55:05,477 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35], 'cur_cost': 118837.0}
2025-06-22 18:55:05,477 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:55:05,477 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:55:05,477 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:55:05,477 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 116820.0
2025-06-22 18:55:05,978 - ExploitationExpert - INFO - res_population_num: 20
2025-06-22 18:55:05,978 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9526, 9527, 9545, 9551, 9593, 91003, 9521, 9521, 9521]
2025-06-22 18:55:05,979 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:55:05,986 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:55:05,986 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}, {'tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}, {'tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}, {'tour': array([19, 13, 37,  8, 57,  1, 15, 41, 23, 25, 16, 24,  9, 11, 35, 12, 27,
       28,  4, 50, 10, 61,  7, 47,  6, 39, 14, 38, 33, 29, 17, 51, 20,  2,
       49, 26, 56, 42, 64, 55, 65, 31, 21,  5, 59, 43, 60, 44, 32,  3, 22,
        0, 34, 18, 53, 40, 58, 48, 46, 30, 36, 52, 63, 54, 62, 45]), 'cur_cost': 104006.0}, {'tour': [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47], 'cur_cost': 111512.0}, {'tour': array([44, 63,  0, 23, 64, 65,  7, 25, 51, 26, 46, 55, 40, 22, 53, 52, 48,
       20, 62, 41, 33, 49, 16, 43, 18,  1, 24, 59, 32, 11,  2, 45, 56, 15,
       29, 38,  6, 37,  5, 13, 35,  4, 36,  9, 34, 50, 27, 31,  8,  3, 54,
       30, 17, 10, 12, 21, 14, 39, 57, 47, 19, 28, 42, 61, 60, 58]), 'cur_cost': 113264.0}, {'tour': [55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35], 'cur_cost': 118837.0}, {'tour': array([43, 30, 48,  5, 34, 22, 64, 41,  6, 62, 36, 37, 20, 61, 19, 52, 49,
        8, 59, 44,  7, 51, 46, 21, 17, 23, 40,  3, 28, 47, 25, 39, 33, 56,
       26, 45, 27, 42, 55, 58,  9, 31, 13, 14,  1, 53,  2, 54, 29, 15, 63,
       35, 65,  0, 10,  4, 38, 16, 11, 18, 57, 24, 50, 60, 12, 32]), 'cur_cost': 116820.0}, {'tour': array([ 1, 15, 47,  6, 37, 36, 38,  7, 44, 58, 12, 31, 11, 40, 57, 25, 28,
       63, 65,  0, 61, 22, 59, 64, 21,  3, 52, 60, 48, 27, 35, 46, 14, 33,
       32, 23, 41, 62, 42, 43, 26, 19, 17, 53,  8, 51, 50, 55, 45,  5, 34,
       24, 29,  2,  4, 10, 30, 56, 39,  9, 54, 18, 13, 16, 20, 49]), 'cur_cost': 100404.0}, {'tour': array([23, 65, 43, 12, 47, 30, 61, 13, 39, 38, 40, 50, 42, 55, 54, 17, 15,
        6, 14, 52, 26,  8, 62, 10, 58,  7, 60, 44, 22, 48,  9, 25, 36, 53,
       21,  5, 19, 57, 64, 27, 31, 56, 34, 32, 18,  0, 16,  1,  2,  3, 20,
       28,  4, 11, 37, 24, 63, 46, 45, 35, 49, 41, 29, 33, 51, 59]), 'cur_cost': 95077.0}]
2025-06-22 18:55:05,986 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:55:05,988 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 18:55:05,988 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:55:05,988 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 18:55:05,988 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:55:05,988 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 15, 47] and ending with [16, 20, 49]
- Cost: 100404.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8630769230769231
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:55:05,988 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:55:09,078 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38,
    39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56,
    57, 58, 59, 60, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "Complete Reordering and Exploration. Generated a new path by creating a simple sequential order from 0 to 65 and then wrapping around to 0. This should provide a significantly different path and ensure all nodes are visited exactly once while remaining within the index constraints. This also effectively targets all regions.",
  "targeted_regions": "All regions are targeted for exploration by visiting each node exactly once in a seemingly uniform order."
}
```
2025-06-22 18:55:09,078 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:55:09,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0]
2025-06-22 18:55:09,078 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}
2025-06-22 18:55:09,078 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:55:09,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:55:09,081 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:55:09,081 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 106426.0
2025-06-22 18:55:09,582 - ExploitationExpert - INFO - res_population_num: 20
2025-06-22 18:55:09,583 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9524, 9526, 9526, 9527, 9545, 9551, 9593, 91003, 9521, 9521, 9521]
2025-06-22 18:55:09,583 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 18, 16, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 56, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 62, 59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 13, 20, 21, 19, 16,
       18, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56, 59, 62,
       53, 64, 57, 54, 52, 63, 65, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 53,  6, 48, 26, 33,  2, 22, 50, 19, 27, 16, 58, 62,  5, 65, 18,
        3, 31, 60, 17, 32, 36, 41,  7, 51, 47, 12, 37, 15, 40, 35, 14, 23,
       11,  1, 56, 59, 46, 49,  4, 20, 45, 42, 13, 28, 38, 57,  9, 44, 25,
       24, 61, 21, 43, 30, 34, 52, 63, 10, 39, 29,  8, 64, 54, 55],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:55:09,589 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:55:09,589 - ExploitationExpert - INFO - populations: [{'tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}, {'tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}, {'tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}, {'tour': array([19, 13, 37,  8, 57,  1, 15, 41, 23, 25, 16, 24,  9, 11, 35, 12, 27,
       28,  4, 50, 10, 61,  7, 47,  6, 39, 14, 38, 33, 29, 17, 51, 20,  2,
       49, 26, 56, 42, 64, 55, 65, 31, 21,  5, 59, 43, 60, 44, 32,  3, 22,
        0, 34, 18, 53, 40, 58, 48, 46, 30, 36, 52, 63, 54, 62, 45]), 'cur_cost': 104006.0}, {'tour': [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47], 'cur_cost': 111512.0}, {'tour': array([44, 63,  0, 23, 64, 65,  7, 25, 51, 26, 46, 55, 40, 22, 53, 52, 48,
       20, 62, 41, 33, 49, 16, 43, 18,  1, 24, 59, 32, 11,  2, 45, 56, 15,
       29, 38,  6, 37,  5, 13, 35,  4, 36,  9, 34, 50, 27, 31,  8,  3, 54,
       30, 17, 10, 12, 21, 14, 39, 57, 47, 19, 28, 42, 61, 60, 58]), 'cur_cost': 113264.0}, {'tour': [55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35], 'cur_cost': 118837.0}, {'tour': array([43, 30, 48,  5, 34, 22, 64, 41,  6, 62, 36, 37, 20, 61, 19, 52, 49,
        8, 59, 44,  7, 51, 46, 21, 17, 23, 40,  3, 28, 47, 25, 39, 33, 56,
       26, 45, 27, 42, 55, 58,  9, 31, 13, 14,  1, 53,  2, 54, 29, 15, 63,
       35, 65,  0, 10,  4, 38, 16, 11, 18, 57, 24, 50, 60, 12, 32]), 'cur_cost': 116820.0}, {'tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}, {'tour': array([62, 60, 39, 13,  6, 24, 59, 23, 57, 44,  7, 38, 47, 63, 25, 32, 43,
        2, 12, 20, 37, 56, 50, 64, 55, 45, 22,  9, 52, 27, 28, 16, 33, 18,
        1,  8, 51, 40, 46, 30,  0,  5, 21, 42, 58, 35, 54, 17, 36, 49, 34,
       15, 41,  3, 29, 14, 31, 53, 65, 48, 26, 19, 61,  4, 11, 10]), 'cur_cost': 106426.0}]
2025-06-22 18:55:09,591 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:55:09,591 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-22 18:55:09,591 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:55:09,592 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [42, 50, 38, 3, 5, 8, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 1, 4, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 39, 41, 44, 47, 51, 54, 57, 60, 63, 2, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 45, 48, 53, 56, 59, 62, 0, 65], 'cur_cost': 34012.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [65, 10, 22, 3, 55, 1, 48, 2, 57, 4, 44, 5, 38, 6, 60, 7, 32, 8, 50, 9, 31, 11, 59, 12, 43, 13, 26, 14, 27, 15, 41, 16, 47, 17, 29, 18, 34, 19, 53, 20, 64, 21, 37, 23, 56, 24, 35, 25, 63, 28, 40, 30, 58, 33, 51, 36, 49, 39, 42, 45, 61, 46, 54, 52, 0, 62], 'cur_cost': 107089.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [31, 25, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 15817.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [40, 32, 50, 12, 35, 41, 2, 54, 21, 15, 60, 1, 44, 3, 48, 58, 7, 19, 33, 52, 9, 28, 38, 61, 26, 5, 14, 36, 45, 34, 20, 53, 17, 42, 27, 11, 31, 63, 4, 56, 23, 55, 64, 65, 0, 25, 16, 8, 51, 29, 18, 37, 62, 6, 46, 49, 10, 22, 59, 24, 30, 13, 43, 57, 39, 47], 'cur_cost': 111512.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [55, 5, 37, 12, 28, 45, 1, 18, 22, 63, 3, 58, 40, 60, 16, 50, 31, 9, 51, 2, 13, 21, 47, 39, 4, 26, 52, 48, 33, 54, 0, 43, 19, 23, 11, 64, 30, 38, 10, 25, 62, 42, 53, 65, 7, 44, 36, 20, 24, 46, 15, 29, 56, 32, 8, 41, 14, 34, 57, 61, 6, 59, 27, 17, 49, 35], 'cur_cost': 118837.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}}]
2025-06-22 18:55:09,592 - __main__ - INFO - 进化阶段完成
2025-06-22 18:55:09,592 - __main__ - INFO - 开始评估阶段
2025-06-22 18:55:09,592 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:55:09,593 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:55:09,593 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/2
- Population changes:
  * Cost: min 11479.0->11479.0, mean 80331.9->83926.2
  * Diversity: 0.7738461538461539->0.8092307692307692
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 33.3%, avg improvement -395.5
- Exploit: 4 individuals, success rate 25.0%, avg improvement -8392.5
- Overall improvement: 0.0

## Historical Trends:
Last 1 iterations: costs [11479.0], diversity [0.9296296296296297]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:55:15,393 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Near Convergence",
    "features": {
      "cost_min": 11479.0,
      "cost_mean": 83926.2,
      "diversity": 0.8092307692307692,
      "elite_solutions": 1,
      "historical_trends": "Cost is stagnating at its minimum, diversity is increasing, but slowly. No change in elite solutions. This indicates that the algorithm is potentially stuck or near a local optima."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.333,
      "avg_improvement": -395.5,
      "evaluation": "Explore is having limited success, with a low success rate and small negative average improvement. This suggests the exploration is not finding significantly better solutions. Explore attempts, though, seem to be reducing the cost slightly.",
      "individual_count": 6
    },
    "exploit": {
      "success_rate": 0.25,
      "avg_improvement": -8392.5,
      "evaluation": "Exploit is also performing poorly, with a low success rate and large negative average improvement. This suggests the exploit strategy is moving the population away from the optimal region of the search space.",
      "individual_count": 4
    },
    "overall": {
      "improvement": 0.0,
      "assessment": "Overall, there is no improvement, suggesting that the current strategy is ineffective in progressing towards a better solution. Both exploration and exploitation are regressing in solution quality on average."
    }
  },
  "balance_state": {
    "assessment": "The system appears to be imbalanced. Both explore and exploit are performing badly; therefore, the problem may not be with the balance but the operators themselves. The focus should be on improving the quality of the solutions generated by both strategies. More exploration is likely required.",
    "adjustment_needs": "Prioritize modifying/improving explore and exploit operators to locate and refine better regions of the search space. The balance is secondary to the quality of the operators.",
    "possible_actions": [
        "Adjust operators: Improve the selection criteria, mutation rates, or crossover methods used in both explore and exploit.",
        "Modify Parameters: Fine-tune parameters of both explore and exploit strategies. Explore could benefit from more diversity.",
        "Increase Explore Weight: Temporarily increase the proportion of individuals allocated to explore to help diversify the population."
    ]
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Operator Optimization:** Focus on refining the Explore and Exploit strategies. Investigate why both are yielding negative average improvements and low success rates. Consider changing or reconfiguring the strategies.",
      "**Explore Strategy Enhancement:** Experiment with more diverse exploration techniques (e.g., larger mutation steps, more exploration individuals, different exploration operators, increasing the total number of exploration attempts). This could help escape local optima. Ensure that these exploration attempts don't have too large a step size to avoid moving further from the optimum.",
      "**Exploit Strategy Review:** Analyze the Exploit strategy's behavior and try to understand the causes for failure. Is it too aggressive? Are the selected parents not representative of the better solutions? Are the exploit operators, too destructive?",
      "**Diversity Management:** While diversity is slowly increasing, monitor it closely. Ensure the population is sufficiently diverse to allow exploration to find new areas of the search space. Consider a more aggressive method to increase diversity."
    ]
  }
}
```
2025-06-22 18:55:15,422 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:55:15,422 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Near Convergence",
    "features": {
      "cost_min": 11479.0,
      "cost_mean": 83926.2,
      "diversity": 0.8092307692307692,
      "elite_solutions": 1,
      "historical_trends": "Cost is stagnating at its minimum, diversity is increasing, but slowly. No change in elite solutions. This indicates that the algorithm is potentially stuck or near a local optima."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.333,
      "avg_improvement": -395.5,
      "evaluation": "Explore is having limited success, with a low success rate and small negative average improvement. This suggests the exploration is not finding significantly better solutions. Explore attempts, though, seem to be reducing the cost slightly.",
      "individual_count": 6
    },
    "exploit": {
      "success_rate": 0.25,
      "avg_improvement": -8392.5,
      "evaluation": "Exploit is also performing poorly, with a low success rate and large negative average improvement. This suggests the exploit strategy is moving the population away from the optimal region of the search space.",
      "individual_count": 4
    },
    "overall": {
      "improvement": 0.0,
      "assessment": "Overall, there is no improvement, suggesting that the current strategy is ineffective in progressing towards a better solution. Both exploration and exploitation are regressing in solution quality on average."
    }
  },
  "balance_state": {
    "assessment": "The system appears to be imbalanced. Both explore and exploit are performing badly; therefore, the problem may not be with the balance but the operators themselves. The focus should be on improving the quality of the solutions generated by both strategies. More exploration is likely required.",
    "adjustment_needs": "Prioritize modifying/improving explore and exploit operators to locate and refine better regions of the search space. The balance is secondary to the quality of the operators.",
    "possible_actions": [
        "Adjust operators: Improve the selection criteria, mutation rates, or crossover methods used in both explore and exploit.",
        "Modify Parameters: Fine-tune parameters of both explore and exploit strategies. Explore could benefit from more diversity.",
        "Increase Explore Weight: Temporarily increase the proportion of individuals allocated to explore to help diversify the population."
    ]
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Operator Optimization:** Focus on refining the Explore and Exploit strategies. Investigate why both are yielding negative average improvements and low success rates. Consider changing or reconfiguring the strategies.",
      "**Explore Strategy Enhancement:** Experiment with more diverse exploration techniques (e.g., larger mutation steps, more exploration individuals, different exploration operators, increasing the total number of exploration attempts). This could help escape local optima. Ensure that these exploration attempts don't have too large a step size to avoid moving further from the optimum.",
      "**Exploit Strategy Review:** Analyze the Exploit strategy's behavior and try to understand the causes for failure. Is it too aggressive? Are the selected parents not representative of the better solutions? Are the exploit operators, too destructive?",
      "**Diversity Management:** While diversity is slowly increasing, monitor it closely. Ensure the population is sufficiently diverse to allow exploration to find new areas of the search space. Consider a more aggressive method to increase diversity."
    ]
  }
}
```
2025-06-22 18:55:15,423 - __main__ - INFO - 评估阶段完成
2025-06-22 18:55:15,423 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Near Convergence",
    "features": {
      "cost_min": 11479.0,
      "cost_mean": 83926.2,
      "diversity": 0.8092307692307692,
      "elite_solutions": 1,
      "historical_trends": "Cost is stagnating at its minimum, diversity is increasing, but slowly. No change in elite solutions. This indicates that the algorithm is potentially stuck or near a local optima."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.333,
      "avg_improvement": -395.5,
      "evaluation": "Explore is having limited success, with a low success rate and small negative average improvement. This suggests the exploration is not finding significantly better solutions. Explore attempts, though, seem to be reducing the cost slightly.",
      "individual_count": 6
    },
    "exploit": {
      "success_rate": 0.25,
      "avg_improvement": -8392.5,
      "evaluation": "Exploit is also performing poorly, with a low success rate and large negative average improvement. This suggests the exploit strategy is moving the population away from the optimal region of the search space.",
      "individual_count": 4
    },
    "overall": {
      "improvement": 0.0,
      "assessment": "Overall, there is no improvement, suggesting that the current strategy is ineffective in progressing towards a better solution. Both exploration and exploitation are regressing in solution quality on average."
    }
  },
  "balance_state": {
    "assessment": "The system appears to be imbalanced. Both explore and exploit are performing badly; therefore, the problem may not be with the balance but the operators themselves. The focus should be on improving the quality of the solutions generated by both strategies. More exploration is likely required.",
    "adjustment_needs": "Prioritize modifying/improving explore and exploit operators to locate and refine better regions of the search space. The balance is secondary to the quality of the operators.",
    "possible_actions": [
        "Adjust operators: Improve the selection criteria, mutation rates, or crossover methods used in both explore and exploit.",
        "Modify Parameters: Fine-tune parameters of both explore and exploit strategies. Explore could benefit from more diversity.",
        "Increase Explore Weight: Temporarily increase the proportion of individuals allocated to explore to help diversify the population."
    ]
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Operator Optimization:** Focus on refining the Explore and Exploit strategies. Investigate why both are yielding negative average improvements and low success rates. Consider changing or reconfiguring the strategies.",
      "**Explore Strategy Enhancement:** Experiment with more diverse exploration techniques (e.g., larger mutation steps, more exploration individuals, different exploration operators, increasing the total number of exploration attempts). This could help escape local optima. Ensure that these exploration attempts don't have too large a step size to avoid moving further from the optimum.",
      "**Exploit Strategy Review:** Analyze the Exploit strategy's behavior and try to understand the causes for failure. Is it too aggressive? Are the selected parents not representative of the better solutions? Are the exploit operators, too destructive?",
      "**Diversity Management:** While diversity is slowly increasing, monitor it closely. Ensure the population is sufficiently diverse to allow exploration to find new areas of the search space. Consider a more aggressive method to increase diversity."
    ]
  }
}
```
2025-06-22 18:55:15,423 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-22 18:55:15,426 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 18:55:15,438 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 18:55:15,439 - __main__ - INFO - 实例 composite13_66 处理完成
