"""配置文件 - 存储API密钥和其他配置信息
注意: 在生产环境中，应将此文件添加到.gitignore以避免泄露敏感信息
"""

# API配置
API_CONFIG = {
    # 讯飞星火API
    "xfyun": {
        "endpoint": "https://maas-api.cn-huabei-1.xf-yun.com/v1",
        "api_key": "sk-Oq7cjXadPI3JRMIhFdFb83E93e8343Ee96B51205E1859b27",
        "model": "xdeepseekr1"
    },
    
    # DeepSeek API
    "deepseek": {
        "api_base": "https://api.deepseek.com",
        "api_key": "***********************************",
        # "model": "deepseek-reasoner"
        "model": "deepseek-chat"
    },
    
    # 智谱AI API
    "zhipu": {
        "api_key": "e24cdd5e8735465dfd5817655250e3db.bVkP5vE2g7RqS53T",
        "model": "glm-4-air"
    },
    
    # Google Gemini API
    "gemini": {
        "api_base": "https://generativelanguage.googleapis.com/v1beta/models",
        "api_key": "AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ",  # Replace with your actual API key
        # "model": "gemini-2.5-pro-exp-03-25"
        # "model": "gemini-2.0-flash"
        "model": "gemini-2.0-flash-lite"
    }
}

# 算法配置
ALGORITHM_CONFIG = {
    "pop_size": 10,              # 种群数量
    "evo_num": 20,               # 进化代数
    "time_limit": 0.5,          # 时间限制(秒)
    "iter_max": 1000,             # 最大迭代次数
    "perturbation_moves": 1    # 扰动移动次数
}

# 路径配置
def get_paths(project_root):
    """根据项目根目录获取所有相关路径"""
    return {
        "input_path": f"{project_root}\\benchmark_MMTSP",
        "input_path_tsplib": f"{project_root}\\results\\TSPLIB-ms",
        "output_path": f"{project_root}\\benchmark_MMTSP\\instance_pkl",
        "output_path_tsplib": f"{project_root}\\results\\TSPLIB-ms\\instance_pkl",
        "visualization_dir": f"{project_root}\\results\\visualization",
        "adaptive_clustering_dir": f"{project_root}\\results\\visualization\\adaptive_clustering",
        "log_dir": f"{project_root}\\results\\deepseekR1-test\\Log",
        "strategy_tracker_dir": f"{project_root}\\results\\strategy_tracking"
    }