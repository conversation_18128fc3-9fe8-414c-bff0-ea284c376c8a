2025-06-26 20:22:43,108 - __main__ - INFO - pr76 开始进化第 1 代
2025-06-26 20:22:43,108 - __main__ - INFO - 开始分析阶段
2025-06-26 20:22:43,108 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:22:43,133 - <PERSON>atsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 131607.0, 'max': 625022.0, 'mean': 445284.1, 'std': 200960.48781710796}, 'diversity': 0.927485380116959, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:22:43,133 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 131607.0, 'max': 625022.0, 'mean': 445284.1, 'std': 200960.48781710796}, 'diversity_level': 0.927485380116959, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3600, 2300], [3100, 3300], [4700, 5750], [5400, 5750], [5608, 7103], [4493, 7102], [3600, 6950], [3100, 7250], [4700, 8450], [5400, 8450], [5610, 10053], [4492, 10052], [3600, 10800], [3100, 10950], [4700, 11650], [5400, 11650], [6650, 10800], [7300, 10950], [7300, 7250], [6650, 6950], [7300, 3300], [6650, 2300], [5400, 1600], [8350, 2300], [7850, 3300], [9450, 5750], [10150, 5750], [10358, 7103], [9243, 7102], [8350, 6950], [7850, 7250], [9450, 8450], [10150, 8450], [10360, 10053], [9242, 10052], [8350, 10800], [7850, 10950], [9450, 11650], [10150, 11650], [11400, 10800], [12050, 10950], [12050, 7250], [11400, 6950], [12050, 3300], [11400, 2300], [10150, 1600], [13100, 2300], [12600, 3300], [14200, 5750], [14900, 5750], [15108, 7103], [13993, 7102], [13100, 6950], [12600, 7250], [14200, 8450], [14900, 8450], [15110, 10053], [13992, 10052], [13100, 10800], [12600, 10950], [14200, 11650], [14900, 11650], [16150, 10800], [16800, 10950], [16800, 7250], [16150, 6950], [16800, 3300], [16150, 2300], [14900, 1600], [19800, 800], [19800, 10000], [19800, 11900], [19800, 12200], [200, 12200], [200, 1100], [200, 800]], 'distance_matrix': array([[    0.,  1118.,  3621., ..., 10468.,  3606.,  3716.],
       [ 1118.,     0.,  2926., ...,  9361.,  3640.,  3829.],
       [ 3621.,  2926.,     0., ...,  7865.,  6471.,  6690.],
       ...,
       [10468.,  9361.,  7865., ...,     0., 11100., 11400.],
       [ 3606.,  3640.,  6471., ..., 11100.,     0.,   300.],
       [ 3716.,  3829.,  6690., ..., 11400.,   300.,     0.]])}
2025-06-26 20:22:43,133 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:22:43,133 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:22:43,133 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:22:43,133 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:22:43,140 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (10, 11, 12), 'frequency': 0.3}, {'subpath': (11, 12, 13), 'frequency': 0.3}, {'subpath': (12, 13, 14), 'frequency': 0.3}, {'subpath': (13, 14, 15), 'frequency': 0.3}, {'subpath': (57, 56, 62), 'frequency': 0.3}, {'subpath': (56, 62, 63), 'frequency': 0.3}, {'subpath': (62, 63, 61), 'frequency': 0.3}, {'subpath': (63, 61, 60), 'frequency': 0.3}, {'subpath': (50, 65, 64), 'frequency': 0.3}, {'subpath': (5, 6, 7), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(11, 12)', 'frequency': 0.4}, {'edge': '(16, 17)', 'frequency': 0.4}, {'edge': '(40, 59)', 'frequency': 0.4}, {'edge': '(56, 57)', 'frequency': 0.4}, {'edge': '(60, 61)', 'frequency': 0.4}, {'edge': '(50, 65)', 'frequency': 0.4}, {'edge': '(74, 75)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(10, 11)', 'frequency': 0.3}, {'edge': '(12, 13)', 'frequency': 0.3}, {'edge': '(13, 14)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(17, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(34, 35)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(33, 39)', 'frequency': 0.3}, {'edge': '(39, 40)', 'frequency': 0.3}, {'edge': '(58, 59)', 'frequency': 0.3}, {'edge': '(57, 58)', 'frequency': 0.3}, {'edge': '(56, 62)', 'frequency': 0.3}, {'edge': '(62, 63)', 'frequency': 0.3}, {'edge': '(61, 63)', 'frequency': 0.3}, {'edge': '(54, 55)', 'frequency': 0.3}, {'edge': '(64, 65)', 'frequency': 0.3}, {'edge': '(48, 49)', 'frequency': 0.3}, {'edge': '(51, 52)', 'frequency': 0.3}, {'edge': '(52, 53)', 'frequency': 0.3}, {'edge': '(41, 53)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(27, 42)', 'frequency': 0.3}, {'edge': '(27, 28)', 'frequency': 0.3}, {'edge': '(28, 29)', 'frequency': 0.3}, {'edge': '(29, 30)', 'frequency': 0.3}, {'edge': '(18, 30)', 'frequency': 0.3}, {'edge': '(18, 19)', 'frequency': 0.3}, {'edge': '(4, 19)', 'frequency': 0.3}, {'edge': '(5, 6)', 'frequency': 0.3}, {'edge': '(6, 7)', 'frequency': 0.3}, {'edge': '(8, 9)', 'frequency': 0.3}, {'edge': '(2, 3)', 'frequency': 0.3}, {'edge': '(1, 2)', 'frequency': 0.3}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(0, 22)', 'frequency': 0.3}, {'edge': '(21, 22)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(20, 24)', 'frequency': 0.3}, {'edge': '(23, 24)', 'frequency': 0.2}, {'edge': '(23, 45)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(43, 44)', 'frequency': 0.2}, {'edge': '(43, 47)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 68)', 'frequency': 0.2}, {'edge': '(67, 68)', 'frequency': 0.3}, {'edge': '(66, 67)', 'frequency': 0.3}, {'edge': '(69, 70)', 'frequency': 0.2}, {'edge': '(70, 71)', 'frequency': 0.3}, {'edge': '(71, 72)', 'frequency': 0.3}, {'edge': '(37, 38)', 'frequency': 0.3}, {'edge': '(31, 32)', 'frequency': 0.3}, {'edge': '(26, 32)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(73, 75)', 'frequency': 0.2}, {'edge': '(3, 4)', 'frequency': 0.2}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(55, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.2}, {'edge': '(58, 60)', 'frequency': 0.2}, {'edge': '(10, 16)', 'frequency': 0.2}, {'edge': '(9, 15)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(37, 73)', 'frequency': 0.2}, {'edge': '(25, 31)', 'frequency': 0.2}, {'edge': '(49, 66)', 'frequency': 0.2}, {'edge': '(24, 25)', 'frequency': 0.2}, {'edge': '(1, 74)', 'frequency': 0.2}, {'edge': '(27, 49)', 'frequency': 0.2}, {'edge': '(9, 65)', 'frequency': 0.2}, {'edge': '(12, 14)', 'frequency': 0.2}, {'edge': '(59, 75)', 'frequency': 0.2}, {'edge': '(16, 66)', 'frequency': 0.2}, {'edge': '(3, 31)', 'frequency': 0.2}, {'edge': '(35, 63)', 'frequency': 0.2}, {'edge': '(6, 72)', 'frequency': 0.2}, {'edge': '(17, 26)', 'frequency': 0.2}, {'edge': '(36, 62)', 'frequency': 0.2}, {'edge': '(52, 71)', 'frequency': 0.2}, {'edge': '(0, 67)', 'frequency': 0.2}, {'edge': '(33, 46)', 'frequency': 0.2}, {'edge': '(7, 46)', 'frequency': 0.2}, {'edge': '(4, 15)', 'frequency': 0.3}, {'edge': '(8, 35)', 'frequency': 0.2}, {'edge': '(22, 32)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(47, 74)', 'frequency': 0.3}, {'edge': '(19, 42)', 'frequency': 0.2}, {'edge': '(28, 57)', 'frequency': 0.2}, {'edge': '(25, 64)', 'frequency': 0.2}, {'edge': '(48, 64)', 'frequency': 0.2}, {'edge': '(29, 52)', 'frequency': 0.2}, {'edge': '(56, 68)', 'frequency': 0.2}, {'edge': '(67, 71)', 'frequency': 0.2}, {'edge': '(34, 48)', 'frequency': 0.2}, {'edge': '(19, 66)', 'frequency': 0.2}, {'edge': '(20, 53)', 'frequency': 0.2}, {'edge': '(55, 71)', 'frequency': 0.2}, {'edge': '(12, 21)', 'frequency': 0.2}, {'edge': '(5, 7)', 'frequency': 0.2}, {'edge': '(20, 69)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [27, 75, 70, 74, 33], 'cost': 68675.0, 'size': 5}, {'region': [46, 8, 63, 6, 72, 47], 'cost': 65037.0, 'size': 6}, {'region': [33, 73, 66, 22, 61], 'cost': 54574.0, 'size': 5}, {'region': [34, 69, 6, 72], 'cost': 48395.0, 'size': 4}, {'region': [29, 72, 0, 52], 'cost': 42159.0, 'size': 4}]}
2025-06-26 20:22:43,141 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:22:43,142 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:22:43,142 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:22:43,142 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 20:22:43,142 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 20:22:43,142 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:22:43,142 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:22:43,144 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:22:43,144 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 131607.0, mean 445284.1, max 625022.0, std 200960.48781710796
- diversity: 0.927485380116959
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3410.0, 3316.0, 3410.0] (Δ 47.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (10, 11, 12), 'frequency': 0.3}, {'subpath': (11, 12, 13), 'frequency': 0.3}, {'subpath': (12, 13, 14), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [27, 75, 70, 74, 33], 'cost': 68675.0, 'size': 5}, {'region': [46, 8, 63, 6, 72, 47], 'cost': 65037.0, 'size': 6}, {'region': [33, 73, 66, 22, 61], 'cost': 54574.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [200.0, 800.0, 19800.0, 12200.0]
- centroid/spread: [10014.592, 7383.289] | rms 5979.163
- density_grid(3x3): [[6, 8, 4], [7, 11, 6], [10, 13, 11]]
- edge_len_stats(min,q1,med,q3,max): [300.0, 4699.0, 7108.0, 9948.75, 22674.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [16, 3, 8, 4, 20, 9, 6, 9]
- nn_median_dist: 700.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:22:43,144 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:23:12,101 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High (wide range of edge lengths from 300 to 22674, high std deviation)",
    "modality": "Potentially multimodal (presence of common subpaths with moderate frequency)",
    "deceptiveness": "Moderate (long_edge_ratio of 0.25 suggests some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Good initial diversity (0.927), but uneven spatial distribution (density_grid shows clusters)",
    "convergence": "Early stage (0.0 convergence, no stagnation detected)"
  },
  "difficult_regions": [
    {"region": [27, 75, 70, 74, 33], "reason": "High cost (68675) in sparse NW quadrant (low density_grid[0][0])"},
    {"region": [46, 8, 63, 6, 72, 47], "reason": "High cost (65037) with long edges in NE quadrant"},
    {"region": [33, 73, 66, 22, 61], "reason": "High cost (54574) in directionally inconsistent area (sector histogram gaps)"}
  ],
  "opportunity_regions": [
    {"region": [10, 11, 12, 13, 14], "reason": "Frequent subpaths (0.3 frequency) in dense SE quadrant (density_grid[2][2]=11)"},
    {"region": "SW quadrant nodes", "reason": "High density (grid[2][0]=10) with directional consistency (sector 4 dominance)"}
  ],
  "evolution_phase": "Exploration (iter 0/5, high diversity)",
  "evolution_direction": [
    "Prioritize 2-opt near difficult regions to break long edges",
    "Enforce greedy crossover on opportunity region subpaths",
    "Adaptive mutation: higher rate in NW quadrant, lower in SE",
    "Add path-relinking between dense cells in SW and SE"
  ]
}
```
2025-06-26 20:23:12,101 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:23:12,101 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High (wide range of edge lengths from 300 to 22674, high std deviation)', 'modality': 'Potentially multimodal (presence of common subpaths with moderate frequency)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Good initial diversity (0.927), but uneven spatial distribution (density_grid shows clusters)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)'}, 'difficult_regions': [{'region': [27, 75, 70, 74, 33], 'reason': 'High cost (68675) in sparse NW quadrant (low density_grid[0][0])'}, {'region': [46, 8, 63, 6, 72, 47], 'reason': 'High cost (65037) with long edges in NE quadrant'}, {'region': [33, 73, 66, 22, 61], 'reason': 'High cost (54574) in directionally inconsistent area (sector histogram gaps)'}], 'opportunity_regions': [{'region': [10, 11, 12, 13, 14], 'reason': 'Frequent subpaths (0.3 frequency) in dense SE quadrant (density_grid[2][2]=11)'}, {'region': 'SW quadrant nodes', 'reason': 'High density (grid[2][0]=10) with directional consistency (sector 4 dominance)'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity)', 'evolution_direction': ['Prioritize 2-opt near difficult regions to break long edges', 'Enforce greedy crossover on opportunity region subpaths', 'Adaptive mutation: higher rate in NW quadrant, lower in SE', 'Add path-relinking between dense cells in SW and SE']}
2025-06-26 20:23:12,101 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:23:12,101 - __main__ - INFO - 分析阶段完成
2025-06-26 20:23:12,101 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High (wide range of edge lengths from 300 to 22674, high std deviation)', 'modality': 'Potentially multimodal (presence of common subpaths with moderate frequency)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Good initial diversity (0.927), but uneven spatial distribution (density_grid shows clusters)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)'}, 'difficult_regions': [{'region': [27, 75, 70, 74, 33], 'reason': 'High cost (68675) in sparse NW quadrant (low density_grid[0][0])'}, {'region': [46, 8, 63, 6, 72, 47], 'reason': 'High cost (65037) with long edges in NE quadrant'}, {'region': [33, 73, 66, 22, 61], 'reason': 'High cost (54574) in directionally inconsistent area (sector histogram gaps)'}], 'opportunity_regions': [{'region': [10, 11, 12, 13, 14], 'reason': 'Frequent subpaths (0.3 frequency) in dense SE quadrant (density_grid[2][2]=11)'}, {'region': 'SW quadrant nodes', 'reason': 'High density (grid[2][0]=10) with directional consistency (sector 4 dominance)'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity)', 'evolution_direction': ['Prioritize 2-opt near difficult regions to break long edges', 'Enforce greedy crossover on opportunity region subpaths', 'Adaptive mutation: higher rate in NW quadrant, lower in SE', 'Add path-relinking between dense cells in SW and SE']}
2025-06-26 20:23:12,101 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:23:12,101 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:23:12,101 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 131607.0
  • mean_cost: 445284.1
- Search-space: ruggedness High (wide range of edge lengths from 300 to 22674, high std deviation), modality Potentially multimodal (presence of common subpaths with moderate frequency), deceptiveness Moderate (long_edge_ratio of 0.25 suggests some deceptive long edges)
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [27, 75, , {'region': [46, 8, 6...
- Opportunity regions (# 2): {'region': [10, 11, , {'region': 'SW quadr
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:23:12,101 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:23:12,101 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:23:23,258 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and rugged landscape favors exploration. Top 70% explore to sample space; bottom 30% exploit early promising paths."
}
```
2025-06-26 20:23:23,258 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:23:23,258 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:23:23,258 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and rugged landscape favors exploration. Top 70% explore to sample space; bottom 30% exploit early promising paths."
}
```
2025-06-26 20:23:23,258 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:23:23,258 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:23:23,258 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and rugged landscape favors exploration. Top 70% explore to sample space; bottom 30% exploit early promising paths."
}
```
2025-06-26 20:23:23,264 - __main__ - INFO - 开始进化阶段
2025-06-26 20:23:23,264 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:23:23,264 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:23:23,264 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 131607.0 (rank 0.0%)
- Population diversity: 0.7706666666666667
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:23:23,264 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:23:23,264 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:23:40,966 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46],
  "targeted_regions": "SW opportunity region and adjacent sparse cells",
  "strategy_comment": "Prioritize SW region, skip difficult nodes 27/46, balance diversity"
}
```
2025-06-26 20:23:40,966 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:23:40,966 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174313.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46]
2025-06-26 20:23:40,966 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}
2025-06-26 20:23:40,966 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:23:40,966 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:23:40,966 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 147530.0 (rank 10.0%)
- Population diversity: 0.788
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:23:40,970 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:23:40,970 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:23:58,590 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46],
  "targeted_regions": "SW opportunity regions, avoiding 27 and 46",
  "strategy_comment": "Prioritize SW region, skip difficult nodes, balance diversity"
}
```
2025-06-26 20:23:58,590 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:23:58,591 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174313.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46]
2025-06-26 20:23:58,591 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}
2025-06-26 20:23:58,591 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:23:58,591 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:23:58,592 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 143812.0 (rank 0.0%)
- Population diversity: 0.7813333333333333
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:23:58,592 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:23:58,592 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:24:16,310 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27],
  "targeted_regions": "SW opportunity regions, avoiding 27 & 46",
  "strategy_comment": "Prioritize SW cells, shift difficult regions to end"
}
```
2025-06-26 20:24:16,311 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:24:16,311 - ExplorationExpert - INFO - 探索路径生成完成，成本: 187033.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27]
2025-06-26 20:24:16,311 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27], 'cur_cost': 187033.0}
2025-06-26 20:24:16,312 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:24:16,312 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:24:16,313 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 562487.0 (rank 60.0%)
- Population diversity: 0.7453333333333333
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:24:16,313 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:24:16,313 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:24:34,007 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46],
  "targeted_regions": "SW opportunity regions and adjacent sparse cells",
  "strategy_comment": "Prioritize SW regions, avoid difficult nodes 27 & 46, maintain diversity"
}
```
2025-06-26 20:24:34,007 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:24:34,007 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174313.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46]
2025-06-26 20:24:34,009 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}
2025-06-26 20:24:34,010 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:24:34,010 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:24:34,010 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 527839.0 (rank 40.0%)
- Population diversity: 0.6626666666666666
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:24:34,011 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:24:34,011 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:24:51,141 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9],
  "targeted_regions": "SW opportunity regions",
  "strategy_comment": "Prioritize SW region, avoid dense clusters, maintain diversity"
}
```
2025-06-26 20:24:51,152 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:24:51,152 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162576.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-06-26 20:24:51,152 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 162576.0}
2025-06-26 20:24:51,152 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:24:51,152 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:24:51,152 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 625022.0 (rank 90.0%)
- Population diversity: 0.588
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:24:51,152 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:24:51,152 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:25:08,552 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46],
  "targeted_regions": "SW opportunity regions, avoiding 27 & 46",
  "strategy_comment": "Cluster opportunity regions early, defer difficult nodes"
}
```
2025-06-26 20:25:08,553 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:25:08,553 - ExplorationExpert - INFO - 探索路径生成完成，成本: 187210.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46]
2025-06-26 20:25:08,554 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}
2025-06-26 20:25:08,554 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:25:08,554 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:25:08,554 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 603306.0 (rank 90.0%)
- Population diversity: 0.49333333333333335
- Evolution phase: Exploration (iter 0/5, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [10,, {'region': 'SW 
- Difficult regions to avoid (sample): {'region': [27,, {'region': [46,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:25:08,554 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:25:08,554 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:25:26,438 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46],
  "targeted_regions": "SW opportunity region and adjacent sparse cells",
  "strategy_comment": "Prioritize SW region, avoid difficult nodes 27/46, balance diversity"
}
```
2025-06-26 20:25:26,438 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:25:26,445 - ExplorationExpert - INFO - 探索路径生成完成，成本: 187210.0, 路径: [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46]
2025-06-26 20:25:26,445 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}
2025-06-26 20:25:26,445 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:25:26,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:25:26,445 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:25:26,446 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 618389.0
2025-06-26 20:25:26,948 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:25:26,948 - ExploitationExpert - INFO - res_population_costs: [112286, 111684, 109912, 109653, 109261, 109190, 109160, 109085, 108159]
2025-06-26 20:25:26,948 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:25:26,952 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:25:26,952 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27], 'cur_cost': 187033.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 162576.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': array([ 8, 48, 59, 50, 73, 24, 70,  4, 26, 53,  6, 69, 67, 57, 17, 40, 22,
       61, 16, 15, 45, 58, 30,  3, 52,  7, 18, 60, 51, 68, 49, 14, 12, 23,
       54, 21, 62,  5, 74, 13, 44,  0, 20, 27, 43,  2, 63, 41, 10,  1, 31,
        9, 32, 34, 28, 36, 71, 29, 46, 35, 56, 75, 37, 19, 72, 42, 64, 11,
       65, 66, 33, 55, 25, 38, 47, 39]), 'cur_cost': 618389.0}, {'tour': [68, 58, 21, 3, 54, 14, 22, 32, 52, 49, 29, 51, 63, 10, 72, 70, 16, 53, 17, 4, 31, 40, 34, 65, 73, 39, 62, 55, 71, 38, 27, 60, 15, 59, 74, 47, 1, 18, 75, 11, 9, 67, 0, 43, 69, 20, 23, 57, 6, 28, 45, 35, 8, 13, 44, 50, 41, 7, 30, 64, 25, 2, 48, 66, 19, 42, 37, 46, 33, 24, 26, 36, 12, 5, 61, 56], 'cur_cost': 561470.0}, {'tour': [47, 44, 3, 18, 40, 59, 52, 54, 70, 34, 37, 7, 5, 57, 56, 66, 1, 2, 19, 43, 12, 21, 64, 62, 48, 38, 50, 0, 74, 68, 36, 28, 16, 41, 14, 71, 17, 55, 26, 49, 27, 58, 9, 60, 10, 67, 73, 29, 45, 33, 31, 25, 39, 75, 51, 35, 4, 32, 15, 42, 13, 69, 20, 53, 65, 30, 22, 11, 23, 61, 24, 46, 8, 63, 6, 72], 'cur_cost': 597189.0}]
2025-06-26 20:25:26,954 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:25:26,954 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 36, 'skip_rate': 0.05555555555555555, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 34, 'cache_hits': 23, 'similarity_calculations': 280, 'cache_hit_rate': 0.08214285714285714, 'cache_size': 257}}
2025-06-26 20:25:26,954 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:25:26,954 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:25:26,954 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:25:26,954 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:25:26,954 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 569414.0
2025-06-26 20:25:27,456 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:25:27,458 - ExploitationExpert - INFO - res_population_costs: [112286, 111684, 109912, 109653, 109261, 109190, 109160, 109085, 108159]
2025-06-26 20:25:27,458 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:25:27,461 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:25:27,462 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27], 'cur_cost': 187033.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 162576.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': array([ 8, 48, 59, 50, 73, 24, 70,  4, 26, 53,  6, 69, 67, 57, 17, 40, 22,
       61, 16, 15, 45, 58, 30,  3, 52,  7, 18, 60, 51, 68, 49, 14, 12, 23,
       54, 21, 62,  5, 74, 13, 44,  0, 20, 27, 43,  2, 63, 41, 10,  1, 31,
        9, 32, 34, 28, 36, 71, 29, 46, 35, 56, 75, 37, 19, 72, 42, 64, 11,
       65, 66, 33, 55, 25, 38, 47, 39]), 'cur_cost': 618389.0}, {'tour': array([50, 49, 14, 13,  6, 68, 42, 27, 40, 15, 35, 28, 26, 53,  8, 67, 65,
       29, 47, 64, 22, 21, 32, 61, 55, 57, 73, 18, 30, 48, 45, 44, 36,  9,
       33,  4, 43, 63,  5, 10, 71, 23, 37, 54, 46, 12, 51, 17,  7, 38, 31,
        1,  2, 39, 62, 72, 19, 58, 60,  0, 16, 25, 75, 70, 69, 59,  3, 41,
       34, 24, 66, 20, 52, 11, 56, 74]), 'cur_cost': 569414.0}, {'tour': [47, 44, 3, 18, 40, 59, 52, 54, 70, 34, 37, 7, 5, 57, 56, 66, 1, 2, 19, 43, 12, 21, 64, 62, 48, 38, 50, 0, 74, 68, 36, 28, 16, 41, 14, 71, 17, 55, 26, 49, 27, 58, 9, 60, 10, 67, 73, 29, 45, 33, 31, 25, 39, 75, 51, 35, 4, 32, 15, 42, 13, 69, 20, 53, 65, 30, 22, 11, 23, 61, 24, 46, 8, 63, 6, 72], 'cur_cost': 597189.0}]
2025-06-26 20:25:27,463 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:25:27,463 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 37, 'skip_rate': 0.05405405405405406, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 35, 'cache_hits': 23, 'similarity_calculations': 281, 'cache_hit_rate': 0.08185053380782918, 'cache_size': 258}}
2025-06-26 20:25:27,463 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:25:27,463 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:25:27,463 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:25:27,464 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:25:27,465 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 604948.0
2025-06-26 20:25:27,967 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:25:27,967 - ExploitationExpert - INFO - res_population_costs: [112286, 111684, 109912, 109653, 109261, 109190, 109160, 109085, 108159]
2025-06-26 20:25:27,968 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:25:27,971 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:25:27,972 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27], 'cur_cost': 187033.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 162576.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}, {'tour': array([ 8, 48, 59, 50, 73, 24, 70,  4, 26, 53,  6, 69, 67, 57, 17, 40, 22,
       61, 16, 15, 45, 58, 30,  3, 52,  7, 18, 60, 51, 68, 49, 14, 12, 23,
       54, 21, 62,  5, 74, 13, 44,  0, 20, 27, 43,  2, 63, 41, 10,  1, 31,
        9, 32, 34, 28, 36, 71, 29, 46, 35, 56, 75, 37, 19, 72, 42, 64, 11,
       65, 66, 33, 55, 25, 38, 47, 39]), 'cur_cost': 618389.0}, {'tour': array([50, 49, 14, 13,  6, 68, 42, 27, 40, 15, 35, 28, 26, 53,  8, 67, 65,
       29, 47, 64, 22, 21, 32, 61, 55, 57, 73, 18, 30, 48, 45, 44, 36,  9,
       33,  4, 43, 63,  5, 10, 71, 23, 37, 54, 46, 12, 51, 17,  7, 38, 31,
        1,  2, 39, 62, 72, 19, 58, 60,  0, 16, 25, 75, 70, 69, 59,  3, 41,
       34, 24, 66, 20, 52, 11, 56, 74]), 'cur_cost': 569414.0}, {'tour': array([61, 14, 60, 43, 54, 53, 56, 58, 23,  7, 42, 33, 55, 68, 57, 35, 59,
       17, 74, 49, 13, 65, 31,  2, 38, 10, 18, 46,  6, 71,  8, 11, 20, 47,
       15, 39,  0, 29,  4, 36, 75, 16, 22, 12, 21, 19, 37, 24, 51, 70,  3,
       30, 72, 28, 62, 69, 41, 44, 50, 48,  5, 67, 73, 40, 63, 27, 25, 64,
        9, 34, 66,  1, 45, 52, 32, 26]), 'cur_cost': 604948.0}]
2025-06-26 20:25:27,974 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:25:27,974 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 38, 'skip_rate': 0.05263157894736842, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 36, 'cache_hits': 23, 'similarity_calculations': 283, 'cache_hit_rate': 0.0812720848056537, 'cache_size': 260}}
2025-06-26 20:25:27,974 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:25:27,974 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 46, 27], 'cur_cost': 187033.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 27, 46], 'cur_cost': 174313.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9], 'cur_cost': 162576.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 27, 46], 'cur_cost': 187210.0}}]
2025-06-26 20:25:27,974 - __main__ - INFO - 进化阶段完成
2025-06-26 20:25:27,975 - __main__ - INFO - 开始评估阶段
2025-06-26 20:25:27,975 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:25:27,976 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:25:27,977 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 131607.0 → 162576.0 (Δ -30969.0)
- Mean cost: 445284.1 → 303971.9
- Diversity: 0.771 → 0.388 (Δ -0.3827)

#### Elite metrics
- Elite count: 0 → 9
- Best elite cost: 131607.0 → 108159 (Δ 23448.0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ 213519.29
- Exploit: 3 inds, success 0.0% , avg Δ -27171.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [3410.0, 3316.0, 3410.0], diversity [0.7596825396825395, 0.8438095238095239, 0.8755555555555555]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:25:27,977 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:25:41,694 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 213519.29,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -27171.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "explore_heavy",
    "diversity": "low",
    "stagnation": "no"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 20:25:41,735 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:25:41,735 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 213519.29,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -27171.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "explore_heavy",
    "diversity": "low",
    "stagnation": "no"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 20:25:41,735 - __main__ - INFO - 评估阶段完成
2025-06-26 20:25:41,735 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 213519.29,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -27171.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "explore_heavy",
    "diversity": "low",
    "stagnation": "no"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 20:25:41,735 - __main__ - INFO - 当前最佳适应度: 162576.0
2025-06-26 20:25:41,735 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_route_0.pkl
2025-06-26 20:25:41,735 - __main__ - INFO - pr76 开始进化第 2 代
2025-06-26 20:25:41,735 - __main__ - INFO - 开始分析阶段
2025-06-26 20:25:41,742 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:25:41,761 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 162576.0, 'max': 618389.0, 'mean': 303971.9, 'std': 192684.3829937704}, 'diversity': 0.531578947368421, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:25:41,761 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 162576.0, 'max': 618389.0, 'mean': 303971.9, 'std': 192684.3829937704}, 'diversity_level': 0.531578947368421, 'convergence_level': 0.0, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'coordinates': [[3600, 2300], [3100, 3300], [4700, 5750], [5400, 5750], [5608, 7103], [4493, 7102], [3600, 6950], [3100, 7250], [4700, 8450], [5400, 8450], [5610, 10053], [4492, 10052], [3600, 10800], [3100, 10950], [4700, 11650], [5400, 11650], [6650, 10800], [7300, 10950], [7300, 7250], [6650, 6950], [7300, 3300], [6650, 2300], [5400, 1600], [8350, 2300], [7850, 3300], [9450, 5750], [10150, 5750], [10358, 7103], [9243, 7102], [8350, 6950], [7850, 7250], [9450, 8450], [10150, 8450], [10360, 10053], [9242, 10052], [8350, 10800], [7850, 10950], [9450, 11650], [10150, 11650], [11400, 10800], [12050, 10950], [12050, 7250], [11400, 6950], [12050, 3300], [11400, 2300], [10150, 1600], [13100, 2300], [12600, 3300], [14200, 5750], [14900, 5750], [15108, 7103], [13993, 7102], [13100, 6950], [12600, 7250], [14200, 8450], [14900, 8450], [15110, 10053], [13992, 10052], [13100, 10800], [12600, 10950], [14200, 11650], [14900, 11650], [16150, 10800], [16800, 10950], [16800, 7250], [16150, 6950], [16800, 3300], [16150, 2300], [14900, 1600], [19800, 800], [19800, 10000], [19800, 11900], [19800, 12200], [200, 12200], [200, 1100], [200, 800]], 'distance_matrix': array([[    0.,  1118.,  3621., ..., 10468.,  3606.,  3716.],
       [ 1118.,     0.,  2926., ...,  9361.,  3640.,  3829.],
       [ 3621.,  2926.,     0., ...,  7865.,  6471.,  6690.],
       ...,
       [10468.,  9361.,  7865., ...,     0., 11100., 11400.],
       [ 3606.,  3640.,  6471., ..., 11100.,     0.,   300.],
       [ 3716.,  3829.,  6690., ..., 11400.,   300.,     0.]])}
2025-06-26 20:25:41,762 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:25:41,762 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:25:41,762 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:25:41,765 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:25:41,765 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:25:41,765 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:25:41,765 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:25:41,766 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:25:41,766 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:25:41,769 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:25:41,770 - __main__ - INFO - 精英专家分析报告: {'elite_count': 9, 'elite_common_features': {'common_edges': {'(7, 6)': 0.7777777777777778, '(6, 5)': 0.7777777777777778}, 'common_edge_ratio': 0.02631578947368421}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 54417.0, 'avg_gap': 194150.7888888889}, 'structure_gap': {'unique_elite_edges': 149, 'unique_pop_edges': 243, 'common_edges': 67}}, 'elite_diversity': {'diversity_score': 0.19883040935672514}}
2025-06-26 20:25:41,770 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:25:41,770 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:25:41,772 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:25:41,772 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 162576.0, mean 303971.9, max 618389.0, std 192684.3829937704
- diversity: 0.531578947368421
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3316.0, 3410.0, 162576.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [200.0, 800.0, 19800.0, 12200.0]
- centroid/spread: [10014.592, 7383.289] | rms 5979.163
- density_grid(3x3): [[6, 8, 4], [7, 11, 6], [10, 13, 11]]
- edge_len_stats(min,q1,med,q3,max): [300.0, 4699.0, 7108.0, 9948.75, 22674.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [16, 3, 8, 4, 20, 9, 6, 9]
- nn_median_dist: 700.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:25:41,773 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:26:06,512 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 300 to 22674, with Q3 at 9948.75 indicating significant variability)",
    "modality": "Potentially multimodal (high std dev in costs, diverse direction histogram, and no clear clustering yet)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some locally optimal long edges may mislead)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.53, density_grid shows uneven distribution with some high-density cells like [2,2]=13)",
    "convergence": "Early stage (0.0 convergence, recent best costs show no stagnation)"
  },
  "difficult_regions": [
    "Low-density cells (e.g., [0,2]=4) or long-edge corridors (edges > Q3=9948.75)",
    "Direction sectors with low counts (e.g., sector 1=3, sector 3=4) may indicate sparse connectivity"
  ],
  "opportunity_regions": [
    "High-density cells (e.g., [2,2]=13 nodes, [1,1]=11 nodes) where local refinement may help",
    "Direction sectors with high counts (e.g., sector 0=16, sector 4=20) suggest promising edge orientations"
  ],
  "evolution_phase": "Exploration (low convergence, high diversity, no elites yet)",
  "evolution_direction": [
    "Prioritize density-guided operators (e.g., local search in high-density cells)",
    "Mitigate long-edge deception with edge recombination or 2-opt near Q3 length thresholds",
    "Balance exploration of low-density regions with exploitation of direction-sector biases"
  ]
}
```
2025-06-26 20:26:06,514 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:26:06,514 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674, with Q3 at 9948.75 indicating significant variability)', 'modality': 'Potentially multimodal (high std dev in costs, diverse direction histogram, and no clear clustering yet)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some locally optimal long edges may mislead)'}, 'population_state': {'coverage': 'Moderate (diversity 0.53, density_grid shows uneven distribution with some high-density cells like [2,2]=13)', 'convergence': 'Early stage (0.0 convergence, recent best costs show no stagnation)'}, 'difficult_regions': ['Low-density cells (e.g., [0,2]=4) or long-edge corridors (edges > Q3=9948.75)', 'Direction sectors with low counts (e.g., sector 1=3, sector 3=4) may indicate sparse connectivity'], 'opportunity_regions': ['High-density cells (e.g., [2,2]=13 nodes, [1,1]=11 nodes) where local refinement may help', 'Direction sectors with high counts (e.g., sector 0=16, sector 4=20) suggest promising edge orientations'], 'evolution_phase': 'Exploration (low convergence, high diversity, no elites yet)', 'evolution_direction': ['Prioritize density-guided operators (e.g., local search in high-density cells)', 'Mitigate long-edge deception with edge recombination or 2-opt near Q3 length thresholds', 'Balance exploration of low-density regions with exploitation of direction-sector biases']}
2025-06-26 20:26:06,514 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:26:06,514 - __main__ - INFO - 分析阶段完成
2025-06-26 20:26:06,515 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674, with Q3 at 9948.75 indicating significant variability)', 'modality': 'Potentially multimodal (high std dev in costs, diverse direction histogram, and no clear clustering yet)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some locally optimal long edges may mislead)'}, 'population_state': {'coverage': 'Moderate (diversity 0.53, density_grid shows uneven distribution with some high-density cells like [2,2]=13)', 'convergence': 'Early stage (0.0 convergence, recent best costs show no stagnation)'}, 'difficult_regions': ['Low-density cells (e.g., [0,2]=4) or long-edge corridors (edges > Q3=9948.75)', 'Direction sectors with low counts (e.g., sector 1=3, sector 3=4) may indicate sparse connectivity'], 'opportunity_regions': ['High-density cells (e.g., [2,2]=13 nodes, [1,1]=11 nodes) where local refinement may help', 'Direction sectors with high counts (e.g., sector 0=16, sector 4=20) suggest promising edge orientations'], 'evolution_phase': 'Exploration (low convergence, high diversity, no elites yet)', 'evolution_direction': ['Prioritize density-guided operators (e.g., local search in high-density cells)', 'Mitigate long-edge deception with edge recombination or 2-opt near Q3 length thresholds', 'Balance exploration of low-density regions with exploitation of direction-sector biases']}
2025-06-26 20:26:06,515 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:26:06,515 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:26:06,515 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 162576.0
  • mean_cost: 303971.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 300 to 22674, with Q3 at 9948.75 indicating significant variability), modality Potentially multimodal (high std dev in costs, diverse direction histogram, and no clear clustering yet), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some locally optimal long edges may mislead)
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells (e, Direction sectors wi
- Opportunity regions (# 2): High-density cells (, Direction sectors wi
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:26:06,516 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:26:06,516 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:26:17,161 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% performers exploit to refine promising paths."
}
```
2025-06-26 20:26:17,161 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:26:17,161 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:26:17,161 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% performers exploit to refine promising paths."
}
```
2025-06-26 20:26:17,161 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:26:17,161 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:26:17,161 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% performers exploit to refine promising paths."
}
```
2025-06-26 20:26:17,161 - __main__ - INFO - 开始进化阶段
2025-06-26 20:26:17,161 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:26:17,161 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:26:17,170 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 174313.0 (rank 10.0%)
- Population diversity: 0.388
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:26:17,170 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:26:17,170 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:26:34,446 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions while maintaining structure"
}
```
2025-06-26 20:26:34,452 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:26:34,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 556944.0, 路径: [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75]
2025-06-26 20:26:34,454 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}
2025-06-26 20:26:34,454 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:26:34,454 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:26:34,454 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 174313.0 (rank 10.0%)
- Population diversity: 0.48133333333333334
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:26:34,454 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:26:34,454 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:26:51,460 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 20:26:51,460 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:26:51,460 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:26:51,465 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:26:51,465 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:26:51,465 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:26:51,466 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 187033.0 (rank 20.0%)
- Population diversity: 0.49733333333333335
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:26:51,466 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:26:51,466 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:27:09,927 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions"
}
```
2025-06-26 20:27:09,927 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:27:09,930 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:27:09,930 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:27:09,930 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:27:09,930 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:27:09,930 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 174313.0 (rank 10.0%)
- Population diversity: 0.496
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:27:09,930 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:27:09,930 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:27:28,030 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Structured leapfrog pattern to explore opportunity regions"
}
```
2025-06-26 20:27:28,030 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:27:28,030 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:27:28,030 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:27:28,030 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:27:28,030 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:27:28,030 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 162576.0 (rank 0.0%)
- Population diversity: 0.49466666666666664
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:27:28,030 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:27:28,030 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:27:44,361 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Sequential exploration with opportunistic jumps"
}
```
2025-06-26 20:27:44,361 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:27:44,361 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:27:44,361 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:27:44,361 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:27:44,361 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:27:44,361 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 187210.0 (rank 10.0%)
- Population diversity: 0.496
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:27:44,361 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:27:44,368 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:28:02,372 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions while maintaining structure"
}
```
2025-06-26 20:28:02,372 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:28:02,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 556944.0, 路径: [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75]
2025-06-26 20:28:02,372 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}
2025-06-26 20:28:02,372 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:28:02,372 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:28:02,372 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 187210.0 (rank 10.0%)
- Population diversity: 0.496
- Evolution phase: Exploration (low convergence, high diversity, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Direction secto
- Difficult regions to avoid (sample): Low-density cel, Direction secto
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:28:02,372 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:28:02,372 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:28:20,458 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-density ce, Direction secto",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:28:20,458 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:28:20,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:28:20,458 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:28:20,458 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:28:20,458 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:28:20,458 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:28:20,458 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 574999.0
2025-06-26 20:28:20,960 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:28:20,960 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:28:20,960 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:28:20,964 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:28:20,964 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([72, 69, 33, 54, 43, 42, 70, 53, 19, 11, 39, 34, 26, 68, 13, 56,  2,
       23, 63, 55, 40, 12,  0, 45, 32,  1, 71, 61, 30, 18, 52, 17, 58, 67,
       37, 44,  6,  3, 57, 51, 35, 62, 10, 60, 15, 14, 59, 21, 64, 49, 66,
       75, 48, 16, 27, 28,  5,  8, 20, 24, 25, 47, 36,  9, 73, 38, 22, 41,
       29, 31, 65,  7, 46, 74, 50,  4]), 'cur_cost': 574999.0}, {'tour': array([50, 49, 14, 13,  6, 68, 42, 27, 40, 15, 35, 28, 26, 53,  8, 67, 65,
       29, 47, 64, 22, 21, 32, 61, 55, 57, 73, 18, 30, 48, 45, 44, 36,  9,
       33,  4, 43, 63,  5, 10, 71, 23, 37, 54, 46, 12, 51, 17,  7, 38, 31,
        1,  2, 39, 62, 72, 19, 58, 60,  0, 16, 25, 75, 70, 69, 59,  3, 41,
       34, 24, 66, 20, 52, 11, 56, 74]), 'cur_cost': 569414.0}, {'tour': array([61, 14, 60, 43, 54, 53, 56, 58, 23,  7, 42, 33, 55, 68, 57, 35, 59,
       17, 74, 49, 13, 65, 31,  2, 38, 10, 18, 46,  6, 71,  8, 11, 20, 47,
       15, 39,  0, 29,  4, 36, 75, 16, 22, 12, 21, 19, 37, 24, 51, 70,  3,
       30, 72, 28, 62, 69, 41, 44, 50, 48,  5, 67, 73, 40, 63, 27, 25, 64,
        9, 34, 66,  1, 45, 52, 32, 26]), 'cur_cost': 604948.0}]
2025-06-26 20:28:20,966 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:28:20,966 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 39, 'skip_rate': 0.05128205128205128, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 37, 'cache_hits': 23, 'similarity_calculations': 286, 'cache_hit_rate': 0.08041958041958042, 'cache_size': 263}}
2025-06-26 20:28:20,966 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:28:20,966 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:28:20,966 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:28:20,966 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:28:20,966 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 553005.0
2025-06-26 20:28:21,470 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:28:21,470 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:28:21,471 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:28:21,475 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:28:21,475 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([72, 69, 33, 54, 43, 42, 70, 53, 19, 11, 39, 34, 26, 68, 13, 56,  2,
       23, 63, 55, 40, 12,  0, 45, 32,  1, 71, 61, 30, 18, 52, 17, 58, 67,
       37, 44,  6,  3, 57, 51, 35, 62, 10, 60, 15, 14, 59, 21, 64, 49, 66,
       75, 48, 16, 27, 28,  5,  8, 20, 24, 25, 47, 36,  9, 73, 38, 22, 41,
       29, 31, 65,  7, 46, 74, 50,  4]), 'cur_cost': 574999.0}, {'tour': array([60, 61,  2,  8,  1, 58, 55, 18,  5, 49, 10, 28, 41, 47,  3, 37, 30,
       44, 34, 20, 23, 12, 52, 16, 13, 43, 46, 65, 57, 54, 66, 31, 42, 51,
       21, 73, 26, 53, 56, 39, 74, 72,  4, 17, 62, 71, 48, 24, 19, 35,  7,
       11, 50, 64,  6, 25, 70, 59, 38, 22, 69, 75, 36, 15, 27, 45,  0,  9,
       40, 63, 32, 29, 33, 67, 14, 68]), 'cur_cost': 553005.0}, {'tour': array([61, 14, 60, 43, 54, 53, 56, 58, 23,  7, 42, 33, 55, 68, 57, 35, 59,
       17, 74, 49, 13, 65, 31,  2, 38, 10, 18, 46,  6, 71,  8, 11, 20, 47,
       15, 39,  0, 29,  4, 36, 75, 16, 22, 12, 21, 19, 37, 24, 51, 70,  3,
       30, 72, 28, 62, 69, 41, 44, 50, 48,  5, 67, 73, 40, 63, 27, 25, 64,
        9, 34, 66,  1, 45, 52, 32, 26]), 'cur_cost': 604948.0}]
2025-06-26 20:28:21,476 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:28:21,476 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 40, 'skip_rate': 0.05, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 38, 'cache_hits': 23, 'similarity_calculations': 290, 'cache_hit_rate': 0.07931034482758621, 'cache_size': 267}}
2025-06-26 20:28:21,476 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:28:21,477 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:28:21,477 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:28:21,477 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:28:21,477 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 584448.0
2025-06-26 20:28:21,979 - root - WARNING - 无法找到足够的不重叠段 (找到 3/4)，使用退化策略
2025-06-26 20:28:21,980 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:28:21,981 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:28:21,981 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:28:21,984 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:28:21,984 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([72, 69, 33, 54, 43, 42, 70, 53, 19, 11, 39, 34, 26, 68, 13, 56,  2,
       23, 63, 55, 40, 12,  0, 45, 32,  1, 71, 61, 30, 18, 52, 17, 58, 67,
       37, 44,  6,  3, 57, 51, 35, 62, 10, 60, 15, 14, 59, 21, 64, 49, 66,
       75, 48, 16, 27, 28,  5,  8, 20, 24, 25, 47, 36,  9, 73, 38, 22, 41,
       29, 31, 65,  7, 46, 74, 50,  4]), 'cur_cost': 574999.0}, {'tour': array([60, 61,  2,  8,  1, 58, 55, 18,  5, 49, 10, 28, 41, 47,  3, 37, 30,
       44, 34, 20, 23, 12, 52, 16, 13, 43, 46, 65, 57, 54, 66, 31, 42, 51,
       21, 73, 26, 53, 56, 39, 74, 72,  4, 17, 62, 71, 48, 24, 19, 35,  7,
       11, 50, 64,  6, 25, 70, 59, 38, 22, 69, 75, 36, 15, 27, 45,  0,  9,
       40, 63, 32, 29, 33, 67, 14, 68]), 'cur_cost': 553005.0}, {'tour': array([54, 58, 59, 13, 42, 49, 18, 64, 33,  5, 52, 19, 69, 30, 21,  8, 35,
       29,  9, 27, 37, 14, 68, 72, 38, 22, 43, 11, 71, 23, 31, 17, 44, 50,
       65, 46, 16, 67, 55, 36, 15, 41, 28, 62, 39, 12,  0,  6, 57, 40, 32,
       26, 75, 73, 60, 47, 74,  3, 24,  2, 51, 20, 66,  4,  1, 70, 25, 48,
       56, 45, 53, 34,  7, 63, 10, 61]), 'cur_cost': 584448.0}]
2025-06-26 20:28:21,985 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:28:21,985 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 41, 'skip_rate': 0.04878048780487805, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 39, 'cache_hits': 23, 'similarity_calculations': 295, 'cache_hit_rate': 0.07796610169491526, 'cache_size': 272}}
2025-06-26 20:28:21,985 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:28:21,985 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}]
2025-06-26 20:28:21,985 - __main__ - INFO - 进化阶段完成
2025-06-26 20:28:21,988 - __main__ - INFO - 开始评估阶段
2025-06-26 20:28:21,988 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:28:21,993 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:28:21,994 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 162576.0 → 150781.0 (Δ 11795.0)
- Mean cost: 303971.9 → 532167.3
- Diversity: 0.388 → 0.484 (Δ 0.096)

#### Elite metrics
- Elite count: 9 → 9
- Best elite cost: 108159 → 108159 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ -337464.71
- Exploit: 3 inds, success 100.0% , avg Δ 26766.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [3316.0, 3410.0, 162576.0], diversity [0.8438095238095239, 0.8755555555555555, 0.531578947368421]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:28:21,995 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:28:34,378 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (14.3%) but high impact when successful (avg Δ -337464.71)",
    "exploit": "high success rate (100%) but modest improvements (avg Δ 26766.33)"
  },
  "balance_state": "exploit-heavy (70% explore / 30% exploit ratio) with insufficient exploration success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15%",
    "elite_preservation": "maintain current 9 elites"
  }
}
```
2025-06-26 20:28:34,410 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:28:34,410 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (14.3%) but high impact when successful (avg Δ -337464.71)",
    "exploit": "high success rate (100%) but modest improvements (avg Δ 26766.33)"
  },
  "balance_state": "exploit-heavy (70% explore / 30% exploit ratio) with insufficient exploration success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15%",
    "elite_preservation": "maintain current 9 elites"
  }
}
```
2025-06-26 20:28:34,410 - __main__ - INFO - 评估阶段完成
2025-06-26 20:28:34,412 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (14.3%) but high impact when successful (avg Δ -337464.71)",
    "exploit": "high success rate (100%) but modest improvements (avg Δ 26766.33)"
  },
  "balance_state": "exploit-heavy (70% explore / 30% exploit ratio) with insufficient exploration success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15%",
    "elite_preservation": "maintain current 9 elites"
  }
}
```
2025-06-26 20:28:34,412 - __main__ - INFO - 当前最佳适应度: 150781.0
2025-06-26 20:28:34,412 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_route_1.pkl
2025-06-26 20:28:34,412 - __main__ - INFO - pr76 开始进化第 3 代
2025-06-26 20:28:34,412 - __main__ - INFO - 开始分析阶段
2025-06-26 20:28:34,412 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:28:34,434 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 150781.0, 'max': 586138.0, 'mean': 532167.3, 'std': 127803.23205854381}, 'diversity': 0.677485380116959, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:28:34,436 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 150781.0, 'max': 586138.0, 'mean': 532167.3, 'std': 127803.23205854381}, 'diversity_level': 0.677485380116959, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[3600, 2300], [3100, 3300], [4700, 5750], [5400, 5750], [5608, 7103], [4493, 7102], [3600, 6950], [3100, 7250], [4700, 8450], [5400, 8450], [5610, 10053], [4492, 10052], [3600, 10800], [3100, 10950], [4700, 11650], [5400, 11650], [6650, 10800], [7300, 10950], [7300, 7250], [6650, 6950], [7300, 3300], [6650, 2300], [5400, 1600], [8350, 2300], [7850, 3300], [9450, 5750], [10150, 5750], [10358, 7103], [9243, 7102], [8350, 6950], [7850, 7250], [9450, 8450], [10150, 8450], [10360, 10053], [9242, 10052], [8350, 10800], [7850, 10950], [9450, 11650], [10150, 11650], [11400, 10800], [12050, 10950], [12050, 7250], [11400, 6950], [12050, 3300], [11400, 2300], [10150, 1600], [13100, 2300], [12600, 3300], [14200, 5750], [14900, 5750], [15108, 7103], [13993, 7102], [13100, 6950], [12600, 7250], [14200, 8450], [14900, 8450], [15110, 10053], [13992, 10052], [13100, 10800], [12600, 10950], [14200, 11650], [14900, 11650], [16150, 10800], [16800, 10950], [16800, 7250], [16150, 6950], [16800, 3300], [16150, 2300], [14900, 1600], [19800, 800], [19800, 10000], [19800, 11900], [19800, 12200], [200, 12200], [200, 1100], [200, 800]], 'distance_matrix': array([[    0.,  1118.,  3621., ..., 10468.,  3606.,  3716.],
       [ 1118.,     0.,  2926., ...,  9361.,  3640.,  3829.],
       [ 3621.,  2926.,     0., ...,  7865.,  6471.,  6690.],
       ...,
       [10468.,  9361.,  7865., ...,     0., 11100., 11400.],
       [ 3606.,  3640.,  6471., ..., 11100.,     0.,   300.],
       [ 3716.,  3829.,  6690., ..., 11400.,   300.,     0.]])}
2025-06-26 20:28:34,436 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:28:34,436 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:28:34,436 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:28:34,438 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:28:34,438 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:28:34,439 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:28:34,439 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:28:34,439 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:28:34,439 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:28:34,441 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:28:34,441 - __main__ - INFO - 精英专家分析报告: {'elite_count': 9, 'elite_common_features': {'common_edges': {'(7, 6)': 0.7777777777777778, '(6, 5)': 0.7777777777777778}, 'common_edge_ratio': 0.02631578947368421}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 42622.0, 'avg_gap': 422346.1888888889}, 'structure_gap': {'unique_elite_edges': 152, 'unique_pop_edges': 314, 'common_edges': 64}}, 'elite_diversity': {'diversity_score': 0.19883040935672514}}
2025-06-26 20:28:34,442 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:28:34,443 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:28:34,444 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:28:34,445 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 150781.0, mean 532167.3, max 586138.0, std 127803.23205854381
- diversity: 0.677485380116959
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3410.0, 162576.0, 150781.0] (Δ 5897.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [200.0, 800.0, 19800.0, 12200.0]
- centroid/spread: [10014.592, 7383.289] | rms 5979.163
- density_grid(3x3): [[6, 8, 4], [7, 11, 6], [10, 13, 11]]
- edge_len_stats(min,q1,med,q3,max): [300.0, 4699.0, 7108.0, 9948.75, 22674.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [16, 3, 8, 4, 20, 9, 6, 9]
- nn_median_dist: 700.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:28:34,445 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:28:57,369 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 300 to 22674, with significant std deviation)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible presence (long_edge_ratio of 0.25 indicates some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.677, density grid shows uneven distribution with some high-density cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs show improvement but no stagnation)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max quartile (≥9948.75 length)",
    "low_density_cells": "Top-left grid cell (6 nodes) and middle-right (6 nodes)"
  },
  "opportunity_regions": {
    "high_density_cells": "Bottom-middle (13 nodes) and bottom-right (11 nodes) grid cells",
    "frequent_directions": "Sectors with 20 and 16 edges (direction histogram)"
  },
  "evolution_phase": "Exploration (early iteration, no elites, improving but non-converged costs)",
  "evolution_direction": [
    "Prioritize crossover in high-density cells to exploit clustered nodes",
    "Apply local search/2-opt near long edges to reduce costly connections",
    "Maintain diversity via restart or mutation in low-density regions",
    "Track direction histogram to reinforce promising edge orientations"
  ]
}
```
2025-06-26 20:28:57,384 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:28:57,384 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 300 to 22674, with significant std deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 indicates some deceptive long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.677, density grid shows uneven distribution with some high-density cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (≥9948.75 length)', 'low_density_cells': 'Top-left grid cell (6 nodes) and middle-right (6 nodes)'}, 'opportunity_regions': {'high_density_cells': 'Bottom-middle (13 nodes) and bottom-right (11 nodes) grid cells', 'frequent_directions': 'Sectors with 20 and 16 edges (direction histogram)'}, 'evolution_phase': 'Exploration (early iteration, no elites, improving but non-converged costs)', 'evolution_direction': ['Prioritize crossover in high-density cells to exploit clustered nodes', 'Apply local search/2-opt near long edges to reduce costly connections', 'Maintain diversity via restart or mutation in low-density regions', 'Track direction histogram to reinforce promising edge orientations']}
2025-06-26 20:28:57,384 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:28:57,384 - __main__ - INFO - 分析阶段完成
2025-06-26 20:28:57,386 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 300 to 22674, with significant std deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 indicates some deceptive long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.677, density grid shows uneven distribution with some high-density cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (≥9948.75 length)', 'low_density_cells': 'Top-left grid cell (6 nodes) and middle-right (6 nodes)'}, 'opportunity_regions': {'high_density_cells': 'Bottom-middle (13 nodes) and bottom-right (11 nodes) grid cells', 'frequent_directions': 'Sectors with 20 and 16 edges (direction histogram)'}, 'evolution_phase': 'Exploration (early iteration, no elites, improving but non-converged costs)', 'evolution_direction': ['Prioritize crossover in high-density cells to exploit clustered nodes', 'Apply local search/2-opt near long edges to reduce costly connections', 'Maintain diversity via restart or mutation in low-density regions', 'Track direction histogram to reinforce promising edge orientations']}
2025-06-26 20:28:57,386 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:28:57,386 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:28:57,386 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 150781.0
  • mean_cost: 532167.3
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 300 to 22674, with significant std deviation), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible presence (long_edge_ratio of 0.25 indicates some deceptive long edges)
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_cells
- Opportunity regions (# 2): high_density_cells, frequent_directions
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low s...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:28:57,388 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:28:57,388 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:29:08,835 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early iteration favor exploration. Top 60% explore to diversify; bottom 40% exploit current bests."
}
```
2025-06-26 20:29:08,835 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:29:08,835 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:29:08,843 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early iteration favor exploration. Top 60% explore to diversify; bottom 40% exploit current bests."
}
```
2025-06-26 20:29:08,843 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:29:08,843 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:29:08,843 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early iteration favor exploration. Top 60% explore to diversify; bottom 40% exploit current bests."
}
```
2025-06-26 20:29:08,844 - __main__ - INFO - 开始进化阶段
2025-06-26 20:29:08,844 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:29:08,844 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:29:08,844 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 556944.0 (rank 20.0%)
- Population diversity: 0.484
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:29:08,844 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:29:08,844 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:29:26,381 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "high_density_ce, frequent_direct",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:29:26,381 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:29:26,381 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:29:26,381 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:29:26,381 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:29:26,381 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:29:26,381 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 50.0%)
- Population diversity: 0.484
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:29:26,381 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:29:26,381 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:29:44,105 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "high_density_ce, frequent_direct",
  "strategy_comment": "Leverage grid pattern to explore sparse cells near opportunities"
}
```
2025-06-26 20:29:44,107 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:29:44,107 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:29:44,107 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:29:44,107 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:29:44,107 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:29:44,107 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 50.0%)
- Population diversity: 0.484
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:29:44,107 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:29:44,107 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:30:04,952 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75],
  "targeted_regions": "high_density_ce, adjacent low-density cells",
  "strategy_comment": "Leverage opportunity regions with structured jumps to explore sparse areas"
}
```
2025-06-26 20:30:04,952 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:30:04,952 - ExplorationExpert - INFO - 探索路径生成完成，成本: 556944.0, 路径: [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75]
2025-06-26 20:30:04,952 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}
2025-06-26 20:30:04,952 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:30:04,952 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:30:04,952 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 60.0%)
- Population diversity: 0.484
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:30:04,952 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:30:04,952 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:30:22,974 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75],
  "targeted_regions": "high_density_ce, adjacent low-density cells",
  "strategy_comment": "Layered grid traversal to balance exploration and cost"
}
```
2025-06-26 20:30:22,974 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:30:22,974 - ExplorationExpert - INFO - 探索路径生成完成，成本: 556944.0, 路径: [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75]
2025-06-26 20:30:22,974 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}
2025-06-26 20:30:22,974 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:30:22,977 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:30:22,977 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 150781.0 (rank 0.0%)
- Population diversity: 0.484
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:30:22,977 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:30:22,977 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:30:40,600 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70],
  "targeted_regions": "high_density_ce, frequent_direct, adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore opportunity regions while avoiding long edges"
}
```
2025-06-26 20:30:40,603 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:30:40,603 - ExplorationExpert - INFO - 探索路径生成完成，成本: 476135.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70]
2025-06-26 20:30:40,603 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}
2025-06-26 20:30:40,603 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:30:40,603 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:30:40,603 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 556944.0 (rank 20.0%)
- Population diversity: 0.48933333333333334
- Evolution phase: Exploration (early iteration, no elites, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, frequent_direct
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:30:40,605 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:30:40,605 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:30:58,969 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "high_density_ce, frequent_direct",
  "strategy_comment": "Sequential sweep through opportunity regions, avoiding sparse areas"
}
```
2025-06-26 20:30:58,969 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:30:58,969 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:30:58,969 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:30:58,969 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:30:58,969 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:30:58,969 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:30:58,969 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 565134.0
2025-06-26 20:30:59,471 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:30:59,472 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:30:59,472 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:30:59,476 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:30:59,476 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([72, 69, 33, 54, 43, 42, 70, 53, 19, 11, 39, 34, 26, 68, 13, 56,  2,
       23, 63, 55, 40, 12,  0, 45, 32,  1, 71, 61, 30, 18, 52, 17, 58, 67,
       37, 44,  6,  3, 57, 51, 35, 62, 10, 60, 15, 14, 59, 21, 64, 49, 66,
       75, 48, 16, 27, 28,  5,  8, 20, 24, 25, 47, 36,  9, 73, 38, 22, 41,
       29, 31, 65,  7, 46, 74, 50,  4]), 'cur_cost': 574999.0}, {'tour': array([60, 61,  2,  8,  1, 58, 55, 18,  5, 49, 10, 28, 41, 47,  3, 37, 30,
       44, 34, 20, 23, 12, 52, 16, 13, 43, 46, 65, 57, 54, 66, 31, 42, 51,
       21, 73, 26, 53, 56, 39, 74, 72,  4, 17, 62, 71, 48, 24, 19, 35,  7,
       11, 50, 64,  6, 25, 70, 59, 38, 22, 69, 75, 36, 15, 27, 45,  0,  9,
       40, 63, 32, 29, 33, 67, 14, 68]), 'cur_cost': 553005.0}, {'tour': array([54, 58, 59, 13, 42, 49, 18, 64, 33,  5, 52, 19, 69, 30, 21,  8, 35,
       29,  9, 27, 37, 14, 68, 72, 38, 22, 43, 11, 71, 23, 31, 17, 44, 50,
       65, 46, 16, 67, 55, 36, 15, 41, 28, 62, 39, 12,  0,  6, 57, 40, 32,
       26, 75, 73, 60, 47, 74,  3, 24,  2, 51, 20, 66,  4,  1, 70, 25, 48,
       56, 45, 53, 34,  7, 63, 10, 61]), 'cur_cost': 584448.0}]
2025-06-26 20:30:59,478 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:30:59,478 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 42, 'skip_rate': 0.047619047619047616, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 40, 'cache_hits': 23, 'similarity_calculations': 301, 'cache_hit_rate': 0.07641196013289037, 'cache_size': 278}}
2025-06-26 20:30:59,479 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:30:59,479 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:30:59,479 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:30:59,479 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:30:59,479 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 542208.0
2025-06-26 20:30:59,981 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:30:59,982 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:30:59,982 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:30:59,986 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:30:59,987 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([47, 22, 72,  6, 75, 73, 54, 65, 51, 31, 18, 33, 50, 55, 32, 44, 11,
        5,  0, 36, 10, 68, 64, 38, 58,  1, 69, 43, 15, 30,  4, 23, 13, 60,
        3, 27, 74, 40,  8, 14, 24, 12, 71, 42, 26, 66, 67, 39, 46, 28, 56,
        2, 21, 61, 70, 49, 35, 37, 62, 41, 20,  7, 52, 16, 19, 63, 25, 29,
       57, 59, 34,  9, 17, 53, 48, 45]), 'cur_cost': 542208.0}, {'tour': array([60, 61,  2,  8,  1, 58, 55, 18,  5, 49, 10, 28, 41, 47,  3, 37, 30,
       44, 34, 20, 23, 12, 52, 16, 13, 43, 46, 65, 57, 54, 66, 31, 42, 51,
       21, 73, 26, 53, 56, 39, 74, 72,  4, 17, 62, 71, 48, 24, 19, 35,  7,
       11, 50, 64,  6, 25, 70, 59, 38, 22, 69, 75, 36, 15, 27, 45,  0,  9,
       40, 63, 32, 29, 33, 67, 14, 68]), 'cur_cost': 553005.0}, {'tour': array([54, 58, 59, 13, 42, 49, 18, 64, 33,  5, 52, 19, 69, 30, 21,  8, 35,
       29,  9, 27, 37, 14, 68, 72, 38, 22, 43, 11, 71, 23, 31, 17, 44, 50,
       65, 46, 16, 67, 55, 36, 15, 41, 28, 62, 39, 12,  0,  6, 57, 40, 32,
       26, 75, 73, 60, 47, 74,  3, 24,  2, 51, 20, 66,  4,  1, 70, 25, 48,
       56, 45, 53, 34,  7, 63, 10, 61]), 'cur_cost': 584448.0}]
2025-06-26 20:30:59,989 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:30:59,989 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 43, 'skip_rate': 0.046511627906976744, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 41, 'cache_hits': 23, 'similarity_calculations': 308, 'cache_hit_rate': 0.07467532467532467, 'cache_size': 285}}
2025-06-26 20:30:59,989 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:30:59,989 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:30:59,989 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:30:59,990 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:30:59,990 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 569941.0
2025-06-26 20:31:00,492 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:31:00,492 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:31:00,492 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:31:00,497 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:31:00,497 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([47, 22, 72,  6, 75, 73, 54, 65, 51, 31, 18, 33, 50, 55, 32, 44, 11,
        5,  0, 36, 10, 68, 64, 38, 58,  1, 69, 43, 15, 30,  4, 23, 13, 60,
        3, 27, 74, 40,  8, 14, 24, 12, 71, 42, 26, 66, 67, 39, 46, 28, 56,
        2, 21, 61, 70, 49, 35, 37, 62, 41, 20,  7, 52, 16, 19, 63, 25, 29,
       57, 59, 34,  9, 17, 53, 48, 45]), 'cur_cost': 542208.0}, {'tour': array([50, 72, 52, 73, 36, 21, 62, 44, 57, 20,  2, 19, 23, 65, 33, 27, 63,
        3, 31, 11, 18, 29,  7,  5, 25, 58, 46, 51, 14, 54,  6, 41, 45, 68,
       43, 67,  9, 32, 15, 37, 48, 13,  1, 47, 28, 71, 35, 70,  0, 55, 30,
       42, 66, 38, 49, 24, 74, 26, 59, 12, 22, 64, 17, 10, 56, 61, 40, 60,
        8, 16, 75,  4, 39, 34, 69, 53]), 'cur_cost': 569941.0}, {'tour': array([54, 58, 59, 13, 42, 49, 18, 64, 33,  5, 52, 19, 69, 30, 21,  8, 35,
       29,  9, 27, 37, 14, 68, 72, 38, 22, 43, 11, 71, 23, 31, 17, 44, 50,
       65, 46, 16, 67, 55, 36, 15, 41, 28, 62, 39, 12,  0,  6, 57, 40, 32,
       26, 75, 73, 60, 47, 74,  3, 24,  2, 51, 20, 66,  4,  1, 70, 25, 48,
       56, 45, 53, 34,  7, 63, 10, 61]), 'cur_cost': 584448.0}]
2025-06-26 20:31:00,499 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:31:00,499 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 44, 'skip_rate': 0.045454545454545456, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 42, 'cache_hits': 23, 'similarity_calculations': 316, 'cache_hit_rate': 0.07278481012658228, 'cache_size': 293}}
2025-06-26 20:31:00,499 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:31:00,499 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:31:00,499 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:31:00,499 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:31:00,501 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 535799.0
2025-06-26 20:31:01,004 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:31:01,004 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:31:01,004 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:31:01,005 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:31:01,005 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([47, 22, 72,  6, 75, 73, 54, 65, 51, 31, 18, 33, 50, 55, 32, 44, 11,
        5,  0, 36, 10, 68, 64, 38, 58,  1, 69, 43, 15, 30,  4, 23, 13, 60,
        3, 27, 74, 40,  8, 14, 24, 12, 71, 42, 26, 66, 67, 39, 46, 28, 56,
        2, 21, 61, 70, 49, 35, 37, 62, 41, 20,  7, 52, 16, 19, 63, 25, 29,
       57, 59, 34,  9, 17, 53, 48, 45]), 'cur_cost': 542208.0}, {'tour': array([50, 72, 52, 73, 36, 21, 62, 44, 57, 20,  2, 19, 23, 65, 33, 27, 63,
        3, 31, 11, 18, 29,  7,  5, 25, 58, 46, 51, 14, 54,  6, 41, 45, 68,
       43, 67,  9, 32, 15, 37, 48, 13,  1, 47, 28, 71, 35, 70,  0, 55, 30,
       42, 66, 38, 49, 24, 74, 26, 59, 12, 22, 64, 17, 10, 56, 61, 40, 60,
        8, 16, 75,  4, 39, 34, 69, 53]), 'cur_cost': 569941.0}, {'tour': array([74, 37, 23, 49, 52, 57, 15,  0, 26,  2, 11, 51, 30, 42, 31,  3, 18,
        8, 13, 71, 55, 17, 29, 41, 65, 12, 43, 68, 21, 63, 66, 47, 22, 69,
       59, 40, 54, 58, 67, 14, 27, 45, 24, 25, 44, 19, 20, 56, 36, 46,  5,
       32, 38, 73, 35, 33, 16, 50, 39, 61, 53, 34,  4,  6, 72, 75,  7,  1,
        9, 60, 64, 70, 48, 10, 28, 62]), 'cur_cost': 535799.0}]
2025-06-26 20:31:01,005 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:31:01,005 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 45, 'skip_rate': 0.044444444444444446, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 43, 'cache_hits': 23, 'similarity_calculations': 325, 'cache_hit_rate': 0.07076923076923076, 'cache_size': 302}}
2025-06-26 20:31:01,011 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:31:01,011 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}]
2025-06-26 20:31:01,011 - __main__ - INFO - 进化阶段完成
2025-06-26 20:31:01,011 - __main__ - INFO - 开始评估阶段
2025-06-26 20:31:01,011 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:31:01,012 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:31:01,012 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 150781.0 → 476135.0 (Δ -325354.0)
- Mean cost: 532167.3 → 556151.9
- Diversity: 0.484 → 0.579 (Δ 0.0947)

#### Elite metrics
- Elite count: 9 → 9
- Best elite cost: 108159 → 108159 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ -54225.67
- Exploit: 4 inds, success 75.0% , avg Δ 21377.0

#### Other indicators
- No-change individuals: 1
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [3410.0, 162576.0, 150781.0], diversity [0.8755555555555555, 0.531578947368421, 0.677485380116959]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:31:01,012 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:31:16,983 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -54225.67,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 21377.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment",
    "diversity_trend": "increasing but not effectively utilized",
    "cost_trend": "increasing mean cost, stagnant best elite"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to enhance exploration effectiveness."
  }
}
```
2025-06-26 20:31:17,015 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:31:17,020 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -54225.67,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 21377.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment",
    "diversity_trend": "increasing but not effectively utilized",
    "cost_trend": "increasing mean cost, stagnant best elite"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to enhance exploration effectiveness."
  }
}
```
2025-06-26 20:31:17,021 - __main__ - INFO - 评估阶段完成
2025-06-26 20:31:17,021 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -54225.67,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 21377.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment",
    "diversity_trend": "increasing but not effectively utilized",
    "cost_trend": "increasing mean cost, stagnant best elite"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to enhance exploration effectiveness."
  }
}
```
2025-06-26 20:31:17,021 - __main__ - INFO - 当前最佳适应度: 476135.0
2025-06-26 20:31:17,021 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_route_2.pkl
2025-06-26 20:31:17,021 - __main__ - INFO - pr76 开始进化第 4 代
2025-06-26 20:31:17,021 - __main__ - INFO - 开始分析阶段
2025-06-26 20:31:17,021 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:31:17,046 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 476135.0, 'max': 586138.0, 'mean': 556151.9, 'std': 31592.63837494425}, 'diversity': 0.7824561403508773, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:31:17,047 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 476135.0, 'max': 586138.0, 'mean': 556151.9, 'std': 31592.63837494425}, 'diversity_level': 0.7824561403508773, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[3600, 2300], [3100, 3300], [4700, 5750], [5400, 5750], [5608, 7103], [4493, 7102], [3600, 6950], [3100, 7250], [4700, 8450], [5400, 8450], [5610, 10053], [4492, 10052], [3600, 10800], [3100, 10950], [4700, 11650], [5400, 11650], [6650, 10800], [7300, 10950], [7300, 7250], [6650, 6950], [7300, 3300], [6650, 2300], [5400, 1600], [8350, 2300], [7850, 3300], [9450, 5750], [10150, 5750], [10358, 7103], [9243, 7102], [8350, 6950], [7850, 7250], [9450, 8450], [10150, 8450], [10360, 10053], [9242, 10052], [8350, 10800], [7850, 10950], [9450, 11650], [10150, 11650], [11400, 10800], [12050, 10950], [12050, 7250], [11400, 6950], [12050, 3300], [11400, 2300], [10150, 1600], [13100, 2300], [12600, 3300], [14200, 5750], [14900, 5750], [15108, 7103], [13993, 7102], [13100, 6950], [12600, 7250], [14200, 8450], [14900, 8450], [15110, 10053], [13992, 10052], [13100, 10800], [12600, 10950], [14200, 11650], [14900, 11650], [16150, 10800], [16800, 10950], [16800, 7250], [16150, 6950], [16800, 3300], [16150, 2300], [14900, 1600], [19800, 800], [19800, 10000], [19800, 11900], [19800, 12200], [200, 12200], [200, 1100], [200, 800]], 'distance_matrix': array([[    0.,  1118.,  3621., ..., 10468.,  3606.,  3716.],
       [ 1118.,     0.,  2926., ...,  9361.,  3640.,  3829.],
       [ 3621.,  2926.,     0., ...,  7865.,  6471.,  6690.],
       ...,
       [10468.,  9361.,  7865., ...,     0., 11100., 11400.],
       [ 3606.,  3640.,  6471., ..., 11100.,     0.,   300.],
       [ 3716.,  3829.,  6690., ..., 11400.,   300.,     0.]])}
2025-06-26 20:31:17,047 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:31:17,048 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:31:17,048 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:31:17,049 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:31:17,049 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:31:17,049 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:31:17,049 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:31:17,049 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:31:17,052 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:31:17,053 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:31:17,055 - __main__ - INFO - 精英专家分析报告: {'elite_count': 9, 'elite_common_features': {'common_edges': {'(7, 6)': 0.7777777777777778, '(6, 5)': 0.7777777777777778}, 'common_edge_ratio': 0.02631578947368421}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 367976.0, 'avg_gap': 446330.7888888889}, 'structure_gap': {'unique_elite_edges': 202, 'unique_pop_edges': 443, 'common_edges': 14}}, 'elite_diversity': {'diversity_score': 0.19883040935672514}}
2025-06-26 20:31:17,055 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:31:17,055 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:31:17,056 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:31:17,056 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 476135.0, mean 556151.9, max 586138.0, std 31592.63837494425
- diversity: 0.7824561403508773
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [162576.0, 150781.0, 476135.0] (Δ 5897.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [200.0, 800.0, 19800.0, 12200.0]
- centroid/spread: [10014.592, 7383.289] | rms 5979.163
- density_grid(3x3): [[6, 8, 4], [7, 11, 6], [10, 13, 11]]
- edge_len_stats(min,q1,med,q3,max): [300.0, 4699.0, 7108.0, 9948.75, 22674.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [16, 3, 8, 4, 20, 9, 6, 9]
- nn_median_dist: 700.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:31:17,056 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:31:42,702 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 300 to 22674 with Q3 at 9948.75, indicating significant variability)",
    "modality": "Potentially multimodal (direction histogram shows uneven distribution with peaks in sectors 1 and 5)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some edges may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.78, density_grid shows nodes distributed across all cells with some concentration in bottom-right)",
    "convergence": "Early stage (convergence 0.0, recent best costs show large jumps without stagnation)",
    "quality": "Wide solution quality range (min-max 476135-586138 with std 31592)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges >9948.75 length, particularly in direction sectors 1 and 5)",
    "Low-density grid cells (top-left and middle-right with 4-6 nodes)"
  ],
  "opportunity_regions": [
    "High-density cells (bottom-right with 11-13 nodes, potentially good for exploitation)",
    "Short-edge clusters (edges <4699, particularly in direction sectors 3 and 7)"
  ],
  "evolution_phase": "Exploration-dominant (high diversity, no convergence, large cost improvements)",
  "evolution_direction": [
    "Prioritize 2-opt mutations in high-density regions",
    "Apply edge recombination focusing on short-edge sectors (3,7)",
    "Introduce directed mutations to break long-edge corridors (sectors 1,5)",
    "Maintain population diversity with restart strategies in low-density cells"
  ]
}
```
2025-06-26 20:31:42,705 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:31:42,705 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674 with Q3 at 9948.75, indicating significant variability)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution with peaks in sectors 1 and 5)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.78, density_grid shows nodes distributed across all cells with some concentration in bottom-right)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large jumps without stagnation)', 'quality': 'Wide solution quality range (min-max 476135-586138 with std 31592)'}, 'difficult_regions': ['Long-edge corridors (edges >9948.75 length, particularly in direction sectors 1 and 5)', 'Low-density grid cells (top-left and middle-right with 4-6 nodes)'], 'opportunity_regions': ['High-density cells (bottom-right with 11-13 nodes, potentially good for exploitation)', 'Short-edge clusters (edges <4699, particularly in direction sectors 3 and 7)'], 'evolution_phase': 'Exploration-dominant (high diversity, no convergence, large cost improvements)', 'evolution_direction': ['Prioritize 2-opt mutations in high-density regions', 'Apply edge recombination focusing on short-edge sectors (3,7)', 'Introduce directed mutations to break long-edge corridors (sectors 1,5)', 'Maintain population diversity with restart strategies in low-density cells']}
2025-06-26 20:31:42,705 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:31:42,705 - __main__ - INFO - 分析阶段完成
2025-06-26 20:31:42,705 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674 with Q3 at 9948.75, indicating significant variability)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution with peaks in sectors 1 and 5)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.78, density_grid shows nodes distributed across all cells with some concentration in bottom-right)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large jumps without stagnation)', 'quality': 'Wide solution quality range (min-max 476135-586138 with std 31592)'}, 'difficult_regions': ['Long-edge corridors (edges >9948.75 length, particularly in direction sectors 1 and 5)', 'Low-density grid cells (top-left and middle-right with 4-6 nodes)'], 'opportunity_regions': ['High-density cells (bottom-right with 11-13 nodes, potentially good for exploitation)', 'Short-edge clusters (edges <4699, particularly in direction sectors 3 and 7)'], 'evolution_phase': 'Exploration-dominant (high diversity, no convergence, large cost improvements)', 'evolution_direction': ['Prioritize 2-opt mutations in high-density regions', 'Apply edge recombination focusing on short-edge sectors (3,7)', 'Introduce directed mutations to break long-edge corridors (sectors 1,5)', 'Maintain population diversity with restart strategies in low-density cells']}
2025-06-26 20:31:42,705 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:31:42,705 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:31:42,705 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 476135.0
  • mean_cost: 556151.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 300 to 22674 with Q3 at 9948.75, indicating significant variability), modality Potentially multimodal (direction histogram shows uneven distribution with peaks in sectors 1 and 5), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some edges may mislead local search)
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:31:42,705 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:31:42,705 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:31:53,593 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 30% exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 20:31:53,593 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:31:53,593 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:31:53,593 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 30% exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 20:31:53,593 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:31:53,593 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:31:53,593 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 30% exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 20:31:53,593 - __main__ - INFO - 开始进化阶段
2025-06-26 20:31:53,601 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:31:53,602 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:31:53,602 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 70.0%)
- Population diversity: 0.5786666666666667
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:31:53,602 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:31:53,602 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:32:10,953 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:32:10,953 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:32:10,953 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:32:10,953 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:32:10,953 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:32:10,953 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:32:10,953 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 70.0%)
- Population diversity: 0.5786666666666667
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:32:10,953 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:32:10,953 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:32:28,437 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize dense clusters with short edges, avoid long corridors"
}
```
2025-06-26 20:32:28,437 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:32:28,437 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165291.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:32:28,437 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}
2025-06-26 20:32:28,437 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 20:32:28,437 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:32:28,437 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:32:28,437 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 576884.0
2025-06-26 20:32:28,946 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:32:28,946 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:32:28,947 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:32:28,949 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:32:28,951 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}, {'tour': array([66, 51, 23, 49,  6, 50, 52, 20, 13, 53, 32, 75,  9, 19, 46, 59, 29,
       21,  0, 38, 61, 30, 58, 39, 37, 70, 31, 63, 65, 71,  5, 43, 55, 69,
        1, 72, 64, 26, 27, 10,  2, 40, 73, 48, 62, 24, 54,  3, 45, 68, 41,
       67, 17, 36,  4, 56, 74, 35, 22, 12, 18, 16, 11, 34, 42, 33, 28, 14,
       47,  8, 44, 25, 57,  7, 15, 60]), 'cur_cost': 576884.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 11, 23, 35, 47, 59, 71, 10, 22, 34, 46, 58, 70, 9, 21, 33, 45, 57, 69, 8, 20, 32, 44, 56, 68, 7, 19, 31, 43, 55, 67, 6, 18, 30, 42, 54, 66, 5, 17, 29, 41, 53, 65, 4, 16, 28, 40, 52, 64, 3, 15, 27, 39, 51, 63, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 73, 74, 75], 'cur_cost': 556944.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 6, 13, 20, 27, 34, 41, 48, 55, 62, 69, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 3, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 4, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70], 'cur_cost': 476135.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([47, 22, 72,  6, 75, 73, 54, 65, 51, 31, 18, 33, 50, 55, 32, 44, 11,
        5,  0, 36, 10, 68, 64, 38, 58,  1, 69, 43, 15, 30,  4, 23, 13, 60,
        3, 27, 74, 40,  8, 14, 24, 12, 71, 42, 26, 66, 67, 39, 46, 28, 56,
        2, 21, 61, 70, 49, 35, 37, 62, 41, 20,  7, 52, 16, 19, 63, 25, 29,
       57, 59, 34,  9, 17, 53, 48, 45]), 'cur_cost': 542208.0}, {'tour': array([50, 72, 52, 73, 36, 21, 62, 44, 57, 20,  2, 19, 23, 65, 33, 27, 63,
        3, 31, 11, 18, 29,  7,  5, 25, 58, 46, 51, 14, 54,  6, 41, 45, 68,
       43, 67,  9, 32, 15, 37, 48, 13,  1, 47, 28, 71, 35, 70,  0, 55, 30,
       42, 66, 38, 49, 24, 74, 26, 59, 12, 22, 64, 17, 10, 56, 61, 40, 60,
        8, 16, 75,  4, 39, 34, 69, 53]), 'cur_cost': 569941.0}, {'tour': array([74, 37, 23, 49, 52, 57, 15,  0, 26,  2, 11, 51, 30, 42, 31,  3, 18,
        8, 13, 71, 55, 17, 29, 41, 65, 12, 43, 68, 21, 63, 66, 47, 22, 69,
       59, 40, 54, 58, 67, 14, 27, 45, 24, 25, 44, 19, 20, 56, 36, 46,  5,
       32, 38, 73, 35, 33, 16, 50, 39, 61, 53, 34,  4,  6, 72, 75,  7,  1,
        9, 60, 64, 70, 48, 10, 28, 62]), 'cur_cost': 535799.0}]
2025-06-26 20:32:28,953 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:32:28,953 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 46, 'skip_rate': 0.043478260869565216, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 44, 'cache_hits': 23, 'similarity_calculations': 335, 'cache_hit_rate': 0.06865671641791045, 'cache_size': 312}}
2025-06-26 20:32:28,954 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-26 20:32:28,954 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:32:28,954 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:32:28,955 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 556944.0 (rank 40.0%)
- Population diversity: 0.756
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:32:28,955 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:32:28,955 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:32:46,205 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration with adjacency focus"
}
```
2025-06-26 20:32:46,212 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:32:46,214 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:32:46,214 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:32:46,214 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 20:32:46,215 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:32:46,215 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:32:46,215 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 543061.0
2025-06-26 20:32:46,715 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:32:46,715 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:32:46,715 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:32:46,720 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:32:46,720 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}, {'tour': array([66, 51, 23, 49,  6, 50, 52, 20, 13, 53, 32, 75,  9, 19, 46, 59, 29,
       21,  0, 38, 61, 30, 58, 39, 37, 70, 31, 63, 65, 71,  5, 43, 55, 69,
        1, 72, 64, 26, 27, 10,  2, 40, 73, 48, 62, 24, 54,  3, 45, 68, 41,
       67, 17, 36,  4, 56, 74, 35, 22, 12, 18, 16, 11, 34, 42, 33, 28, 14,
       47,  8, 44, 25, 57,  7, 15, 60]), 'cur_cost': 576884.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([ 4, 66,  2, 63, 39,  3, 69, 48, 30, 70, 71, 13, 51, 23, 20,  7,  1,
       42, 47, 46, 57, 24, 37, 17, 19, 28, 35, 12, 26, 62, 58, 34, 60, 59,
       27, 73, 40, 55, 75,  6, 68, 53, 45, 21, 50, 64, 15, 74, 41, 54, 38,
        9, 44, 14, 18, 16, 49, 56, 52, 29,  5,  8, 65, 31, 11, 67, 72, 32,
       25, 33, 43,  0, 22, 61, 10, 36]), 'cur_cost': 543061.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([57, 11,  3,  9, 72,  0, 30, 53, 20, 23, 26, 21, 42, 55, 28, 63, 34,
       74, 71, 65, 61, 15, 43, 17,  7, 56, 51,  4, 38,  6, 13, 36, 73, 19,
       44, 47,  2, 58, 31, 40, 25, 39, 16, 54, 60,  5, 24, 29, 14, 59, 52,
       49, 70, 32, 27, 41, 46, 62, 22, 37, 67, 35, 66,  8, 50, 48, 12, 69,
       64, 68, 45, 18, 33, 10, 75,  1]), 'cur_cost': 565134.0}, {'tour': array([47, 22, 72,  6, 75, 73, 54, 65, 51, 31, 18, 33, 50, 55, 32, 44, 11,
        5,  0, 36, 10, 68, 64, 38, 58,  1, 69, 43, 15, 30,  4, 23, 13, 60,
        3, 27, 74, 40,  8, 14, 24, 12, 71, 42, 26, 66, 67, 39, 46, 28, 56,
        2, 21, 61, 70, 49, 35, 37, 62, 41, 20,  7, 52, 16, 19, 63, 25, 29,
       57, 59, 34,  9, 17, 53, 48, 45]), 'cur_cost': 542208.0}, {'tour': array([50, 72, 52, 73, 36, 21, 62, 44, 57, 20,  2, 19, 23, 65, 33, 27, 63,
        3, 31, 11, 18, 29,  7,  5, 25, 58, 46, 51, 14, 54,  6, 41, 45, 68,
       43, 67,  9, 32, 15, 37, 48, 13,  1, 47, 28, 71, 35, 70,  0, 55, 30,
       42, 66, 38, 49, 24, 74, 26, 59, 12, 22, 64, 17, 10, 56, 61, 40, 60,
        8, 16, 75,  4, 39, 34, 69, 53]), 'cur_cost': 569941.0}, {'tour': array([74, 37, 23, 49, 52, 57, 15,  0, 26,  2, 11, 51, 30, 42, 31,  3, 18,
        8, 13, 71, 55, 17, 29, 41, 65, 12, 43, 68, 21, 63, 66, 47, 22, 69,
       59, 40, 54, 58, 67, 14, 27, 45, 24, 25, 44, 19, 20, 56, 36, 46,  5,
       32, 38, 73, 35, 33, 16, 50, 39, 61, 53, 34,  4,  6, 72, 75,  7,  1,
        9, 60, 64, 70, 48, 10, 28, 62]), 'cur_cost': 535799.0}]
2025-06-26 20:32:46,722 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:32:46,722 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 47, 'skip_rate': 0.0425531914893617, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 45, 'cache_hits': 23, 'similarity_calculations': 346, 'cache_hit_rate': 0.06647398843930635, 'cache_size': 323}}
2025-06-26 20:32:46,722 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-26 20:32:46,722 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:32:46,722 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:32:46,724 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 80.0%)
- Population diversity: 0.7386666666666667
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:32:46,724 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:32:46,724 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:33:03,105 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration of low-density cells"
}
```
2025-06-26 20:33:03,105 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:33:03,105 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:33:03,105 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:33:03,105 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:33:03,108 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:33:03,108 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 565134.0 (rank 60.0%)
- Population diversity: 0.7386666666666667
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:33:03,108 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:33:03,108 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:33:20,539 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize high-density clusters, then loop back through sparse cells"
}
```
2025-06-26 20:33:20,539 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:33:20,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 164600.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 20:33:20,539 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 164600.0}
2025-06-26 20:33:20,539 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:33:20,539 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:33:20,539 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:33:20,539 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 534030.0
2025-06-26 20:33:21,048 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:33:21,048 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:33:21,048 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:33:21,053 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:33:21,053 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}, {'tour': array([66, 51, 23, 49,  6, 50, 52, 20, 13, 53, 32, 75,  9, 19, 46, 59, 29,
       21,  0, 38, 61, 30, 58, 39, 37, 70, 31, 63, 65, 71,  5, 43, 55, 69,
        1, 72, 64, 26, 27, 10,  2, 40, 73, 48, 62, 24, 54,  3, 45, 68, 41,
       67, 17, 36,  4, 56, 74, 35, 22, 12, 18, 16, 11, 34, 42, 33, 28, 14,
       47,  8, 44, 25, 57,  7, 15, 60]), 'cur_cost': 576884.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([ 4, 66,  2, 63, 39,  3, 69, 48, 30, 70, 71, 13, 51, 23, 20,  7,  1,
       42, 47, 46, 57, 24, 37, 17, 19, 28, 35, 12, 26, 62, 58, 34, 60, 59,
       27, 73, 40, 55, 75,  6, 68, 53, 45, 21, 50, 64, 15, 74, 41, 54, 38,
        9, 44, 14, 18, 16, 49, 56, 52, 29,  5,  8, 65, 31, 11, 67, 72, 32,
       25, 33, 43,  0, 22, 61, 10, 36]), 'cur_cost': 543061.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 164600.0}, {'tour': array([ 6,  3, 46, 30, 29, 13, 38, 12, 21,  2, 14, 16,  7, 40, 25,  0, 58,
       42, 56, 47, 60, 10, 53, 11, 75,  4, 36, 62, 50, 28, 74, 68, 64, 43,
       72, 37, 17, 55, 26, 24, 19, 51, 34, 41, 18, 63, 73,  8, 20,  1, 54,
        5, 27, 66, 70, 61, 39, 69, 23, 52, 71, 33, 15, 22, 48, 57, 35, 67,
       65, 49, 44,  9, 45, 31, 59, 32]), 'cur_cost': 534030.0}, {'tour': array([50, 72, 52, 73, 36, 21, 62, 44, 57, 20,  2, 19, 23, 65, 33, 27, 63,
        3, 31, 11, 18, 29,  7,  5, 25, 58, 46, 51, 14, 54,  6, 41, 45, 68,
       43, 67,  9, 32, 15, 37, 48, 13,  1, 47, 28, 71, 35, 70,  0, 55, 30,
       42, 66, 38, 49, 24, 74, 26, 59, 12, 22, 64, 17, 10, 56, 61, 40, 60,
        8, 16, 75,  4, 39, 34, 69, 53]), 'cur_cost': 569941.0}, {'tour': array([74, 37, 23, 49, 52, 57, 15,  0, 26,  2, 11, 51, 30, 42, 31,  3, 18,
        8, 13, 71, 55, 17, 29, 41, 65, 12, 43, 68, 21, 63, 66, 47, 22, 69,
       59, 40, 54, 58, 67, 14, 27, 45, 24, 25, 44, 19, 20, 56, 36, 46,  5,
       32, 38, 73, 35, 33, 16, 50, 39, 61, 53, 34,  4,  6, 72, 75,  7,  1,
        9, 60, 64, 70, 48, 10, 28, 62]), 'cur_cost': 535799.0}]
2025-06-26 20:33:21,055 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:33:21,055 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 48, 'skip_rate': 0.041666666666666664, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 46, 'cache_hits': 23, 'similarity_calculations': 358, 'cache_hit_rate': 0.06424581005586592, 'cache_size': 335}}
2025-06-26 20:33:21,056 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:33:21,056 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 20:33:21,056 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:33:21,056 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 569941.0 (rank 70.0%)
- Population diversity: 0.6506666666666666
- Evolution phase: Exploration-dominant (high diversity, no convergence, large cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:33:21,056 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:33:21,056 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:33:37,923 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration of adjacent nodes in opportunity regions"
}
```
2025-06-26 20:33:37,923 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:33:37,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:33:37,923 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:33:37,928 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:33:37,928 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:33:37,928 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:33:37,928 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 621764.0
2025-06-26 20:33:38,432 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:33:38,432 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:33:38,432 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:33:38,436 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:33:38,436 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}, {'tour': array([66, 51, 23, 49,  6, 50, 52, 20, 13, 53, 32, 75,  9, 19, 46, 59, 29,
       21,  0, 38, 61, 30, 58, 39, 37, 70, 31, 63, 65, 71,  5, 43, 55, 69,
        1, 72, 64, 26, 27, 10,  2, 40, 73, 48, 62, 24, 54,  3, 45, 68, 41,
       67, 17, 36,  4, 56, 74, 35, 22, 12, 18, 16, 11, 34, 42, 33, 28, 14,
       47,  8, 44, 25, 57,  7, 15, 60]), 'cur_cost': 576884.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([ 4, 66,  2, 63, 39,  3, 69, 48, 30, 70, 71, 13, 51, 23, 20,  7,  1,
       42, 47, 46, 57, 24, 37, 17, 19, 28, 35, 12, 26, 62, 58, 34, 60, 59,
       27, 73, 40, 55, 75,  6, 68, 53, 45, 21, 50, 64, 15, 74, 41, 54, 38,
        9, 44, 14, 18, 16, 49, 56, 52, 29,  5,  8, 65, 31, 11, 67, 72, 32,
       25, 33, 43,  0, 22, 61, 10, 36]), 'cur_cost': 543061.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 164600.0}, {'tour': array([ 6,  3, 46, 30, 29, 13, 38, 12, 21,  2, 14, 16,  7, 40, 25,  0, 58,
       42, 56, 47, 60, 10, 53, 11, 75,  4, 36, 62, 50, 28, 74, 68, 64, 43,
       72, 37, 17, 55, 26, 24, 19, 51, 34, 41, 18, 63, 73,  8, 20,  1, 54,
        5, 27, 66, 70, 61, 39, 69, 23, 52, 71, 33, 15, 22, 48, 57, 35, 67,
       65, 49, 44,  9, 45, 31, 59, 32]), 'cur_cost': 534030.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([71, 23, 27, 35, 48, 31, 62,  6, 44, 14, 42, 16, 50, 32,  4, 30, 70,
       38, 60, 73,  9, 46, 11, 10,  1, 57,  8, 69, 52, 59, 72, 74, 55,  5,
       43, 41,  2, 45, 68, 40, 51, 29, 65, 20, 63,  0, 61, 24, 19, 34, 18,
       12, 56, 49, 15, 54, 17, 39, 33, 66, 28, 37, 58, 26, 53, 13, 75, 22,
        3,  7, 67, 36, 25, 64, 47, 21]), 'cur_cost': 621764.0}]
2025-06-26 20:33:38,438 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:33:38,438 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 49, 'skip_rate': 0.04081632653061224, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 47, 'cache_hits': 23, 'similarity_calculations': 371, 'cache_hit_rate': 0.06199460916442048, 'cache_size': 348}}
2025-06-26 20:33:38,438 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:33:38,438 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 165291.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 164600.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}]
2025-06-26 20:33:38,438 - __main__ - INFO - 进化阶段完成
2025-06-26 20:33:38,438 - __main__ - INFO - 开始评估阶段
2025-06-26 20:33:38,438 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:33:38,440 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:33:38,440 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 476135.0 → 150781.0 (Δ 325354.0)
- Mean cost: 556151.9 → 364411.1
- Diversity: 0.579 → 0.565 (Δ -0.0133)

#### Elite metrics
- Elite count: 9 → 9
- Best elite cost: 108159 → 108159 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 83.3% , avg Δ 347010.17
- Exploit: 4 inds, success 25.0% , avg Δ -41163.25

#### Other indicators
- No-change individuals: 1
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [162576.0, 150781.0, 476135.0], diversity [0.531578947368421, 0.677485380116959, 0.7824561403508773]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:33:38,441 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:33:53,104 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 347010.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -41163.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is more effective but diversity is slightly decreasing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 20:33:53,146 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:33:53,146 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 347010.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -41163.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is more effective but diversity is slightly decreasing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 20:33:53,146 - __main__ - INFO - 评估阶段完成
2025-06-26 20:33:53,146 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 347010.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -41163.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is more effective but diversity is slightly decreasing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 20:33:53,146 - __main__ - INFO - 当前最佳适应度: 150781.0
2025-06-26 20:33:53,146 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_route_3.pkl
2025-06-26 20:33:53,146 - __main__ - INFO - pr76 开始进化第 5 代
2025-06-26 20:33:53,146 - __main__ - INFO - 开始分析阶段
2025-06-26 20:33:53,146 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:33:53,171 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 150781.0, 'max': 621764.0, 'mean': 364411.1, 'std': 209215.*********}, 'diversity': 0.7657894736842108, 'clusters': {'clusters': 6, 'cluster_sizes': [1, 5, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:33:53,172 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 150781.0, 'max': 621764.0, 'mean': 364411.1, 'std': 209215.*********}, 'diversity_level': 0.7657894736842108, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [1, 5, 1, 1, 1, 1]}, 'coordinates': [[3600, 2300], [3100, 3300], [4700, 5750], [5400, 5750], [5608, 7103], [4493, 7102], [3600, 6950], [3100, 7250], [4700, 8450], [5400, 8450], [5610, 10053], [4492, 10052], [3600, 10800], [3100, 10950], [4700, 11650], [5400, 11650], [6650, 10800], [7300, 10950], [7300, 7250], [6650, 6950], [7300, 3300], [6650, 2300], [5400, 1600], [8350, 2300], [7850, 3300], [9450, 5750], [10150, 5750], [10358, 7103], [9243, 7102], [8350, 6950], [7850, 7250], [9450, 8450], [10150, 8450], [10360, 10053], [9242, 10052], [8350, 10800], [7850, 10950], [9450, 11650], [10150, 11650], [11400, 10800], [12050, 10950], [12050, 7250], [11400, 6950], [12050, 3300], [11400, 2300], [10150, 1600], [13100, 2300], [12600, 3300], [14200, 5750], [14900, 5750], [15108, 7103], [13993, 7102], [13100, 6950], [12600, 7250], [14200, 8450], [14900, 8450], [15110, 10053], [13992, 10052], [13100, 10800], [12600, 10950], [14200, 11650], [14900, 11650], [16150, 10800], [16800, 10950], [16800, 7250], [16150, 6950], [16800, 3300], [16150, 2300], [14900, 1600], [19800, 800], [19800, 10000], [19800, 11900], [19800, 12200], [200, 12200], [200, 1100], [200, 800]], 'distance_matrix': array([[    0.,  1118.,  3621., ..., 10468.,  3606.,  3716.],
       [ 1118.,     0.,  2926., ...,  9361.,  3640.,  3829.],
       [ 3621.,  2926.,     0., ...,  7865.,  6471.,  6690.],
       ...,
       [10468.,  9361.,  7865., ...,     0., 11100., 11400.],
       [ 3606.,  3640.,  6471., ..., 11100.,     0.,   300.],
       [ 3716.,  3829.,  6690., ..., 11400.,   300.,     0.]])}
2025-06-26 20:33:53,172 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:33:53,172 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:33:53,173 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:33:53,175 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:33:53,175 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:33:53,176 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:33:53,176 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:33:53,176 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:33:53,176 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:33:53,179 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:33:53,179 - __main__ - INFO - 精英专家分析报告: {'elite_count': 9, 'elite_common_features': {'common_edges': {'(7, 6)': 0.7777777777777778, '(6, 5)': 0.7777777777777778}, 'common_edge_ratio': 0.02631578947368421}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 42622.0, 'avg_gap': 254589.98888888885}, 'structure_gap': {'unique_elite_edges': 140, 'unique_pop_edges': 377, 'common_edges': 76}}, 'elite_diversity': {'diversity_score': 0.19883040935672514}}
2025-06-26 20:33:53,179 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:33:53,179 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:33:53,181 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:33:53,181 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 150781.0, mean 364411.1, max 621764.0, std 209215.*********
- diversity: 0.7657894736842108
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [150781.0, 476135.0, 150781.0] (Δ 162677.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [200.0, 800.0, 19800.0, 12200.0]
- centroid/spread: [10014.592, 7383.289] | rms 5979.163
- density_grid(3x3): [[6, 8, 4], [7, 11, 6], [10, 13, 11]]
- edge_len_stats(min,q1,med,q3,max): [300.0, 4699.0, 7108.0, 9948.75, 22674.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [16, 3, 8, 4, 20, 9, 6, 9]
- nn_median_dist: 700.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:33:53,182 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:34:21,848 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 300 to 22674, with Q1-Q3 spread of 4699-9948.75)",
    "modality": "Potentially multimodal (high diversity score 0.766, no convergence yet, and no clustering detected)",
    "deceptiveness": "Possible deceptive regions (25% long edges may create false 'shortcut' attractors)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (density_grid shows nodes distributed across all cells, with some concentration in bottom-right)",
    "convergence_status": "No convergence (diversity=0.766, convergence=0.0)",
    "quality_spread": "Wide (min cost 150781 vs max 621764, std=209215)"
  },
  "difficult_regions": [
    {
      "type": "Long-edge corridor",
      "evidence": "25% long edges (max=22674), direction histogram shows predominant N-S orientation (sectors 0/4)"
    },
    {
      "type": "Low-density zone",
      "evidence": "Top-left grid cell has only 4 nodes (vs avg ~8.5)"
    }
  ],
  "opportunity_regions": [
    {
      "type": "High-density cluster",
      "evidence": "Bottom-right grid cell has 13 nodes (50% above average)",
      "suggested_action": "Intensify local search around nodes in this region"
    },
    {
      "type": "Short-edge neighborhood",
      "evidence": "Median NN distance=700 suggests tight clusters exist",
      "suggested_action": "Apply 2-opt swaps within these dense clusters"
    }
  ],
  "evolution_phase": "Early exploration (iteration 4/5, no convergence, high diversity)",
  "evolution_direction": [
    "Maintain exploration (diversity-preserving operators like edge recombination)",
    "Targeted intensification in high-density opportunity regions",
    "Consider long-edge mutation operators to escape deceptive regions"
  ]
}
```
2025-06-26 20:34:21,849 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:34:21,849 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674, with Q1-Q3 spread of 4699-9948.75)', 'modality': 'Potentially multimodal (high diversity score 0.766, no convergence yet, and no clustering detected)', 'deceptiveness': "Possible deceptive regions (25% long edges may create false 'shortcut' attractors)"}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across all cells, with some concentration in bottom-right)', 'convergence_status': 'No convergence (diversity=0.766, convergence=0.0)', 'quality_spread': 'Wide (min cost 150781 vs max 621764, std=209215)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': '25% long edges (max=22674), direction histogram shows predominant N-S orientation (sectors 0/4)'}, {'type': 'Low-density zone', 'evidence': 'Top-left grid cell has only 4 nodes (vs avg ~8.5)'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Bottom-right grid cell has 13 nodes (50% above average)', 'suggested_action': 'Intensify local search around nodes in this region'}, {'type': 'Short-edge neighborhood', 'evidence': 'Median NN distance=700 suggests tight clusters exist', 'suggested_action': 'Apply 2-opt swaps within these dense clusters'}], 'evolution_phase': 'Early exploration (iteration 4/5, no convergence, high diversity)', 'evolution_direction': ['Maintain exploration (diversity-preserving operators like edge recombination)', 'Targeted intensification in high-density opportunity regions', 'Consider long-edge mutation operators to escape deceptive regions']}
2025-06-26 20:34:21,849 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:34:21,850 - __main__ - INFO - 分析阶段完成
2025-06-26 20:34:21,850 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 300 to 22674, with Q1-Q3 spread of 4699-9948.75)', 'modality': 'Potentially multimodal (high diversity score 0.766, no convergence yet, and no clustering detected)', 'deceptiveness': "Possible deceptive regions (25% long edges may create false 'shortcut' attractors)"}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across all cells, with some concentration in bottom-right)', 'convergence_status': 'No convergence (diversity=0.766, convergence=0.0)', 'quality_spread': 'Wide (min cost 150781 vs max 621764, std=209215)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': '25% long edges (max=22674), direction histogram shows predominant N-S orientation (sectors 0/4)'}, {'type': 'Low-density zone', 'evidence': 'Top-left grid cell has only 4 nodes (vs avg ~8.5)'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Bottom-right grid cell has 13 nodes (50% above average)', 'suggested_action': 'Intensify local search around nodes in this region'}, {'type': 'Short-edge neighborhood', 'evidence': 'Median NN distance=700 suggests tight clusters exist', 'suggested_action': 'Apply 2-opt swaps within these dense clusters'}], 'evolution_phase': 'Early exploration (iteration 4/5, no convergence, high diversity)', 'evolution_direction': ['Maintain exploration (diversity-preserving operators like edge recombination)', 'Targeted intensification in high-density opportunity regions', 'Consider long-edge mutation operators to escape deceptive regions']}
2025-06-26 20:34:21,851 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:34:21,851 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:34:21,851 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 150781.0
  • mean_cost: 364411.1
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 300 to 22674, with Q1-Q3 spread of 4699-9948.75), modality Potentially multimodal (high diversity score 0.766, no convergence yet, and no clustering detected), deceptiveness Possible deceptive regions (25% long edges may create false 'shortcut' attractors)
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:34:21,851 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:34:21,851 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:34:32,601 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and no convergence favor exploration. Top 50% individuals exploit to refine solutions, others explore to diversify."
}
```
2025-06-26 20:34:32,602 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:34:32,602 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:34:32,602 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and no convergence favor exploration. Top 50% individuals exploit to refine solutions, others explore to diversify."
}
```
2025-06-26 20:34:32,602 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:34:32,603 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:34:32,603 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and no convergence favor exploration. Top 50% individuals exploit to refine solutions, others explore to diversify."
}
```
2025-06-26 20:34:32,604 - __main__ - INFO - 开始进化阶段
2025-06-26 20:34:32,604 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:34:32,604 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:34:32,604 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 586138.0 (rank 80.0%)
- Population diversity: 0.5653333333333334
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:34:32,606 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:34:32,606 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:34:49,779 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 20:34:49,780 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:34:49,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:34:49,780 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:34:49,781 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:34:49,781 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:34:49,781 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 165291.0 (rank 40.0%)
- Population diversity: 0.5653333333333334
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:34:49,781 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:34:49,781 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:35:06,690 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-opportunity, low-density adjacent",
  "strategy_comment": "Sequential exploration with opportunistic jumps"
}
```
2025-06-26 20:35:06,696 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:35:06,697 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:35:06,697 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:35:06,698 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:35:06,698 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:35:06,698 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 576884.0 (rank 70.0%)
- Population diversity: 0.5653333333333334
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:35:06,699 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:35:06,699 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:35:24,289 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Structured leapfrog to explore sparse regions"
}
```
2025-06-26 20:35:24,295 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:35:24,295 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:35:24,295 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:35:24,297 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:35:24,297 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:35:24,297 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 150781.0 (rank 0.0%)
- Population diversity: 0.48
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:35:24,297 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:35:24,297 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:35:41,390 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Sequential path to explore adjacent low-density regions"
}
```
2025-06-26 20:35:41,390 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:35:41,390 - ExplorationExpert - INFO - 探索路径生成完成，成本: 150781.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]
2025-06-26 20:35:41,390 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}
2025-06-26 20:35:41,390 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:35:41,390 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:35:41,390 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 543061.0 (rank 60.0%)
- Population diversity: 0.48
- Evolution phase: Early exploration (iteration 4/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 75]  
• Path length == 76 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:35:41,390 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:35:41,397 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:35:59,683 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions"
}
```
2025-06-26 20:35:59,683 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:35:59,683 - ExplorationExpert - INFO - 探索路径生成完成，成本: 586138.0, 路径: [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71]
2025-06-26 20:35:59,683 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}
2025-06-26 20:35:59,683 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-26 20:35:59,683 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:35:59,683 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:35:59,683 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 600589.0
2025-06-26 20:36:00,190 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:36:00,190 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:36:00,192 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:36:00,194 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:36:00,196 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([58, 61, 64, 11, 33, 46, 16, 14,  8, 73, 72, 51, 56, 29, 59, 25, 70,
       57, 37, 32, 19, 68, 38, 41, 27, 65,  9, 42,  1, 15, 23,  5, 21, 39,
       22, 13, 50, 35,  4, 12, 49, 30, 54, 43, 10, 53, 63, 28,  0, 48, 74,
       45, 31, 44, 71, 40,  3, 52, 66, 62, 24, 18,  6, 20, 26, 36, 60, 47,
       17, 55, 34, 67, 75,  2, 69,  7]), 'cur_cost': 600589.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 164600.0}, {'tour': array([ 6,  3, 46, 30, 29, 13, 38, 12, 21,  2, 14, 16,  7, 40, 25,  0, 58,
       42, 56, 47, 60, 10, 53, 11, 75,  4, 36, 62, 50, 28, 74, 68, 64, 43,
       72, 37, 17, 55, 26, 24, 19, 51, 34, 41, 18, 63, 73,  8, 20,  1, 54,
        5, 27, 66, 70, 61, 39, 69, 23, 52, 71, 33, 15, 22, 48, 57, 35, 67,
       65, 49, 44,  9, 45, 31, 59, 32]), 'cur_cost': 534030.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([71, 23, 27, 35, 48, 31, 62,  6, 44, 14, 42, 16, 50, 32,  4, 30, 70,
       38, 60, 73,  9, 46, 11, 10,  1, 57,  8, 69, 52, 59, 72, 74, 55,  5,
       43, 41,  2, 45, 68, 40, 51, 29, 65, 20, 63,  0, 61, 24, 19, 34, 18,
       12, 56, 49, 15, 54, 17, 39, 33, 66, 28, 37, 58, 26, 53, 13, 75, 22,
        3,  7, 67, 36, 25, 64, 47, 21]), 'cur_cost': 621764.0}]
2025-06-26 20:36:00,198 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:36:00,198 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 50, 'skip_rate': 0.04, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 48, 'cache_hits': 23, 'similarity_calculations': 385, 'cache_hit_rate': 0.05974025974025974, 'cache_size': 362}}
2025-06-26 20:36:00,198 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-26 20:36:00,198 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:36:00,199 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:36:00,199 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:36:00,199 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 579341.0
2025-06-26 20:36:00,701 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:36:00,701 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:36:00,701 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:36:00,705 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:36:00,706 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([58, 61, 64, 11, 33, 46, 16, 14,  8, 73, 72, 51, 56, 29, 59, 25, 70,
       57, 37, 32, 19, 68, 38, 41, 27, 65,  9, 42,  1, 15, 23,  5, 21, 39,
       22, 13, 50, 35,  4, 12, 49, 30, 54, 43, 10, 53, 63, 28,  0, 48, 74,
       45, 31, 44, 71, 40,  3, 52, 66, 62, 24, 18,  6, 20, 26, 36, 60, 47,
       17, 55, 34, 67, 75,  2, 69,  7]), 'cur_cost': 600589.0}, {'tour': array([53, 29, 28, 62, 55, 21, 47, 27, 14,  8, 42, 56, 71, 63, 46, 25, 50,
        4, 39, 16, 48,  9, 70, 19,  3, 45, 38, 73,  2, 68, 60, 15, 22, 75,
       51, 57, 59, 10,  6, 58,  5, 11, 67, 13, 61, 23, 12, 24, 49, 34, 37,
       52, 33, 26, 17, 30, 31, 41, 32, 65, 18, 44,  1, 20, 66, 69, 74, 64,
       35,  0, 54, 72,  7, 40, 43, 36]), 'cur_cost': 579341.0}, {'tour': array([ 6,  3, 46, 30, 29, 13, 38, 12, 21,  2, 14, 16,  7, 40, 25,  0, 58,
       42, 56, 47, 60, 10, 53, 11, 75,  4, 36, 62, 50, 28, 74, 68, 64, 43,
       72, 37, 17, 55, 26, 24, 19, 51, 34, 41, 18, 63, 73,  8, 20,  1, 54,
        5, 27, 66, 70, 61, 39, 69, 23, 52, 71, 33, 15, 22, 48, 57, 35, 67,
       65, 49, 44,  9, 45, 31, 59, 32]), 'cur_cost': 534030.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([71, 23, 27, 35, 48, 31, 62,  6, 44, 14, 42, 16, 50, 32,  4, 30, 70,
       38, 60, 73,  9, 46, 11, 10,  1, 57,  8, 69, 52, 59, 72, 74, 55,  5,
       43, 41,  2, 45, 68, 40, 51, 29, 65, 20, 63,  0, 61, 24, 19, 34, 18,
       12, 56, 49, 15, 54, 17, 39, 33, 66, 28, 37, 58, 26, 53, 13, 75, 22,
        3,  7, 67, 36, 25, 64, 47, 21]), 'cur_cost': 621764.0}]
2025-06-26 20:36:00,707 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:36:00,707 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 51, 'skip_rate': 0.0392156862745098, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 49, 'cache_hits': 23, 'similarity_calculations': 400, 'cache_hit_rate': 0.0575, 'cache_size': 377}}
2025-06-26 20:36:00,708 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:36:00,708 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:36:00,708 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:36:00,708 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:36:00,708 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 562301.0
2025-06-26 20:36:01,212 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:36:01,212 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:36:01,212 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:36:01,214 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:36:01,216 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([58, 61, 64, 11, 33, 46, 16, 14,  8, 73, 72, 51, 56, 29, 59, 25, 70,
       57, 37, 32, 19, 68, 38, 41, 27, 65,  9, 42,  1, 15, 23,  5, 21, 39,
       22, 13, 50, 35,  4, 12, 49, 30, 54, 43, 10, 53, 63, 28,  0, 48, 74,
       45, 31, 44, 71, 40,  3, 52, 66, 62, 24, 18,  6, 20, 26, 36, 60, 47,
       17, 55, 34, 67, 75,  2, 69,  7]), 'cur_cost': 600589.0}, {'tour': array([53, 29, 28, 62, 55, 21, 47, 27, 14,  8, 42, 56, 71, 63, 46, 25, 50,
        4, 39, 16, 48,  9, 70, 19,  3, 45, 38, 73,  2, 68, 60, 15, 22, 75,
       51, 57, 59, 10,  6, 58,  5, 11, 67, 13, 61, 23, 12, 24, 49, 34, 37,
       52, 33, 26, 17, 30, 31, 41, 32, 65, 18, 44,  1, 20, 66, 69, 74, 64,
       35,  0, 54, 72,  7, 40, 43, 36]), 'cur_cost': 579341.0}, {'tour': array([54, 49, 59, 42, 10, 60, 69, 44, 16, 37, 61, 72, 28, 47, 19,  3, 74,
       62, 26, 17, 67,  1, 56, 73,  2,  8,  0, 66, 39, 23, 50, 46, 12, 71,
       32, 13, 58, 33, 48, 70, 27, 36, 30, 65, 11, 52, 45, 43, 21, 22, 63,
       38, 15, 34, 75,  6, 40, 25, 57, 53, 55, 14,  7, 68, 51, 41, 20,  4,
       29, 64,  5, 24,  9, 35, 31, 18]), 'cur_cost': 562301.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': array([71, 23, 27, 35, 48, 31, 62,  6, 44, 14, 42, 16, 50, 32,  4, 30, 70,
       38, 60, 73,  9, 46, 11, 10,  1, 57,  8, 69, 52, 59, 72, 74, 55,  5,
       43, 41,  2, 45, 68, 40, 51, 29, 65, 20, 63,  0, 61, 24, 19, 34, 18,
       12, 56, 49, 15, 54, 17, 39, 33, 66, 28, 37, 58, 26, 53, 13, 75, 22,
        3,  7, 67, 36, 25, 64, 47, 21]), 'cur_cost': 621764.0}]
2025-06-26 20:36:01,217 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:36:01,218 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 52, 'skip_rate': 0.038461538461538464, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 50, 'cache_hits': 23, 'similarity_calculations': 416, 'cache_hit_rate': 0.055288461538461536, 'cache_size': 393}}
2025-06-26 20:36:01,218 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:36:01,218 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:36:01,218 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:36:01,218 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:36:01,219 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 581878.0
2025-06-26 20:36:01,219 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：47,跳过局部搜索
2025-06-26 20:36:01,220 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 20:36:01,220 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([22, 30, 57, 45, 36, 42, 58, 24, 52, 18, 69, 10, 26, 49,  1, 17, 63,
       38, 29, 75, 19, 56, 65, 46, 11, 28, 51, 71, 23, 50, 25, 74, 72, 55,
       37, 73,  5, 68, 20, 66, 59, 62, 32,  0,  9, 14, 70, 60,  8, 31, 47,
       61, 39,  7,  6,  3, 48, 64, 35, 21, 13, 33,  4, 53, 54, 40,  2, 44,
       67, 16, 43, 15, 41, 27, 34, 12]), 'cur_cost': 581878.0}
2025-06-26 20:36:01,220 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:36:01,221 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:36:01,221 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:36:01,221 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 536715.0
2025-06-26 20:36:01,722 - ExploitationExpert - INFO - res_population_num: 9
2025-06-26 20:36:01,722 - ExploitationExpert - INFO - res_population_costs: [108159, 109085, 109160, 109190, 109261, 109653, 109912, 111684, 112286]
2025-06-26 20:36:01,722 - ExploitationExpert - INFO - res_populations: [array([ 0, 75, 74,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 56, 57, 58, 59, 40, 60,
       61, 62, 63, 72, 71, 70, 64, 65, 50, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 75, 74,  1,  2,  3,  5,  6,  7,  8,  9,  4, 19, 18, 30, 29, 28,
       25, 26, 27, 32, 31, 33, 34, 35, 36, 17, 16, 10, 11, 12, 13, 73, 14,
       15, 37, 38, 39, 40, 59, 58, 60, 61, 57, 56, 62, 63, 72, 71, 70, 64,
       65, 50, 55, 54, 51, 52, 53, 41, 42, 48, 49, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 15, 14, 73, 13, 12, 11, 10, 16, 17,
       36, 35, 34, 33, 31, 32, 27, 26, 25, 28, 29, 30, 18, 19,  4,  9,  8,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       50, 65, 64, 70, 71, 72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 55, 54,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 10, 11, 15, 14, 12, 13, 73,  7,  6,  5,
        8,  9,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 69, 66, 49, 48,
       51, 52, 42, 41, 53, 54, 55, 50, 65, 64, 70, 71, 72, 63, 62, 56, 57,
       61, 60, 58, 59, 40, 39, 38, 37, 17, 36, 35, 34, 33, 31, 32, 27, 26,
       25, 28, 29, 30, 18, 19,  4,  9,  8, 11, 10, 16, 15, 14, 12, 13, 73,
        7,  6,  5,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 43, 47, 46, 68, 67, 66, 69, 70, 71,
       72, 63, 62, 61, 60, 40, 59, 58, 57, 56, 54, 55, 64, 65, 50, 49, 48,
       51, 52, 53, 41, 42, 27, 26, 25, 19, 18, 30, 29, 28, 31, 32, 34, 33,
       39, 38, 37, 35, 36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,
        6,  5,  4,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3,  4,  9,  8, 10, 11, 12, 13, 73,
       14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31, 28, 29, 30, 18,
       19, 25, 26, 27, 42, 41, 53, 52, 51, 48, 49, 50, 55, 54, 56, 57, 58,
       59, 40, 60, 61, 62, 63, 72, 71, 70, 64, 65, 66, 69, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64), array([ 0, 22, 21, 20, 24, 23, 45, 44, 46, 68, 67, 69, 66, 47, 43, 26, 25,
       28, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 70, 71, 72,
       63, 62, 61, 60, 56, 57, 58, 59, 40, 39, 33, 32, 31, 34, 38, 37, 35,
       36, 17, 16, 15, 14, 73, 13, 12, 11, 10,  9,  8,  7,  6,  5,  4, 19,
       18, 30, 29,  3,  2,  1, 74, 75], dtype=int64), array([ 0, 75, 74,  1,  7,  6,  5,  2,  3, 29, 30, 18, 19,  4,  9,  8, 10,
       11, 12, 13, 73, 14, 15, 16, 17, 36, 35, 37, 38, 39, 33, 34, 32, 31,
       28, 25, 26, 27, 42, 41, 53, 52, 51, 54, 55, 50, 48, 49, 65, 64, 56,
       57, 58, 59, 40, 60, 61, 62, 63, 72, 71, 70, 69, 66, 67, 68, 46, 47,
       43, 44, 45, 23, 24, 20, 21, 22], dtype=int64)]
2025-06-26 20:36:01,725 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:36:01,725 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}, {'tour': array([58, 61, 64, 11, 33, 46, 16, 14,  8, 73, 72, 51, 56, 29, 59, 25, 70,
       57, 37, 32, 19, 68, 38, 41, 27, 65,  9, 42,  1, 15, 23,  5, 21, 39,
       22, 13, 50, 35,  4, 12, 49, 30, 54, 43, 10, 53, 63, 28,  0, 48, 74,
       45, 31, 44, 71, 40,  3, 52, 66, 62, 24, 18,  6, 20, 26, 36, 60, 47,
       17, 55, 34, 67, 75,  2, 69,  7]), 'cur_cost': 600589.0}, {'tour': array([53, 29, 28, 62, 55, 21, 47, 27, 14,  8, 42, 56, 71, 63, 46, 25, 50,
        4, 39, 16, 48,  9, 70, 19,  3, 45, 38, 73,  2, 68, 60, 15, 22, 75,
       51, 57, 59, 10,  6, 58,  5, 11, 67, 13, 61, 23, 12, 24, 49, 34, 37,
       52, 33, 26, 17, 30, 31, 41, 32, 65, 18, 44,  1, 20, 66, 69, 74, 64,
       35,  0, 54, 72,  7, 40, 43, 36]), 'cur_cost': 579341.0}, {'tour': array([54, 49, 59, 42, 10, 60, 69, 44, 16, 37, 61, 72, 28, 47, 19,  3, 74,
       62, 26, 17, 67,  1, 56, 73,  2,  8,  0, 66, 39, 23, 50, 46, 12, 71,
       32, 13, 58, 33, 48, 70, 27, 36, 30, 65, 11, 52, 45, 43, 21, 22, 63,
       38, 15, 34, 75,  6, 40, 25, 57, 53, 55, 14,  7, 68, 51, 41, 20,  4,
       29, 64,  5, 24,  9, 35, 31, 18]), 'cur_cost': 562301.0}, {'tour': array([22, 30, 57, 45, 36, 42, 58, 24, 52, 18, 69, 10, 26, 49,  1, 17, 63,
       38, 29, 75, 19, 56, 65, 46, 11, 28, 51, 71, 23, 50, 25, 74, 72, 55,
       37, 73,  5, 68, 20, 66, 59, 62, 32,  0,  9, 14, 70, 60,  8, 31, 47,
       61, 39,  7,  6,  3, 48, 64, 35, 21, 13, 33,  4, 53, 54, 40,  2, 44,
       67, 16, 43, 15, 41, 27, 34, 12]), 'cur_cost': 581878.0}, {'tour': array([15, 62, 48,  3, 52, 25, 42, 38, 40, 14,  5, 68, 46, 22, 11,  8, 58,
       33,  4, 74, 30, 57, 12, 73, 18, 44, 75, 49, 70, 66,  2,  1, 69, 61,
        7, 51, 59, 55, 53, 71, 16, 47, 65, 43, 32, 50, 72, 26, 21, 28, 39,
       36,  9, 20, 41, 34, 29, 27, 63, 56, 13,  6,  0, 45, 37, 10, 67, 31,
       17, 54, 19, 64, 24, 60, 23, 35]), 'cur_cost': 536715.0}]
2025-06-26 20:36:01,728 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:36:01,728 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 54, 'skip_rate': 0.05555555555555555, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 51, 'cache_hits': 37, 'similarity_calculations': 448, 'cache_hit_rate': 0.08258928571428571, 'cache_size': 411}}
2025-06-26 20:36:01,728 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:36:01,729 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75], 'cur_cost': 150781.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 1, 13, 25, 37, 49, 61, 73, 2, 14, 26, 38, 50, 62, 74, 3, 15, 27, 39, 51, 63, 75, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 70, 11, 23, 35, 47, 59, 71], 'cur_cost': 586138.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([22, 30, 57, 45, 36, 42, 58, 24, 52, 18, 69, 10, 26, 49,  1, 17, 63,
       38, 29, 75, 19, 56, 65, 46, 11, 28, 51, 71, 23, 50, 25, 74, 72, 55,
       37, 73,  5, 68, 20, 66, 59, 62, 32,  0,  9, 14, 70, 60,  8, 31, 47,
       61, 39,  7,  6,  3, 48, 64, 35, 21, 13, 33,  4, 53, 54, 40,  2, 44,
       67, 16, 43, 15, 41, 27, 34, 12]), 'cur_cost': 581878.0}}]
2025-06-26 20:36:01,729 - __main__ - INFO - 进化阶段完成
2025-06-26 20:36:01,729 - __main__ - INFO - 开始评估阶段
2025-06-26 20:36:01,729 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:36:01,730 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:36:01,730 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 150781.0 → 150781.0 (Δ 0.0)
- Mean cost: 364411.1 → 492080.0
- Diversity: 0.565 → 0.653 (Δ 0.088)

#### Elite metrics
- Elite count: 9 → 9
- Best elite cost: 108159 → 108159 (Δ 0)

#### Strategy performance
- Explore: 5 inds, success 20.0% , avg Δ -7564.2
- Exploit: 5 inds, success 20.0% , avg Δ -247773.6

#### Other indicators
- No-change individuals: 2
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [150781.0, 476135.0, 150781.0], diversity [0.677485380116959, 0.7824561403508773, 0.7657894736842108]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:36:01,730 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:36:12,814 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low (20% success, modest avg Δ)",
    "exploit": "high impact but inconsistent (20% success, large avg Δ)"
  },
  "balance_state": "suboptimal (exploit dominates with high variance, explore underperforms)",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 15%",
    "elite_preservation": "maintain current count (9)"
  }
}
```
2025-06-26 20:36:12,848 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:36:12,848 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low (20% success, modest avg Δ)",
    "exploit": "high impact but inconsistent (20% success, large avg Δ)"
  },
  "balance_state": "suboptimal (exploit dominates with high variance, explore underperforms)",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 15%",
    "elite_preservation": "maintain current count (9)"
  }
}
```
2025-06-26 20:36:12,855 - __main__ - INFO - 评估阶段完成
2025-06-26 20:36:12,855 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low (20% success, modest avg Δ)",
    "exploit": "high impact but inconsistent (20% success, large avg Δ)"
  },
  "balance_state": "suboptimal (exploit dominates with high variance, explore underperforms)",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 15%",
    "elite_preservation": "maintain current count (9)"
  }
}
```
2025-06-26 20:36:12,855 - __main__ - INFO - 当前最佳适应度: 150781.0
2025-06-26 20:36:12,865 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_route_4.pkl
2025-06-26 20:36:12,874 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\pr76_solution.json
2025-06-26 20:36:12,874 - __main__ - INFO - 实例 pr76 处理完成
