2025-07-04 21:32:36,660 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 21:32:36,660 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-04 21:32:36,660 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:32:36,681 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9863.0, 'max': 109235.0, 'mean': 75080.2, 'std': 42745.60186217993}, 'diversity': 0.9171717171717172, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:32:36,681 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:32:36,690 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:32:36,690 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:32:36,690 - <PERSON>Expert - WARNING - 没有精英解可供分析
2025-07-04 21:32:37,748 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:32:37,748 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:32:37,748 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/8)
- population_size: 10
- cost_stats: min 9863.0, mean 75080.2, max 109235.0, std 42745.60186217993
- diversity: 0.9171717171717172
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (7, 11), 'frequency': 0.5, 'avg_cost': 20.0}]
- common_subpaths_sample: [{'subpath': (18, 16, 23), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (9, 11, 7), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [59, 38, 9, 41, 56, 50, 26], 'cost': 15551.0, 'size': 7}, {'region': [55, 32, 63, 44, 64, 34], 'cost': 14574.0, 'size': 6}, {'region': [37, 54, 36, 62, 41, 53], 'cost': 14485.0, 'size': 6}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:32:37,748 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:32:44,091 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.917,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [
    {"region": [59, 38, 9, 41, 56, 50, 26], "cost": 15551.0, "size": 7, "reason": "Identified in difficult_regions_sample."},
    {"region": [55, 32, 63, 44, 64, 34], "cost": 14574.0, "size": 6, "reason": "Identified in difficult_regions_sample."},
    {"region": [37, 54, 36, 62, 41, 53], "cost": 14485.0, "size": 6, "reason": "Identified in difficult_regions_sample."}
  ],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply edge recombination operators focusing on high-quality edges (7, 11).",
      "Incorporate subpaths (18, 16, 23), (22, 12, 17), and (9, 11, 7) into new solutions, potentially using path relinking or edge assembly crossover.",
      "Employ operators that diversify the population, such as random insertion or mutation, given high diversity and low convergence.",
      "Avoid premature convergence by preventing solutions that may get stuck in local optima"
    ]
  }
}
```
2025-07-04 21:32:44,092 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:32:44,092 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.917, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [{'region': [59, 38, 9, 41, 56, 50, 26], 'cost': 15551.0, 'size': 7, 'reason': 'Identified in difficult_regions_sample.'}, {'region': [55, 32, 63, 44, 64, 34], 'cost': 14574.0, 'size': 6, 'reason': 'Identified in difficult_regions_sample.'}, {'region': [37, 54, 36, 62, 41, 53], 'cost': 14485.0, 'size': 6, 'reason': 'Identified in difficult_regions_sample.'}], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply edge recombination operators focusing on high-quality edges (7, 11).', 'Incorporate subpaths (18, 16, 23), (22, 12, 17), and (9, 11, 7) into new solutions, potentially using path relinking or edge assembly crossover.', 'Employ operators that diversify the population, such as random insertion or mutation, given high diversity and low convergence.', 'Avoid premature convergence by preventing solutions that may get stuck in local optima']}}
2025-07-04 21:32:44,093 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:32:44,093 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:32:44,093 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.917
  • best_cost: 9863.0
  • mean_cost: 75080.2
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [59, 38, , {'region': [55, 32, ...
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:32:44,094 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:32:44,094 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:32:52,096 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended focus is explore and diversity is high, favoring exploration. Difficult regions identified, suggesting exploration is key at this stage."
}
```
2025-07-04 21:32:52,097 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:32:52,097 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:32:52,098 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended focus is explore and diversity is high, favoring exploration. Difficult regions identified, suggesting exploration is key at this stage."
}
```
2025-07-04 21:32:52,099 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:32:52,099 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:32:52,099 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended focus is explore and diversity is high, favoring exploration. Difficult regions identified, suggesting exploration is key at this stage."
}
```
2025-07-04 21:32:52,100 - __main__ - INFO - 开始进化阶段
2025-07-04 21:32:52,100 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:32:52,100 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:32:52,100 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9863.0 (rank 0.0%)
- Population diversity: 0.7446153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:32:52,101 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:32:52,101 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:32:58,960 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Avoids regions 59 & 55 initially.",
  "strategy_comment": "Prioritizes exploring the graph while avoiding the identified difficult regions at the beginning."
}
```
2025-07-04 21:32:58,961 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:32:58,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11438.0, 路径: [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65]
2025-07-04 21:32:58,962 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}
2025-07-04 21:32:58,962 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:32:58,963 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:32:58,963 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9903.0 (rank 0.0%)
- Population diversity: 0.8061538461538461
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:32:58,965 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:32:58,965 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:03,405 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Avoids difficult regions. Explores sparse cells.",
  "strategy_comment": "Prioritizes exploration of potentially unvisited areas while avoiding known problematic regions."
}
```
2025-07-04 21:33:03,406 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:03,406 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11438.0, 路径: [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:03,407 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}
2025-07-04 21:33:03,408 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:33:03,408 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:03,408 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10248.0 (rank 0.0%)
- Population diversity: 0.7876923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:03,409 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:03,409 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:10,323 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65, 59],
  "targeted_regions": "Difficult regions avoided, sparse cells integrated.",
  "strategy_comment": "Prioritizes avoiding difficult regions and integrating all nodes for complete coverage."
}
```
2025-07-04 21:33:10,324 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:10,325 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13481.0, 路径: [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:10,325 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}
2025-07-04 21:33:10,325 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:33:10,325 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:10,327 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101380.0 (rank 50.0%)
- Population diversity: 0.7307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:10,327 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:10,327 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:14,056 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58],
  "targeted_regions": "difficult regions and unexplored cells",
  "strategy_comment": "Prioritizes avoiding difficult regions and explores sparse areas for diversity."
}
```
2025-07-04 21:33:14,057 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:14,057 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11370.0, 路径: [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58]
2025-07-04 21:33:14,057 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58], 'cur_cost': 11370.0}
2025-07-04 21:33:14,058 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:33:14,058 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:14,058 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104053.0 (rank 70.0%)
- Population diversity: 0.6584615384615384
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:14,059 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:14,059 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:20,196 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0],
  "targeted_regions": "Avoids region 59, targets general exploration of unexplored areas.",
  "strategy_comment": "Prioritizes diversification by initial node choice and a mostly random path."
}
```
2025-07-04 21:33:20,197 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:20,198 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21770.0, 路径: [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0]
2025-07-04 21:33:20,198 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0], 'cur_cost': 21770.0}
2025-07-04 21:33:20,198 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:33:20,199 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:20,200 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108013.0 (rank 80.0%)
- Population diversity: 0.5876923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:20,200 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:20,200 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:25,146 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells based on density grid.",
  "strategy_comment": "Constructing a naive path visiting nodes sequentially for diversity, avoiding identified regions."
}
```
2025-07-04 21:33:25,146 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:25,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:25,147 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:33:25,149 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:33:25,149 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:25,149 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96441.0 (rank 60.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:25,150 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:25,150 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:30,614 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65, 59],
  "targeted_regions": "Avoids difficult regions: 55, 59.",
  "strategy_comment": "Prioritizes avoiding difficult regions while maintaining a diverse and viable path through unexplored areas."
}
```
2025-07-04 21:33:30,614 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:30,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11438.0, 路径: [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:30,616 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}
2025-07-04 21:33:30,617 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:33:30,617 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:30,617 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101924.0 (rank 80.0%)
- Population diversity: 0.4169230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [59,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:30,618 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:30,626 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:34,352 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65, 59],
  "targeted_regions": "Avoiding difficult regions [59, 55]",
  "strategy_comment": "Prioritizes visiting all nodes while attempting to avoid the difficult region by using them as transitions."
}
```
2025-07-04 21:33:34,353 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:34,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13481.0, 路径: [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:34,354 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}
2025-07-04 21:33:34,354 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:33:34,354 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:33:34,367 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:33:34,369 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 124261.0
2025-07-04 21:33:36,863 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 21:33:36,863 - ExploitationExpert - INFO - res_population_costs: [97491]
2025-07-04 21:33:36,863 - ExploitationExpert - INFO - res_populations: [array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:33:36,864 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:33:36,865 - ExploitationExpert - INFO - populations: [{'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58], 'cur_cost': 11370.0}, {'tour': [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0], 'cur_cost': 21770.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': array([ 3, 42, 64, 33, 39,  0, 45, 17,  7, 49, 15, 62, 18, 32, 55, 36, 40,
       25, 12, 50, 34, 57, 46, 53, 44, 28, 22,  9, 43, 59, 31, 16, 56, 35,
        5, 60, 29, 58,  4, 24, 20, 61, 37,  6, 41, 10, 52, 27, 65, 54,  1,
       13, 21, 63, 51, 47, 48, 11, 23, 38,  2, 14, 26, 30, 19,  8]), 'cur_cost': 124261.0}, {'tour': [38, 43, 40, 8, 27, 24, 44, 11, 6, 25, 29, 34, 26, 51, 20, 21, 60, 13, 37, 16, 47, 17, 55, 32, 53, 1, 31, 3, 58, 52, 9, 18, 5, 30, 4, 15, 36, 14, 41, 2, 46, 23, 65, 0, 63, 35, 28, 42, 49, 61, 19, 56, 50, 7, 59, 22, 45, 57, 54, 62, 33, 64, 39, 12, 10, 48], 'cur_cost': 99742.0}]
2025-07-04 21:33:36,866 - ExploitationExpert - INFO - 局部搜索耗时: 2.50秒
2025-07-04 21:33:36,866 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 21:33:36,866 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:33:36,866 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:33:36,866 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:33:36,867 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:33:36,867 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113715.0
2025-07-04 21:33:37,834 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 21:33:37,834 - ExploitationExpert - INFO - res_population_costs: [97491, 9573]
2025-07-04 21:33:37,834 - ExploitationExpert - INFO - res_populations: [array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64)]
2025-07-04 21:33:37,836 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:33:37,836 - ExploitationExpert - INFO - populations: [{'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58], 'cur_cost': 11370.0}, {'tour': [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0], 'cur_cost': 21770.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': array([ 3, 42, 64, 33, 39,  0, 45, 17,  7, 49, 15, 62, 18, 32, 55, 36, 40,
       25, 12, 50, 34, 57, 46, 53, 44, 28, 22,  9, 43, 59, 31, 16, 56, 35,
        5, 60, 29, 58,  4, 24, 20, 61, 37,  6, 41, 10, 52, 27, 65, 54,  1,
       13, 21, 63, 51, 47, 48, 11, 23, 38,  2, 14, 26, 30, 19,  8]), 'cur_cost': 124261.0}, {'tour': array([23, 28, 65, 39, 12, 62, 60, 21, 20, 57, 26, 55, 50, 31, 24, 40, 64,
       27, 42, 58, 37, 61,  5, 45,  3, 36,  6, 22, 11, 59, 43, 51, 44,  7,
       32, 52, 48, 47, 16,  4, 54, 41, 18, 33, 30,  8, 10, 17, 25, 14, 38,
       56, 13, 46, 35,  0, 53,  1, 63, 49,  2, 34, 15,  9, 19, 29]), 'cur_cost': 113715.0}]
2025-07-04 21:33:37,837 - ExploitationExpert - INFO - 局部搜索耗时: 0.97秒
2025-07-04 21:33:37,838 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 21:33:37,838 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:33:37,838 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [59, 60, 55, 56, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 57, 58], 'cur_cost': 11370.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [59, 10, 23, 31, 45, 55, 60, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 61, 62, 63, 64, 65, 0], 'cur_cost': 21770.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}}]
2025-07-04 21:33:37,839 - __main__ - INFO - 进化阶段完成
2025-07-04 21:33:37,839 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:33:37,855 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11370.0, 'max': 124261.0, 'mean': 34387.1, 'std': 42471.29192395729}, 'diversity': 0.4276094276094276, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:33:37,855 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-04 21:33:37,855 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-04 21:33:37,856 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:33:37,856 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 2 → 2
- Best elite cost: 9573 → 9573 (Δ 0)
- Elite diversity: 0.985 → 0.985 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:33:37,856 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:41,023 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_larger_population_size"]
}
```
2025-07-04 21:33:41,024 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:33:41,024 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-04 21:33:41,024 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-04 21:33:41,025 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-04 21:33:41,025 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:33:41,040 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11370.0, 'max': 124261.0, 'mean': 34387.1, 'std': 42471.29192395729}, 'diversity': 0.4276094276094276, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:33:41,041 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:33:41,044 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:33:41,044 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:33:41,045 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:33:41,045 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:33:41,046 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:33:41,055 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:33:41,056 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/8)
- population_size: 10
- cost_stats: min 11370.0, mean 34387.1, max 124261.0, std 42471.29192395729
- diversity: 0.4276094276094276
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.9696969696969697}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:33:41,056 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:44,649 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "N/A",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.4276094276094276,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Increase mutation rate (e.g., swap, insert, reverse)."
    ]
  }
}
```
2025-07-04 21:33:44,650 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:33:44,650 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'N/A', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.4276094276094276, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Increase mutation rate (e.g., swap, insert, reverse).']}}
2025-07-04 21:33:44,652 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:33:44,652 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:33:44,652 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.428
  • best_cost: 11370.0
  • mean_cost: 34387.1
- Search-space: ruggedness N/A, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:33:44,653 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:33:44,653 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:47,615 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus on exploration due to initial evolution stage and landscape suggestion. Favor explore for higher diversity and a broader search."
}
```
2025-07-04 21:33:47,616 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:33:47,617 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:33:47,617 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus on exploration due to initial evolution stage and landscape suggestion. Favor explore for higher diversity and a broader search."
}
```
2025-07-04 21:33:47,617 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:33:47,618 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:33:47,618 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Focus on exploration due to initial evolution stage and landscape suggestion. Favor explore for higher diversity and a broader search."
}
```
2025-07-04 21:33:47,619 - __main__ - INFO - 开始进化阶段
2025-07-04 21:33:47,619 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:33:47,619 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:47,620 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11438.0 (rank 10.0%)
- Population diversity: 0.32461538461538464
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:47,620 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:47,621 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:53,242 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells",
  "strategy_comment": "Prioritizes exploring all nodes in a single, sweeping path to maximize diversity."
}
```
2025-07-04 21:33:53,243 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:53,244 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 21:33:53,244 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 21:33:53,245 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:33:53,245 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:53,245 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11438.0 (rank 10.0%)
- Population diversity: 0.3261538461538461
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:53,246 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:53,246 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:33:57,368 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells - all nodes equally",
  "strategy_comment": "Simple linear path to explore unexplored cells efficiently, aiming for diversity."
}
```
2025-07-04 21:33:57,369 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:33:57,369 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:33:57,370 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:33:57,370 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:33:57,370 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:33:57,373 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13481.0 (rank 40.0%)
- Population diversity: 0.3261538461538461
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:33:57,374 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:33:57,374 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:02,814 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all).",
  "strategy_comment": "A simple path through all nodes to hit every sparse cell, maximizing diversity."
}
```
2025-07-04 21:34:02,815 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:02,815 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:02,816 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:02,816 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:34:02,817 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:02,817 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11370.0 (rank 0.0%)
- Population diversity: 0.3261538461538461
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:02,819 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:02,819 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:10,132 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited",
  "strategy_comment": "Simple linear path covering all nodes. Focus on basic exploration for initial iteration."
}
```
2025-07-04 21:34:10,133 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:10,134 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:10,134 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:10,135 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:34:10,135 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:10,135 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 21770.0 (rank 70.0%)
- Population diversity: 0.3230769230769231
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:10,136 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:10,137 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:14,286 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells",
  "strategy_comment": "Initial path exploring nodes sequentially, targeting all unvisited nodes to increase diversity."
}
```
2025-07-04 21:34:14,287 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:14,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:14,287 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:14,288 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:34:14,288 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:34:14,288 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:34:14,289 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 104623.0
2025-07-04 21:34:14,792 - ExploitationExpert - INFO - res_population_num: 12
2025-07-04 21:34:14,793 - ExploitationExpert - INFO - res_population_costs: [9573, 97491, 9573, 9560, 9549, 9538, 9537, 9530, 9528, 9527, 9527, 9521]
2025-07-04 21:34:14,793 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:34:14,799 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:34:14,799 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 35, 46,  3, 56, 25, 33, 30, 51, 21, 16, 32, 18, 27, 42, 49, 12,
       60, 28, 44, 50, 36, 19,  9, 59, 47, 29, 48,  4, 53, 10, 20, 61, 63,
       22, 54, 11,  6,  2, 39, 26, 58, 17, 37, 14, 31, 52,  1, 38, 64, 13,
       23, 62, 40, 45,  5, 41, 43, 55, 65, 24,  8, 15,  0, 57, 34]), 'cur_cost': 104623.0}, {'tour': [59, 55, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 11438.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': array([ 3, 42, 64, 33, 39,  0, 45, 17,  7, 49, 15, 62, 18, 32, 55, 36, 40,
       25, 12, 50, 34, 57, 46, 53, 44, 28, 22,  9, 43, 59, 31, 16, 56, 35,
        5, 60, 29, 58,  4, 24, 20, 61, 37,  6, 41, 10, 52, 27, 65, 54,  1,
       13, 21, 63, 51, 47, 48, 11, 23, 38,  2, 14, 26, 30, 19,  8]), 'cur_cost': 124261.0}, {'tour': array([23, 28, 65, 39, 12, 62, 60, 21, 20, 57, 26, 55, 50, 31, 24, 40, 64,
       27, 42, 58, 37, 61,  5, 45,  3, 36,  6, 22, 11, 59, 43, 51, 44,  7,
       32, 52, 48, 47, 16,  4, 54, 41, 18, 33, 30,  8, 10, 17, 25, 14, 38,
       56, 13, 46, 35,  0, 53,  1, 63, 49,  2, 34, 15,  9, 19, 29]), 'cur_cost': 113715.0}]
2025-07-04 21:34:14,809 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:34:14,809 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 21:34:14,810 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 21:34:14,810 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:34:14,810 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:34:14,810 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:34:14,811 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 117138.0
2025-07-04 21:34:15,315 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-07-04 21:34:15,317 - ExploitationExpert - INFO - res_population_num: 16
2025-07-04 21:34:15,317 - ExploitationExpert - INFO - res_population_costs: [9573, 97491, 9573, 9560, 9549, 9538, 9537, 9530, 9528, 9527, 9527, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:34:15,317 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:34:15,323 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:34:15,324 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 35, 46,  3, 56, 25, 33, 30, 51, 21, 16, 32, 18, 27, 42, 49, 12,
       60, 28, 44, 50, 36, 19,  9, 59, 47, 29, 48,  4, 53, 10, 20, 61, 63,
       22, 54, 11,  6,  2, 39, 26, 58, 17, 37, 14, 31, 52,  1, 38, 64, 13,
       23, 62, 40, 45,  5, 41, 43, 55, 65, 24,  8, 15,  0, 57, 34]), 'cur_cost': 104623.0}, {'tour': array([54, 51, 55, 60,  3, 13, 36, 14, 17, 22, 16, 40, 63, 20, 23, 65, 15,
       38, 21, 62, 12, 24,  1, 46,  0, 49, 26, 58, 30, 61,  7,  2, 48, 47,
       45, 56, 25,  8, 32, 19, 18,  9, 33, 43, 31, 28, 42,  6, 34, 53, 50,
       57, 10, 37, 44, 59, 41,  4,  5, 27, 29, 64, 39, 11, 52, 35]), 'cur_cost': 117138.0}, {'tour': [0, 59, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 61, 62, 63, 64, 65], 'cur_cost': 13481.0}, {'tour': array([ 3, 42, 64, 33, 39,  0, 45, 17,  7, 49, 15, 62, 18, 32, 55, 36, 40,
       25, 12, 50, 34, 57, 46, 53, 44, 28, 22,  9, 43, 59, 31, 16, 56, 35,
        5, 60, 29, 58,  4, 24, 20, 61, 37,  6, 41, 10, 52, 27, 65, 54,  1,
       13, 21, 63, 51, 47, 48, 11, 23, 38,  2, 14, 26, 30, 19,  8]), 'cur_cost': 124261.0}, {'tour': array([23, 28, 65, 39, 12, 62, 60, 21, 20, 57, 26, 55, 50, 31, 24, 40, 64,
       27, 42, 58, 37, 61,  5, 45,  3, 36,  6, 22, 11, 59, 43, 51, 44,  7,
       32, 52, 48, 47, 16,  4, 54, 41, 18, 33, 30,  8, 10, 17, 25, 14, 38,
       56, 13, 46, 35,  0, 53,  1, 63, 49,  2, 34, 15,  9, 19, 29]), 'cur_cost': 113715.0}]
2025-07-04 21:34:15,325 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:34:15,325 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 21:34:15,325 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:34:15,325 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:34:15,327 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:34:15,327 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:34:15,327 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111460.0
2025-07-04 21:34:15,830 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 21:34:15,830 - ExploitationExpert - INFO - res_population_costs: [9573, 97491, 9573, 9560, 9549, 9538, 9537, 9530, 9528, 9527, 9527, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:34:15,830 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:34:15,837 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:34:15,838 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 35, 46,  3, 56, 25, 33, 30, 51, 21, 16, 32, 18, 27, 42, 49, 12,
       60, 28, 44, 50, 36, 19,  9, 59, 47, 29, 48,  4, 53, 10, 20, 61, 63,
       22, 54, 11,  6,  2, 39, 26, 58, 17, 37, 14, 31, 52,  1, 38, 64, 13,
       23, 62, 40, 45,  5, 41, 43, 55, 65, 24,  8, 15,  0, 57, 34]), 'cur_cost': 104623.0}, {'tour': array([54, 51, 55, 60,  3, 13, 36, 14, 17, 22, 16, 40, 63, 20, 23, 65, 15,
       38, 21, 62, 12, 24,  1, 46,  0, 49, 26, 58, 30, 61,  7,  2, 48, 47,
       45, 56, 25,  8, 32, 19, 18,  9, 33, 43, 31, 28, 42,  6, 34, 53, 50,
       57, 10, 37, 44, 59, 41,  4,  5, 27, 29, 64, 39, 11, 52, 35]), 'cur_cost': 117138.0}, {'tour': array([24, 54, 25,  1, 28, 34, 26, 16, 56, 43, 36, 50, 29, 51, 59, 58, 12,
       61, 33, 11, 21, 22, 55, 45, 38, 32, 23, 44,  3, 62,  2, 40,  7, 31,
        4, 30, 19, 41, 65, 57, 64, 47, 48,  9, 37, 49, 52, 14, 13, 27,  8,
       35,  0,  6, 46, 20, 15, 10, 42, 53,  5, 60, 18, 63, 39, 17]), 'cur_cost': 111460.0}, {'tour': array([ 3, 42, 64, 33, 39,  0, 45, 17,  7, 49, 15, 62, 18, 32, 55, 36, 40,
       25, 12, 50, 34, 57, 46, 53, 44, 28, 22,  9, 43, 59, 31, 16, 56, 35,
        5, 60, 29, 58,  4, 24, 20, 61, 37,  6, 41, 10, 52, 27, 65, 54,  1,
       13, 21, 63, 51, 47, 48, 11, 23, 38,  2, 14, 26, 30, 19,  8]), 'cur_cost': 124261.0}, {'tour': array([23, 28, 65, 39, 12, 62, 60, 21, 20, 57, 26, 55, 50, 31, 24, 40, 64,
       27, 42, 58, 37, 61,  5, 45,  3, 36,  6, 22, 11, 59, 43, 51, 44,  7,
       32, 52, 48, 47, 16,  4, 54, 41, 18, 33, 30,  8, 10, 17, 25, 14, 38,
       56, 13, 46, 35,  0, 53,  1, 63, 49,  2, 34, 15,  9, 19, 29]), 'cur_cost': 113715.0}]
2025-07-04 21:34:15,840 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:34:15,840 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 21:34:15,840 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:34:15,841 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:34:15,841 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:34:15,841 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:34:15,842 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114919.0
2025-07-04 21:34:16,349 - ExploitationExpert - INFO - res_population_num: 20
2025-07-04 21:34:16,349 - ExploitationExpert - INFO - res_population_costs: [9573, 97491, 9573, 9560, 9549, 9538, 9537, 9530, 9528, 9527, 9527, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:34:16,349 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:34:16,357 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:34:16,357 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 35, 46,  3, 56, 25, 33, 30, 51, 21, 16, 32, 18, 27, 42, 49, 12,
       60, 28, 44, 50, 36, 19,  9, 59, 47, 29, 48,  4, 53, 10, 20, 61, 63,
       22, 54, 11,  6,  2, 39, 26, 58, 17, 37, 14, 31, 52,  1, 38, 64, 13,
       23, 62, 40, 45,  5, 41, 43, 55, 65, 24,  8, 15,  0, 57, 34]), 'cur_cost': 104623.0}, {'tour': array([54, 51, 55, 60,  3, 13, 36, 14, 17, 22, 16, 40, 63, 20, 23, 65, 15,
       38, 21, 62, 12, 24,  1, 46,  0, 49, 26, 58, 30, 61,  7,  2, 48, 47,
       45, 56, 25,  8, 32, 19, 18,  9, 33, 43, 31, 28, 42,  6, 34, 53, 50,
       57, 10, 37, 44, 59, 41,  4,  5, 27, 29, 64, 39, 11, 52, 35]), 'cur_cost': 117138.0}, {'tour': array([24, 54, 25,  1, 28, 34, 26, 16, 56, 43, 36, 50, 29, 51, 59, 58, 12,
       61, 33, 11, 21, 22, 55, 45, 38, 32, 23, 44,  3, 62,  2, 40,  7, 31,
        4, 30, 19, 41, 65, 57, 64, 47, 48,  9, 37, 49, 52, 14, 13, 27,  8,
       35,  0,  6, 46, 20, 15, 10, 42, 53,  5, 60, 18, 63, 39, 17]), 'cur_cost': 111460.0}, {'tour': array([ 6, 53, 18, 63, 14, 45, 29, 15, 36, 22, 12,  7, 20, 50, 52, 30, 37,
       39, 11, 46, 10,  2, 31,  9,  8, 35, 55, 60, 58, 26,  0, 33, 19, 65,
       41, 54, 16, 61, 24, 21, 40, 44,  3, 38, 56, 42, 23, 43, 62,  1,  5,
       17, 25,  4, 28, 59, 64, 34, 57, 49, 47, 13, 32, 51, 27, 48]), 'cur_cost': 114919.0}, {'tour': array([23, 28, 65, 39, 12, 62, 60, 21, 20, 57, 26, 55, 50, 31, 24, 40, 64,
       27, 42, 58, 37, 61,  5, 45,  3, 36,  6, 22, 11, 59, 43, 51, 44,  7,
       32, 52, 48, 47, 16,  4, 54, 41, 18, 33, 30,  8, 10, 17, 25, 14, 38,
       56, 13, 46, 35,  0, 53,  1, 63, 49,  2, 34, 15,  9, 19, 29]), 'cur_cost': 113715.0}]
2025-07-04 21:34:16,358 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:34:16,359 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-04 21:34:16,359 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:34:16,359 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:34:16,359 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:34:16,360 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:34:16,360 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104067.0
2025-07-04 21:34:18,010 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:34:18,010 - ExploitationExpert - INFO - res_population_costs: [9573, 97491, 9573, 9560, 9549, 9538, 9537, 9530, 9528, 9527, 9527, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:34:18,010 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:34:18,017 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:34:18,017 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 35, 46,  3, 56, 25, 33, 30, 51, 21, 16, 32, 18, 27, 42, 49, 12,
       60, 28, 44, 50, 36, 19,  9, 59, 47, 29, 48,  4, 53, 10, 20, 61, 63,
       22, 54, 11,  6,  2, 39, 26, 58, 17, 37, 14, 31, 52,  1, 38, 64, 13,
       23, 62, 40, 45,  5, 41, 43, 55, 65, 24,  8, 15,  0, 57, 34]), 'cur_cost': 104623.0}, {'tour': array([54, 51, 55, 60,  3, 13, 36, 14, 17, 22, 16, 40, 63, 20, 23, 65, 15,
       38, 21, 62, 12, 24,  1, 46,  0, 49, 26, 58, 30, 61,  7,  2, 48, 47,
       45, 56, 25,  8, 32, 19, 18,  9, 33, 43, 31, 28, 42,  6, 34, 53, 50,
       57, 10, 37, 44, 59, 41,  4,  5, 27, 29, 64, 39, 11, 52, 35]), 'cur_cost': 117138.0}, {'tour': array([24, 54, 25,  1, 28, 34, 26, 16, 56, 43, 36, 50, 29, 51, 59, 58, 12,
       61, 33, 11, 21, 22, 55, 45, 38, 32, 23, 44,  3, 62,  2, 40,  7, 31,
        4, 30, 19, 41, 65, 57, 64, 47, 48,  9, 37, 49, 52, 14, 13, 27,  8,
       35,  0,  6, 46, 20, 15, 10, 42, 53,  5, 60, 18, 63, 39, 17]), 'cur_cost': 111460.0}, {'tour': array([ 6, 53, 18, 63, 14, 45, 29, 15, 36, 22, 12,  7, 20, 50, 52, 30, 37,
       39, 11, 46, 10,  2, 31,  9,  8, 35, 55, 60, 58, 26,  0, 33, 19, 65,
       41, 54, 16, 61, 24, 21, 40, 44,  3, 38, 56, 42, 23, 43, 62,  1,  5,
       17, 25,  4, 28, 59, 64, 34, 57, 49, 47, 13, 32, 51, 27, 48]), 'cur_cost': 114919.0}, {'tour': array([ 5, 55, 36, 35, 17, 59, 25, 18, 57,  8, 15, 42, 12, 64, 63, 58,  0,
        6, 46,  2, 39, 60, 43, 50, 47, 62, 31, 11, 10, 38, 54, 51, 53,  1,
       52, 19, 45, 13, 20,  4, 21, 44, 16, 28, 29,  7, 14,  9, 56, 32, 26,
        3, 33, 23, 22, 49, 41, 65, 24, 40, 27, 61, 34, 37, 48, 30]), 'cur_cost': 104067.0}]
2025-07-04 21:34:18,019 - ExploitationExpert - INFO - 局部搜索耗时: 1.66秒
2025-07-04 21:34:18,020 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-04 21:34:18,020 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:34:18,020 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:34:18,021 - __main__ - INFO - 进化阶段完成
2025-07-04 21:34:18,021 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:34:18,038 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 117138.0, 'mean': 61168.6, 'std': 49418.60003723295}, 'diversity': 0.7511784511784512, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:34:18,039 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-04 21:34:18,039 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-04 21:34:18,041 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:34:18,041 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.136 → 0.136 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:34:18,042 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:24,049 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate", "increase_diversity_weight"]
}
```
2025-07-04 21:34:24,050 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:34:24,050 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-04 21:34:24,051 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-04 21:34:24,051 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-04 21:34:24,051 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:34:24,068 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 117138.0, 'mean': 61168.6, 'std': 49418.60003723295}, 'diversity': 0.7511784511784512, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:34:24,069 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:34:24,072 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:34:24,072 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:34:24,072 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:34:24,080 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:34:24,082 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:34:24,083 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:34:24,083 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/8)
- population_size: 10
- cost_stats: min 11479.0, mean 61168.6, max 117138.0, std 49418.60003723295
- diversity: 0.7511784511784512
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.30995670995671}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:34:24,083 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:28,814 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.75,
    "convergence": 0.0,
    "clustering": "none"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "exploration",
    "operators": [
      "Apply large-scale mutation operators (e.g., 2-opt, 3-opt) with increased frequency to explore the search space more broadly.",
      "Increase population size to improve diversity and explore the search space more extensively."
    ]
  }
}
```
2025-07-04 21:34:28,814 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:34:28,814 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.75, 'convergence': 0.0, 'clustering': 'none'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['Apply large-scale mutation operators (e.g., 2-opt, 3-opt) with increased frequency to explore the search space more broadly.', 'Increase population size to improve diversity and explore the search space more extensively.']}}
2025-07-04 21:34:28,816 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:34:28,816 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:34:28,816 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.75
  • best_cost: 11479.0
  • mean_cost: 61168.6
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:34:28,817 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:34:28,817 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:32,377 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity and landscape focus on exploration calls for a bias towards exploring the search space. Individuals are assigned based on relative cost rank."
}
```
2025-07-04 21:34:32,378 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:34:32,378 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:34:32,379 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity and landscape focus on exploration calls for a bias towards exploring the search space. Individuals are assigned based on relative cost rank."
}
```
2025-07-04 21:34:32,379 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:34:32,379 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:34:32,380 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity and landscape focus on exploration calls for a bias towards exploring the search space. Individuals are assigned based on relative cost rank."
}
```
2025-07-04 21:34:32,380 - __main__ - INFO - 开始进化阶段
2025-07-04 21:34:32,380 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:34:32,381 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:32,382 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 40.0%)
- Population diversity: 0.5538461538461539
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:32,382 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:32,382 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:36,395 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore cells with zero visits in density grid.",
  "strategy_comment": "Prioritizing unexplored cells by a simple ordered path covering all nodes."
}
```
2025-07-04 21:34:36,396 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:36,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:36,397 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:36,398 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:34:36,398 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:36,399 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5507692307692308
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:36,399 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:36,399 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:45,338 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells exploration by simple path.",
  "strategy_comment": "Creates a basic, linear path to explore the space and seek diverse starting points."
}
```
2025-07-04 21:34:45,338 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:45,340 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:45,340 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:45,341 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:34:45,341 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:45,341 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5507692307692308
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:45,342 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:45,342 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:51,052 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells - all unvisited areas.",
  "strategy_comment": "Prioritized visiting all nodes sequentially to cover potentially sparse/unvisited cells."
}
```
2025-07-04 21:34:51,053 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:51,054 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 21:34:51,054 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 21:34:51,055 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:34:51,055 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:51,056 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5538461538461539
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:51,057 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:51,057 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:34:59,551 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes",
  "strategy_comment": "Baseline path to maximize node coverage; explores all possible connections.  Simple approach for exploration."
}
```
2025-07-04 21:34:59,552 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:34:59,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:34:59,552 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:34:59,553 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:34:59,553 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:34:59,553 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5538461538461539
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:34:59,554 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:34:59,554 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:35:07,428 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells: all cells visited once in the seed path.",
  "strategy_comment": "Basic linear path covering all nodes for initial diversity, exploring every node."
}
```
2025-07-04 21:35:07,429 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:35:07,429 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:35:07,430 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:35:07,430 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:35:07,430 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:35:07,431 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104623.0 (rank 60.0%)
- Population diversity: 0.5538461538461539
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:35:07,431 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:35:07,432 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:35:21,410 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2406)')))
2025-07-04 21:35:23,412 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:35:29,838 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all)",
  "strategy_comment": "Visit all nodes in a simple sequential order to explore the entire space and increase diversity."
}
```
2025-07-04 21:35:29,839 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:35:29,839 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:35:29,839 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:35:29,839 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:35:29,841 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:35:29,841 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:35:29,841 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 98143.0
2025-07-04 21:35:30,344 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:35:30,344 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491]
2025-07-04 21:35:30,344 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:35:30,352 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:35:30,352 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 41, 25,  8, 52, 56,  2, 36, 51, 40, 33, 23, 43, 19, 45, 13, 44,
       54, 10, 31, 37, 26, 30, 49, 11,  1,  3, 32, 46,  6, 61, 27, 64, 24,
       57,  5, 62, 59, 29, 17, 14, 63, 60,  7, 22,  9, 34,  0, 39, 65, 42,
       48, 47, 28, 15, 35, 38, 12,  4, 58, 50, 55, 20, 16, 21, 53]), 'cur_cost': 98143.0}, {'tour': array([24, 54, 25,  1, 28, 34, 26, 16, 56, 43, 36, 50, 29, 51, 59, 58, 12,
       61, 33, 11, 21, 22, 55, 45, 38, 32, 23, 44,  3, 62,  2, 40,  7, 31,
        4, 30, 19, 41, 65, 57, 64, 47, 48,  9, 37, 49, 52, 14, 13, 27,  8,
       35,  0,  6, 46, 20, 15, 10, 42, 53,  5, 60, 18, 63, 39, 17]), 'cur_cost': 111460.0}, {'tour': array([ 6, 53, 18, 63, 14, 45, 29, 15, 36, 22, 12,  7, 20, 50, 52, 30, 37,
       39, 11, 46, 10,  2, 31,  9,  8, 35, 55, 60, 58, 26,  0, 33, 19, 65,
       41, 54, 16, 61, 24, 21, 40, 44,  3, 38, 56, 42, 23, 43, 62,  1,  5,
       17, 25,  4, 28, 59, 64, 34, 57, 49, 47, 13, 32, 51, 27, 48]), 'cur_cost': 114919.0}, {'tour': array([ 5, 55, 36, 35, 17, 59, 25, 18, 57,  8, 15, 42, 12, 64, 63, 58,  0,
        6, 46,  2, 39, 60, 43, 50, 47, 62, 31, 11, 10, 38, 54, 51, 53,  1,
       52, 19, 45, 13, 20,  4, 21, 44, 16, 28, 29,  7, 14,  9, 56, 32, 26,
        3, 33, 23, 22, 49, 41, 65, 24, 40, 27, 61, 34, 37, 48, 30]), 'cur_cost': 104067.0}]
2025-07-04 21:35:30,354 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:35:30,354 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-04 21:35:30,354 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:35:30,355 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:35:30,355 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:35:30,355 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111460.0 (rank 80.0%)
- Population diversity: 0.4646153846153846
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:35:30,355 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:35:30,357 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:35:37,390 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Basic path through all nodes to increase diversity and initialize exploration. No specific regions targeted."
}
```
2025-07-04 21:35:37,390 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:35:37,390 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:35:37,390 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:35:37,393 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:35:37,393 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:35:37,393 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:35:37,393 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108475.0
2025-07-04 21:35:37,896 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:35:37,896 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491]
2025-07-04 21:35:37,896 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:35:37,904 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:35:37,904 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 41, 25,  8, 52, 56,  2, 36, 51, 40, 33, 23, 43, 19, 45, 13, 44,
       54, 10, 31, 37, 26, 30, 49, 11,  1,  3, 32, 46,  6, 61, 27, 64, 24,
       57,  5, 62, 59, 29, 17, 14, 63, 60,  7, 22,  9, 34,  0, 39, 65, 42,
       48, 47, 28, 15, 35, 38, 12,  4, 58, 50, 55, 20, 16, 21, 53]), 'cur_cost': 98143.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([12, 43, 41, 18, 56, 52, 51, 33, 14,  0, 61, 58, 30, 15, 38, 54, 28,
       34, 21, 45, 46, 13, 22, 29, 24, 11, 64, 37,  9, 20, 35, 59, 44,  1,
       39, 53, 17, 26, 63, 31, 49, 55,  2, 23, 19, 16, 40,  6, 47,  5, 10,
       62, 48, 57,  4,  8, 50, 65, 25,  3,  7, 27, 32, 60, 42, 36]), 'cur_cost': 108475.0}, {'tour': array([ 5, 55, 36, 35, 17, 59, 25, 18, 57,  8, 15, 42, 12, 64, 63, 58,  0,
        6, 46,  2, 39, 60, 43, 50, 47, 62, 31, 11, 10, 38, 54, 51, 53,  1,
       52, 19, 45, 13, 20,  4, 21, 44, 16, 28, 29,  7, 14,  9, 56, 32, 26,
        3, 33, 23, 22, 49, 41, 65, 24, 40, 27, 61, 34, 37, 48, 30]), 'cur_cost': 104067.0}]
2025-07-04 21:35:37,906 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:35:37,906 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-04 21:35:37,906 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:35:37,907 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:35:37,907 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:35:37,907 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:35:37,907 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110475.0
2025-07-04 21:35:38,409 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:35:38,410 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491]
2025-07-04 21:35:38,410 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:35:38,417 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:35:38,418 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 41, 25,  8, 52, 56,  2, 36, 51, 40, 33, 23, 43, 19, 45, 13, 44,
       54, 10, 31, 37, 26, 30, 49, 11,  1,  3, 32, 46,  6, 61, 27, 64, 24,
       57,  5, 62, 59, 29, 17, 14, 63, 60,  7, 22,  9, 34,  0, 39, 65, 42,
       48, 47, 28, 15, 35, 38, 12,  4, 58, 50, 55, 20, 16, 21, 53]), 'cur_cost': 98143.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([12, 43, 41, 18, 56, 52, 51, 33, 14,  0, 61, 58, 30, 15, 38, 54, 28,
       34, 21, 45, 46, 13, 22, 29, 24, 11, 64, 37,  9, 20, 35, 59, 44,  1,
       39, 53, 17, 26, 63, 31, 49, 55,  2, 23, 19, 16, 40,  6, 47,  5, 10,
       62, 48, 57,  4,  8, 50, 65, 25,  3,  7, 27, 32, 60, 42, 36]), 'cur_cost': 108475.0}, {'tour': array([41,  9, 25, 63,  4, 10, 37,  6,  8, 43,  3, 21,  0, 53, 34, 65, 30,
        5, 36, 54, 26, 57, 62, 49, 24, 28, 48, 42, 17, 19, 11, 38, 59, 29,
        1, 32, 60, 35, 51, 33, 13, 18, 22, 64, 27, 20, 15, 50, 39, 31,  2,
       47, 14, 61, 55, 16, 40, 52, 58,  7, 23, 56, 46, 12, 44, 45]), 'cur_cost': 110475.0}]
2025-07-04 21:35:38,419 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:35:38,420 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-04 21:35:38,420 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:35:38,420 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:35:38,421 - __main__ - INFO - 进化阶段完成
2025-07-04 21:35:38,421 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:35:38,436 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110475.0, 'mean': 39953.0, 'std': 43145.9583553315}, 'diversity': 0.5252525252525252, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:35:38,436 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-04 21:35:38,437 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-04 21:35:38,438 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:35:38,438 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.136 → 0.136 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:35:38,439 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:35:41,932 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate", "consider_increasing_diversity_weight"]
}
```
2025-07-04 21:35:41,933 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:35:41,933 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-04 21:35:41,934 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-04 21:35:41,934 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-04 21:35:41,934 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:35:41,949 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110475.0, 'mean': 39953.0, 'std': 43145.9583553315}, 'diversity': 0.5252525252525252, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:35:41,949 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:35:41,955 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:35:41,955 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:35:41,955 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:35:41,963 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:35:41,966 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:35:41,967 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:35:41,967 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/8)
- population_size: 10
- cost_stats: min 11479.0, mean 39953.0, max 110475.0, std 43145.9583553315
- diversity: 0.5252525252525252
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.30995670995671}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:35:41,968 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:35:55,933 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.525,
    "convergence": 0.0,
    "clustering": "none"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation: Apply node swaps, insertion, or inversion mutation operators with a high probability. This helps to explore the search space more widely.",
      "Crossover: Utilize crossover operators that promote diversity. Consider order crossover or partially mapped crossover, to introduce new edges into the population and enhance exploration.",
      "Increase mutation rate (or other perturbation rate): Given the low convergence and high diversity, increasing the mutation rate can assist in exploring the search space"
    ]
  }
}
```
2025-07-04 21:35:55,934 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:35:55,934 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.525, 'convergence': 0.0, 'clustering': 'none'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation: Apply node swaps, insertion, or inversion mutation operators with a high probability. This helps to explore the search space more widely.', 'Crossover: Utilize crossover operators that promote diversity. Consider order crossover or partially mapped crossover, to introduce new edges into the population and enhance exploration.', 'Increase mutation rate (or other perturbation rate): Given the low convergence and high diversity, increasing the mutation rate can assist in exploring the search space']}}
2025-07-04 21:35:55,935 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:35:55,935 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:35:55,935 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.525
  • best_cost: 11479.0
  • mean_cost: 39953.0
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state":...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:35:55,935 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:35:55,936 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:00,595 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage and recommended focus is explore. Population diversity is moderate, but current stage suggests wider exploration."
}
```
2025-07-04 21:36:00,596 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:36:00,596 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:36:00,596 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage and recommended focus is explore. Population diversity is moderate, but current stage suggests wider exploration."
}
```
2025-07-04 21:36:00,597 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:36:00,597 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:36:00,597 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage and recommended focus is explore. Population diversity is moderate, but current stage suggests wider exploration."
}
```
2025-07-04 21:36:00,597 - __main__ - INFO - 开始进化阶段
2025-07-04 21:36:00,598 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:36:00,598 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:00,598 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.38461538461538464
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:00,599 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:00,599 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:05,453 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells via sequential node visit.",
  "strategy_comment": "Creates a path that directly visits all nodes sequentially, enhancing exploration for unvisited cells."
}
```
2025-07-04 21:36:05,454 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:05,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:05,455 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:05,455 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:36:05,456 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:05,456 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.38461538461538464
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:05,457 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:05,457 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:12,605 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Sparse cells via systematic sweeping.",
  "strategy_comment": "Spreads the path systematically to explore all nodes, prioritizing sparse area visits to enhance diversity."
}
```
2025-07-04 21:36:12,606 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:12,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25989.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-04 21:36:12,606 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}
2025-07-04 21:36:12,607 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:36:12,607 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:12,608 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 50.0%)
- Population diversity: 0.4323076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:12,609 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:12,609 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:18,569 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells - all cells explored.",
  "strategy_comment": "Construct a basic path visiting each node once to ensure coverage of sparse areas."
}
```
2025-07-04 21:36:18,570 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:18,570 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:18,571 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:18,571 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:36:18,571 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:18,572 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.42923076923076925
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:18,572 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:18,573 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:26,932 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited in a linear fashion",
  "strategy_comment": "Simple path to explore all nodes, increasing diversity by starting from the beginning."
}
```
2025-07-04 21:36:26,933 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:26,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:26,934 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:26,934 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:36:26,934 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:26,935 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.42923076923076925
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:26,935 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:26,936 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:35,000 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Basic path through all nodes to ensure full coverage and exploration focus."
}
```
2025-07-04 21:36:35,001 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:35,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:35,002 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:35,002 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:36:35,002 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:35,004 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.42923076923076925
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:35,004 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:35,005 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:39,932 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "all nodes visited",
  "strategy_comment": "Simple linear path for maximum diversity, ensuring all nodes are visited once."
}
```
2025-07-04 21:36:39,933 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:39,934 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:39,935 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:39,935 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:36:39,935 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:39,936 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98143.0 (rank 70.0%)
- Population diversity: 0.42923076923076925
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:39,936 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:39,936 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:45,973 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore zero visit cells (all cells).",
  "strategy_comment": "Simple path covering all nodes, focusing on previously unexplored cells. Prioritizing breadth over depth."
}
```
2025-07-04 21:36:45,974 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:45,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:45,975 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:45,976 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:36:45,976 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:36:45,977 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.34307692307692306
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:36:45,977 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:36:45,978 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:36:50,525 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells: all cells are visited.",
  "strategy_comment": "A basic path, ensuring all nodes are visited to prioritize exploration in a controlled manner."
}
```
2025-07-04 21:36:50,525 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:36:50,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:36:50,527 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:36:50,528 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:36:50,528 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:36:50,528 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:36:50,528 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 98710.0
2025-07-04 21:36:51,032 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:36:51,032 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521, 9521]
2025-07-04 21:36:51,032 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:36:51,040 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:36:51,041 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([33, 45, 49, 38, 47, 34, 11, 46, 43, 27,  0, 16, 39, 23, 60, 22,  6,
       53,  4, 14, 42,  3,  8, 12,  9, 35, 15, 40, 55, 36, 31, 63, 30, 20,
       18, 62,  5, 65, 64, 19, 10, 57, 24, 44, 37, 25, 48, 26, 41, 13, 52,
       56,  1,  2, 32, 28, 58, 61, 50, 21, 17, 51, 54, 29, 59,  7]), 'cur_cost': 98710.0}, {'tour': array([41,  9, 25, 63,  4, 10, 37,  6,  8, 43,  3, 21,  0, 53, 34, 65, 30,
        5, 36, 54, 26, 57, 62, 49, 24, 28, 48, 42, 17, 19, 11, 38, 59, 29,
        1, 32, 60, 35, 51, 33, 13, 18, 22, 64, 27, 20, 15, 50, 39, 31,  2,
       47, 14, 61, 55, 16, 40, 52, 58,  7, 23, 56, 46, 12, 44, 45]), 'cur_cost': 110475.0}]
2025-07-04 21:36:51,042 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:36:51,042 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-04 21:36:51,043 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:36:51,043 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:36:51,043 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:36:51,043 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:36:51,044 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105205.0
2025-07-04 21:36:51,546 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:36:51,546 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521, 9521]
2025-07-04 21:36:51,546 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:36:51,554 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:36:51,554 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([33, 45, 49, 38, 47, 34, 11, 46, 43, 27,  0, 16, 39, 23, 60, 22,  6,
       53,  4, 14, 42,  3,  8, 12,  9, 35, 15, 40, 55, 36, 31, 63, 30, 20,
       18, 62,  5, 65, 64, 19, 10, 57, 24, 44, 37, 25, 48, 26, 41, 13, 52,
       56,  1,  2, 32, 28, 58, 61, 50, 21, 17, 51, 54, 29, 59,  7]), 'cur_cost': 98710.0}, {'tour': array([41, 10, 48, 27, 59, 26, 62, 20,  8, 37, 33, 18, 13, 54, 14, 23, 34,
       15, 29, 30, 24,  4, 58, 56, 46,  9, 63, 45,  2, 28, 39, 53, 11, 49,
       21, 61, 42,  6,  0, 57,  3, 16, 60, 43, 44, 25,  7, 31, 50, 47, 52,
        1, 22, 17, 64, 35, 12, 32,  5, 65, 36, 19, 55, 40, 38, 51]), 'cur_cost': 105205.0}]
2025-07-04 21:36:51,555 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:36:51,556 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-04 21:36:51,556 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:36:51,556 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:36:51,557 - __main__ - INFO - 进化阶段完成
2025-07-04 21:36:51,557 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:36:51,572 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 105205.0, 'mean': 31025.7, 'std': 35754.18259183113}, 'diversity': 0.45319865319865316, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:36:51,572 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-04 21:36:51,573 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-04 21:36:51,574 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:36:51,575 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 23 → 23
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.126 → 0.126 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:36:51,575 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:00,404 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_mutation_rate_increase"]
}
```
2025-07-04 21:37:00,405 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:37:00,405 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-04 21:37:00,405 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-04 21:37:00,405 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-04 21:37:00,406 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:37:00,429 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 105205.0, 'mean': 31025.7, 'std': 35754.18259183113}, 'diversity': 0.45319865319865316, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:37:00,430 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:37:00,434 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:37:00,434 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:37:00,435 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:37:00,445 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:37:00,447 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:37:00,448 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:37:00,448 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/8)
- population_size: 10
- cost_stats: min 11479.0, mean 31025.7, max 105205.0, std 35754.18259183113
- diversity: 0.45319865319865316
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.3078811833752546}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:37:00,448 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:04,936 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.453,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation: Apply a broader range of mutation operators, such as 2-opt, Or-opt, or relocate to increase exploration.",
      "Population Size: Consider increasing the population size to improve exploration, given the low convergence and medium diversity.",
      "Crossover: Evaluate and potentially diversify the crossover operator if only one is used.",
      "Restart: Since convergence is 0.0 and diversity is moderate, it may be beneficial to reset some populations and start exploration again."
    ]
  }
}
```
2025-07-04 21:37:04,937 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:37:04,937 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.453, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation: Apply a broader range of mutation operators, such as 2-opt, Or-opt, or relocate to increase exploration.', 'Population Size: Consider increasing the population size to improve exploration, given the low convergence and medium diversity.', 'Crossover: Evaluate and potentially diversify the crossover operator if only one is used.', 'Restart: Since convergence is 0.0 and diversity is moderate, it may be beneficial to reset some populations and start exploration again.']}}
2025-07-04 21:37:04,938 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:37:04,938 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:37:04,938 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.453
  • best_cost: 11479.0
  • mean_cost: 31025.7
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:37:04,939 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:37:04,939 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:08,479 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stagnation and high diversity suggest strong exploration. Most individuals explore, while top performers begin exploitation."
}
```
2025-07-04 21:37:08,480 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:37:08,480 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:37:08,481 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stagnation and high diversity suggest strong exploration. Most individuals explore, while top performers begin exploitation."
}
```
2025-07-04 21:37:08,481 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:37:08,481 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 21:37:08,481 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stagnation and high diversity suggest strong exploration. Most individuals explore, while top performers begin exploitation."
}
```
2025-07-04 21:37:08,482 - __main__ - INFO - 开始进化阶段
2025-07-04 21:37:08,482 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:37:08,482 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:08,483 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.34307692307692306
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:08,483 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:08,483 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:12,633 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited",
  "strategy_comment": "Simple path, covering all nodes and maximizing diversity relative to likely existing paths."
}
```
2025-07-04 21:37:12,634 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:12,634 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:37:12,635 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:37:12,635 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:37:12,636 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:12,636 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25989.0 (rank 70.0%)
- Population diversity: 0.34307692307692306
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:12,636 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:12,637 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:19,070 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells - prioritize node 65 first.",
  "strategy_comment": "Prioritizes visiting a potentially unexplored node (65) at the start of the path to increase exploration."
}
```
2025-07-04 21:37:19,071 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:19,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 21:37:19,071 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 21:37:19,072 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:37:19,072 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:19,072 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:19,073 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:19,073 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:23,253 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Simple path to maximize node coverage, aiming for exploration. This provides a good starting point."
}
```
2025-07-04 21:37:23,253 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:23,253 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:37:23,253 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:37:23,253 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:37:23,253 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:23,257 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:23,257 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:23,257 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:43,661 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells - all",
  "strategy_comment": "Basic path, attempts to explore all sparse cells as all cells have zero visits."
}
```
2025-07-04 21:37:43,662 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:43,662 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:37:43,664 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:37:43,664 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 21:37:43,664 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:37:43,665 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:37:43,665 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 116922.0
2025-07-04 21:37:43,665 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：2,跳过局部搜索
2025-07-04 21:37:43,666 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-04 21:37:43,666 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([62,  1, 37, 46, 13, 48, 11, 22, 57, 27, 14, 55, 29,  8, 32,  7, 25,
       56, 61, 47, 49,  6, 15, 20, 42, 10, 40,  3, 51,  2, 30, 60, 17,  0,
       19, 12, 31, 18, 23, 50, 44, 64,  9, 28, 16, 53, 52, 59, 24, 39, 41,
       63,  4, 43, 34, 38, 54, 26, 65, 35, 33,  5, 21, 58, 45, 36]), 'cur_cost': 116922.0}
2025-07-04 21:37:43,666 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:37:43,666 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:43,667 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.39076923076923076
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:43,667 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:43,668 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:47,861 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Basic linear traversal for diversity, ensuring all nodes are explored without specific region focus."
}
```
2025-07-04 21:37:47,862 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:47,862 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:37:47,864 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:37:47,864 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:37:47,864 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:37:47,864 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:37:47,865 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 119426.0
2025-07-04 21:37:47,865 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：2,跳过局部搜索
2025-07-04 21:37:47,865 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-04 21:37:47,866 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([54, 12,  9, 64, 28, 41, 24, 21, 33, 40, 65, 32, 16, 34, 53, 37, 30,
       51, 23, 45, 60, 56, 26, 63, 18, 47, 13, 57,  0, 15, 29, 19, 52,  5,
       35,  2, 25,  3, 17, 14,  8, 39, 46, 27, 58, 10, 20,  1, 49, 62, 31,
       55, 43,  6, 22, 42, 11, 44, 50, 48, 61,  4,  7, 38, 59, 36]), 'cur_cost': 119426.0}
2025-07-04 21:37:47,866 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:37:47,866 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:37:47,866 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4815384615384615
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:37:47,866 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:37:47,866 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:37:55,712 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells and high connectivity.",
  "strategy_comment": "Prioritizes complete coverage, exploring all nodes with a near-linear path for diversity."
}
```
2025-07-04 21:37:55,713 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:37:55,713 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 21:37:55,714 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 21:37:55,714 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:37:55,714 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:37:55,714 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:37:55,715 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105697.0
2025-07-04 21:37:56,217 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:37:56,217 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491]
2025-07-04 21:37:56,217 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:37:56,226 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:37:56,226 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([62,  1, 37, 46, 13, 48, 11, 22, 57, 27, 14, 55, 29,  8, 32,  7, 25,
       56, 61, 47, 49,  6, 15, 20, 42, 10, 40,  3, 51,  2, 30, 60, 17,  0,
       19, 12, 31, 18, 23, 50, 44, 64,  9, 28, 16, 53, 52, 59, 24, 39, 41,
       63,  4, 43, 34, 38, 54, 26, 65, 35, 33,  5, 21, 58, 45, 36]), 'cur_cost': 116922.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([54, 12,  9, 64, 28, 41, 24, 21, 33, 40, 65, 32, 16, 34, 53, 37, 30,
       51, 23, 45, 60, 56, 26, 63, 18, 47, 13, 57,  0, 15, 29, 19, 52,  5,
       35,  2, 25,  3, 17, 14,  8, 39, 46, 27, 58, 10, 20,  1, 49, 62, 31,
       55, 43,  6, 22, 42, 11, 44, 50, 48, 61,  4,  7, 38, 59, 36]), 'cur_cost': 119426.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([44, 49, 39, 10, 13,  7, 60, 17, 29, 56, 35, 65, 24, 45, 50, 42, 40,
       54, 27, 18, 16, 23, 34, 55, 36,  6, 47,  5, 26, 12, 61,  0, 43, 22,
       64, 11, 33, 53,  8,  3,  9, 57, 59, 28, 62, 32, 21, 15, 14,  4, 30,
       48, 25, 63, 38, 20, 52, 19, 31,  2, 58,  1, 51, 46, 41, 37]), 'cur_cost': 105697.0}, {'tour': array([41, 10, 48, 27, 59, 26, 62, 20,  8, 37, 33, 18, 13, 54, 14, 23, 34,
       15, 29, 30, 24,  4, 58, 56, 46,  9, 63, 45,  2, 28, 39, 53, 11, 49,
       21, 61, 42,  6,  0, 57,  3, 16, 60, 43, 44, 25,  7, 31, 50, 47, 52,
        1, 22, 17, 64, 35, 12, 32,  5, 65, 36, 19, 55, 40, 38, 51]), 'cur_cost': 105205.0}]
2025-07-04 21:37:56,228 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:37:56,228 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 15, 'skip_rate': 0.13333333333333333, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 13, 'cache_hits': 5, 'similarity_calculations': 84, 'cache_hit_rate': 0.05952380952380952, 'cache_size': 79}}
2025-07-04 21:37:56,229 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:37:56,229 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:37:56,229 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:37:56,229 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:37:56,230 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 106496.0
2025-07-04 21:37:56,734 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:37:56,734 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491]
2025-07-04 21:37:56,734 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64)]
2025-07-04 21:37:56,743 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:37:56,743 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([62,  1, 37, 46, 13, 48, 11, 22, 57, 27, 14, 55, 29,  8, 32,  7, 25,
       56, 61, 47, 49,  6, 15, 20, 42, 10, 40,  3, 51,  2, 30, 60, 17,  0,
       19, 12, 31, 18, 23, 50, 44, 64,  9, 28, 16, 53, 52, 59, 24, 39, 41,
       63,  4, 43, 34, 38, 54, 26, 65, 35, 33,  5, 21, 58, 45, 36]), 'cur_cost': 116922.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([54, 12,  9, 64, 28, 41, 24, 21, 33, 40, 65, 32, 16, 34, 53, 37, 30,
       51, 23, 45, 60, 56, 26, 63, 18, 47, 13, 57,  0, 15, 29, 19, 52,  5,
       35,  2, 25,  3, 17, 14,  8, 39, 46, 27, 58, 10, 20,  1, 49, 62, 31,
       55, 43,  6, 22, 42, 11, 44, 50, 48, 61,  4,  7, 38, 59, 36]), 'cur_cost': 119426.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([44, 49, 39, 10, 13,  7, 60, 17, 29, 56, 35, 65, 24, 45, 50, 42, 40,
       54, 27, 18, 16, 23, 34, 55, 36,  6, 47,  5, 26, 12, 61,  0, 43, 22,
       64, 11, 33, 53,  8,  3,  9, 57, 59, 28, 62, 32, 21, 15, 14,  4, 30,
       48, 25, 63, 38, 20, 52, 19, 31,  2, 58,  1, 51, 46, 41, 37]), 'cur_cost': 105697.0}, {'tour': array([54, 47, 59, 12, 64, 56,  0, 31,  5, 14, 18,  3, 53, 52, 24, 62, 46,
       60, 10, 65,  8, 35, 16, 50, 63, 38, 23,  4,  2, 21, 19, 22,  1, 43,
       55, 51, 58, 25, 15, 34, 32, 40, 27, 48, 37, 61,  7, 20, 41, 13, 33,
       39, 26,  6, 11, 30, 44, 49, 45, 42, 29, 17, 36, 57, 28,  9]), 'cur_cost': 106496.0}]
2025-07-04 21:37:56,745 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:37:56,745 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 16, 'skip_rate': 0.125, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 14, 'cache_hits': 5, 'similarity_calculations': 97, 'cache_hit_rate': 0.05154639175257732, 'cache_size': 92}}
2025-07-04 21:37:56,745 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:37:56,746 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([62,  1, 37, 46, 13, 48, 11, 22, 57, 27, 14, 55, 29,  8, 32,  7, 25,
       56, 61, 47, 49,  6, 15, 20, 42, 10, 40,  3, 51,  2, 30, 60, 17,  0,
       19, 12, 31, 18, 23, 50, 44, 64,  9, 28, 16, 53, 52, 59, 24, 39, 41,
       63,  4, 43, 34, 38, 54, 26, 65, 35, 33,  5, 21, 58, 45, 36]), 'cur_cost': 116922.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 12,  9, 64, 28, 41, 24, 21, 33, 40, 65, 32, 16, 34, 53, 37, 30,
       51, 23, 45, 60, 56, 26, 63, 18, 47, 13, 57,  0, 15, 29, 19, 52,  5,
       35,  2, 25,  3, 17, 14,  8, 39, 46, 27, 58, 10, 20,  1, 49, 62, 31,
       55, 43,  6, 22, 42, 11, 44, 50, 48, 61,  4,  7, 38, 59, 36]), 'cur_cost': 119426.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}]
2025-07-04 21:37:56,746 - __main__ - INFO - 进化阶段完成
2025-07-04 21:37:56,747 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:37:56,763 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 119426.0, 'mean': 52158.3, 'std': 49129.08964971772}, 'diversity': 0.6622895622895623, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:37:56,764 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-04 21:37:56,764 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-04 21:37:56,766 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:37:56,766 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 23 → 23
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.126 → 0.126 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:37:56,767 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:00,903 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stuck",
  "recommendations": [
    "increase_explore_ratio_to_0.8",
    "increase_mutation_rate_by_10%",
    "decrease_crossover_rate_by_10%",
    "consider_a_larger_population_size"
  ]
}
```
2025-07-04 21:38:00,904 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:38:00,904 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-04 21:38:00,905 - __main__ - INFO - composite13_66 开始进化第 6 代
2025-07-04 21:38:00,905 - __main__ - INFO - --- Running Analysis Phase (Iteration 5) ---
2025-07-04 21:38:00,905 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:38:00,921 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 119426.0, 'mean': 52158.3, 'std': 49129.08964971772}, 'diversity': 0.6622895622895623, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:38:00,921 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:38:00,926 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:38:00,927 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:38:00,927 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:38:00,937 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:38:00,940 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:38:00,940 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:38:00,940 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 5/8)
- population_size: 10
- cost_stats: min 11479.0, mean 52158.3, max 119426.0, std 49129.08964971772
- diversity: 0.6622895622895623
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.3078811833752546}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:38:00,941 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:04,886 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "N/A",
    "modality": "N/A",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.662,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply large-scale mutation operators (e.g., 2-opt, or edge recombination) to increase exploration and potentially discover new promising regions.",
      "Increase population size to maintain or improve diversity, as stagnation has not been detected."
    ]
  }
}
```
2025-07-04 21:38:04,886 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:38:04,886 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'N/A', 'modality': 'N/A', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.662, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply large-scale mutation operators (e.g., 2-opt, or edge recombination) to increase exploration and potentially discover new promising regions.', 'Increase population size to maintain or improve diversity, as stagnation has not been detected.']}}
2025-07-04 21:38:04,886 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:38:04,886 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:38:04,886 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 5
- Population: 10 individuals
  • diversity: 0.662
  • best_cost: 11479.0
  • mean_cost: 52158.3
- Search-space: ruggedness N/A, modality N/A, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:38:04,886 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:38:04,889 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:08,281 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution phase is stagnant and the landscape focus is to explore. Increased exploration focus to find better solutions."
}
```
2025-07-04 21:38:08,282 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:38:08,282 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:38:08,282 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution phase is stagnant and the landscape focus is to explore. Increased exploration focus to find better solutions."
}
```
2025-07-04 21:38:08,283 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:38:08,283 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:38:08,283 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution phase is stagnant and the landscape focus is to explore. Increased exploration focus to find better solutions."
}
```
2025-07-04 21:38:08,284 - __main__ - INFO - 开始进化阶段
2025-07-04 21:38:08,284 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:38:08,284 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:08,285 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:08,285 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:08,285 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:12,596 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All Nodes",
  "strategy_comment": "Basic linear path to explore entire solution space."
}
```
2025-07-04 21:38:12,597 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:38:12,597 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:38:12,597 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:38:12,598 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:38:12,598 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:12,599 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 40.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:12,599 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:12,599 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:21,041 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells based on initial visit count.",
  "strategy_comment": "Create a basic Hamiltonian path for initial coverage and exploration of the graph. Focus on visiting all nodes."
}
```
2025-07-04 21:38:21,043 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:38:21,043 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:38:21,043 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:38:21,043 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:38:21,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:21,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:21,045 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:21,045 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:26,744 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes, explore all.",
  "strategy_comment": "Simple linear path to ensure full coverage and maximize diversity by changing node order."
}
```
2025-07-04 21:38:26,744 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:38:26,744 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:38:26,746 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:38:26,746 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:38:26,746 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:26,747 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:26,747 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:26,748 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:32,711 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "all nodes",
  "strategy_comment": "A simple path to maximize coverage and explore all regions.  Basic exploration strategy."
}
```
2025-07-04 21:38:32,711 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:38:32,711 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:38:32,711 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:38:32,711 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:38:32,711 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:32,715 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116922.0 (rank 80.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:32,715 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:32,715 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:38:45,322 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all).",
  "strategy_comment": "A simple linear path to ensure coverage of all nodes and improve diversity by changing the order."
}
```
2025-07-04 21:38:45,323 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:38:45,323 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:38:45,324 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:38:45,324 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:38:45,324 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:38:45,325 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3923076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:38:45,325 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:38:45,325 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:15,360 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=30)
2025-07-04 21:39:17,361 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:39:23,186 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All Nodes",
  "strategy_comment": "Basic path for increased diversity. All nodes visited in sequential order. Complete coverage."
}
```
2025-07-04 21:39:23,187 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:39:23,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:39:23,188 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:39:23,188 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:39:23,189 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:39:23,189 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 119426.0 (rank 90.0%)
- Population diversity: 0.3923076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:39:23,191 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:39:23,191 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:28,288 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all).",
  "strategy_comment": "Basic sequential path covering all nodes. Prioritizes unexplored cells as a starting point."
}
```
2025-07-04 21:39:28,289 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:39:28,290 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:39:28,290 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:39:28,290 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:39:28,290 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:39:28,291 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 70.0%)
- Population diversity: 0.2953846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:39:28,291 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:39:28,292 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:31,261 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Basic path through all nodes for initial exploration, avoiding any specific regions."
}
```
2025-07-04 21:39:31,261 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:39:31,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:39:31,262 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:39:31,262 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:39:31,263 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:39:31,263 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:39:31,263 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102294.0
2025-07-04 21:39:31,768 - ExploitationExpert - INFO - res_population_num: 24
2025-07-04 21:39:31,768 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521]
2025-07-04 21:39:31,769 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:39:31,776 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:39:31,777 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([46, 23, 39, 28, 11, 56, 22, 38,  8, 58, 43,  9, 60, 18, 31, 13, 15,
       34, 27,  7,  0, 30, 59, 35, 20,  1, 50, 24, 55,  5, 54, 37, 17, 40,
       41, 53,  4, 65, 45, 44, 63, 29,  3,  2, 47, 42, 48, 25, 36, 64,  6,
       61, 26, 49, 16, 32, 14, 19, 10, 33, 21, 57, 12, 52, 62, 51]), 'cur_cost': 102294.0}, {'tour': array([54, 47, 59, 12, 64, 56,  0, 31,  5, 14, 18,  3, 53, 52, 24, 62, 46,
       60, 10, 65,  8, 35, 16, 50, 63, 38, 23,  4,  2, 21, 19, 22,  1, 43,
       55, 51, 58, 25, 15, 34, 32, 40, 27, 48, 37, 61,  7, 20, 41, 13, 33,
       39, 26,  6, 11, 30, 44, 49, 45, 42, 29, 17, 36, 57, 28,  9]), 'cur_cost': 106496.0}]
2025-07-04 21:39:31,778 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:39:31,778 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 17, 'skip_rate': 0.11764705882352941, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 15, 'cache_hits': 5, 'similarity_calculations': 111, 'cache_hit_rate': 0.04504504504504504, 'cache_size': 106}}
2025-07-04 21:39:31,778 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:39:31,779 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:39:31,779 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:39:31,788 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:39:31,789 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109050.0
2025-07-04 21:39:32,290 - ExploitationExpert - INFO - res_population_num: 24
2025-07-04 21:39:32,290 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521]
2025-07-04 21:39:32,290 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:39:32,300 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:39:32,301 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([46, 23, 39, 28, 11, 56, 22, 38,  8, 58, 43,  9, 60, 18, 31, 13, 15,
       34, 27,  7,  0, 30, 59, 35, 20,  1, 50, 24, 55,  5, 54, 37, 17, 40,
       41, 53,  4, 65, 45, 44, 63, 29,  3,  2, 47, 42, 48, 25, 36, 64,  6,
       61, 26, 49, 16, 32, 14, 19, 10, 33, 21, 57, 12, 52, 62, 51]), 'cur_cost': 102294.0}, {'tour': array([38, 28, 45, 32, 48, 20, 50, 61, 62, 47, 17, 13, 56, 21, 42, 51, 58,
        5, 52, 26, 54,  8,  4, 57, 34, 16,  1, 43, 59, 29, 30, 63, 19, 39,
       12, 11, 33,  3, 44, 15, 40,  6,  0, 46, 41, 55,  2, 23, 22, 65, 10,
       35, 24,  7, 49,  9, 60, 31, 36, 25, 18, 37, 53, 27, 14, 64]), 'cur_cost': 109050.0}]
2025-07-04 21:39:32,303 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:39:32,303 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 18, 'skip_rate': 0.1111111111111111, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 16, 'cache_hits': 5, 'similarity_calculations': 126, 'cache_hit_rate': 0.03968253968253968, 'cache_size': 121}}
2025-07-04 21:39:32,303 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:39:32,303 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:39:32,304 - __main__ - INFO - 进化阶段完成
2025-07-04 21:39:32,304 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:39:32,318 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109050.0, 'mean': 30317.6, 'std': 37707.47375043839}, 'diversity': 0.35824915824915826, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:39:32,319 - __main__ - INFO - --- Running Assessment Phase (Iteration 5) ---
2025-07-04 21:39:32,319 - EvolutionAssessmentExpert - INFO - --- Iteration 5 Assessment ---
2025-07-04 21:39:32,322 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:39:32,322 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 5/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 24 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.121 → 0.121 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:39:32,322 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:40,379 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "unbalanced",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate", "increase_diversity_weight"]
}
```
2025-07-04 21:39:40,380 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:39:40,380 - __main__ - INFO - --- Finished Evolution Iteration 6 ---
2025-07-04 21:39:40,380 - __main__ - INFO - composite13_66 开始进化第 7 代
2025-07-04 21:39:40,381 - __main__ - INFO - --- Running Analysis Phase (Iteration 6) ---
2025-07-04 21:39:40,381 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:39:40,395 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109050.0, 'mean': 30317.6, 'std': 37707.47375043839}, 'diversity': 0.35824915824915826, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:39:40,395 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:39:40,397 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:39:40,398 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:39:40,398 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:39:40,409 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:39:40,411 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:39:40,411 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:39:40,411 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 6/8)
- population_size: 10
- cost_stats: min 11479.0, mean 30317.6, max 109050.0, std 37707.47375043839
- diversity: 0.35824915824915826
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.3055555555555556}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:39:40,413 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:44,736 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.358,
    "convergence": 0.0,
    "clustering": "no information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Introduce/increase mutation rate.",
      "Increase population size to enhance exploration of the search space.",
      "Consider using a diversity-promoting selection strategy (e.g., tournament selection with a high tournament size or crowding).",
      "Implement a diversification operator to disrupt the potentially stagnant search."
    ]
  }
}
```
2025-07-04 21:39:44,736 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:39:44,737 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.358, 'convergence': 0.0, 'clustering': 'no information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Introduce/increase mutation rate.', 'Increase population size to enhance exploration of the search space.', 'Consider using a diversity-promoting selection strategy (e.g., tournament selection with a high tournament size or crowding).', 'Implement a diversification operator to disrupt the potentially stagnant search.']}}
2025-07-04 21:39:44,737 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:39:44,737 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:39:44,738 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 6
- Population: 10 individuals
  • diversity: 0.358
  • best_cost: 11479.0
  • mean_cost: 30317.6
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:39:44,738 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:39:44,739 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:39:50,453 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Stagnant evolution stage and low diversity necessitate a strong exploration bias. Most individuals explore."
}
```
2025-07-04 21:39:50,454 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:39:50,454 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:39:50,455 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Stagnant evolution stage and low diversity necessitate a strong exploration bias. Most individuals explore."
}
```
2025-07-04 21:39:50,455 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:39:50,456 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:39:50,456 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Stagnant evolution stage and low diversity necessitate a strong exploration bias. Most individuals explore."
}
```
2025-07-04 21:39:50,456 - __main__ - INFO - 开始进化阶段
2025-07-04 21:39:50,456 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:39:50,457 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:39:50,457 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.28615384615384615
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:39:50,457 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:39:50,458 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:07,349 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))
2025-07-04 21:40:09,350 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:40:15,612 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1],
  "targeted_regions": "sparse cells (density grid)",
  "strategy_comment": "Prioritizes unexplored cells through a systematic grid traversal pattern, aiming for path diversity."
}
```
2025-07-04 21:40:15,613 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:40:15,613 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17680.0, 路径: [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1]
2025-07-04 21:40:15,614 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1], 'cur_cost': 17680.0}
2025-07-04 21:40:15,614 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:40:15,614 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:40:15,615 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3153846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:40:15,616 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:40:15,616 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:22,994 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes, ensuring comprehensive coverage.",
  "strategy_comment": "Creates a sequential path to explore all nodes and potentially leverage unexplored areas."
}
```
2025-07-04 21:40:22,995 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:40:22,996 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:40:22,996 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:40:22,996 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:40:22,997 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:40:22,997 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3153846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:40:22,998 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:40:22,998 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:29,600 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all)",
  "strategy_comment": "Create a complete and simple path for early exploration of all nodes, maximizing coverage."
}
```
2025-07-04 21:40:29,601 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:40:29,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:40:29,602 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:40:29,602 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:40:29,603 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:40:29,603 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3153846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:40:29,604 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:40:29,604 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:33,678 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited in sequence.",
  "strategy_comment": "Simple, direct path to maximize coverage and explore the full space initially."
}
```
2025-07-04 21:40:33,679 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:40:33,679 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:40:33,679 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:40:33,681 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:40:33,681 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:40:33,681 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3153846153846154
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:40:33,682 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:40:33,682 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:38,872 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells (all)",
  "strategy_comment": "Creates a diverse path by visiting all nodes linearly, aiming to find low-cost edges later."
}
```
2025-07-04 21:40:38,874 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:40:38,874 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 21:40:38,874 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 21:40:38,875 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:40:38,875 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:40:38,875 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:40:38,875 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 95111.0
2025-07-04 21:40:38,876 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：2,跳过局部搜索
2025-07-04 21:40:38,876 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-04 21:40:38,876 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([24, 36,  6, 53,  8, 32, 22, 28, 21, 46, 38, 64, 52,  3, 47,  5, 61,
       63, 56, 43, 51,  9, 34,  4, 65, 35, 16, 54, 42, 18, 44, 39, 13, 23,
       26, 30, 41,  1, 40, 49, 19,  0, 20, 29,  7, 11, 50, 12, 33, 10, 17,
       59, 62, 58, 55, 31, 57, 48, 15,  2, 14, 37, 45, 27, 60, 25]), 'cur_cost': 95111.0}
2025-07-04 21:40:38,876 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:40:38,876 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:40:38,876 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:40:38,877 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 106340.0
2025-07-04 21:40:38,877 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：2,跳过局部搜索
2025-07-04 21:40:38,877 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-04 21:40:38,877 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([18, 59, 34, 26, 10, 55, 49, 25, 35, 32, 48, 30, 17, 31,  7,  9, 64,
       12, 56, 16, 47, 19, 13, 14, 65, 58,  0, 63, 23, 57, 27, 45, 44, 50,
        6, 41, 46,  8, 52, 15, 61, 38, 11, 20, 62, 51, 29, 40, 28, 24, 54,
       21, 22, 39, 33, 60,  3, 42,  1,  2, 43,  4, 37, 53,  5, 36]), 'cur_cost': 106340.0}
2025-07-04 21:40:38,878 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:40:38,878 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:40:38,878 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:40:38,878 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 110775.0
2025-07-04 21:40:38,880 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：2,跳过局部搜索
2025-07-04 21:40:38,880 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-04 21:40:38,880 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([25, 53, 56, 31, 48, 54,  3, 24, 32, 20, 21,  1, 18, 51, 35, 38,  0,
       33, 28, 44, 49, 10, 55, 58, 12, 36, 43, 11, 42, 29, 65, 17,  8, 52,
       15,  4, 40, 13, 19,  6, 30,  5, 16, 46, 47, 23, 64, 57, 60, 22, 59,
       39, 61, 27, 41, 63, 50,  7, 37, 34,  9, 26, 14, 62, 45,  2]), 'cur_cost': 110775.0}
2025-07-04 21:40:38,880 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:40:38,881 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:40:38,881 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:40:38,881 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 111323.0
2025-07-04 21:40:39,383 - ExploitationExpert - INFO - res_population_num: 26
2025-07-04 21:40:39,383 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521, 9521]
2025-07-04 21:40:39,383 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:40:39,393 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:40:39,393 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1], 'cur_cost': 17680.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([24, 36,  6, 53,  8, 32, 22, 28, 21, 46, 38, 64, 52,  3, 47,  5, 61,
       63, 56, 43, 51,  9, 34,  4, 65, 35, 16, 54, 42, 18, 44, 39, 13, 23,
       26, 30, 41,  1, 40, 49, 19,  0, 20, 29,  7, 11, 50, 12, 33, 10, 17,
       59, 62, 58, 55, 31, 57, 48, 15,  2, 14, 37, 45, 27, 60, 25]), 'cur_cost': 95111.0}, {'tour': array([18, 59, 34, 26, 10, 55, 49, 25, 35, 32, 48, 30, 17, 31,  7,  9, 64,
       12, 56, 16, 47, 19, 13, 14, 65, 58,  0, 63, 23, 57, 27, 45, 44, 50,
        6, 41, 46,  8, 52, 15, 61, 38, 11, 20, 62, 51, 29, 40, 28, 24, 54,
       21, 22, 39, 33, 60,  3, 42,  1,  2, 43,  4, 37, 53,  5, 36]), 'cur_cost': 106340.0}, {'tour': array([25, 53, 56, 31, 48, 54,  3, 24, 32, 20, 21,  1, 18, 51, 35, 38,  0,
       33, 28, 44, 49, 10, 55, 58, 12, 36, 43, 11, 42, 29, 65, 17,  8, 52,
       15,  4, 40, 13, 19,  6, 30,  5, 16, 46, 47, 23, 64, 57, 60, 22, 59,
       39, 61, 27, 41, 63, 50,  7, 37, 34,  9, 26, 14, 62, 45,  2]), 'cur_cost': 110775.0}, {'tour': array([38, 10, 31, 65,  8, 43, 28, 46, 30, 20, 39,  6, 50, 29, 52, 57, 19,
       41, 11,  3, 53, 40, 55, 23, 54, 47, 37, 45, 15,  2,  7, 48, 59, 63,
       61, 58, 34, 12, 16, 51, 24, 32, 33, 42, 62, 21, 27,  4, 35,  9,  5,
       36, 22, 25, 14, 18,  0, 17, 13, 49,  1, 60, 26, 64, 44, 56]), 'cur_cost': 111323.0}, {'tour': array([38, 28, 45, 32, 48, 20, 50, 61, 62, 47, 17, 13, 56, 21, 42, 51, 58,
        5, 52, 26, 54,  8,  4, 57, 34, 16,  1, 43, 59, 29, 30, 63, 19, 39,
       12, 11, 33,  3, 44, 15, 40,  6,  0, 46, 41, 55,  2, 23, 22, 65, 10,
       35, 24,  7, 49,  9, 60, 31, 36, 25, 18, 37, 53, 27, 14, 64]), 'cur_cost': 109050.0}]
2025-07-04 21:40:39,396 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:40:39,396 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 22, 'skip_rate': 0.22727272727272727, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 17, 'cache_hits': 14, 'similarity_calculations': 151, 'cache_hit_rate': 0.09271523178807947, 'cache_size': 137}}
2025-07-04 21:40:39,397 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:40:39,397 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:40:39,397 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:40:39,397 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:40:39,398 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108550.0
2025-07-04 21:40:39,900 - ExploitationExpert - INFO - res_population_num: 29
2025-07-04 21:40:39,900 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:40:39,901 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:40:39,911 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:40:39,912 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1], 'cur_cost': 17680.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': array([24, 36,  6, 53,  8, 32, 22, 28, 21, 46, 38, 64, 52,  3, 47,  5, 61,
       63, 56, 43, 51,  9, 34,  4, 65, 35, 16, 54, 42, 18, 44, 39, 13, 23,
       26, 30, 41,  1, 40, 49, 19,  0, 20, 29,  7, 11, 50, 12, 33, 10, 17,
       59, 62, 58, 55, 31, 57, 48, 15,  2, 14, 37, 45, 27, 60, 25]), 'cur_cost': 95111.0}, {'tour': array([18, 59, 34, 26, 10, 55, 49, 25, 35, 32, 48, 30, 17, 31,  7,  9, 64,
       12, 56, 16, 47, 19, 13, 14, 65, 58,  0, 63, 23, 57, 27, 45, 44, 50,
        6, 41, 46,  8, 52, 15, 61, 38, 11, 20, 62, 51, 29, 40, 28, 24, 54,
       21, 22, 39, 33, 60,  3, 42,  1,  2, 43,  4, 37, 53,  5, 36]), 'cur_cost': 106340.0}, {'tour': array([25, 53, 56, 31, 48, 54,  3, 24, 32, 20, 21,  1, 18, 51, 35, 38,  0,
       33, 28, 44, 49, 10, 55, 58, 12, 36, 43, 11, 42, 29, 65, 17,  8, 52,
       15,  4, 40, 13, 19,  6, 30,  5, 16, 46, 47, 23, 64, 57, 60, 22, 59,
       39, 61, 27, 41, 63, 50,  7, 37, 34,  9, 26, 14, 62, 45,  2]), 'cur_cost': 110775.0}, {'tour': array([38, 10, 31, 65,  8, 43, 28, 46, 30, 20, 39,  6, 50, 29, 52, 57, 19,
       41, 11,  3, 53, 40, 55, 23, 54, 47, 37, 45, 15,  2,  7, 48, 59, 63,
       61, 58, 34, 12, 16, 51, 24, 32, 33, 42, 62, 21, 27,  4, 35,  9,  5,
       36, 22, 25, 14, 18,  0, 17, 13, 49,  1, 60, 26, 64, 44, 56]), 'cur_cost': 111323.0}, {'tour': array([55, 14, 25, 57, 44, 61, 48,  8, 15, 30, 17, 49, 21, 11, 18, 31, 28,
       41, 58,  3, 40, 60, 46, 32, 19, 22,  1,  9, 45, 36, 52, 65, 38, 43,
       63, 64, 62,  7,  0, 53, 20, 23, 54, 26,  2, 37, 12, 16, 33, 24, 13,
       56, 29, 50,  4, 59, 27, 51, 42,  6,  5, 47, 10, 35, 39, 34]), 'cur_cost': 108550.0}]
2025-07-04 21:40:39,914 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:40:39,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 23, 'skip_rate': 0.21739130434782608, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 18, 'cache_hits': 14, 'similarity_calculations': 168, 'cache_hit_rate': 0.08333333333333333, 'cache_size': 154}}
2025-07-04 21:40:39,915 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:40:39,915 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 53, 52, 51, 50, 49, 47, 46, 45, 44, 43, 41, 40, 39, 38, 37, 35, 34, 33, 32, 31, 29, 28, 27, 26, 25, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 5, 4, 3, 2, 1], 'cur_cost': 17680.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([24, 36,  6, 53,  8, 32, 22, 28, 21, 46, 38, 64, 52,  3, 47,  5, 61,
       63, 56, 43, 51,  9, 34,  4, 65, 35, 16, 54, 42, 18, 44, 39, 13, 23,
       26, 30, 41,  1, 40, 49, 19,  0, 20, 29,  7, 11, 50, 12, 33, 10, 17,
       59, 62, 58, 55, 31, 57, 48, 15,  2, 14, 37, 45, 27, 60, 25]), 'cur_cost': 95111.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([18, 59, 34, 26, 10, 55, 49, 25, 35, 32, 48, 30, 17, 31,  7,  9, 64,
       12, 56, 16, 47, 19, 13, 14, 65, 58,  0, 63, 23, 57, 27, 45, 44, 50,
        6, 41, 46,  8, 52, 15, 61, 38, 11, 20, 62, 51, 29, 40, 28, 24, 54,
       21, 22, 39, 33, 60,  3, 42,  1,  2, 43,  4, 37, 53,  5, 36]), 'cur_cost': 106340.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 53, 56, 31, 48, 54,  3, 24, 32, 20, 21,  1, 18, 51, 35, 38,  0,
       33, 28, 44, 49, 10, 55, 58, 12, 36, 43, 11, 42, 29, 65, 17,  8, 52,
       15,  4, 40, 13, 19,  6, 30,  5, 16, 46, 47, 23, 64, 57, 60, 22, 59,
       39, 61, 27, 41, 63, 50,  7, 37, 34,  9, 26, 14, 62, 45,  2]), 'cur_cost': 110775.0}}]
2025-07-04 21:40:39,916 - __main__ - INFO - 进化阶段完成
2025-07-04 21:40:39,916 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:40:39,933 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 111323.0, 'mean': 59777.9, 'std': 46860.62076296045}, 'diversity': 0.7858585858585858, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:40:39,934 - __main__ - INFO - --- Running Assessment Phase (Iteration 6) ---
2025-07-04 21:40:39,934 - EvolutionAssessmentExpert - INFO - --- Iteration 6 Assessment ---
2025-07-04 21:40:39,937 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:40:39,937 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 6/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 29 → 29
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.100 → 0.100 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:40:39,937 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:45,160 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "unbalanced",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate", "increase_diversity_weight"]
}
```
2025-07-04 21:40:45,161 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:40:45,161 - __main__ - INFO - --- Finished Evolution Iteration 7 ---
2025-07-04 21:40:45,161 - __main__ - INFO - composite13_66 开始进化第 8 代
2025-07-04 21:40:45,161 - __main__ - INFO - --- Running Analysis Phase (Iteration 7) ---
2025-07-04 21:40:45,163 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:40:45,180 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 111323.0, 'mean': 59777.9, 'std': 46860.62076296045}, 'diversity': 0.7858585858585858, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:40:45,180 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:40:45,183 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:40:45,184 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:40:45,184 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:40:45,199 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:40:45,202 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:40:45,202 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:40:45,203 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 7/8)
- population_size: 10
- cost_stats: min 11479.0, mean 59777.9, max 111323.0, std 46860.62076296045
- diversity: 0.7858585858585858
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.28884908195253023}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:40:45,203 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:54,699 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "N/A",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.7858585858585858,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "exploration",
    "operators": [
      "Increase mutation rate to promote exploration of new solutions.",
      "Consider using a population initialization strategy that diversifies the initial population.",
      "Since convergence is low and diversity is high, prioritize operators that maintain diversity while potentially improving cost (e.g., crossover operators with a low probability of disrupting good edges)."
    ]
  }
}
```
2025-07-04 21:40:54,699 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:40:54,699 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'N/A', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.7858585858585858, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration', 'operators': ['Increase mutation rate to promote exploration of new solutions.', 'Consider using a population initialization strategy that diversifies the initial population.', 'Since convergence is low and diversity is high, prioritize operators that maintain diversity while potentially improving cost (e.g., crossover operators with a low probability of disrupting good edges).']}}
2025-07-04 21:40:54,699 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:40:54,699 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:40:54,699 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 7
- Population: 10 individuals
  • diversity: 0.786
  • best_cost: 11479.0
  • mean_cost: 59777.9
- Search-space: ruggedness N/A, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: exploration
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:40:54,702 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:40:54,702 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:40:58,189 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution is stagnant and exploration is recommended. High diversity and high explore ratio to seek novel solutions."
}
```
2025-07-04 21:40:58,190 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:40:58,190 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:40:58,191 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution is stagnant and exploration is recommended. High diversity and high explore ratio to seek novel solutions."
}
```
2025-07-04 21:40:58,191 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:40:58,192 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 21:40:58,192 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Evolution is stagnant and exploration is recommended. High diversity and high explore ratio to seek novel solutions."
}
```
2025-07-04 21:40:58,192 - __main__ - INFO - 开始进化阶段
2025-07-04 21:40:58,193 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:40:58,193 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:40:58,193 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17680.0 (rank 40.0%)
- Population diversity: 0.5907692307692308
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:40:58,193 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:40:58,193 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:03,125 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited to ensure complete coverage.",
  "strategy_comment": "Simple linear traversal to ensure all nodes are visited, prioritizing exploration in this phase."
}
```
2025-07-04 21:41:03,126 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:03,127 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:03,127 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:03,128 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:41:03,128 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:03,128 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.56
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:03,129 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:03,129 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:08,724 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells",
  "strategy_comment": "Construct a simple linear path that visits all nodes to explore unexplored areas. Prioritize diversity."
}
```
2025-07-04 21:41:08,725 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:08,725 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:08,725 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:08,725 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:41:08,725 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:08,727 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.56
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:08,727 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:08,727 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:15,395 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all)",
  "strategy_comment": "Visit all nodes sequentially to comprehensively explore the space and find new paths."
}
```
2025-07-04 21:41:15,395 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:15,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:15,396 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:15,396 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:41:15,397 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:15,397 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.56
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:15,398 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:15,398 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:22,431 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes",
  "strategy_comment": "Simple path visiting all nodes in order, ensuring coverage and diversity."
}
```
2025-07-04 21:41:22,432 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:22,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:22,432 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:22,433 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:41:22,433 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:22,433 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 40.0%)
- Population diversity: 0.56
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:22,434 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:22,434 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:27,093 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65],
  "targeted_regions": "Explore all nodes",
  "strategy_comment": "Aims for high diversity through a linear sweep, visiting all nodes without specific region focus."
}
```
2025-07-04 21:41:27,093 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:27,094 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19756.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65]
2025-07-04 21:41:27,094 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}
2025-07-04 21:41:27,094 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:41:27,094 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:27,095 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95111.0 (rank 50.0%)
- Population diversity: 0.6523076923076923
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:27,096 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:27,096 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:31,604 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Simple, complete path for exploration with a focus on breadth."
}
```
2025-07-04 21:41:31,605 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:31,605 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:31,606 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:31,606 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:41:31,606 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:31,607 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106340.0 (rank 60.0%)
- Population diversity: 0.563076923076923
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:31,607 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:31,607 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:35,750 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes.",
  "strategy_comment": "Simple path traversing all nodes sequentially, promoting exploration."
}
```
2025-07-04 21:41:35,751 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:35,751 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:35,752 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:35,753 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 21:41:35,761 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:41:35,762 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 110775.0 (rank 80.0%)
- Population diversity: 0.4815384615384615
- Evolution phase: exploration
- Landscape recommends focus: exploration
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:41:35,762 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:41:35,763 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:40,847 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "None. Base path as exploration start.",
  "strategy_comment": "Basic path for initial exploration, covering all nodes. Increasing diversity through random ordering will follow."
}
```
2025-07-04 21:41:40,848 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:41:40,848 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:41:40,849 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:41:40,849 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:41:40,849 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:41:40,849 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:41:40,850 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108242.0
2025-07-04 21:41:41,355 - ExploitationExpert - INFO - res_population_num: 30
2025-07-04 21:41:41,355 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521]
2025-07-04 21:41:41,355 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:41:41,364 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:41:41,371 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([46, 65, 37, 16, 13, 35, 48, 20,  2, 28, 22, 55,  1, 26, 14, 49, 33,
       41, 51, 52, 64, 36, 56,  0, 27, 38, 45, 31, 32,  4, 25, 43, 17, 47,
       61, 23, 58, 15, 63,  9, 34,  7, 30,  6,  3, 60, 11, 29, 59, 19, 39,
       24, 18, 57, 42, 53,  8, 54, 40, 44, 50, 62,  5, 21, 10, 12]), 'cur_cost': 108242.0}, {'tour': array([55, 14, 25, 57, 44, 61, 48,  8, 15, 30, 17, 49, 21, 11, 18, 31, 28,
       41, 58,  3, 40, 60, 46, 32, 19, 22,  1,  9, 45, 36, 52, 65, 38, 43,
       63, 64, 62,  7,  0, 53, 20, 23, 54, 26,  2, 37, 12, 16, 33, 24, 13,
       56, 29, 50,  4, 59, 27, 51, 42,  6,  5, 47, 10, 35, 39, 34]), 'cur_cost': 108550.0}]
2025-07-04 21:41:41,373 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:41:41,373 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 24, 'skip_rate': 0.20833333333333334, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 19, 'cache_hits': 14, 'similarity_calculations': 186, 'cache_hit_rate': 0.07526881720430108, 'cache_size': 172}}
2025-07-04 21:41:41,374 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:41:41,374 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:41:41,374 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:41:41,374 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:41:41,375 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 111692.0
2025-07-04 21:41:41,878 - ExploitationExpert - INFO - res_population_num: 30
2025-07-04 21:41:41,879 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9527, 9528, 9530, 9537, 9538, 9549, 9560, 9573, 9573, 97491, 9521]
2025-07-04 21:41:41,879 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 18, 16, 23, 22, 15, 14, 20, 21, 13,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40,
       49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50,
       42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       36, 26, 25, 37, 24, 31, 33, 29, 32, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 56, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43,
       40, 34, 35, 30, 28, 32, 29, 33, 31, 24, 37, 25, 26, 36, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 40, 49, 47,
       39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35, 30, 28, 33, 32,
       29, 24, 31, 25, 26, 36, 37, 27,  3,  9,  4,  5, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 17, 12, 22, 23,
       18, 16, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 62, 59, 56, 55, 61, 53, 64, 57, 54, 65, 52, 63, 10],
      dtype=int64), array([ 0,  7, 51, 25, 27, 29, 42, 60, 39, 26,  4, 34, 17, 48,  9, 47,  6,
        8, 45, 22, 64, 31, 24, 63, 59, 38, 65, 46, 18, 40, 50, 30, 36,  1,
       13, 55, 11, 52, 56, 32,  2, 20, 37, 33, 15, 16, 21, 58, 53, 41, 62,
       54, 57,  5, 23, 10, 12,  3, 19, 28, 61, 14, 35, 49, 43, 44],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:41:41,889 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:41:41,890 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([46, 65, 37, 16, 13, 35, 48, 20,  2, 28, 22, 55,  1, 26, 14, 49, 33,
       41, 51, 52, 64, 36, 56,  0, 27, 38, 45, 31, 32,  4, 25, 43, 17, 47,
       61, 23, 58, 15, 63,  9, 34,  7, 30,  6,  3, 60, 11, 29, 59, 19, 39,
       24, 18, 57, 42, 53,  8, 54, 40, 44, 50, 62,  5, 21, 10, 12]), 'cur_cost': 108242.0}, {'tour': array([17, 59, 40, 37, 34,  5, 31, 41,  0, 58,  3, 55, 20, 50, 46, 14, 10,
       23, 56, 65,  9, 52, 35, 33, 49,  4, 22, 57, 15, 61, 27, 36, 62, 38,
       42, 53, 43, 26,  1,  8,  6, 29, 51, 32, 39, 12,  2, 13, 45, 30, 60,
       47, 16, 24, 44, 54, 11, 28,  7, 19, 48, 21, 63, 25, 18, 64]), 'cur_cost': 111692.0}]
2025-07-04 21:41:41,891 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:41:41,892 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 25, 'skip_rate': 0.2, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 20, 'cache_hits': 14, 'similarity_calculations': 205, 'cache_hit_rate': 0.06829268292682927, 'cache_size': 191}}
2025-07-04 21:41:41,892 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:41:41,892 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:41:41,893 - __main__ - INFO - 进化阶段完成
2025-07-04 21:41:41,893 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:41:41,908 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 111692.0, 'mean': 32004.3, 'std': 39065.781530771914}, 'diversity': 0.5242424242424243, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:41:41,909 - __main__ - INFO - --- Running Assessment Phase (Iteration 7) ---
2025-07-04 21:41:41,909 - EvolutionAssessmentExpert - INFO - --- Iteration 7 Assessment ---
2025-07-04 21:41:41,912 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:41:41,913 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 7/8

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 30 → 30
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.096 → 0.096 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:41:41,913 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:41:45,604 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {
    "explore": 0,
    "exploit": 0
  },
  "balance_state": "stagnant",
  "recommendations": [
    "increase_explore_rate_to_0.5",
    "increase_mutation_rate_by_10%",
    "increase_diversity_weight_by_20%"
  ]
}
```
2025-07-04 21:41:45,605 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:41:45,605 - __main__ - INFO - --- Finished Evolution Iteration 8 ---
2025-07-04 21:41:45,611 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-04 21:41:45,611 - __main__ - INFO - 实例 composite13_66 处理完成
