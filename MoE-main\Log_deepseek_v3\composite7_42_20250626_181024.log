2025-06-26 18:10:24,179 - __main__ - INFO - composite7_42 开始进化第 1 代
2025-06-26 18:10:24,180 - __main__ - INFO - 开始分析阶段
2025-06-26 18:10:24,180 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:10:24,188 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14649.0, 'max': 114994.0, 'mean': 76537.9, 'std': 41687.69470587214}, 'diversity': 0.88994708994709, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:10:24,189 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14649.0, 'max': 114994.0, 'mean': 76537.9, 'std': 41687.69470587214}, 'diversity_level': 0.88994708994709, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[738, 3697], [692, 3762], [762, 3766], [739, 3741], [758, 3714], [684, 3694], [929, 540], [868, 510], [891, 498], [933, 491], [852, 532], [897, 526], [929, 580], [915, 553], [1890, 1863], [1929, 1848], [1944, 1878], [1953, 1816], [1917, 1886], [1918, 1872], [1953, 1867], [1896, 1876], [4612, 2136], [4610, 2101], [4642, 2112], [4671, 2119], [4654, 2096], [4668, 2095], [4629, 2076], [4633, 2136], [4690, 2084], [4630, 2063], [5196, 1147], [5225, 1098], [5161, 1092], [5180, 1114], [5237, 1135], [5212, 1134], [5217, 1164], [5214, 1181], [5245, 1156], [5154, 1138]], 'distance_matrix': array([[   0.,   80.,   73., ..., 5135., 5174., 5104.],
       [  80.,    0.,   70., ..., 5207., 5246., 5176.],
       [  73.,   70.,    0., ..., 5148., 5187., 5118.],
       ...,
       [5135., 5207., 5148., ...,    0.,   40.,   74.],
       [5174., 5246., 5187., ...,   40.,    0.,   93.],
       [5104., 5176., 5118., ...,   74.,   93.,    0.]])}
2025-06-26 18:10:24,189 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:10:24,190 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:10:24,191 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:10:24,193 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:10:24,193 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (3, 4), 'frequency': 0.6, 'avg_cost': 33.0}, {'edge': (2, 3), 'frequency': 0.5, 'avg_cost': 34.0}], 'common_subpaths': [{'subpath': (19, 18, 21), 'frequency': 0.3}, {'subpath': (18, 21, 14), 'frequency': 0.3}, {'subpath': (12, 13, 6), 'frequency': 0.3}, {'subpath': (13, 6, 11), 'frequency': 0.3}, {'subpath': (6, 11, 8), 'frequency': 0.3}, {'subpath': (11, 8, 7), 'frequency': 0.3}, {'subpath': (8, 7, 10), 'frequency': 0.3}, {'subpath': (7, 10, 9), 'frequency': 0.3}, {'subpath': (10, 9, 0), 'frequency': 0.3}, {'subpath': (9, 0, 4), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(16, 20)', 'frequency': 0.4}, {'edge': '(7, 8)', 'frequency': 0.4}, {'edge': '(0, 9)', 'frequency': 0.4}, {'edge': '(3, 4)', 'frequency': 0.6}, {'edge': '(2, 3)', 'frequency': 0.5}, {'edge': '(1, 2)', 'frequency': 0.4}, {'edge': '(22, 29)', 'frequency': 0.4}, {'edge': '(24, 26)', 'frequency': 0.4}, {'edge': '(26, 27)', 'frequency': 0.4}, {'edge': '(25, 30)', 'frequency': 0.4}, {'edge': '(32, 37)', 'frequency': 0.4}, {'edge': '(33, 40)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(15, 19)', 'frequency': 0.2}, {'edge': '(18, 19)', 'frequency': 0.3}, {'edge': '(18, 21)', 'frequency': 0.3}, {'edge': '(14, 21)', 'frequency': 0.3}, {'edge': '(14, 16)', 'frequency': 0.3}, {'edge': '(12, 13)', 'frequency': 0.3}, {'edge': '(6, 13)', 'frequency': 0.3}, {'edge': '(6, 11)', 'frequency': 0.3}, {'edge': '(8, 11)', 'frequency': 0.3}, {'edge': '(7, 10)', 'frequency': 0.3}, {'edge': '(9, 10)', 'frequency': 0.3}, {'edge': '(0, 4)', 'frequency': 0.3}, {'edge': '(1, 5)', 'frequency': 0.3}, {'edge': '(5, 22)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(25, 27)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(28, 31)', 'frequency': 0.3}, {'edge': '(23, 31)', 'frequency': 0.3}, {'edge': '(23, 39)', 'frequency': 0.3}, {'edge': '(38, 39)', 'frequency': 0.3}, {'edge': '(32, 38)', 'frequency': 0.3}, {'edge': '(36, 37)', 'frequency': 0.3}, {'edge': '(36, 40)', 'frequency': 0.3}, {'edge': '(33, 35)', 'frequency': 0.3}, {'edge': '(34, 35)', 'frequency': 0.3}, {'edge': '(34, 41)', 'frequency': 0.3}, {'edge': '(17, 41)', 'frequency': 0.2}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(20, 41)', 'frequency': 0.2}, {'edge': '(3, 39)', 'frequency': 0.3}, {'edge': '(16, 39)', 'frequency': 0.2}, {'edge': '(18, 31)', 'frequency': 0.2}, {'edge': '(8, 21)', 'frequency': 0.2}, {'edge': '(1, 40)', 'frequency': 0.2}, {'edge': '(10, 36)', 'frequency': 0.2}, {'edge': '(0, 11)', 'frequency': 0.2}, {'edge': '(7, 32)', 'frequency': 0.2}, {'edge': '(4, 12)', 'frequency': 0.2}, {'edge': '(1, 6)', 'frequency': 0.2}, {'edge': '(1, 4)', 'frequency': 0.2}, {'edge': '(15, 28)', 'frequency': 0.2}, {'edge': '(11, 18)', 'frequency': 0.2}, {'edge': '(21, 39)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(31, 35)', 'frequency': 0.2}, {'edge': '(5, 13)', 'frequency': 0.2}, {'edge': '(19, 32)', 'frequency': 0.2}, {'edge': '(2, 17)', 'frequency': 0.2}, {'edge': '(17, 30)', 'frequency': 0.2}, {'edge': '(18, 22)', 'frequency': 0.3}, {'edge': '(20, 22)', 'frequency': 0.2}, {'edge': '(26, 41)', 'frequency': 0.2}, {'edge': '(13, 33)', 'frequency': 0.2}, {'edge': '(0, 32)', 'frequency': 0.2}, {'edge': '(1, 28)', 'frequency': 0.2}, {'edge': '(21, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.2}, {'edge': '(5, 30)', 'frequency': 0.2}, {'edge': '(39, 40)', 'frequency': 0.2}, {'edge': '(22, 36)', 'frequency': 0.2}, {'edge': '(0, 16)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [37, 7, 32, 9, 28, 12], 'cost': 21090.0, 'size': 6}, {'region': [31, 6, 36, 10, 30], 'cost': 16917.0, 'size': 5}, {'region': [13, 33, 8, 32, 19], 'cost': 16430.0, 'size': 5}, {'region': [12, 41, 21, 39, 2], 'cost': 16141.0, 'size': 5}, {'region': [3, 24, 12, 35, 21], 'cost': 15901.0, 'size': 5}]}
2025-06-26 18:10:24,193 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:10:24,194 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:10:24,194 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:10:24,194 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 18:10:24,195 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 18:10:24,195 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:10:24,195 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:10:24,197 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:10:24,197 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 14649.0, mean 76537.9, max 114994.0, std 41687.69470587214
- diversity: 0.88994708994709
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [125620.0, 64696.0, 145982.0] (Δ 30462.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (3, 4), 'frequency': 0.6, 'avg_cost': 33.0}, {'edge': (2, 3), 'frequency': 0.5, 'avg_cost': 34.0}]
- common_subpaths_sample: [{'subpath': (19, 18, 21), 'frequency': 0.3}, {'subpath': (18, 21, 14), 'frequency': 0.3}, {'subpath': (12, 13, 6), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [37, 7, 32, 9, 28, 12], 'cost': 21090.0, 'size': 6}, {'region': [31, 6, 36, 10, 30], 'cost': 16917.0, 'size': 5}, {'region': [13, 33, 8, 32, 19], 'cost': 16430.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [684.0, 491.0, 5245.0, 3766.0]
- centroid/spread: [2987.31, 1759.214] | rms 2135.914
- density_grid(3x3): [[8, 0, 10], [8, 0, 10], [6, 0, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1120.0, 2749.0, 4100.0, 5258.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [7, 4, 6, 8, 5, 6, 3, 2]
- nn_median_dist: 23.329

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:10:24,198 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:10:54,603 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation)",
    "modality": "Potentially multimodal (presence of both short and long edges, and diverse direction histogram)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead optimization)"
  },
  "population_state": {
    "coverage": "Moderate (diversity score of 0.89, but density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (convergence score of 0.0, recent best costs show significant fluctuations)"
  },
  "difficult_regions": [
    {"region": [37, 7, 32, 9, 28, 12], "cost": 21090.0, "size": 6, "reason": "High cost and located in low-density grid cells"},
    {"region": [31, 6, 36, 10, 30], "cost": 16917.0, "size": 5, "reason": "High cost and potential long-edge corridors"},
    {"region": [13, 33, 8, 32, 19], "cost": 16430.0, "size": 5, "reason": "High cost and common subpaths indicating complexity"}
  ],
  "opportunity_regions": [
    {"region": [3, 4], "reason": "High-quality edge with frequency 0.6 and low avg_cost 33.0"},
    {"region": [2, 3], "reason": "High-quality edge with frequency 0.5 and low avg_cost 34.0"},
    {"region": [19, 18, 21], "reason": "Common subpath with frequency 0.3, potential for exploitation"}
  ],
  "evolution_phase": "Exploration (high diversity, no convergence, early iteration)",
  "evolution_direction": [
    "Focus on exploiting high-quality edges and common subpaths",
    "Increase exploration in low-density grid cells to improve coverage",
    "Consider edge recombination operators to combine high-quality edges from different solutions",
    "Use local search operators to optimize difficult regions identified"
  ]
}
```
2025-06-26 18:10:54,603 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:10:54,603 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of both short and long edges, and diverse direction histogram)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead optimization)'}, 'population_state': {'coverage': 'Moderate (diversity score of 0.89, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence score of 0.0, recent best costs show significant fluctuations)'}, 'difficult_regions': [{'region': [37, 7, 32, 9, 28, 12], 'cost': 21090.0, 'size': 6, 'reason': 'High cost and located in low-density grid cells'}, {'region': [31, 6, 36, 10, 30], 'cost': 16917.0, 'size': 5, 'reason': 'High cost and potential long-edge corridors'}, {'region': [13, 33, 8, 32, 19], 'cost': 16430.0, 'size': 5, 'reason': 'High cost and common subpaths indicating complexity'}], 'opportunity_regions': [{'region': [3, 4], 'reason': 'High-quality edge with frequency 0.6 and low avg_cost 33.0'}, {'region': [2, 3], 'reason': 'High-quality edge with frequency 0.5 and low avg_cost 34.0'}, {'region': [19, 18, 21], 'reason': 'Common subpath with frequency 0.3, potential for exploitation'}], 'evolution_phase': 'Exploration (high diversity, no convergence, early iteration)', 'evolution_direction': ['Focus on exploiting high-quality edges and common subpaths', 'Increase exploration in low-density grid cells to improve coverage', 'Consider edge recombination operators to combine high-quality edges from different solutions', 'Use local search operators to optimize difficult regions identified']}
2025-06-26 18:10:54,603 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:10:54,603 - __main__ - INFO - 分析阶段完成
2025-06-26 18:10:54,603 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of both short and long edges, and diverse direction histogram)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead optimization)'}, 'population_state': {'coverage': 'Moderate (diversity score of 0.89, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence score of 0.0, recent best costs show significant fluctuations)'}, 'difficult_regions': [{'region': [37, 7, 32, 9, 28, 12], 'cost': 21090.0, 'size': 6, 'reason': 'High cost and located in low-density grid cells'}, {'region': [31, 6, 36, 10, 30], 'cost': 16917.0, 'size': 5, 'reason': 'High cost and potential long-edge corridors'}, {'region': [13, 33, 8, 32, 19], 'cost': 16430.0, 'size': 5, 'reason': 'High cost and common subpaths indicating complexity'}], 'opportunity_regions': [{'region': [3, 4], 'reason': 'High-quality edge with frequency 0.6 and low avg_cost 33.0'}, {'region': [2, 3], 'reason': 'High-quality edge with frequency 0.5 and low avg_cost 34.0'}, {'region': [19, 18, 21], 'reason': 'Common subpath with frequency 0.3, potential for exploitation'}], 'evolution_phase': 'Exploration (high diversity, no convergence, early iteration)', 'evolution_direction': ['Focus on exploiting high-quality edges and common subpaths', 'Increase exploration in low-density grid cells to improve coverage', 'Consider edge recombination operators to combine high-quality edges from different solutions', 'Use local search operators to optimize difficult regions identified']}
2025-06-26 18:10:54,603 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:10:54,603 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:10:54,603 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 14649.0
  • mean_cost: 76537.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation), modality Potentially multimodal (presence of both short and long edges, and diverse direction histogram), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead optimization)
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [37, 7, 3, {'region': [31, 6, 3...
- Opportunity regions (# 3): {'region': [3, 4], ', {'region': [2, 3], '...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:10:54,603 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:10:54,603 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:11:04,141 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 70% explore to diversify; bottom 30% exploit to refine promising solutions."
}
```
2025-06-26 18:11:04,150 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:11:04,150 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:11:04,150 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 70% explore to diversify; bottom 30% exploit to refine promising solutions."
}
```
2025-06-26 18:11:04,150 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:11:04,150 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:11:04,150 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 70% explore to diversify; bottom 30% exploit to refine promising solutions."
}
```
2025-06-26 18:11:04,150 - __main__ - INFO - 开始进化阶段
2025-06-26 18:11:04,150 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:11:04,150 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:11:04,150 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14691.0 (rank 10.0%)
- Population diversity: 0.6829268292682927
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:11:04,150 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:11:04,154 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:11:15,316 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1],
  "targeted_regions": "Regions 3, 2, and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while alternating directions"
}
```
2025-06-26 18:11:15,324 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:11:15,325 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19043.0, 路径: [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1]
2025-06-26 18:11:15,325 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}
2025-06-26 18:11:15,325 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:11:15,325 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:11:15,325 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14692.0 (rank 10.0%)
- Population diversity: 0.7439024390243902
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:11:15,325 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:11:15,325 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:11:27,262 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1],
  "targeted_regions": "Regions 3, 2, 5 and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, alternate directions to boost diversity"
}
```
2025-06-26 18:11:27,262 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:11:27,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15629.0, 路径: [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1]
2025-06-26 18:11:27,262 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1], 'cur_cost': 15629.0}
2025-06-26 18:11:27,262 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:11:27,262 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:11:27,262 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14649.0 (rank 0.0%)
- Population diversity: 0.7731707317073171
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:11:27,262 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:11:27,262 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:11:38,142 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41],
  "targeted_regions": "Regions 3, 2, adjacent sparse cells",
  "strategy_comment": "Prioritize early opportunity regions, then spiral outward"
}
```
2025-06-26 18:11:38,142 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:11:38,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15506.0, 路径: [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]
2025-06-26 18:11:38,142 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 15506.0}
2025-06-26 18:11:38,142 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:11:38,142 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:11:38,142 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113206.0 (rank 80.0%)
- Population diversity: 0.7609756097560976
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:11:38,142 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:11:38,142 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:11:49,367 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1],
  "targeted_regions": "Regions 3, 2, and adjacent sparse cells",
  "strategy_comment": "Alternate high-low node traversal to explore sparse areas"
}
```
2025-06-26 18:11:49,367 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:11:49,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19043.0, 路径: [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1]
2025-06-26 18:11:49,367 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}
2025-06-26 18:11:49,367 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:11:49,367 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:11:49,367 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 92088.0 (rank 50.0%)
- Population diversity: 0.6878048780487804
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:11:49,367 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:11:49,367 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:12:00,539 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1],
  "targeted_regions": "Regions 3, 2, and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions with spiral exploration"
}
```
2025-06-26 18:12:00,539 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:12:00,539 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19043.0, 路径: [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1]
2025-06-26 18:12:00,539 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}
2025-06-26 18:12:00,541 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:12:00,541 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:12:00,541 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114994.0 (rank 90.0%)
- Population diversity: 0.6268292682926829
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:12:00,541 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:12:00,541 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:12:14,243 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1],
  "targeted_regions": "Regions 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41",
  "strategy_comment": "Leverage opportunity regions while avoiding difficult areas"
}
```
2025-06-26 18:12:14,243 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:12:14,247 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19043.0, 路径: [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1]
2025-06-26 18:12:14,247 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}
2025-06-26 18:12:14,247 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:12:14,248 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:12:14,248 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108652.0 (rank 80.0%)
- Population diversity: 0.5536585365853659
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [3, , {'region': [2, ...
- Difficult regions to avoid (sample): {'region': [37,, {'region': [31,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:12:14,248 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:12:14,248 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:12:25,776 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1],
  "targeted_regions": "Regions 3, 2, and sparse adjacent cells",
  "strategy_comment": "Leverage opportunity regions while avoiding high-cost areas"
}
```
2025-06-26 18:12:25,776 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:12:25,776 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19043.0, 路径: [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1]
2025-06-26 18:12:25,776 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}
2025-06-26 18:12:25,776 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:12:25,781 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:12:25,781 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:12:25,781 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 96214.0
2025-06-26 18:12:26,284 - ExploitationExpert - INFO - res_population_num: 16
2025-06-26 18:12:26,285 - ExploitationExpert - INFO - res_population_costs: [14473, 14423, 14423, 14414, 14409, 14409, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408]
2025-06-26 18:12:26,285 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64)]
2025-06-26 18:12:26,285 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:12:26,285 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1], 'cur_cost': 15629.0}, {'tour': [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 15506.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': array([38, 35, 21, 12, 13,  2,  7, 36, 31, 17, 33, 37,  0, 19, 10, 24, 20,
       26, 41, 39, 28,  4,  3,  6,  1, 22, 15, 40, 34, 18, 14, 11, 32,  9,
       29, 25, 27, 30, 16,  5,  8, 23]), 'cur_cost': 96214.0}, {'tour': [14, 7, 38, 8, 12, 25, 15, 34, 37, 28, 1, 40, 39, 21, 27, 23, 3, 4, 9, 41, 35, 24, 31, 6, 36, 10, 30, 17, 2, 22, 20, 16, 0, 32, 33, 29, 26, 19, 13, 5, 18, 11], 'cur_cost': 101146.0}, {'tour': [5, 30, 29, 28, 15, 1, 4, 12, 34, 14, 16, 21, 9, 24, 36, 22, 40, 10, 39, 3, 2, 35, 31, 18, 25, 17, 13, 33, 8, 32, 19, 6, 26, 41, 20, 38, 11, 0, 7, 27, 37, 23], 'cur_cost': 110916.0}]
2025-06-26 18:12:26,290 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:12:26,290 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 218, 'skip_rate': 0.04128440366972477, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 209, 'cache_hits': 172, 'similarity_calculations': 3632, 'cache_hit_rate': 0.0473568281938326, 'cache_size': 3460}}
2025-06-26 18:12:26,290 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:12:26,290 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:12:26,291 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:12:26,291 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:12:26,291 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 94347.0
2025-06-26 18:12:26,794 - ExploitationExpert - INFO - res_population_num: 19
2025-06-26 18:12:26,794 - ExploitationExpert - INFO - res_population_costs: [14473, 14423, 14423, 14414, 14409, 14409, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408]
2025-06-26 18:12:26,794 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64)]
2025-06-26 18:12:26,799 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:12:26,799 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1], 'cur_cost': 15629.0}, {'tour': [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 15506.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': array([38, 35, 21, 12, 13,  2,  7, 36, 31, 17, 33, 37,  0, 19, 10, 24, 20,
       26, 41, 39, 28,  4,  3,  6,  1, 22, 15, 40, 34, 18, 14, 11, 32,  9,
       29, 25, 27, 30, 16,  5,  8, 23]), 'cur_cost': 96214.0}, {'tour': array([ 2,  3, 11,  6, 41, 40, 13, 29, 26,  4, 17,  9, 30, 35, 39, 10, 37,
       33, 36, 34, 12, 23,  0, 22, 25, 31, 19, 24, 38, 15,  7,  5, 21,  8,
        1, 16, 28, 32, 18, 20, 27, 14]), 'cur_cost': 94347.0}, {'tour': [5, 30, 29, 28, 15, 1, 4, 12, 34, 14, 16, 21, 9, 24, 36, 22, 40, 10, 39, 3, 2, 35, 31, 18, 25, 17, 13, 33, 8, 32, 19, 6, 26, 41, 20, 38, 11, 0, 7, 27, 37, 23], 'cur_cost': 110916.0}]
2025-06-26 18:12:26,800 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:12:26,801 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 219, 'skip_rate': 0.0410958904109589, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 210, 'cache_hits': 172, 'similarity_calculations': 3633, 'cache_hit_rate': 0.04734379300853289, 'cache_size': 3461}}
2025-06-26 18:12:26,801 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:12:26,801 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:12:26,801 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:12:26,801 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:12:26,802 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101525.0
2025-06-26 18:12:27,304 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:12:27,304 - ExploitationExpert - INFO - res_population_costs: [14473, 14423, 14423, 14414, 14409, 14409, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408]
2025-06-26 18:12:27,305 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64)]
2025-06-26 18:12:27,317 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:12:27,317 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1], 'cur_cost': 15629.0}, {'tour': [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 15506.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}, {'tour': array([38, 35, 21, 12, 13,  2,  7, 36, 31, 17, 33, 37,  0, 19, 10, 24, 20,
       26, 41, 39, 28,  4,  3,  6,  1, 22, 15, 40, 34, 18, 14, 11, 32,  9,
       29, 25, 27, 30, 16,  5,  8, 23]), 'cur_cost': 96214.0}, {'tour': array([ 2,  3, 11,  6, 41, 40, 13, 29, 26,  4, 17,  9, 30, 35, 39, 10, 37,
       33, 36, 34, 12, 23,  0, 22, 25, 31, 19, 24, 38, 15,  7,  5, 21,  8,
        1, 16, 28, 32, 18, 20, 27, 14]), 'cur_cost': 94347.0}, {'tour': array([21, 38, 40, 34, 39, 31, 20, 36,  7,  4, 29, 23, 14, 32, 26, 10,  9,
       24, 25, 11,  5, 19,  8,  2,  6, 15,  1, 30, 12, 16, 22, 37, 28, 27,
       18,  3, 33, 41,  0, 13, 17, 35]), 'cur_cost': 101525.0}]
2025-06-26 18:12:27,319 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:12:27,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 220, 'skip_rate': 0.04090909090909091, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 211, 'cache_hits': 172, 'similarity_calculations': 3635, 'cache_hit_rate': 0.04731774415405777, 'cache_size': 3463}}
2025-06-26 18:12:27,319 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:12:27,320 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 4, 7, 6, 9, 8, 11, 10, 13, 12, 15, 14, 17, 16, 19, 18, 21, 20, 23, 22, 25, 24, 27, 26, 29, 28, 31, 30, 33, 32, 35, 34, 37, 36, 39, 38, 41, 40, 1], 'cur_cost': 15629.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 15506.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1], 'cur_cost': 19043.0}}]
2025-06-26 18:12:27,320 - __main__ - INFO - 进化阶段完成
2025-06-26 18:12:27,321 - __main__ - INFO - 开始评估阶段
2025-06-26 18:12:27,321 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:12:27,322 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:12:27,322 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 14649.0 → 15506.0 (Δ -857.0)
- Mean cost: 76537.9 → 41843.6
- Diversity: 0.683 → 0.493 (Δ -0.1902)

#### Elite metrics
- Elite count: 0 → 22
- Best elite cost: 14649.0 → 14408 (Δ 241.0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ 49517.43
- Exploit: 3 inds, success 66.7% , avg Δ 107.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [125620.0, 64696.0, 145982.0], diversity [0.8438746438746438, 0.9310541310541309, 0.8997150997150996]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:12:27,322 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:12:40,738 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": 49517.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 107.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but diversity is dropping; exploit is stable but low-impact",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators to boost diversity while maintaining high-impact improvements."
  }
}
```
2025-06-26 18:12:40,750 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:12:40,750 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": 49517.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 107.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but diversity is dropping; exploit is stable but low-impact",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators to boost diversity while maintaining high-impact improvements."
  }
}
```
2025-06-26 18:12:40,751 - __main__ - INFO - 评估阶段完成
2025-06-26 18:12:40,751 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": 49517.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 107.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but diversity is dropping; exploit is stable but low-impact",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators to boost diversity while maintaining high-impact improvements."
  }
}
```
2025-06-26 18:12:40,751 - __main__ - INFO - 当前最佳适应度: 15506.0
2025-06-26 18:12:40,753 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_route_0.pkl
2025-06-26 18:12:40,754 - __main__ - INFO - composite7_42 开始进化第 2 代
2025-06-26 18:12:40,754 - __main__ - INFO - 开始分析阶段
2025-06-26 18:12:40,754 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:12:40,762 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15506.0, 'max': 101525.0, 'mean': 41843.6, 'std': 36407.18080598936}, 'diversity': 0.7121693121693122, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:12:40,763 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 15506.0, 'max': 101525.0, 'mean': 41843.6, 'std': 36407.18080598936}, 'diversity_level': 0.7121693121693122, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[738, 3697], [692, 3762], [762, 3766], [739, 3741], [758, 3714], [684, 3694], [929, 540], [868, 510], [891, 498], [933, 491], [852, 532], [897, 526], [929, 580], [915, 553], [1890, 1863], [1929, 1848], [1944, 1878], [1953, 1816], [1917, 1886], [1918, 1872], [1953, 1867], [1896, 1876], [4612, 2136], [4610, 2101], [4642, 2112], [4671, 2119], [4654, 2096], [4668, 2095], [4629, 2076], [4633, 2136], [4690, 2084], [4630, 2063], [5196, 1147], [5225, 1098], [5161, 1092], [5180, 1114], [5237, 1135], [5212, 1134], [5217, 1164], [5214, 1181], [5245, 1156], [5154, 1138]], 'distance_matrix': array([[   0.,   80.,   73., ..., 5135., 5174., 5104.],
       [  80.,    0.,   70., ..., 5207., 5246., 5176.],
       [  73.,   70.,    0., ..., 5148., 5187., 5118.],
       ...,
       [5135., 5207., 5148., ...,    0.,   40.,   74.],
       [5174., 5246., 5187., ...,   40.,    0.,   93.],
       [5104., 5176., 5118., ...,   74.,   93.,    0.]])}
2025-06-26 18:12:40,763 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:12:40,763 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:12:40,764 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:12:40,765 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:12:40,765 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:12:40,765 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:12:40,765 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:12:40,765 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:12:40,765 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:12:40,771 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:12:40,772 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1098.0, 'avg_gap': 27430.918181818182}, 'structure_gap': {'unique_elite_edges': 99, 'unique_pop_edges': 186, 'common_edges': 50}}, 'elite_diversity': {'diversity_score': 0.2403628117913832}}
2025-06-26 18:12:40,772 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:12:40,772 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:12:40,774 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:12:40,775 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 15506.0, mean 41843.6, max 101525.0, std 36407.18080598936
- diversity: 0.7121693121693122
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [64696.0, 145982.0, 15506.0] (Δ 65238.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [684.0, 491.0, 5245.0, 3766.0]
- centroid/spread: [2987.31, 1759.214] | rms 2135.914
- density_grid(3x3): [[8, 0, 10], [8, 0, 10], [6, 0, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1120.0, 2749.0, 4100.0, 5258.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [7, 4, 6, 8, 5, 6, 3, 2]
- nn_median_dist: 23.329

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:12:40,775 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:12:58,472 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "moderate",
    "edge_length_variability": "high",
    "long_edge_presence": "significant (25%)"
  },
  "population_state": {
    "coverage": "partial (uneven density_grid distribution)",
    "diversity": "high (0.71)",
    "convergence": "none (0.0)",
    "elite_influence": "minimal (no elites yet)"
  },
  "difficult_regions": [
    "long-edge corridors (edges >4100 units)",
    "low-density grid cells (empty cells in density_grid)",
    "northwest quadrant (sparse node distribution)"
  ],
  "opportunity_regions": [
    "high-density grid cells (8-10 nodes)",
    "southeast quadrant (dense node distribution)",
    "short-edge clusters (edges <1120 units)"
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": [
    "prioritize exploitation in high-density cells with local search operators",
    "maintain diversity with edge recombination in sparse regions",
    "introduce long-edge mutation to connect distant clusters",
    "implement node-swap mutations in opportunity regions"
  ]
}
```
2025-06-26 18:12:58,473 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:12:58,473 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'moderate', 'edge_length_variability': 'high', 'long_edge_presence': 'significant (25%)'}, 'population_state': {'coverage': 'partial (uneven density_grid distribution)', 'diversity': 'high (0.71)', 'convergence': 'none (0.0)', 'elite_influence': 'minimal (no elites yet)'}, 'difficult_regions': ['long-edge corridors (edges >4100 units)', 'low-density grid cells (empty cells in density_grid)', 'northwest quadrant (sparse node distribution)'], 'opportunity_regions': ['high-density grid cells (8-10 nodes)', 'southeast quadrant (dense node distribution)', 'short-edge clusters (edges <1120 units)'], 'evolution_phase': 'early exploration', 'evolution_direction': ['prioritize exploitation in high-density cells with local search operators', 'maintain diversity with edge recombination in sparse regions', 'introduce long-edge mutation to connect distant clusters', 'implement node-swap mutations in opportunity regions']}
2025-06-26 18:12:58,474 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:12:58,474 - __main__ - INFO - 分析阶段完成
2025-06-26 18:12:58,474 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'moderate', 'edge_length_variability': 'high', 'long_edge_presence': 'significant (25%)'}, 'population_state': {'coverage': 'partial (uneven density_grid distribution)', 'diversity': 'high (0.71)', 'convergence': 'none (0.0)', 'elite_influence': 'minimal (no elites yet)'}, 'difficult_regions': ['long-edge corridors (edges >4100 units)', 'low-density grid cells (empty cells in density_grid)', 'northwest quadrant (sparse node distribution)'], 'opportunity_regions': ['high-density grid cells (8-10 nodes)', 'southeast quadrant (dense node distribution)', 'short-edge clusters (edges <1120 units)'], 'evolution_phase': 'early exploration', 'evolution_direction': ['prioritize exploitation in high-density cells with local search operators', 'maintain diversity with edge recombination in sparse regions', 'introduce long-edge mutation to connect distant clusters', 'implement node-swap mutations in opportunity regions']}
2025-06-26 18:12:58,474 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:12:58,474 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:12:58,474 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 15506.0
  • mean_cost: 41843.6
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness moderate
- Evolution phase: early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): long-edge corridors , low-density grid cel...
- Opportunity regions (# 3): high-density grid ce, southeast quadrant (...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:12:58,474 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:12:58,474 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:13:14,821 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 30% individuals exploit high-density regions."
}
```
2025-06-26 18:13:14,822 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:13:14,822 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:13:14,822 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 30% individuals exploit high-density regions."
}
```
2025-06-26 18:13:14,822 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:13:14,822 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:13:14,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 30% individuals exploit high-density regions."
}
```
2025-06-26 18:13:14,824 - __main__ - INFO - 开始进化阶段
2025-06-26 18:13:14,824 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:13:14,824 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:13:14,824 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19043.0 (rank 20.0%)
- Population diversity: 0.4926829268292683
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:13:14,824 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:13:14,824 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:13:26,222 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38],
  "targeted_regions": "high-density gr, southeast quadrant",
  "strategy_comment": "Prioritize dense clusters while probing adjacent sparse cells"
}
```
2025-06-26 18:13:26,223 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:13:26,223 - ExplorationExpert - INFO - 探索路径生成完成，成本: 73259.0, 路径: [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38]
2025-06-26 18:13:26,224 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38], 'cur_cost': 73259.0}
2025-06-26 18:13:26,224 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:13:26,224 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:13:26,224 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15629.0 (rank 10.0%)
- Population diversity: 0.573170731707317
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:13:26,225 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:13:26,225 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:13:37,232 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 42, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4],
  "targeted_regions": "high-density gr, southeast quadrant",
  "strategy_comment": "Leverage grid patterns, alternate directions for diversity"
}
```
2025-06-26 18:13:37,232 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:13:37,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67003.0, 路径: [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4]
2025-06-26 18:13:37,232 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67003.0}
2025-06-26 18:13:37,232 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:13:37,232 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:13:37,232 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15506.0 (rank 0.0%)
- Population diversity: 0.5951219512195122
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:13:37,232 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:13:37,232 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:13:48,216 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 42, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31],
  "targeted_regions": "high-density gr, southeast quadrant",
  "strategy_comment": "Leverage high-density clusters while exploring adjacent sparse cells"
}
```
2025-06-26 18:13:48,216 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:13:48,216 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126432.0, 路径: [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31]
2025-06-26 18:13:48,216 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}
2025-06-26 18:13:48,216 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:13:48,216 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:13:48,220 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19043.0 (rank 0.0%)
- Population diversity: 0.6170731707317073
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:13:48,220 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:13:48,220 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:13:59,674 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "high-density grid, southeast quadrant",
  "strategy_comment": "Leverage grid pattern in opportunity regions"
}
```
2025-06-26 18:13:59,674 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:13:59,674 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126935.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:13:59,674 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}
2025-06-26 18:13:59,674 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:13:59,674 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:13:59,674 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19043.0 (rank 0.0%)
- Population diversity: 0.6365853658536585
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:13:59,674 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:13:59,674 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:14:10,840 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40],
  "targeted_regions": "high-density grid, southeast quadrant",
  "strategy_comment": "Leverage grid clusters while skipping long-edge corridors"
}
```
2025-06-26 18:14:10,840 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:14:10,840 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89781.0, 路径: [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40]
2025-06-26 18:14:10,840 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89781.0}
2025-06-26 18:14:10,840 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:14:10,840 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:14:10,840 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19043.0 (rank 0.0%)
- Population diversity: 0.651219512195122
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:14:10,842 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:14:10,842 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:14:22,265 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31],
  "targeted_regions": "high-density grid, southeast quadrant",
  "strategy_comment": "Leverage grid symmetry to explore sparse cells near opportunities"
}
```
2025-06-26 18:14:22,265 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:14:22,265 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125733.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31]
2025-06-26 18:14:22,265 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31], 'cur_cost': 125733.0}
2025-06-26 18:14:22,265 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:14:22,265 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:14:22,265 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19043.0 (rank 0.0%)
- Population diversity: 0.6780487804878049
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high-density gr, southeast quadr...
- Difficult regions to avoid (sample): long-edge corri, low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:14:22,265 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:14:22,265 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:14:34,616 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 42, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31],
  "targeted_regions": "high-density gr, southeast quadrant",
  "strategy_comment": "Leverage grid patterns with diagonal jumps to explore sparse cells"
}
```
2025-06-26 18:14:34,617 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:14:34,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126432.0, 路径: [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31]
2025-06-26 18:14:34,617 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}
2025-06-26 18:14:34,617 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:14:34,617 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:14:34,617 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:14:34,617 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107323.0
2025-06-26 18:14:35,119 - ExploitationExpert - INFO - res_population_num: 25
2025-06-26 18:14:35,119 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473, 14408, 14408, 14408]
2025-06-26 18:14:35,119 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64)]
2025-06-26 18:14:35,128 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:14:35,128 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38], 'cur_cost': 73259.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67003.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89781.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31], 'cur_cost': 125733.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': array([24, 22,  4, 30, 11,  0, 25, 10, 29, 36, 23, 26, 39,  2, 27, 19, 18,
       17,  7, 32, 13, 34,  8, 41, 20, 16, 31, 28, 12, 33, 14,  1, 40, 37,
       21, 15,  6,  9,  3,  5, 38, 35]), 'cur_cost': 107323.0}, {'tour': array([ 2,  3, 11,  6, 41, 40, 13, 29, 26,  4, 17,  9, 30, 35, 39, 10, 37,
       33, 36, 34, 12, 23,  0, 22, 25, 31, 19, 24, 38, 15,  7,  5, 21,  8,
        1, 16, 28, 32, 18, 20, 27, 14]), 'cur_cost': 94347.0}, {'tour': array([21, 38, 40, 34, 39, 31, 20, 36,  7,  4, 29, 23, 14, 32, 26, 10,  9,
       24, 25, 11,  5, 19,  8,  2,  6, 15,  1, 30, 12, 16, 22, 37, 28, 27,
       18,  3, 33, 41,  0, 13, 17, 35]), 'cur_cost': 101525.0}]
2025-06-26 18:14:35,129 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:14:35,129 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 221, 'skip_rate': 0.04072398190045249, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 212, 'cache_hits': 172, 'similarity_calculations': 3638, 'cache_hit_rate': 0.04727872457394173, 'cache_size': 3466}}
2025-06-26 18:14:35,129 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:14:35,131 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:14:35,131 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:14:35,131 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:14:35,131 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 116144.0
2025-06-26 18:14:35,637 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:14:35,638 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473, 14408, 14408, 14408, 14408]
2025-06-26 18:14:35,638 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:14:35,653 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:14:35,655 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38], 'cur_cost': 73259.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67003.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89781.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31], 'cur_cost': 125733.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': array([24, 22,  4, 30, 11,  0, 25, 10, 29, 36, 23, 26, 39,  2, 27, 19, 18,
       17,  7, 32, 13, 34,  8, 41, 20, 16, 31, 28, 12, 33, 14,  1, 40, 37,
       21, 15,  6,  9,  3,  5, 38, 35]), 'cur_cost': 107323.0}, {'tour': array([29, 20, 26,  4, 15, 40,  3, 34, 12,  6, 13, 36, 11, 31, 14, 19, 24,
       35, 16, 18, 23, 32,  9,  1, 17,  7, 30, 38, 22, 27,  2, 25, 28, 21,
        8, 39,  0, 37, 41,  5, 10, 33]), 'cur_cost': 116144.0}, {'tour': array([21, 38, 40, 34, 39, 31, 20, 36,  7,  4, 29, 23, 14, 32, 26, 10,  9,
       24, 25, 11,  5, 19,  8,  2,  6, 15,  1, 30, 12, 16, 22, 37, 28, 27,
       18,  3, 33, 41,  0, 13, 17, 35]), 'cur_cost': 101525.0}]
2025-06-26 18:14:35,659 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 18:14:35,660 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 222, 'skip_rate': 0.04054054054054054, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 213, 'cache_hits': 172, 'similarity_calculations': 3642, 'cache_hit_rate': 0.047226798462383306, 'cache_size': 3470}}
2025-06-26 18:14:35,660 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:14:35,660 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:14:35,660 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:14:35,660 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:14:35,662 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108008.0
2025-06-26 18:14:36,163 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:14:36,163 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473, 14408, 14408, 14408, 14408]
2025-06-26 18:14:36,163 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:14:36,173 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:14:36,173 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38], 'cur_cost': 73259.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67003.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89781.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31], 'cur_cost': 125733.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}, {'tour': array([24, 22,  4, 30, 11,  0, 25, 10, 29, 36, 23, 26, 39,  2, 27, 19, 18,
       17,  7, 32, 13, 34,  8, 41, 20, 16, 31, 28, 12, 33, 14,  1, 40, 37,
       21, 15,  6,  9,  3,  5, 38, 35]), 'cur_cost': 107323.0}, {'tour': array([29, 20, 26,  4, 15, 40,  3, 34, 12,  6, 13, 36, 11, 31, 14, 19, 24,
       35, 16, 18, 23, 32,  9,  1, 17,  7, 30, 38, 22, 27,  2, 25, 28, 21,
        8, 39,  0, 37, 41,  5, 10, 33]), 'cur_cost': 116144.0}, {'tour': array([34, 22,  3,  2, 13,  9, 26,  1, 29, 32,  5, 10, 41, 33,  6, 37,  0,
       16, 38, 19, 35, 15, 21, 40, 39,  8, 23, 28, 20, 11, 18, 24, 30, 36,
        7, 17, 14, 12, 25,  4, 27, 31]), 'cur_cost': 108008.0}]
2025-06-26 18:14:36,174 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:14:36,175 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 223, 'skip_rate': 0.04035874439461883, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 214, 'cache_hits': 172, 'similarity_calculations': 3647, 'cache_hit_rate': 0.047162051000822595, 'cache_size': 3475}}
2025-06-26 18:14:36,175 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:14:36,175 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 29, 35, 41, 34, 28, 22, 17, 11, 4, 10, 16, 21, 27, 33, 39, 40, 32, 26, 20, 14, 7, 1, 8, 15, 19, 25, 31, 37, 36, 30, 24, 13, 6, 2, 9, 3, 38], 'cur_cost': 73259.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67003.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 1, 8, 15, 22, 29, 36, 2, 9, 16, 23, 30, 37, 3, 10, 17, 24, 31, 38, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89781.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31], 'cur_cost': 125733.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 10, 22, 34, 3, 15, 27, 39, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 11, 23, 35, 4, 16, 28, 40, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31], 'cur_cost': 126432.0}}]
2025-06-26 18:14:36,175 - __main__ - INFO - 进化阶段完成
2025-06-26 18:14:36,176 - __main__ - INFO - 开始评估阶段
2025-06-26 18:14:36,176 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:14:36,177 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:14:36,177 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 15506.0 → 67003.0 (Δ -51497.0)
- Mean cost: 41843.6 → 106705.0
- Diversity: 0.493 → 0.578 (Δ 0.0854)

#### Elite metrics
- Elite count: 22 → 26
- Best elite cost: 14408 → 14408 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 0.0% , avg Δ -87032.14
- Exploit: 3 inds, success 0.0% , avg Δ -13129.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 10
- Historical trends: Last 3 iterations: costs [64696.0, 145982.0, 15506.0], diversity [0.9310541310541309, 0.8997150997150996, 0.7121693121693122]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:14:36,177 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:14:51,410 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early stagnation",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "negative",
      "reason": "all explore attempts resulted in regression (avg Δ -87032.14)"
    },
    "exploit": {
      "contribution": "minimal",
      "reason": "no successful improvements (avg Δ -13129.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "diagnosis": "over-exploration with ineffective mutations",
    "required_change": "reduce explore ratio while improving mutation quality"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_adjustment": "increase neutral mutation probability by 20%",
    "elite_preservation": "maintain current elite count (26)",
    "additional_measures": [
      "introduce local search around elites",
      "implement catastrophic mutation for worst 10% individuals"
    ]
  }
}
```
2025-06-26 18:14:51,425 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:14:51,425 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early stagnation",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "negative",
      "reason": "all explore attempts resulted in regression (avg Δ -87032.14)"
    },
    "exploit": {
      "contribution": "minimal",
      "reason": "no successful improvements (avg Δ -13129.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "diagnosis": "over-exploration with ineffective mutations",
    "required_change": "reduce explore ratio while improving mutation quality"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_adjustment": "increase neutral mutation probability by 20%",
    "elite_preservation": "maintain current elite count (26)",
    "additional_measures": [
      "introduce local search around elites",
      "implement catastrophic mutation for worst 10% individuals"
    ]
  }
}
```
2025-06-26 18:14:51,426 - __main__ - INFO - 评估阶段完成
2025-06-26 18:14:51,426 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early stagnation",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "negative",
      "reason": "all explore attempts resulted in regression (avg Δ -87032.14)"
    },
    "exploit": {
      "contribution": "minimal",
      "reason": "no successful improvements (avg Δ -13129.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "diagnosis": "over-exploration with ineffective mutations",
    "required_change": "reduce explore ratio while improving mutation quality"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_adjustment": "increase neutral mutation probability by 20%",
    "elite_preservation": "maintain current elite count (26)",
    "additional_measures": [
      "introduce local search around elites",
      "implement catastrophic mutation for worst 10% individuals"
    ]
  }
}
```
2025-06-26 18:14:51,426 - __main__ - INFO - 当前最佳适应度: 67003.0
2025-06-26 18:14:51,428 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_route_1.pkl
2025-06-26 18:14:51,428 - __main__ - INFO - composite7_42 开始进化第 3 代
2025-06-26 18:14:51,428 - __main__ - INFO - 开始分析阶段
2025-06-26 18:14:51,428 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:14:51,436 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 67003.0, 'max': 126935.0, 'mean': 106705.0, 'std': 21499.085310775434}, 'diversity': 0.8455026455026454, 'clusters': {'clusters': 6, 'cluster_sizes': [1, 2, 4, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:14:51,437 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 67003.0, 'max': 126935.0, 'mean': 106705.0, 'std': 21499.085310775434}, 'diversity_level': 0.8455026455026454, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [1, 2, 4, 1, 1, 1]}, 'coordinates': [[738, 3697], [692, 3762], [762, 3766], [739, 3741], [758, 3714], [684, 3694], [929, 540], [868, 510], [891, 498], [933, 491], [852, 532], [897, 526], [929, 580], [915, 553], [1890, 1863], [1929, 1848], [1944, 1878], [1953, 1816], [1917, 1886], [1918, 1872], [1953, 1867], [1896, 1876], [4612, 2136], [4610, 2101], [4642, 2112], [4671, 2119], [4654, 2096], [4668, 2095], [4629, 2076], [4633, 2136], [4690, 2084], [4630, 2063], [5196, 1147], [5225, 1098], [5161, 1092], [5180, 1114], [5237, 1135], [5212, 1134], [5217, 1164], [5214, 1181], [5245, 1156], [5154, 1138]], 'distance_matrix': array([[   0.,   80.,   73., ..., 5135., 5174., 5104.],
       [  80.,    0.,   70., ..., 5207., 5246., 5176.],
       [  73.,   70.,    0., ..., 5148., 5187., 5118.],
       ...,
       [5135., 5207., 5148., ...,    0.,   40.,   74.],
       [5174., 5246., 5187., ...,   40.,    0.,   93.],
       [5104., 5176., 5118., ...,   74.,   93.,    0.]])}
2025-06-26 18:14:51,437 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:14:51,437 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:14:51,438 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:14:51,440 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:14:51,441 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:14:51,441 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:14:51,441 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:14:51,442 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:14:51,442 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:14:51,443 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:14:51,450 - __main__ - INFO - 精英专家分析报告: {'elite_count': 26, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 52595.0, 'avg_gap': 92293.03846153847}, 'structure_gap': {'unique_elite_edges': 123, 'unique_pop_edges': 256, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.2384615384615385}}
2025-06-26 18:14:51,450 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:14:51,450 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:14:51,452 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:14:51,452 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 67003.0, mean 106705.0, max 126935.0, std 21499.085310775434
- diversity: 0.8455026455026454
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [145982.0, 15506.0, 67003.0] (Δ 65238.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [684.0, 491.0, 5245.0, 3766.0]
- centroid/spread: [2987.31, 1759.214] | rms 2135.914
- density_grid(3x3): [[8, 0, 10], [8, 0, 10], [6, 0, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1120.0, 2749.0, 4100.0, 5258.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [7, 4, 6, 8, 5, 6, 3, 2]
- nn_median_dist: 23.329

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:14:51,452 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:15:12,231 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "present due to wide range of edge lengths (13.0 to 5258.0) and long_edge_ratio of 0.25"
  },
  "population_state": {
    "coverage": "good (diversity: 0.845)",
    "convergence": "none (convergence: 0.0)",
    "spread": "uneven (density_grid shows clusters in certain cells while others are empty)"
  },
  "difficult_regions": {
    "identified_by": "low-density cells in density_grid and long edges (edge_len_stats)",
    "locations": "cells with 0 density in density_grid and edges in the upper quartile (q3 to max: 4100.0 to 5258.0)"
  },
  "opportunity_regions": {
    "identified_by": "high-density cells in density_grid and shorter edges",
    "locations": "cells with density 8 and 10 in density_grid and edges in the lower quartile (min to q1: 13.0 to 1120.0)"
  },
  "evolution_phase": "early exploration (iteration 2/5, no convergence, high diversity)",
  "evolution_direction": {
    "suggested_operators": [
      "focus on exploiting high-density regions with local search operators",
      "use crossover to combine promising subpaths from different solutions",
      "apply mutation to explore low-density regions and long edges",
      "maintain diversity with niching or crowding techniques"
    ]
  }
}
```
2025-06-26 18:15:12,234 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:15:12,234 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'present due to wide range of edge lengths (13.0 to 5258.0) and long_edge_ratio of 0.25'}, 'population_state': {'coverage': 'good (diversity: 0.845)', 'convergence': 'none (convergence: 0.0)', 'spread': 'uneven (density_grid shows clusters in certain cells while others are empty)'}, 'difficult_regions': {'identified_by': 'low-density cells in density_grid and long edges (edge_len_stats)', 'locations': 'cells with 0 density in density_grid and edges in the upper quartile (q3 to max: 4100.0 to 5258.0)'}, 'opportunity_regions': {'identified_by': 'high-density cells in density_grid and shorter edges', 'locations': 'cells with density 8 and 10 in density_grid and edges in the lower quartile (min to q1: 13.0 to 1120.0)'}, 'evolution_phase': 'early exploration (iteration 2/5, no convergence, high diversity)', 'evolution_direction': {'suggested_operators': ['focus on exploiting high-density regions with local search operators', 'use crossover to combine promising subpaths from different solutions', 'apply mutation to explore low-density regions and long edges', 'maintain diversity with niching or crowding techniques']}}
2025-06-26 18:15:12,234 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:15:12,234 - __main__ - INFO - 分析阶段完成
2025-06-26 18:15:12,234 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'present due to wide range of edge lengths (13.0 to 5258.0) and long_edge_ratio of 0.25'}, 'population_state': {'coverage': 'good (diversity: 0.845)', 'convergence': 'none (convergence: 0.0)', 'spread': 'uneven (density_grid shows clusters in certain cells while others are empty)'}, 'difficult_regions': {'identified_by': 'low-density cells in density_grid and long edges (edge_len_stats)', 'locations': 'cells with 0 density in density_grid and edges in the upper quartile (q3 to max: 4100.0 to 5258.0)'}, 'opportunity_regions': {'identified_by': 'high-density cells in density_grid and shorter edges', 'locations': 'cells with density 8 and 10 in density_grid and edges in the lower quartile (min to q1: 13.0 to 1120.0)'}, 'evolution_phase': 'early exploration (iteration 2/5, no convergence, high diversity)', 'evolution_direction': {'suggested_operators': ['focus on exploiting high-density regions with local search operators', 'use crossover to combine promising subpaths from different solutions', 'apply mutation to explore low-density regions and long edges', 'maintain diversity with niching or crowding techniques']}}
2025-06-26 18:15:12,235 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:15:12,235 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:15:12,235 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 67003.0
  • mean_cost: 106705.0
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness present due to wide range of edge lengths (13.0 to 5258.0) and long_edge_ratio of 0.25
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): identified_by, locations
- Opportunity regions (# 2): identified_by, locations
- Last-iteration feedback: ```json
{
  "evolution_stage": "early stagnation",
  "strategy_effectiveness": {
    "explore": {
      "contribution": ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:15:12,235 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:15:12,235 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:15:21,856 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early stagnation suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:15:21,860 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:15:21,860 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:15:21,860 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early stagnation suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:15:21,860 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:15:21,861 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:15:21,862 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early stagnation suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:15:21,862 - __main__ - INFO - 开始进化阶段
2025-06-26 18:15:21,862 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:15:21,862 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:15:21,862 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 73259.0 (rank 10.0%)
- Population diversity: 0.5780487804878048
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:15:21,862 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:15:21,862 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:15:33,637 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration of sparse areas while avoiding known costly segments"
}
```
2025-06-26 18:15:33,637 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:15:33,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 143307.0, 路径: [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40]
2025-06-26 18:15:33,637 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40], 'cur_cost': 143307.0}
2025-06-26 18:15:33,645 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:15:33,645 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:15:33,645 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 67003.0 (rank 0.0%)
- Population diversity: 0.5853658536585366
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:15:33,645 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:15:33,645 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:15:44,690 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balance exploration of sparse areas with cost efficiency"
}
```
2025-06-26 18:15:44,690 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:15:44,690 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113489.0, 路径: [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41]
2025-06-26 18:15:44,690 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41], 'cur_cost': 113489.0}
2025-06-26 18:15:44,695 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:15:44,695 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:15:44,696 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126432.0 (rank 60.0%)
- Population diversity: 0.6073170731707317
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:15:44,696 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:15:44,696 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:15:55,953 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 42, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps to increase diversity"
}
```
2025-06-26 18:15:55,954 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:15:55,954 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126436.0, 路径: [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35]
2025-06-26 18:15:55,954 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35], 'cur_cost': 126436.0}
2025-06-26 18:15:55,954 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:15:55,954 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:15:55,954 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126935.0 (rank 80.0%)
- Population diversity: 0.6195121951219512
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:15:55,954 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:15:55,957 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:16:07,137 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with sparse cell focus"
}
```
2025-06-26 18:16:07,137 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:16:07,137 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111527.0, 路径: [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41]
2025-06-26 18:16:07,137 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 111527.0}
2025-06-26 18:16:07,137 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:16:07,137 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:16:07,137 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 89781.0 (rank 0.0%)
- Population diversity: 0.6365853658536585
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:16:07,137 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:16:07,137 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:16:18,345 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse areas"
}
```
2025-06-26 18:16:18,347 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:16:18,347 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126935.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:16:18,347 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}
2025-06-26 18:16:18,347 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:16:18,347 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:16:18,347 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 125733.0 (rank 50.0%)
- Population diversity: 0.5804878048780487
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:16:18,347 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:16:18,347 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:16:30,384 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36],
  "targeted_regions": "Adjacent low-density cells near opportunity zones",
  "strategy_comment": "Spiral pattern balancing exploration and cost"
}
```
2025-06-26 18:16:30,385 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:16:30,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 79778.0, 路径: [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36]
2025-06-26 18:16:30,385 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36], 'cur_cost': 79778.0}
2025-06-26 18:16:30,385 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:16:30,385 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:16:30,385 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126432.0 (rank 60.0%)
- Population diversity: 0.6365853658536585
- Evolution phase: early exploration (iteration 2/5, no convergence, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): identified_by, locations
- Difficult regions to avoid (sample): identified_by, locations
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:16:30,385 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:16:30,385 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:16:41,522 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 42, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 3, 15, 27],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with spiral pattern"
}
```
2025-06-26 18:16:41,522 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:16:41,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125845.0, 路径: [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27]
2025-06-26 18:16:41,522 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27], 'cur_cost': 125845.0}
2025-06-26 18:16:41,530 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:16:41,530 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:16:41,530 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:16:41,530 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 123710.0
2025-06-26 18:16:42,031 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:16:42,032 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:16:42,032 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:16:42,040 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:16:42,040 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40], 'cur_cost': 143307.0}, {'tour': [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41], 'cur_cost': 113489.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35], 'cur_cost': 126436.0}, {'tour': [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 111527.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36], 'cur_cost': 79778.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27], 'cur_cost': 125845.0}, {'tour': array([12,  4, 10, 40, 29, 35, 37, 33,  1, 15, 41, 13, 32,  7, 27, 18, 26,
        8, 17, 21, 22, 39, 19, 34, 38, 14, 23,  9,  0, 25, 11, 24, 16, 30,
       31,  3, 28,  5,  6,  2, 36, 20]), 'cur_cost': 123710.0}, {'tour': array([29, 20, 26,  4, 15, 40,  3, 34, 12,  6, 13, 36, 11, 31, 14, 19, 24,
       35, 16, 18, 23, 32,  9,  1, 17,  7, 30, 38, 22, 27,  2, 25, 28, 21,
        8, 39,  0, 37, 41,  5, 10, 33]), 'cur_cost': 116144.0}, {'tour': array([34, 22,  3,  2, 13,  9, 26,  1, 29, 32,  5, 10, 41, 33,  6, 37,  0,
       16, 38, 19, 35, 15, 21, 40, 39,  8, 23, 28, 20, 11, 18, 24, 30, 36,
        7, 17, 14, 12, 25,  4, 27, 31]), 'cur_cost': 108008.0}]
2025-06-26 18:16:42,042 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:16:42,042 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 224, 'skip_rate': 0.04017857142857143, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 215, 'cache_hits': 172, 'similarity_calculations': 3653, 'cache_hit_rate': 0.04708458800985491, 'cache_size': 3481}}
2025-06-26 18:16:42,042 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:16:42,042 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:16:42,042 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:16:42,042 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:16:42,044 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102330.0
2025-06-26 18:16:42,547 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:16:42,547 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:16:42,547 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:16:42,557 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:16:42,557 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40], 'cur_cost': 143307.0}, {'tour': [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41], 'cur_cost': 113489.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35], 'cur_cost': 126436.0}, {'tour': [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 111527.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36], 'cur_cost': 79778.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27], 'cur_cost': 125845.0}, {'tour': array([12,  4, 10, 40, 29, 35, 37, 33,  1, 15, 41, 13, 32,  7, 27, 18, 26,
        8, 17, 21, 22, 39, 19, 34, 38, 14, 23,  9,  0, 25, 11, 24, 16, 30,
       31,  3, 28,  5,  6,  2, 36, 20]), 'cur_cost': 123710.0}, {'tour': array([38,  7,  0, 30,  8, 20, 31, 34, 25, 14, 41,  1, 24, 18, 16, 29, 35,
       36, 40, 32, 12, 19, 21,  9, 15, 11, 26, 28,  4,  5,  2, 37, 23, 10,
        3, 13, 27, 33, 22, 17, 39,  6]), 'cur_cost': 102330.0}, {'tour': array([34, 22,  3,  2, 13,  9, 26,  1, 29, 32,  5, 10, 41, 33,  6, 37,  0,
       16, 38, 19, 35, 15, 21, 40, 39,  8, 23, 28, 20, 11, 18, 24, 30, 36,
        7, 17, 14, 12, 25,  4, 27, 31]), 'cur_cost': 108008.0}]
2025-06-26 18:16:42,560 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:16:42,561 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 225, 'skip_rate': 0.04, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 216, 'cache_hits': 172, 'similarity_calculations': 3660, 'cache_hit_rate': 0.046994535519125684, 'cache_size': 3488}}
2025-06-26 18:16:42,561 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:16:42,561 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:16:42,561 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:16:42,562 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:16:42,562 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 121862.0
2025-06-26 18:16:43,065 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:16:43,065 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:16:43,065 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:16:43,074 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:16:43,075 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40], 'cur_cost': 143307.0}, {'tour': [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41], 'cur_cost': 113489.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35], 'cur_cost': 126436.0}, {'tour': [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 111527.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}, {'tour': [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36], 'cur_cost': 79778.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27], 'cur_cost': 125845.0}, {'tour': array([12,  4, 10, 40, 29, 35, 37, 33,  1, 15, 41, 13, 32,  7, 27, 18, 26,
        8, 17, 21, 22, 39, 19, 34, 38, 14, 23,  9,  0, 25, 11, 24, 16, 30,
       31,  3, 28,  5,  6,  2, 36, 20]), 'cur_cost': 123710.0}, {'tour': array([38,  7,  0, 30,  8, 20, 31, 34, 25, 14, 41,  1, 24, 18, 16, 29, 35,
       36, 40, 32, 12, 19, 21,  9, 15, 11, 26, 28,  4,  5,  2, 37, 23, 10,
        3, 13, 27, 33, 22, 17, 39,  6]), 'cur_cost': 102330.0}, {'tour': array([ 1, 11,  0, 36, 12, 33,  7, 29,  9, 34, 27, 28, 41,  6, 25, 22, 26,
       14, 35, 19, 18,  4,  8, 38, 16,  3, 40,  2, 32, 10,  5, 31, 13, 37,
       23, 24, 39, 21, 30, 17, 15, 20]), 'cur_cost': 121862.0}]
2025-06-26 18:16:43,077 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:16:43,077 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 226, 'skip_rate': 0.03982300884955752, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 217, 'cache_hits': 172, 'similarity_calculations': 3668, 'cache_hit_rate': 0.04689203925845147, 'cache_size': 3496}}
2025-06-26 18:16:43,077 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:16:43,077 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 8, 17, 30, 3, 41, 19, 7, 25, 11, 36, 2, 28, 14, 39, 6, 22, 10, 33, 1, 27, 15, 38, 4, 20, 9, 34, 13, 37, 16, 31, 21, 29, 18, 32, 24, 35, 26, 40], 'cur_cost': 143307.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 8, 17, 29, 3, 14, 25, 7, 19, 31, 10, 21, 33, 2, 13, 24, 6, 18, 30, 4, 15, 26, 9, 20, 32, 1, 11, 22, 34, 16, 27, 35, 28, 36, 37, 38, 39, 40, 41], 'cur_cost': 113489.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 3, 15, 27, 39, 10, 22, 34, 4, 16, 28, 40, 11, 23, 35], 'cur_cost': 126436.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 8, 17, 30, 3, 14, 25, 9, 19, 31, 6, 21, 33, 11, 24, 7, 18, 29, 2, 15, 26, 10, 20, 32, 4, 16, 27, 1, 13, 22, 34, 28, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 111527.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 39, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 126935.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 23, 29, 35, 41, 6, 11, 17, 22, 28, 34, 40, 4, 10, 16, 21, 27, 33, 39, 3, 9, 15, 20, 26, 32, 38, 2, 8, 14, 19, 25, 31, 37, 1, 7, 13, 24, 30, 36], 'cur_cost': 79778.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 1, 13, 25, 37, 6, 18, 30, 3, 9, 21, 33, 2, 14, 26, 38, 7, 19, 31, 40, 11, 23, 35, 4, 16, 28, 39, 10, 22, 34, 15, 27], 'cur_cost': 125845.0}}]
2025-06-26 18:16:43,078 - __main__ - INFO - 进化阶段完成
2025-06-26 18:16:43,078 - __main__ - INFO - 开始评估阶段
2025-06-26 18:16:43,078 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:16:43,079 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:16:43,079 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 67003.0 → 79778.0 (Δ -12775.0)
- Mean cost: 106705.0 → 117521.9
- Diversity: 0.578 → 0.646 (Δ 0.0683)

#### Elite metrics
- Elite count: 26 → 26
- Best elite cost: 14408 → 14408 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ -13106.0
- Exploit: 3 inds, success 33.3% , avg Δ -5475.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [145982.0, 15506.0, 67003.0], diversity [0.8997150997150996, 0.7121693121693122, 0.8455026455026454]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:16:43,080 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:16:55,512 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -13106.0,
      "contribution": "moderate improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -5475.67,
      "contribution": "limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is more effective but exploit is underperforming; slight imbalance"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation intensity for exploit strategies to improve success rate"
  }
}
```
2025-06-26 18:16:55,526 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:16:55,527 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -13106.0,
      "contribution": "moderate improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -5475.67,
      "contribution": "limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is more effective but exploit is underperforming; slight imbalance"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation intensity for exploit strategies to improve success rate"
  }
}
```
2025-06-26 18:16:55,527 - __main__ - INFO - 评估阶段完成
2025-06-26 18:16:55,527 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -13106.0,
      "contribution": "moderate improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -5475.67,
      "contribution": "limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is more effective but exploit is underperforming; slight imbalance"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing mutation intensity for exploit strategies to improve success rate"
  }
}
```
2025-06-26 18:16:55,527 - __main__ - INFO - 当前最佳适应度: 79778.0
2025-06-26 18:16:55,530 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_route_2.pkl
2025-06-26 18:16:55,530 - __main__ - INFO - composite7_42 开始进化第 4 代
2025-06-26 18:16:55,531 - __main__ - INFO - 开始分析阶段
2025-06-26 18:16:55,531 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:16:55,538 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 79778.0, 'max': 143307.0, 'mean': 117521.9, 'std': 16330.771497084883}, 'diversity': 0.8703703703703705, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:16:55,539 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 79778.0, 'max': 143307.0, 'mean': 117521.9, 'std': 16330.771497084883}, 'diversity_level': 0.8703703703703705, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'coordinates': [[738, 3697], [692, 3762], [762, 3766], [739, 3741], [758, 3714], [684, 3694], [929, 540], [868, 510], [891, 498], [933, 491], [852, 532], [897, 526], [929, 580], [915, 553], [1890, 1863], [1929, 1848], [1944, 1878], [1953, 1816], [1917, 1886], [1918, 1872], [1953, 1867], [1896, 1876], [4612, 2136], [4610, 2101], [4642, 2112], [4671, 2119], [4654, 2096], [4668, 2095], [4629, 2076], [4633, 2136], [4690, 2084], [4630, 2063], [5196, 1147], [5225, 1098], [5161, 1092], [5180, 1114], [5237, 1135], [5212, 1134], [5217, 1164], [5214, 1181], [5245, 1156], [5154, 1138]], 'distance_matrix': array([[   0.,   80.,   73., ..., 5135., 5174., 5104.],
       [  80.,    0.,   70., ..., 5207., 5246., 5176.],
       [  73.,   70.,    0., ..., 5148., 5187., 5118.],
       ...,
       [5135., 5207., 5148., ...,    0.,   40.,   74.],
       [5174., 5246., 5187., ...,   40.,    0.,   93.],
       [5104., 5176., 5118., ...,   74.,   93.,    0.]])}
2025-06-26 18:16:55,540 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:16:55,540 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:16:55,540 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:16:55,544 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:16:55,544 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:16:55,544 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:16:55,544 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:16:55,545 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:16:55,545 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:16:55,553 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:16:55,553 - __main__ - INFO - 精英专家分析报告: {'elite_count': 26, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 65370.0, 'avg_gap': 103109.93846153846}, 'structure_gap': {'unique_elite_edges': 129, 'unique_pop_edges': 271, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.2384615384615385}}
2025-06-26 18:16:55,554 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:16:55,554 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:16:55,556 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:16:55,556 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 79778.0, mean 117521.9, max 143307.0, std 16330.771497084883
- diversity: 0.8703703703703705
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [15506.0, 67003.0, 79778.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [684.0, 491.0, 5245.0, 3766.0]
- centroid/spread: [2987.31, 1759.214] | rms 2135.914
- density_grid(3x3): [[8, 0, 10], [8, 0, 10], [6, 0, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1120.0, 2749.0, 4100.0, 5258.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [7, 4, 6, 8, 5, 6, 3, 2]
- nn_median_dist: 23.329

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:16:55,556 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:17:16,681 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 13.0 to 5258.0, with significant standard deviation)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible presence (long_edge_ratio of 0.25 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Partial (density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (diversity 0.87, convergence 0.0, no elite solutions yet)"
  },
  "difficult_regions": [
    "Low-density cells in grid (particularly center cell and bottom-right)",
    "Long-edge corridors (edges in top quantile length 4100.0-5258.0)"
  ],
  "opportunity_regions": [
    "High-density cells (top-left and top-center grid cells with 8-10 nodes)",
    "Short-edge clusters (edges below median length 2749.0)"
  ],
  "evolution_phase": "Exploration (high diversity, no convergence, no elites)",
  "evolution_direction": [
    "Prioritize spatial exploitation operators for high-density cells (e.g. local search around nodes in top-left grid)",
    "Apply edge recombination to preserve promising short-edge segments",
    "Use diversity maintenance in low-density regions (e.g. forced insertion of distant nodes)",
    "Monitor long-edge usage to avoid premature convergence on deceptive paths"
  ]
}
```
2025-06-26 18:17:16,685 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:17:16,685 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 5258.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Partial (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity 0.87, convergence 0.0, no elite solutions yet)'}, 'difficult_regions': ['Low-density cells in grid (particularly center cell and bottom-right)', 'Long-edge corridors (edges in top quantile length 4100.0-5258.0)'], 'opportunity_regions': ['High-density cells (top-left and top-center grid cells with 8-10 nodes)', 'Short-edge clusters (edges below median length 2749.0)'], 'evolution_phase': 'Exploration (high diversity, no convergence, no elites)', 'evolution_direction': ['Prioritize spatial exploitation operators for high-density cells (e.g. local search around nodes in top-left grid)', 'Apply edge recombination to preserve promising short-edge segments', 'Use diversity maintenance in low-density regions (e.g. forced insertion of distant nodes)', 'Monitor long-edge usage to avoid premature convergence on deceptive paths']}
2025-06-26 18:17:16,685 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:17:16,685 - __main__ - INFO - 分析阶段完成
2025-06-26 18:17:16,685 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 5258.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Partial (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity 0.87, convergence 0.0, no elite solutions yet)'}, 'difficult_regions': ['Low-density cells in grid (particularly center cell and bottom-right)', 'Long-edge corridors (edges in top quantile length 4100.0-5258.0)'], 'opportunity_regions': ['High-density cells (top-left and top-center grid cells with 8-10 nodes)', 'Short-edge clusters (edges below median length 2749.0)'], 'evolution_phase': 'Exploration (high diversity, no convergence, no elites)', 'evolution_direction': ['Prioritize spatial exploitation operators for high-density cells (e.g. local search around nodes in top-left grid)', 'Apply edge recombination to preserve promising short-edge segments', 'Use diversity maintenance in low-density regions (e.g. forced insertion of distant nodes)', 'Monitor long-edge usage to avoid premature convergence on deceptive paths']}
2025-06-26 18:17:16,687 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:17:16,687 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:17:16,687 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 79778.0
  • mean_cost: 117521.9
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 13.0 to 5258.0, with significant standard deviation), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible presence (long_edge_ratio of 0.25 suggests some misleading long edges)
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells in, Long-edge corridors 
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:17:16,687 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:17:16,687 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:17:26,062 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:17:26,062 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:17:26,069 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:17:26,069 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:17:26,069 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:17:26,069 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:17:26,069 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:17:26,071 - __main__ - INFO - 开始进化阶段
2025-06-26 18:17:26,079 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:17:26,079 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:17:26,080 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 143307.0 (rank 90.0%)
- Population diversity: 0.6463414634146342
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:17:26,080 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:17:26,080 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:17:37,743 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 18:17:37,743 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:17:37,745 - ExplorationExpert - INFO - 探索路径生成完成，成本: 132410.0, 路径: [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40]
2025-06-26 18:17:37,745 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}
2025-06-26 18:17:37,745 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:17:37,745 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:17:37,746 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113489.0 (rank 30.0%)
- Population diversity: 0.651219512195122
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:17:37,746 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:17:37,746 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:17:49,297 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Cluster hops in dense regions, sparse adjacency exploration"
}
```
2025-06-26 18:17:49,299 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:17:49,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72700.0, 路径: [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40]
2025-06-26 18:17:49,299 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}
2025-06-26 18:17:49,301 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:17:49,301 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:17:49,301 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126436.0 (rank 70.0%)
- Population diversity: 0.6707317073170732
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:17:49,301 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:17:49,301 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:17:59,787 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Balanced exploration of dense/sparse cells via alternating clusters"
}
```
2025-06-26 18:17:59,787 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:17:59,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53337.0, 路径: [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3]
2025-06-26 18:17:59,790 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}
2025-06-26 18:17:59,790 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:17:59,790 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:17:59,790 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111527.0 (rank 40.0%)
- Population diversity: 0.7048780487804878
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:17:59,791 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:17:59,791 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:18:11,371 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore adjacent sparse cells"
}
```
2025-06-26 18:18:11,371 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:18:11,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110707.0, 路径: [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41]
2025-06-26 18:18:11,375 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}
2025-06-26 18:18:11,375 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:18:11,375 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:18:11,375 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126935.0 (rank 80.0%)
- Population diversity: 0.7048780487804878
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:18:11,375 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:18:11,375 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:18:22,768 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Leverage clustered nodes with short edges, explore adjacent sparse cells"
}
```
2025-06-26 18:18:22,770 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:18:22,770 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67026.0, 路径: [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4]
2025-06-26 18:18:22,771 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}
2025-06-26 18:18:22,772 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:18:22,772 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:18:22,772 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 79778.0 (rank 30.0%)
- Population diversity: 0.7365853658536585
- Evolution phase: Exploration (high diversity, no convergence, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:18:22,772 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:18:22,772 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:18:34,063 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Leverage clusters with short edges, explore adjacent sparse cells"
}
```
2025-06-26 18:18:34,063 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:18:34,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 89780.0, 路径: [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40]
2025-06-26 18:18:34,070 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}
2025-06-26 18:18:34,071 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:18:34,071 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:18:34,071 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:18:34,072 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 105276.0
2025-06-26 18:18:34,575 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:18:34,576 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:18:34,576 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:18:34,584 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:18:34,585 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([12,  4, 10, 40, 29, 35, 37, 33,  1, 15, 41, 13, 32,  7, 27, 18, 26,
        8, 17, 21, 22, 39, 19, 34, 38, 14, 23,  9,  0, 25, 11, 24, 16, 30,
       31,  3, 28,  5,  6,  2, 36, 20]), 'cur_cost': 123710.0}, {'tour': array([38,  7,  0, 30,  8, 20, 31, 34, 25, 14, 41,  1, 24, 18, 16, 29, 35,
       36, 40, 32, 12, 19, 21,  9, 15, 11, 26, 28,  4,  5,  2, 37, 23, 10,
        3, 13, 27, 33, 22, 17, 39,  6]), 'cur_cost': 102330.0}, {'tour': array([ 1, 11,  0, 36, 12, 33,  7, 29,  9, 34, 27, 28, 41,  6, 25, 22, 26,
       14, 35, 19, 18,  4,  8, 38, 16,  3, 40,  2, 32, 10,  5, 31, 13, 37,
       23, 24, 39, 21, 30, 17, 15, 20]), 'cur_cost': 121862.0}]
2025-06-26 18:18:34,586 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:18:34,587 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 227, 'skip_rate': 0.039647577092511016, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 218, 'cache_hits': 172, 'similarity_calculations': 3677, 'cache_hit_rate': 0.04677726407397335, 'cache_size': 3505}}
2025-06-26 18:18:34,587 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:18:34,587 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:18:34,587 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:18:34,589 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:18:34,589 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 118995.0
2025-06-26 18:18:35,092 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:18:35,094 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:18:35,094 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:18:35,103 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:18:35,103 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([38,  7,  0, 30,  8, 20, 31, 34, 25, 14, 41,  1, 24, 18, 16, 29, 35,
       36, 40, 32, 12, 19, 21,  9, 15, 11, 26, 28,  4,  5,  2, 37, 23, 10,
        3, 13, 27, 33, 22, 17, 39,  6]), 'cur_cost': 102330.0}, {'tour': array([ 1, 11,  0, 36, 12, 33,  7, 29,  9, 34, 27, 28, 41,  6, 25, 22, 26,
       14, 35, 19, 18,  4,  8, 38, 16,  3, 40,  2, 32, 10,  5, 31, 13, 37,
       23, 24, 39, 21, 30, 17, 15, 20]), 'cur_cost': 121862.0}]
2025-06-26 18:18:35,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:18:35,105 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 228, 'skip_rate': 0.039473684210526314, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 219, 'cache_hits': 172, 'similarity_calculations': 3687, 'cache_hit_rate': 0.04665039327366423, 'cache_size': 3515}}
2025-06-26 18:18:35,106 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:18:35,106 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:18:35,106 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:18:35,106 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:18:35,106 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108306.0
2025-06-26 18:18:35,609 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:18:35,610 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:18:35,610 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:18:35,619 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:18:35,620 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([12,  2, 32, 35, 21,  9, 14,  1, 11, 13, 25, 20, 34, 39, 15,  3, 28,
       36, 30, 10, 29,  0, 38, 24, 26,  5,  4, 16, 40,  8, 22, 37, 23, 18,
        6,  7, 41, 27, 17, 33, 19, 31]), 'cur_cost': 108306.0}, {'tour': array([ 1, 11,  0, 36, 12, 33,  7, 29,  9, 34, 27, 28, 41,  6, 25, 22, 26,
       14, 35, 19, 18,  4,  8, 38, 16,  3, 40,  2, 32, 10,  5, 31, 13, 37,
       23, 24, 39, 21, 30, 17, 15, 20]), 'cur_cost': 121862.0}]
2025-06-26 18:18:35,621 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:18:35,621 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 229, 'skip_rate': 0.039301310043668124, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 220, 'cache_hits': 172, 'similarity_calculations': 3698, 'cache_hit_rate': 0.046511627906976744, 'cache_size': 3526}}
2025-06-26 18:18:35,622 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:18:35,622 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:18:35,622 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:18:35,622 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:18:35,623 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 89465.0
2025-06-26 18:18:36,125 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:18:36,126 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:18:36,126 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:18:36,135 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:18:36,135 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([12,  2, 32, 35, 21,  9, 14,  1, 11, 13, 25, 20, 34, 39, 15,  3, 28,
       36, 30, 10, 29,  0, 38, 24, 26,  5,  4, 16, 40,  8, 22, 37, 23, 18,
        6,  7, 41, 27, 17, 33, 19, 31]), 'cur_cost': 108306.0}, {'tour': array([33, 37,  0, 40, 28,  7, 12, 29,  1,  9, 22, 25,  3, 24, 21, 19, 17,
       27, 26, 31, 34, 38, 13,  5, 35, 32,  8, 11, 41, 36, 23, 18, 15,  6,
       10,  2,  4, 39, 16, 20, 14, 30]), 'cur_cost': 89465.0}]
2025-06-26 18:18:36,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:18:36,138 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 230, 'skip_rate': 0.0391304347826087, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 221, 'cache_hits': 172, 'similarity_calculations': 3710, 'cache_hit_rate': 0.04636118598382749, 'cache_size': 3538}}
2025-06-26 18:18:36,138 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:18:36,138 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 9, 21, 3, 27, 16, 33, 8, 24, 11, 37, 2, 19, 31, 6, 22, 13, 38, 4, 17, 30, 10, 25, 15, 36, 1, 20, 32, 26, 39, 28, 34, 41, 40], 'cur_cost': 132410.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 34, 41, 36, 30, 24, 19, 13, 8, 3, 9, 14, 20, 25, 31, 37, 32, 26, 21, 15, 10, 4, 1, 6, 11, 16, 22, 27, 33, 38, 39, 35, 28, 17, 2, 40], 'cur_cost': 72700.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 25, 30, 35, 40, 41, 36, 31, 26, 20, 15, 10, 4, 9, 14, 21, 27, 32, 37, 38, 33, 28, 22, 16, 11, 6, 1, 2, 7, 13, 18, 24, 29, 34, 39, 23, 17, 8, 3], 'cur_cost': 53337.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}}]
2025-06-26 18:18:36,138 - __main__ - INFO - 进化阶段完成
2025-06-26 18:18:36,138 - __main__ - INFO - 开始评估阶段
2025-06-26 18:18:36,139 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:18:36,140 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:18:36,140 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 79778.0 → 53337.0 (Δ 26441.0)
- Mean cost: 117521.9 → 94800.2
- Diversity: 0.646 → 0.710 (Δ 0.0634)

#### Elite metrics
- Elite count: 26 → 26
- Best elite cost: 14408 → 14408 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 83.3% , avg Δ 29252.0
- Exploit: 4 inds, success 75.0% , avg Δ 12926.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [15506.0, 67003.0, 79778.0], diversity [0.7121693121693122, 0.8455026455026454, 0.8703703703703705]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:18:36,141 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:18:48,163 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 29252.0,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_improvement": 12926.25,
      "contribution": "moderate"
    }
  },
  "balance_state": "explore-heavy with good results",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Maintain current explore ratio but monitor elite stagnation; consider introducing new mutation operators if best elite remains unchanged next iteration"
  }
}
```
2025-06-26 18:18:48,169 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:18:48,177 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 29252.0,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_improvement": 12926.25,
      "contribution": "moderate"
    }
  },
  "balance_state": "explore-heavy with good results",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Maintain current explore ratio but monitor elite stagnation; consider introducing new mutation operators if best elite remains unchanged next iteration"
  }
}
```
2025-06-26 18:18:48,177 - __main__ - INFO - 评估阶段完成
2025-06-26 18:18:48,177 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 29252.0,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_improvement": 12926.25,
      "contribution": "moderate"
    }
  },
  "balance_state": "explore-heavy with good results",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Maintain current explore ratio but monitor elite stagnation; consider introducing new mutation operators if best elite remains unchanged next iteration"
  }
}
```
2025-06-26 18:18:48,177 - __main__ - INFO - 当前最佳适应度: 53337.0
2025-06-26 18:18:48,180 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_route_3.pkl
2025-06-26 18:18:48,181 - __main__ - INFO - composite7_42 开始进化第 5 代
2025-06-26 18:18:48,181 - __main__ - INFO - 开始分析阶段
2025-06-26 18:18:48,181 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:18:48,190 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 53337.0, 'max': 132410.0, 'mean': 94800.2, 'std': 23617.259484537997}, 'diversity': 0.9089947089947091, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:18:48,191 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 53337.0, 'max': 132410.0, 'mean': 94800.2, 'std': 23617.259484537997}, 'diversity_level': 0.9089947089947091, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'coordinates': [[738, 3697], [692, 3762], [762, 3766], [739, 3741], [758, 3714], [684, 3694], [929, 540], [868, 510], [891, 498], [933, 491], [852, 532], [897, 526], [929, 580], [915, 553], [1890, 1863], [1929, 1848], [1944, 1878], [1953, 1816], [1917, 1886], [1918, 1872], [1953, 1867], [1896, 1876], [4612, 2136], [4610, 2101], [4642, 2112], [4671, 2119], [4654, 2096], [4668, 2095], [4629, 2076], [4633, 2136], [4690, 2084], [4630, 2063], [5196, 1147], [5225, 1098], [5161, 1092], [5180, 1114], [5237, 1135], [5212, 1134], [5217, 1164], [5214, 1181], [5245, 1156], [5154, 1138]], 'distance_matrix': array([[   0.,   80.,   73., ..., 5135., 5174., 5104.],
       [  80.,    0.,   70., ..., 5207., 5246., 5176.],
       [  73.,   70.,    0., ..., 5148., 5187., 5118.],
       ...,
       [5135., 5207., 5148., ...,    0.,   40.,   74.],
       [5174., 5246., 5187., ...,   40.,    0.,   93.],
       [5104., 5176., 5118., ...,   74.,   93.,    0.]])}
2025-06-26 18:18:48,191 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:18:48,192 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:18:48,192 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:18:48,196 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:18:48,196 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:18:48,196 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:18:48,196 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:18:48,197 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:18:48,197 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:18:48,205 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:18:48,205 - __main__ - INFO - 精英专家分析报告: {'elite_count': 26, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 38929.0, 'avg_gap': 80388.23846153845}, 'structure_gap': {'unique_elite_edges': 112, 'unique_pop_edges': 306, 'common_edges': 37}}, 'elite_diversity': {'diversity_score': 0.2384615384615385}}
2025-06-26 18:18:48,205 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:18:48,206 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:18:48,208 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:18:48,209 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 53337.0, mean 94800.2, max 132410.0, std 23617.259484537997
- diversity: 0.9089947089947091
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [67003.0, 79778.0, 53337.0] (Δ 13220.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [684.0, 491.0, 5245.0, 3766.0]
- centroid/spread: [2987.31, 1759.214] | rms 2135.914
- density_grid(3x3): [[8, 0, 10], [8, 0, 10], [6, 0, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1120.0, 2749.0, 4100.0, 5258.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [7, 4, 6, 8, 5, 6, 3, 2]
- nn_median_dist: 23.329

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:18:48,209 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:19:10,770 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation in costs)",
    "modality": "Potentially multimodal (high diversity score of 0.909 suggests multiple local optima)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 indicates some potentially misleading long edges)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (density_grid shows nodes distributed across most cells except center)",
    "convergence": "Early exploration phase (0.0 convergence score, high diversity)",
    "quality_spread": "Wide solution quality range (min 53337 to max 132410)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Potential difficulty in regions with edge lengths >4100 (q3 value)",
    "low_density_zones": "Center cell shows complete avoidance (0 nodes in center 3x3 grid cell)"
  },
  "opportunity_regions": {
    "high_density_clusters": "Top-right and bottom-left grid cells show highest node concentrations (10 nodes each)",
    "promising_edges": "Short edges (q1=1120) in high-density regions may form building blocks"
  },
  "evolution_phase": "Mid-exploration (iteration 4/5 with improving but still diverse solutions)",
  "evolution_direction": [
    "Intensify exploitation in high-density opportunity regions",
    "Consider edge recombination operators that favor shorter edges (1120-2749 length range)",
    "Add directed mutation to improve connectivity in the empty center region",
    "Maintain diversity through niching in spatial grid cells"
  ]
}
```
2025-06-26 18:19:10,775 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:19:10,775 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (high diversity score of 0.909 suggests multiple local optima)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some potentially misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across most cells except center)', 'convergence': 'Early exploration phase (0.0 convergence score, high diversity)', 'quality_spread': 'Wide solution quality range (min 53337 to max 132410)'}, 'difficult_regions': {'long_edge_corridors': 'Potential difficulty in regions with edge lengths >4100 (q3 value)', 'low_density_zones': 'Center cell shows complete avoidance (0 nodes in center 3x3 grid cell)'}, 'opportunity_regions': {'high_density_clusters': 'Top-right and bottom-left grid cells show highest node concentrations (10 nodes each)', 'promising_edges': 'Short edges (q1=1120) in high-density regions may form building blocks'}, 'evolution_phase': 'Mid-exploration (iteration 4/5 with improving but still diverse solutions)', 'evolution_direction': ['Intensify exploitation in high-density opportunity regions', 'Consider edge recombination operators that favor shorter edges (1120-2749 length range)', 'Add directed mutation to improve connectivity in the empty center region', 'Maintain diversity through niching in spatial grid cells']}
2025-06-26 18:19:10,775 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:19:10,775 - __main__ - INFO - 分析阶段完成
2025-06-26 18:19:10,775 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (high diversity score of 0.909 suggests multiple local optima)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some potentially misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across most cells except center)', 'convergence': 'Early exploration phase (0.0 convergence score, high diversity)', 'quality_spread': 'Wide solution quality range (min 53337 to max 132410)'}, 'difficult_regions': {'long_edge_corridors': 'Potential difficulty in regions with edge lengths >4100 (q3 value)', 'low_density_zones': 'Center cell shows complete avoidance (0 nodes in center 3x3 grid cell)'}, 'opportunity_regions': {'high_density_clusters': 'Top-right and bottom-left grid cells show highest node concentrations (10 nodes each)', 'promising_edges': 'Short edges (q1=1120) in high-density regions may form building blocks'}, 'evolution_phase': 'Mid-exploration (iteration 4/5 with improving but still diverse solutions)', 'evolution_direction': ['Intensify exploitation in high-density opportunity regions', 'Consider edge recombination operators that favor shorter edges (1120-2749 length range)', 'Add directed mutation to improve connectivity in the empty center region', 'Maintain diversity through niching in spatial grid cells']}
2025-06-26 18:19:10,775 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:19:10,775 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:19:10,775 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 53337.0
  • mean_cost: 94800.2
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 5258.0, with significant standard deviation in costs), modality Potentially multimodal (high diversity score of 0.909 suggests multiple local optima), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 indicates some potentially misleading long edges)
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_zones
- Opportunity regions (# 2): high_density_cluster, promising_edges
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:19:10,775 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:19:10,777 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:19:19,797 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Low diversity and rugged landscape favor exploration. Top performers exploit, others explore to diversify."
}
```
2025-06-26 18:19:19,798 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-26 18:19:19,798 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-26 18:19:19,799 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Low diversity and rugged landscape favor exploration. Top performers exploit, others explore to diversify."
}
```
2025-06-26 18:19:19,799 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:19:19,799 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-26 18:19:19,799 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Low diversity and rugged landscape favor exploration. Top performers exploit, others explore to diversify."
}
```
2025-06-26 18:19:19,799 - __main__ - INFO - 开始进化阶段
2025-06-26 18:19:19,799 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:19:19,799 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:19:19,799 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 132410.0 (rank 90.0%)
- Population diversity: 0.7097560975609756
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:19:19,801 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:19:19,802 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:19:29,997 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40],
  "targeted_regions": "high_density_cl, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense clusters and sparse edges"
}
```
2025-06-26 18:19:30,002 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:19:30,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114082.0, 路径: [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40]
2025-06-26 18:19:30,002 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}
2025-06-26 18:19:30,003 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:19:30,003 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:19:30,003 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 72700.0 (rank 20.0%)
- Population diversity: 0.7170731707317073
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:19:30,003 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:19:30,003 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:19:40,922 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2],
  "targeted_regions": "high_density_cl, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense clusters and sparse edges"
}
```
2025-06-26 18:19:40,922 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:19:40,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41956.0, 路径: [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2]
2025-06-26 18:19:40,926 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}
2025-06-26 18:19:40,926 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 18:19:40,926 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:19:40,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:19:40,927 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 89056.0
2025-06-26 18:19:41,429 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:19:41,429 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:19:41,429 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:19:41,438 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:19:41,438 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}, {'tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}, {'tour': array([29,  0, 18, 21, 13, 12,  8, 11,  7, 38, 28, 36, 34, 33, 14, 25, 19,
       39, 41, 17, 15,  3, 24, 23, 37, 27,  6, 10, 32, 40,  2,  9, 20, 22,
       16,  5, 26,  4, 35,  1, 30, 31]), 'cur_cost': 89056.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 8, 21, 3, 16, 31, 9, 24, 1, 13, 6, 19, 25, 10, 26, 15, 32, 4, 17, 22, 11, 27, 2, 20, 33, 28, 34, 30, 36, 37, 38, 39, 40, 41], 'cur_cost': 110707.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 34, 27, 20, 13, 6, 1, 7, 14, 21, 28, 35, 41, 36, 29, 22, 15, 8, 2, 9, 16, 23, 30, 37, 31, 24, 17, 10, 3, 11, 18, 25, 32, 39, 38, 4], 'cur_cost': 67026.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([12,  2, 32, 35, 21,  9, 14,  1, 11, 13, 25, 20, 34, 39, 15,  3, 28,
       36, 30, 10, 29,  0, 38, 24, 26,  5,  4, 16, 40,  8, 22, 37, 23, 18,
        6,  7, 41, 27, 17, 33, 19, 31]), 'cur_cost': 108306.0}, {'tour': array([33, 37,  0, 40, 28,  7, 12, 29,  1,  9, 22, 25,  3, 24, 21, 19, 17,
       27, 26, 31, 34, 38, 13,  5, 35, 32,  8, 11, 41, 36, 23, 18, 15,  6,
       10,  2,  4, 39, 16, 20, 14, 30]), 'cur_cost': 89465.0}]
2025-06-26 18:19:41,440 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:19:41,441 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 231, 'skip_rate': 0.03896103896103896, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 222, 'cache_hits': 172, 'similarity_calculations': 3723, 'cache_hit_rate': 0.04619930163846361, 'cache_size': 3551}}
2025-06-26 18:19:41,441 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-26 18:19:41,441 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:19:41,441 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:19:41,441 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 110707.0 (rank 70.0%)
- Population diversity: 0.7585365853658537
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:19:41,442 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:19:41,442 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:19:52,724 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27],
  "targeted_regions": "high_density_cl, promising_edges, adjacent low-density",
  "strategy_comment": "Balanced exploration of dense clusters and sparse edges"
}
```
2025-06-26 18:19:52,724 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:19:52,724 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51039.0, 路径: [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27]
2025-06-26 18:19:52,728 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27], 'cur_cost': 51039.0}
2025-06-26 18:19:52,728 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 18:19:52,728 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:19:52,728 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:19:52,728 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 102714.0
2025-06-26 18:19:53,229 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:19:53,229 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:19:53,229 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:19:53,238 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:19:53,238 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}, {'tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}, {'tour': array([29,  0, 18, 21, 13, 12,  8, 11,  7, 38, 28, 36, 34, 33, 14, 25, 19,
       39, 41, 17, 15,  3, 24, 23, 37, 27,  6, 10, 32, 40,  2,  9, 20, 22,
       16,  5, 26,  4, 35,  1, 30, 31]), 'cur_cost': 89056.0}, {'tour': [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27], 'cur_cost': 51039.0}, {'tour': array([ 2,  7, 17, 10,  5, 12, 37, 30, 19, 31, 34, 14, 11, 23, 20, 29, 13,
        9, 41, 28,  3,  4, 39, 25, 36, 24, 16, 26,  0, 21, 22, 35, 18,  1,
        8,  6, 33, 32, 38, 15, 40, 27]), 'cur_cost': 102714.0}, {'tour': [0, 5, 12, 19, 26, 33, 7, 14, 21, 28, 35, 3, 10, 17, 24, 31, 38, 2, 9, 16, 23, 30, 37, 1, 8, 15, 22, 29, 36, 4, 11, 18, 25, 32, 39, 6, 13, 20, 27, 34, 41, 40], 'cur_cost': 89780.0}, {'tour': array([36,  0,  3, 15,  9, 30, 33, 17, 13, 37,  6, 29, 25, 28,  2, 23, 19,
       31,  1, 12, 20, 18, 10, 40, 27,  8, 39, 26, 14, 38, 32,  4,  7,  5,
       21, 24, 22, 35, 41, 34, 11, 16]), 'cur_cost': 105276.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([12,  2, 32, 35, 21,  9, 14,  1, 11, 13, 25, 20, 34, 39, 15,  3, 28,
       36, 30, 10, 29,  0, 38, 24, 26,  5,  4, 16, 40,  8, 22, 37, 23, 18,
        6,  7, 41, 27, 17, 33, 19, 31]), 'cur_cost': 108306.0}, {'tour': array([33, 37,  0, 40, 28,  7, 12, 29,  1,  9, 22, 25,  3, 24, 21, 19, 17,
       27, 26, 31, 34, 38, 13,  5, 35, 32,  8, 11, 41, 36, 23, 18, 15,  6,
       10,  2,  4, 39, 16, 20, 14, 30]), 'cur_cost': 89465.0}]
2025-06-26 18:19:53,240 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:19:53,240 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 232, 'skip_rate': 0.03879310344827586, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 223, 'cache_hits': 172, 'similarity_calculations': 3737, 'cache_hit_rate': 0.046026224244046024, 'cache_size': 3565}}
2025-06-26 18:19:53,240 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-26 18:19:53,242 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:19:53,242 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:19:53,242 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 89780.0 (rank 40.0%)
- Population diversity: 0.7853658536585366
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:19:53,242 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:19:53,242 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:20:04,057 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2],
  "targeted_regions": "high_density_cl, promising_edges, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense clusters and sparse edges"
}
```
2025-06-26 18:20:04,057 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:20:04,057 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42554.0, 路径: [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2]
2025-06-26 18:20:04,057 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2], 'cur_cost': 42554.0}
2025-06-26 18:20:04,057 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:20:04,057 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:20:04,057 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:20:04,057 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 124374.0
2025-06-26 18:20:04,559 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:20:04,559 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:20:04,559 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:20:04,568 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:20:04,569 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}, {'tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}, {'tour': array([29,  0, 18, 21, 13, 12,  8, 11,  7, 38, 28, 36, 34, 33, 14, 25, 19,
       39, 41, 17, 15,  3, 24, 23, 37, 27,  6, 10, 32, 40,  2,  9, 20, 22,
       16,  5, 26,  4, 35,  1, 30, 31]), 'cur_cost': 89056.0}, {'tour': [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27], 'cur_cost': 51039.0}, {'tour': array([ 2,  7, 17, 10,  5, 12, 37, 30, 19, 31, 34, 14, 11, 23, 20, 29, 13,
        9, 41, 28,  3,  4, 39, 25, 36, 24, 16, 26,  0, 21, 22, 35, 18,  1,
        8,  6, 33, 32, 38, 15, 40, 27]), 'cur_cost': 102714.0}, {'tour': [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2], 'cur_cost': 42554.0}, {'tour': array([ 6, 27, 12, 28, 32,  5, 11, 16, 13, 36, 40, 25, 21, 37, 26, 10, 31,
       29, 18, 38,  1,  8,  0, 33,  3,  7, 30, 41,  9, 24, 22, 17, 35, 19,
       34, 20, 39, 14,  4,  2, 23, 15]), 'cur_cost': 124374.0}, {'tour': array([ 8, 31, 20, 27, 28, 32,  2, 13,  1, 26,  3,  6, 29, 37, 39,  5, 22,
        7,  4, 36, 12, 19, 38, 33, 25, 17, 10, 21, 23, 40, 35, 16, 14, 24,
       15,  0, 11, 34, 18, 30,  9, 41]), 'cur_cost': 118995.0}, {'tour': array([12,  2, 32, 35, 21,  9, 14,  1, 11, 13, 25, 20, 34, 39, 15,  3, 28,
       36, 30, 10, 29,  0, 38, 24, 26,  5,  4, 16, 40,  8, 22, 37, 23, 18,
        6,  7, 41, 27, 17, 33, 19, 31]), 'cur_cost': 108306.0}, {'tour': array([33, 37,  0, 40, 28,  7, 12, 29,  1,  9, 22, 25,  3, 24, 21, 19, 17,
       27, 26, 31, 34, 38, 13,  5, 35, 32,  8, 11, 41, 36, 23, 18, 15,  6,
       10,  2,  4, 39, 16, 20, 14, 30]), 'cur_cost': 89465.0}]
2025-06-26 18:20:04,571 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:20:04,571 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 233, 'skip_rate': 0.03862660944206009, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 224, 'cache_hits': 172, 'similarity_calculations': 3752, 'cache_hit_rate': 0.04584221748400853, 'cache_size': 3580}}
2025-06-26 18:20:04,571 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:20:04,571 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 18:20:04,571 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:20:04,571 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118995.0 (rank 80.0%)
- Population diversity: 0.7341463414634146
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:20:04,571 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:20:04,571 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:20:16,387 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 17, 8, 31, 14, 26, 39, 2, 20, 33, 7, 41, 10, 29, 15, 37, 4, 18, 30, 9, 24, 36, 1, 13, 27, 38, 3, 16, 32, 6, 21, 34, 11, 25, 40, 19, 35, 22, 28],
  "targeted_regions": "high_density_cl, promising_edges",
  "strategy_comment": "Balanced exploration of dense clusters and sparse edges"
}
```
2025-06-26 18:20:16,387 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:20:16,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 134226.0, 路径: [0, 12, 5, 23, 17, 8, 31, 14, 26, 39, 2, 20, 33, 7, 41, 10, 29, 15, 37, 4, 18, 30, 9, 24, 36, 1, 13, 27, 38, 3, 16, 32, 6, 21, 34, 11, 25, 40, 19, 35, 22, 28]
2025-06-26 18:20:16,387 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 17, 8, 31, 14, 26, 39, 2, 20, 33, 7, 41, 10, 29, 15, 37, 4, 18, 30, 9, 24, 36, 1, 13, 27, 38, 3, 16, 32, 6, 21, 34, 11, 25, 40, 19, 35, 22, 28], 'cur_cost': 134226.0}
2025-06-26 18:20:16,387 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:20:16,392 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:20:16,392 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:20:16,392 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114888.0
2025-06-26 18:20:16,894 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 18:20:16,894 - ExploitationExpert - INFO - res_population_costs: [14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14408, 14409, 14409, 14414, 14423, 14423, 14473]
2025-06-26 18:20:16,895 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  5,  1,  3,  2, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38,
       40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,  6, 13, 12, 17,
       15, 20, 16, 18, 19, 14, 21,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 25,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,
        9, 34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27, 26, 31, 28,
       23, 24, 29, 22,  2,  3,  1,  5], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 25, 26,
       27, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  2,  3,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
       11,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 18, 19,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  3,  2,  1,  5, 10,  7,  8, 11,  9,  6, 13, 12, 14, 21, 19, 18,
       16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25, 27,
       26, 31, 28, 23, 24, 29, 22,  4], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6, 11,
        9,  8,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0,  4, 22, 29, 24, 23, 28, 31, 26, 27, 25, 30, 39, 38, 40, 36, 33,
       37, 32, 35, 34, 41, 17, 15, 20, 16, 18, 19, 21, 14, 12, 13,  6,  9,
        8, 11,  7, 10,  5,  1,  2,  3], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 31, 28, 23, 22, 29, 24, 26, 27,
       25, 30, 39, 38, 40, 36, 33, 37, 32, 41, 35, 34,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 34, 35, 41,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 26, 25, 24, 29, 22, 23, 28, 31,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 10,  7,  8,  9, 11,  6, 13, 12, 14, 21, 18,
       19, 16, 20, 15, 17, 41, 34, 35, 32, 37, 33, 36, 40, 38, 39, 30, 25,
       27, 26, 31, 28, 23, 24, 29, 22], dtype=int64), array([ 0, 22, 29, 24, 23, 28, 31, 26, 25, 27, 30, 39, 38, 40, 36, 33, 37,
       32, 35, 34, 41, 17, 15, 20, 16, 19, 18, 21, 14, 12, 13,  6, 11,  9,
        8,  7, 10,  5,  1,  3,  2,  4], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 12, 13,  6, 11, 10,  7,  8,  9,
       34, 35, 41, 32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23,
       24, 29, 22,  4,  3,  2,  1,  5], dtype=int64), array([ 0, 21, 14, 19, 18, 16, 20, 15, 17, 23, 22, 29, 24, 25, 30, 27, 26,
       28, 31, 39, 38, 40, 36, 33, 37, 32, 35, 34, 41,  9,  8,  7, 10, 11,
        6, 13, 12,  5,  1,  3,  2,  4], dtype=int64), array([ 0,  4,  2,  3,  1,  5, 12, 13,  6, 11, 10,  7,  8,  9, 41, 34, 35,
       32, 37, 33, 36, 40, 38, 39, 30, 27, 25, 26, 31, 28, 23, 24, 29, 22,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64), array([ 0,  4,  3,  2,  1,  5, 10,  7, 11,  8,  9,  6, 13, 12, 41, 34, 35,
       33, 36, 40, 37, 32, 38, 39, 31, 28, 26, 27, 30, 25, 24, 29, 22, 23,
       17, 15, 20, 16, 18, 19, 14, 21], dtype=int64)]
2025-06-26 18:20:16,903 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:20:16,904 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}, {'tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}, {'tour': array([29,  0, 18, 21, 13, 12,  8, 11,  7, 38, 28, 36, 34, 33, 14, 25, 19,
       39, 41, 17, 15,  3, 24, 23, 37, 27,  6, 10, 32, 40,  2,  9, 20, 22,
       16,  5, 26,  4, 35,  1, 30, 31]), 'cur_cost': 89056.0}, {'tour': [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27], 'cur_cost': 51039.0}, {'tour': array([ 2,  7, 17, 10,  5, 12, 37, 30, 19, 31, 34, 14, 11, 23, 20, 29, 13,
        9, 41, 28,  3,  4, 39, 25, 36, 24, 16, 26,  0, 21, 22, 35, 18,  1,
        8,  6, 33, 32, 38, 15, 40, 27]), 'cur_cost': 102714.0}, {'tour': [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2], 'cur_cost': 42554.0}, {'tour': array([ 6, 27, 12, 28, 32,  5, 11, 16, 13, 36, 40, 25, 21, 37, 26, 10, 31,
       29, 18, 38,  1,  8,  0, 33,  3,  7, 30, 41,  9, 24, 22, 17, 35, 19,
       34, 20, 39, 14,  4,  2, 23, 15]), 'cur_cost': 124374.0}, {'tour': [0, 12, 5, 23, 17, 8, 31, 14, 26, 39, 2, 20, 33, 7, 41, 10, 29, 15, 37, 4, 18, 30, 9, 24, 36, 1, 13, 27, 38, 3, 16, 32, 6, 21, 34, 11, 25, 40, 19, 35, 22, 28], 'cur_cost': 134226.0}, {'tour': array([25, 15,  9, 30, 17, 24, 13, 28,  6, 41, 38, 14, 19,  4, 36, 26, 22,
       33, 12, 10, 37, 29, 11, 18,  5, 23,  0,  8, 32, 40,  3, 34, 31, 35,
        2,  1, 21, 27,  7, 39, 20, 16]), 'cur_cost': 114888.0}, {'tour': array([33, 37,  0, 40, 28,  7, 12, 29,  1,  9, 22, 25,  3, 24, 21, 19, 17,
       27, 26, 31, 34, 38, 13,  5, 35, 32,  8, 11, 41, 36, 23, 18, 15,  6,
       10,  2,  4, 39, 16, 20, 14, 30]), 'cur_cost': 89465.0}]
2025-06-26 18:20:16,906 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:20:16,906 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 234, 'skip_rate': 0.038461538461538464, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 225, 'cache_hits': 172, 'similarity_calculations': 3768, 'cache_hit_rate': 0.045647558386411886, 'cache_size': 3596}}
2025-06-26 18:20:16,906 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:20:16,907 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-26 18:20:16,907 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:20:16,907 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 89465.0 (rank 40.0%)
- Population diversity: 0.7146341463414634
- Evolution phase: Mid-exploration (iteration 4/5 with improving but still diverse solutions)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 41]  
• Path length == 42 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:20:16,907 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:20:16,907 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:20:28,352 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 14, 35, 41, 3, 9, 22, 16, 31, 38, 2, 10, 25, 19, 33, 8, 20, 36, 1, 13, 26, 39, 4, 15, 28, 37, 6, 21, 30, 17, 34, 11, 24, 40, 27, 32],
  "targeted_regions": "high_density_cl, promising_edges",
  "strategy_comment": "Prioritize dense clusters while exploring adjacent sparse edges"
}
```
2025-06-26 18:20:28,352 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:20:28,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 128857.0, 路径: [0, 12, 5, 18, 23, 7, 29, 14, 35, 41, 3, 9, 22, 16, 31, 38, 2, 10, 25, 19, 33, 8, 20, 36, 1, 13, 26, 39, 4, 15, 28, 37, 6, 21, 30, 17, 34, 11, 24, 40, 27, 32]
2025-06-26 18:20:28,358 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 41, 3, 9, 22, 16, 31, 38, 2, 10, 25, 19, 33, 8, 20, 36, 1, 13, 26, 39, 4, 15, 28, 37, 6, 21, 30, 17, 34, 11, 24, 40, 27, 32], 'cur_cost': 128857.0}
2025-06-26 18:20:28,358 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 36, 41, 3, 17, 9, 25, 31, 38, 20, 2, 11, 26, 34, 8, 22, 37, 15, 4, 19, 30, 6, 21, 33, 10, 24, 39, 16, 1, 13, 27, 35, 32, 28, 40], 'cur_cost': 114082.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 8, 17, 20, 23, 26, 29, 32, 35, 38, 41, 39, 36, 33, 30, 27, 24, 21, 18, 15, 9, 6, 3, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 14, 11, 2], 'cur_cost': 41956.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 8, 19, 22, 31, 35, 38, 41, 39, 36, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 40, 34, 30, 26, 23, 18, 15, 11, 7, 3, 27], 'cur_cost': 51039.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 8, 19, 23, 27, 30, 33, 36, 39, 41, 38, 35, 32, 29, 26, 22, 18, 15, 11, 7, 3, 1, 4, 9, 13, 16, 20, 24, 28, 31, 34, 37, 40, 25, 21, 17, 14, 10, 6, 2], 'cur_cost': 42554.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 17, 8, 31, 14, 26, 39, 2, 20, 33, 7, 41, 10, 29, 15, 37, 4, 18, 30, 9, 24, 36, 1, 13, 27, 38, 3, 16, 32, 6, 21, 34, 11, 25, 40, 19, 35, 22, 28], 'cur_cost': 134226.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 35, 41, 3, 9, 22, 16, 31, 38, 2, 10, 25, 19, 33, 8, 20, 36, 1, 13, 26, 39, 4, 15, 28, 37, 6, 21, 30, 17, 34, 11, 24, 40, 27, 32], 'cur_cost': 128857.0}}]
2025-06-26 18:20:28,358 - __main__ - INFO - 进化阶段完成
2025-06-26 18:20:28,358 - __main__ - INFO - 开始评估阶段
2025-06-26 18:20:28,358 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:20:28,360 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:20:28,360 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 53337.0 → 41956.0 (Δ 11381.0)
- Mean cost: 94800.2 → 94374.6
- Diversity: 0.710 → 0.707 (Δ -0.0024)

#### Elite metrics
- Elite count: 26 → 26
- Best elite cost: 14408 → 14408 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 16890.5
- Exploit: 4 inds, success 0.0% , avg Δ -24271.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [67003.0, 79778.0, 53337.0], diversity [0.8455026455026454, 0.8703703703703705, 0.9089947089947091]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:20:28,360 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:20:40,153 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 16890.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -24271.75,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy (effective but exploit failing)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_retry_threshold": 0.2,
    "mutation_intensity": "increase for exploit attempts"
  }
}
```
2025-06-26 18:20:40,169 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:20:40,169 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 16890.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -24271.75,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy (effective but exploit failing)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_retry_threshold": 0.2,
    "mutation_intensity": "increase for exploit attempts"
  }
}
```
2025-06-26 18:20:40,170 - __main__ - INFO - 评估阶段完成
2025-06-26 18:20:40,170 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 16890.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -24271.75,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy (effective but exploit failing)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_retry_threshold": 0.2,
    "mutation_intensity": "increase for exploit attempts"
  }
}
```
2025-06-26 18:20:40,170 - __main__ - INFO - 当前最佳适应度: 41956.0
2025-06-26 18:20:40,172 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_route_4.pkl
2025-06-26 18:20:40,180 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite7_42_solution.json
2025-06-26 18:20:40,180 - __main__ - INFO - 实例 composite7_42 处理完成
