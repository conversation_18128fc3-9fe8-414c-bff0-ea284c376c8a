2025-07-03 17:15:28,931 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 17:15:28,931 - __main__ - INFO - 开始分析阶段
2025-07-03 17:15:28,931 - StatsExpert - INFO - 开始统计分析
2025-07-03 17:15:28,951 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9964.0, 'max': 117470.0, 'mean': 79404.7, 'std': 45718.41983282012}, 'diversity': 0.9205387205387205, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 17:15:28,952 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9964.0, 'max': 117470.0, 'mean': 79404.7, 'std': 45718.41983282012}, 'diversity_level': 0.9205387205387205, 'convergence_level': 0.0, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 17:15:28,953 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 17:15:28,954 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 17:15:28,954 - PathExpert - INFO - 开始路径结构分析
2025-07-03 17:15:28,959 - PathExpert - INFO - 路径结构分析完成
2025-07-03 17:15:28,960 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (25, 26), 'frequency': 0.5, 'avg_cost': 12.0}], 'common_subpaths': [{'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (57, 54, 65), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(12, 17)', 'frequency': 0.4}, {'edge': '(25, 26)', 'frequency': 0.5}, {'edge': '(28, 35)', 'frequency': 0.4}, {'edge': '(44, 45)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(39, 63)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.3}, {'edge': '(30, 52)', 'frequency': 0.3}, {'edge': '(55, 59)', 'frequency': 0.2}, {'edge': '(37, 43)', 'frequency': 0.2}, {'edge': '(39, 48)', 'frequency': 0.2}, {'edge': '(17, 29)', 'frequency': 0.3}, {'edge': '(29, 60)', 'frequency': 0.2}, {'edge': '(19, 61)', 'frequency': 0.2}, {'edge': '(7, 33)', 'frequency': 0.2}, {'edge': '(10, 41)', 'frequency': 0.2}, {'edge': '(41, 49)', 'frequency': 0.2}, {'edge': '(21, 65)', 'frequency': 0.2}, {'edge': '(17, 28)', 'frequency': 0.2}, {'edge': '(51, 64)', 'frequency': 0.2}, {'edge': '(33, 46)', 'frequency': 0.2}, {'edge': '(38, 53)', 'frequency': 0.2}, {'edge': '(8, 39)', 'frequency': 0.2}, {'edge': '(2, 60)', 'frequency': 0.2}, {'edge': '(15, 47)', 'frequency': 0.2}, {'edge': '(3, 50)', 'frequency': 0.2}, {'edge': '(13, 32)', 'frequency': 0.2}, {'edge': '(32, 49)', 'frequency': 0.2}, {'edge': '(49, 64)', 'frequency': 0.2}, {'edge': '(14, 24)', 'frequency': 0.2}, {'edge': '(22, 27)', 'frequency': 0.2}, {'edge': '(31, 56)', 'frequency': 0.2}, {'edge': '(45, 53)', 'frequency': 0.2}, {'edge': '(46, 53)', 'frequency': 0.2}, {'edge': '(7, 57)', 'frequency': 0.2}, {'edge': '(30, 38)', 'frequency': 0.2}, {'edge': '(22, 59)', 'frequency': 0.2}, {'edge': '(13, 59)', 'frequency': 0.2}, {'edge': '(6, 20)', 'frequency': 0.2}, {'edge': '(11, 30)', 'frequency': 0.2}, {'edge': '(2, 19)', 'frequency': 0.2}, {'edge': '(24, 56)', 'frequency': 0.2}, {'edge': '(13, 47)', 'frequency': 0.2}, {'edge': '(18, 32)', 'frequency': 0.2}, {'edge': '(2, 35)', 'frequency': 0.2}, {'edge': '(22, 43)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [58, 45, 53, 46, 57, 28], 'cost': 14270.0, 'size': 6}, {'region': [55, 45, 53, 46, 52], 'cost': 11305.0, 'size': 5}, {'region': [62, 35, 53, 38, 1], 'cost': 11298.0, 'size': 5}, {'region': [2, 38, 30, 65, 25], 'cost': 10614.0, 'size': 5}, {'region': [57, 29, 52, 32], 'cost': 8979.0, 'size': 4}]}
2025-07-03 17:15:28,961 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 17:15:28,961 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 17:15:28,961 - EliteExpert - INFO - 开始精英解分析
2025-07-03 17:15:28,961 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 17:15:28,961 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 17:15:28,962 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 17:15:28,962 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 17:15:29,526 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 17:15:29,526 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9964.0, mean 79404.7, max 117470.0, std 45718.41983282012
- diversity: 0.9205387205387205
- convergence: 0.0
- clustering: 8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (25, 26), 'frequency': 0.5, 'avg_cost': 12.0}]
- common_subpaths_sample: [{'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [58, 45, 53, 46, 57, 28], 'cost': 14270.0, 'size': 6}, {'region': [55, 45, 53, 46, 52], 'cost': 11305.0, 'size': 5}, {'region': [62, 35, 53, 38, 1], 'cost': 11298.0, 'size': 5}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 17:15:29,528 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:15:43,349 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9205387205387205,
    "convergence": 0.0,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6},
    {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5},
    {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}
  ],
  "opportunity_regions": [],
  "evolution_phase": "initial",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation", "local_search"]
  }
}
```
2025-07-03 17:15:43,349 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 17:15:43,349 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9205387205387205, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [58, 45, 53, 46, 57, 28], 'cost': 14270.0, 'size': 6}, {'region': [55, 45, 53, 46, 52], 'cost': 11305.0, 'size': 5}, {'region': [62, 35, 53, 38, 1], 'cost': 11298.0, 'size': 5}], 'opportunity_regions': [], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation', 'local_search']}}
2025-07-03 17:15:43,350 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 17:15:43,350 - __main__ - INFO - 分析阶段完成
2025-07-03 17:15:43,350 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9205387205387205, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [58, 45, 53, 46, 57, 28], 'cost': 14270.0, 'size': 6}, {'region': [55, 45, 53, 46, 52], 'cost': 11305.0, 'size': 5}, {'region': [62, 35, 53, 38, 1], 'cost': 11298.0, 'size': 5}], 'opportunity_regions': [], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation', 'local_search']}}
2025-07-03 17:15:43,350 - __main__ - INFO - 开始策略分配阶段
2025-07-03 17:15:43,350 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 17:15:43,351 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.921
  • best_cost: 9964.0
  • mean_cost: 79404.7
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: initial
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [58, 45, , {'region': [55, 45, ...
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 17:15:43,351 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 17:15:43,351 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:15:52,721 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; majority explore to discover new regions."
}
```
2025-07-03 17:15:52,721 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:15:52,721 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:15:52,722 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; majority explore to discover new regions."
}
```
2025-07-03 17:15:52,722 - __main__ - INFO - 策略分配阶段完成
2025-07-03 17:15:52,722 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:15:52,722 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; majority explore to discover new regions."
}
```
2025-07-03 17:15:52,723 - __main__ - INFO - 开始进化阶段
2025-07-03 17:15:52,723 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 17:15:52,723 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:15:52,723 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10255.0 (rank 20.0%)
- Population diversity: 0.74
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:15:52,724 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:15:52,724 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:16:07,282 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse_cells(14-42)",
  "strategy_comment": "Prioritizes unexplored mid-path regions, avoids high-cost clusters"
}
```
2025-07-03 17:16:07,282 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:16:07,300 - ExplorationKnowledgeBase - INFO - 已保存14条知识条目
2025-07-03 17:16:07,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10328.0, 路径: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 17:16:07,305 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}
2025-07-03 17:16:07,305 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 17:16:07,305 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:16:07,306 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10045.0 (rank 10.0%)
- Population diversity: 0.7523076923076923
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:16:07,306 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:16:07,307 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:16:21,145 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1],
  "targeted_regions": "avoid_difficult_regions",
  "strategy_comment": "Reordered to avoid costly regions and maintain diversity"
}
```
2025-07-03 17:16:21,146 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:16:21,164 - ExplorationKnowledgeBase - INFO - 已保存15条知识条目
2025-07-03 17:16:21,167 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10045.0, 路径: [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1]
2025-07-03 17:16:21,168 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}
2025-07-03 17:16:21,168 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 17:16:21,168 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:16:21,169 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9964.0 (rank 0.0%)
- Population diversity: 0.7538461538461538
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:16:21,170 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:16:21,170 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:16:35,508 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
  "targeted_regions": "unexplored_cells(65,54,57)",
  "strategy_comment": "Prioritizes sparse cells while avoiding high-cost regions."
}
```
2025-07-03 17:16:35,509 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:16:35,528 - ExplorationKnowledgeBase - INFO - 已保存16条知识条目
2025-07-03 17:16:35,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10059.0, 路径: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
2025-07-03 17:16:35,532 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}
2025-07-03 17:16:35,532 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 17:16:35,533 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:16:35,533 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100420.0 (rank 40.0%)
- Population diversity: 0.7553846153846154
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:16:35,534 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:16:35,534 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:16:48,913 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58],
  "targeted_regions": "sparse_cells",
  "strategy_comment": "Explores sparse cells while avoiding difficult regions"
}
```
2025-07-03 17:16:48,913 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:16:48,932 - ExplorationKnowledgeBase - INFO - 已保存17条知识条目
2025-07-03 17:16:48,935 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100420.0, 路径: [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58]
2025-07-03 17:16:48,936 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}
2025-07-03 17:16:48,936 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 17:16:48,936 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:16:48,937 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100247.0 (rank 30.0%)
- Population diversity: 0.7553846153846154
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 55, 32, 27, 16, 30, 58, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:16:48,937 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:16:48,938 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:17:02,698 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58],
  "targeted_regions": "avoided difficult regions [58,55]",
  "strategy_comment": "Reordered to skip high-cost clusters while maintaining diversity"
}
```
2025-07-03 17:17:02,698 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:17:02,717 - ExplorationKnowledgeBase - INFO - 已保存18条知识条目
2025-07-03 17:17:02,720 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95802.0, 路径: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58]
2025-07-03 17:17:02,721 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}
2025-07-03 17:17:02,721 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 17:17:02,721 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:17:02,722 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116490.0 (rank 80.0%)
- Population diversity: 0.7569230769230769
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:17:02,723 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:17:02,723 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:17:15,935 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9],
  "targeted_regions": "sparse_cells",
  "strategy_comment": "Reordered to explore sparse cells while avoiding difficult regions"
}
```
2025-07-03 17:17:15,936 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:17:15,954 - ExplorationKnowledgeBase - INFO - 已保存19条知识条目
2025-07-03 17:17:15,958 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116490.0, 路径: [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9]
2025-07-03 17:17:15,958 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}
2025-07-03 17:17:15,959 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 17:17:15,959 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:17:15,960 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114155.0 (rank 70.0%)
- Population diversity: 0.7569230769230769
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [58,, {'region': [55,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- Full current path sequence: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 55, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [58, 45, 53, 46, 57, 28], "cost": 14270.0, "size": 6}, {"region": [55, 45, 53, 46, 52], "cost": 11305.0, "size": 5}, {"region": [62, 35, 53, 38, 1], "cost": 11298.0, "size": 5}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:17:15,960 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:17:15,961 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:17:29,978 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55],
  "targeted_regions": "avoided [58,45,53,46,57,28]",
  "strategy_comment": "Reordered to exclude high-cost cluster"
}
```
2025-07-03 17:17:29,979 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:17:29,998 - ExplorationKnowledgeBase - INFO - 已保存20条知识条目
2025-07-03 17:17:30,001 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112898.0, 路径: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55]
2025-07-03 17:17:30,001 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}
2025-07-03 17:17:30,001 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 17:17:30,002 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:17:30,003 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:17:30,004 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 93230.0
2025-07-03 17:17:32,671 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 17:17:32,671 - ExploitationExpert - INFO - res_population_costs: [95277]
2025-07-03 17:17:32,671 - ExploitationExpert - INFO - res_populations: [array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64)]
2025-07-03 17:17:32,672 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:17:32,672 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([ 7, 57, 30, 41,  5, 60, 50, 43, 11, 53, 10, 26, 24, 16, 47, 20, 38,
        6, 27,  4, 51, 21, 46, 32, 29, 13, 18, 35, 22, 39, 44, 63, 61,  0,
       54, 17, 25, 45, 31, 48, 42, 65, 36,  2, 12, 37,  3,  9, 19, 23, 56,
       49, 62,  8, 40, 55, 58, 59, 64,  1, 52, 34, 28, 33, 14, 15]), 'cur_cost': 93230.0}, {'tour': [3, 5, 49, 56, 24, 35, 2, 19, 45, 26, 42, 44, 61, 13, 47, 12, 60, 48, 0, 53, 8, 14, 54, 18, 57, 29, 52, 32, 11, 59, 58, 64, 9, 28, 51, 17, 15, 10, 41, 7, 33, 65, 39, 63, 55, 6, 62, 4, 16, 50, 21, 40, 25, 22, 43, 1, 31, 34, 20, 27, 46, 23, 30, 38, 37, 36], 'cur_cost': 108056.0}, {'tour': [65, 44, 50, 64, 51, 10, 61, 0, 26, 19, 1, 15, 24, 14, 7, 36, 39, 49, 37, 48, 42, 27, 54, 25, 4, 31, 41, 21, 62, 9, 5, 47, 63, 16, 3, 6, 58, 45, 53, 46, 57, 28, 17, 34, 2, 35, 56, 8, 55, 38, 52, 30, 11, 33, 18, 32, 13, 12, 40, 59, 22, 43, 60, 29, 23, 20], 'cur_cost': 117470.0}]
2025-07-03 17:17:32,674 - ExploitationExpert - INFO - 局部搜索耗时: 2.67秒
2025-07-03 17:17:32,674 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 17:17:32,675 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 17:17:32,675 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 17:17:32,675 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:17:32,675 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:17:32,676 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105279.0
2025-07-03 17:17:33,784 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 17:17:33,784 - ExploitationExpert - INFO - res_population_costs: [95277, 9602]
2025-07-03 17:17:33,784 - ExploitationExpert - INFO - res_populations: [array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-07-03 17:17:33,786 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:17:33,786 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([ 7, 57, 30, 41,  5, 60, 50, 43, 11, 53, 10, 26, 24, 16, 47, 20, 38,
        6, 27,  4, 51, 21, 46, 32, 29, 13, 18, 35, 22, 39, 44, 63, 61,  0,
       54, 17, 25, 45, 31, 48, 42, 65, 36,  2, 12, 37,  3,  9, 19, 23, 56,
       49, 62,  8, 40, 55, 58, 59, 64,  1, 52, 34, 28, 33, 14, 15]), 'cur_cost': 93230.0}, {'tour': array([65,  3,  6, 26, 22, 25,  4, 42, 24,  1,  5, 57, 45, 18,  7,  0, 64,
       12, 51,  9, 37, 14, 17, 21, 30, 40, 58, 27, 31, 61, 46, 34, 55, 10,
       16, 23, 36, 52, 41, 33, 49, 38, 29, 19, 62, 48, 32, 39, 50, 43, 13,
       15, 53, 60, 20, 35,  8, 56, 44, 54, 63, 11, 47,  2, 59, 28]), 'cur_cost': 105279.0}, {'tour': [65, 44, 50, 64, 51, 10, 61, 0, 26, 19, 1, 15, 24, 14, 7, 36, 39, 49, 37, 48, 42, 27, 54, 25, 4, 31, 41, 21, 62, 9, 5, 47, 63, 16, 3, 6, 58, 45, 53, 46, 57, 28, 17, 34, 2, 35, 56, 8, 55, 38, 52, 30, 11, 33, 18, 32, 13, 12, 40, 59, 22, 43, 60, 29, 23, 20], 'cur_cost': 117470.0}]
2025-07-03 17:17:33,787 - ExploitationExpert - INFO - 局部搜索耗时: 1.11秒
2025-07-03 17:17:33,787 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 17:17:33,787 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 17:17:33,788 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 17:17:33,788 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:17:33,788 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:17:33,788 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 106775.0
2025-07-03 17:17:34,291 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 17:17:34,291 - ExploitationExpert - INFO - res_population_costs: [95277, 9602, 9573, 9552, 9552, 9551, 9542, 9542, 9541, 9532, 9524, 9523, 9521, 9521, 9521]
2025-07-03 17:17:34,292 - ExploitationExpert - INFO - res_populations: [array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 17:17:34,298 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:17:34,298 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([ 7, 57, 30, 41,  5, 60, 50, 43, 11, 53, 10, 26, 24, 16, 47, 20, 38,
        6, 27,  4, 51, 21, 46, 32, 29, 13, 18, 35, 22, 39, 44, 63, 61,  0,
       54, 17, 25, 45, 31, 48, 42, 65, 36,  2, 12, 37,  3,  9, 19, 23, 56,
       49, 62,  8, 40, 55, 58, 59, 64,  1, 52, 34, 28, 33, 14, 15]), 'cur_cost': 93230.0}, {'tour': array([65,  3,  6, 26, 22, 25,  4, 42, 24,  1,  5, 57, 45, 18,  7,  0, 64,
       12, 51,  9, 37, 14, 17, 21, 30, 40, 58, 27, 31, 61, 46, 34, 55, 10,
       16, 23, 36, 52, 41, 33, 49, 38, 29, 19, 62, 48, 32, 39, 50, 43, 13,
       15, 53, 60, 20, 35,  8, 56, 44, 54, 63, 11, 47,  2, 59, 28]), 'cur_cost': 105279.0}, {'tour': array([32, 53, 27, 46, 10, 43, 54, 41,  2,  4, 49, 45, 11, 59, 58, 52,  9,
       22, 50, 65, 20, 25, 61,  3, 24, 12, 13, 37, 28,  1, 30, 21, 60,  7,
       40, 44, 56, 31, 17,  0, 62, 63, 19, 47, 29, 36, 16,  8, 39, 64, 55,
       38, 35, 23, 34, 57, 48,  5, 14, 18,  6, 42, 33, 26, 51, 15]), 'cur_cost': 106775.0}]
2025-07-03 17:17:34,300 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:17:34,300 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-03 17:17:34,301 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 17:17:34,301 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}}]
2025-07-03 17:17:34,301 - __main__ - INFO - 进化阶段完成
2025-07-03 17:17:34,301 - __main__ - INFO - 开始评估阶段
2025-07-03 17:17:34,302 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 17:17:34,302 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 17:17:34,304 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9964.0 → 10045.0 (Δ -81.0)
- Mean cost: 79404.7 → 76132.6
- Diversity: 0.740 → 0.762 (Δ 0.0215)

#### Elite metrics
- Elite count: 0 → 15
- Best elite cost: 9964.0 → 9521 (Δ 443.0)
- Elite diversity: 0.000 → 0.187 (Δ 0.1867)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ 790.57
- Exploit: 3 inds, success 100.0% , avg Δ 9062.33

#### Other indicators
- No-change individuals: 3
- Regression individuals: 2
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 17:17:34,304 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:17:41,838 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:17:41,869 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 17:17:41,869 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:17:41,870 - __main__ - INFO - 评估阶段完成
2025-07-03 17:17:41,870 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:17:41,870 - __main__ - INFO - 当前最佳适应度: 10045.0
2025-07-03 17:17:41,872 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 17:17:41,872 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 17:17:41,873 - __main__ - INFO - 开始分析阶段
2025-07-03 17:17:41,873 - StatsExpert - INFO - 开始统计分析
2025-07-03 17:17:41,891 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10045.0, 'max': 116490.0, 'mean': 76132.6, 'std': 43705.26825955882}, 'diversity': 0.9276094276094276, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 17:17:41,892 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10045.0, 'max': 116490.0, 'mean': 76132.6, 'std': 43705.26825955882}, 'diversity_level': 0.9276094276094276, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 17:17:41,892 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 17:17:41,892 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 17:17:41,892 - PathExpert - INFO - 开始路径结构分析
2025-07-03 17:17:41,895 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 17:17:41,895 - PathExpert - INFO - 路径结构分析完成
2025-07-03 17:17:41,895 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 17:17:41,896 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 17:17:41,896 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 17:17:41,896 - EliteExpert - INFO - 开始精英解分析
2025-07-03 17:17:41,901 - EliteExpert - INFO - 精英解分析完成
2025-07-03 17:17:41,901 - __main__ - INFO - 精英专家分析报告: {'elite_count': 15, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 524.0, 'avg_gap': 60874.33333333334}, 'structure_gap': {'unique_elite_edges': 177, 'unique_pop_edges': 444, 'common_edges': 103}}, 'elite_diversity': {'diversity_score': 0.3464646464646465}}
2025-07-03 17:17:41,902 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 17:17:41,902 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 17:17:41,904 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 17:17:41,904 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 10045.0, mean 76132.6, max 116490.0, std 43705.26825955882
- diversity: 0.9276094276094276
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10045.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.3464646464646465}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 17:17:41,905 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:17:56,012 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9276094276094276,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"
    }
  ],
  "evolution_phase": "initial",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "increase population size to improve coverage",
      "use spatial-aware crossover to exploit high-density regions",
      "apply local search in low-density regions to improve connectivity"
    ]
  }
}
```
2025-07-03 17:17:56,013 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 17:17:56,013 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9276094276094276, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)'}], 'opportunity_regions': [{'description': 'High-density cells in the grid (e.g., [0,0], [2,0], [2,2])'}], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase population size to improve coverage', 'use spatial-aware crossover to exploit high-density regions', 'apply local search in low-density regions to improve connectivity']}}
2025-07-03 17:17:56,013 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 17:17:56,014 - __main__ - INFO - 分析阶段完成
2025-07-03 17:17:56,014 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9276094276094276, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)'}], 'opportunity_regions': [{'description': 'High-density cells in the grid (e.g., [0,0], [2,0], [2,2])'}], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase population size to improve coverage', 'use spatial-aware crossover to exploit high-density regions', 'apply local search in low-density regions to improve connectivity']}}
2025-07-03 17:17:56,014 - __main__ - INFO - 开始策略分配阶段
2025-07-03 17:17:56,014 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 17:17:56,014 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.928
  • best_cost: 10045.0
  • mean_cost: 76132.6
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: initial
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Low
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 1.0},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 17:17:56,015 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 17:17:56,015 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:18:04,870 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top 30% exploit."
}
```
2025-07-03 17:18:04,871 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:18:04,871 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:18:04,871 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top 30% exploit."
}
```
2025-07-03 17:18:04,872 - __main__ - INFO - 策略分配阶段完成
2025-07-03 17:18:04,872 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 17:18:04,872 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top 30% exploit."
}
```
2025-07-03 17:18:04,873 - __main__ - INFO - 开始进化阶段
2025-07-03 17:18:04,873 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 17:18:04,873 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:18:04,874 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10328.0 (rank 20.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:18:04,874 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:18:04,874 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:18:19,049 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Explores dense cells while maintaining diversity."
}
```
2025-07-03 17:18:19,050 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:18:19,070 - ExplorationKnowledgeBase - INFO - 已保存21条知识条目
2025-07-03 17:18:19,073 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10328.0, 路径: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 17:18:19,073 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}
2025-07-03 17:18:19,074 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 17:18:19,074 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:18:19,075 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10045.0 (rank 0.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:18:19,075 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:18:19,076 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:18:32,883 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Focus on dense cells while avoiding low-density areas."
}
```
2025-07-03 17:18:32,883 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:18:32,902 - ExplorationKnowledgeBase - INFO - 已保存22条知识条目
2025-07-03 17:18:32,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10045.0, 路径: [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1]
2025-07-03 17:18:32,906 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}
2025-07-03 17:18:32,906 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 17:18:32,906 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:18:32,907 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10059.0 (rank 10.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:18:32,907 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:18:32,907 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:18:46,351 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Focuses on dense cells while avoiding low-density areas."
}
```
2025-07-03 17:18:46,352 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:18:46,371 - ExplorationKnowledgeBase - INFO - 已保存23条知识条目
2025-07-03 17:18:46,374 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10059.0, 路径: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
2025-07-03 17:18:46,375 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}
2025-07-03 17:18:46,376 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 17:18:46,376 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:18:46,377 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100420.0 (rank 50.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:18:46,377 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:18:46,377 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:18:59,336 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Prioritizes dense regions while avoiding long edges."
}
```
2025-07-03 17:18:59,337 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:18:59,347 - ExplorationKnowledgeBase - INFO - 已保存24条知识条目
2025-07-03 17:18:59,350 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100420.0, 路径: [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58]
2025-07-03 17:18:59,351 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}
2025-07-03 17:18:59,351 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 17:18:59,352 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:18:59,352 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95802.0 (rank 40.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:18:59,353 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:18:59,353 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:19:12,851 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Prioritizes dense regions while avoiding long edges."
}
```
2025-07-03 17:19:12,852 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:19:12,869 - ExplorationKnowledgeBase - INFO - 已保存25条知识条目
2025-07-03 17:19:12,873 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95802.0, 路径: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58]
2025-07-03 17:19:12,874 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}
2025-07-03 17:19:12,874 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 17:19:12,875 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:19:12,875 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116490.0 (rank 90.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:19:12,876 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:19:12,876 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:19:27,106 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Explores dense cells while avoiding low-density areas."
}
```
2025-07-03 17:19:27,106 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:19:27,126 - ExplorationKnowledgeBase - INFO - 已保存26条知识条目
2025-07-03 17:19:27,128 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116490.0, 路径: [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9]
2025-07-03 17:19:27,130 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}
2025-07-03 17:19:27,130 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 17:19:27,130 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:19:27,131 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112898.0 (rank 80.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: initial
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0]) and long-edge corridors (long_edge_ratio: 0.249)"}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:19:27,132 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:19:27,132 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:19:41,743 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Optimized dense regions with minimal long-edge corridors."
}
```
2025-07-03 17:19:41,743 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:19:41,762 - ExplorationKnowledgeBase - INFO - 已保存27条知识条目
2025-07-03 17:19:41,766 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112898.0, 路径: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55]
2025-07-03 17:19:41,767 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}
2025-07-03 17:19:41,767 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 17:19:41,767 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:19:41,768 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:19:41,768 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 95520.0
2025-07-03 17:19:42,270 - ExploitationExpert - INFO - res_population_num: 19
2025-07-03 17:19:42,270 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277, 9521, 9521, 9521, 9521]
2025-07-03 17:19:42,270 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 17:19:42,279 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:19:42,279 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([44,  5, 62, 19, 12, 29, 48, 21,  4, 54, 40, 41, 60, 11, 39, 65, 10,
       61, 49, 51, 46, 31, 37, 36, 18, 30, 15, 22,  9, 57, 52, 27, 63, 56,
       23, 35, 38, 25,  6, 28, 50, 42, 26,  2, 20, 16, 24, 32, 58, 47,  8,
       55, 43, 34, 14, 13, 45,  0, 64, 17, 33,  1,  3, 59,  7, 53]), 'cur_cost': 95520.0}, {'tour': array([65,  3,  6, 26, 22, 25,  4, 42, 24,  1,  5, 57, 45, 18,  7,  0, 64,
       12, 51,  9, 37, 14, 17, 21, 30, 40, 58, 27, 31, 61, 46, 34, 55, 10,
       16, 23, 36, 52, 41, 33, 49, 38, 29, 19, 62, 48, 32, 39, 50, 43, 13,
       15, 53, 60, 20, 35,  8, 56, 44, 54, 63, 11, 47,  2, 59, 28]), 'cur_cost': 105279.0}, {'tour': array([32, 53, 27, 46, 10, 43, 54, 41,  2,  4, 49, 45, 11, 59, 58, 52,  9,
       22, 50, 65, 20, 25, 61,  3, 24, 12, 13, 37, 28,  1, 30, 21, 60,  7,
       40, 44, 56, 31, 17,  0, 62, 63, 19, 47, 29, 36, 16,  8, 39, 64, 55,
       38, 35, 23, 34, 57, 48,  5, 14, 18,  6, 42, 33, 26, 51, 15]), 'cur_cost': 106775.0}]
2025-07-03 17:19:42,281 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:19:42,281 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-03 17:19:42,281 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 17:19:42,281 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 17:19:42,282 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:19:42,282 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:19:42,283 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 104050.0
2025-07-03 17:19:43,806 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 17:19:43,807 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 17:19:43,807 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 17:19:43,815 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:19:43,815 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([44,  5, 62, 19, 12, 29, 48, 21,  4, 54, 40, 41, 60, 11, 39, 65, 10,
       61, 49, 51, 46, 31, 37, 36, 18, 30, 15, 22,  9, 57, 52, 27, 63, 56,
       23, 35, 38, 25,  6, 28, 50, 42, 26,  2, 20, 16, 24, 32, 58, 47,  8,
       55, 43, 34, 14, 13, 45,  0, 64, 17, 33,  1,  3, 59,  7, 53]), 'cur_cost': 95520.0}, {'tour': array([ 4, 33, 44, 10, 18, 20, 29, 41, 14, 26,  7, 42, 16,  0, 17, 48, 61,
        6,  5, 39, 54, 11, 62, 64,  9, 38, 55, 24, 47, 13, 36, 25, 19, 49,
       60, 65, 50, 63, 23, 43, 59, 51, 27, 32, 37, 15, 57, 31,  2, 52, 12,
       56,  8, 45, 46, 21, 28, 58, 53,  3, 35, 30, 34,  1, 40, 22]), 'cur_cost': 104050.0}, {'tour': array([32, 53, 27, 46, 10, 43, 54, 41,  2,  4, 49, 45, 11, 59, 58, 52,  9,
       22, 50, 65, 20, 25, 61,  3, 24, 12, 13, 37, 28,  1, 30, 21, 60,  7,
       40, 44, 56, 31, 17,  0, 62, 63, 19, 47, 29, 36, 16,  8, 39, 64, 55,
       38, 35, 23, 34, 57, 48,  5, 14, 18,  6, 42, 33, 26, 51, 15]), 'cur_cost': 106775.0}]
2025-07-03 17:19:43,817 - ExploitationExpert - INFO - 局部搜索耗时: 1.54秒
2025-07-03 17:19:43,817 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-03 17:19:43,817 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 17:19:43,817 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 17:19:43,817 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:19:43,818 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:19:43,818 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104215.0
2025-07-03 17:19:44,321 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 17:19:44,321 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 17:19:44,322 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 17:19:44,329 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:19:44,329 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([44,  5, 62, 19, 12, 29, 48, 21,  4, 54, 40, 41, 60, 11, 39, 65, 10,
       61, 49, 51, 46, 31, 37, 36, 18, 30, 15, 22,  9, 57, 52, 27, 63, 56,
       23, 35, 38, 25,  6, 28, 50, 42, 26,  2, 20, 16, 24, 32, 58, 47,  8,
       55, 43, 34, 14, 13, 45,  0, 64, 17, 33,  1,  3, 59,  7, 53]), 'cur_cost': 95520.0}, {'tour': array([ 4, 33, 44, 10, 18, 20, 29, 41, 14, 26,  7, 42, 16,  0, 17, 48, 61,
        6,  5, 39, 54, 11, 62, 64,  9, 38, 55, 24, 47, 13, 36, 25, 19, 49,
       60, 65, 50, 63, 23, 43, 59, 51, 27, 32, 37, 15, 57, 31,  2, 52, 12,
       56,  8, 45, 46, 21, 28, 58, 53,  3, 35, 30, 34,  1, 40, 22]), 'cur_cost': 104050.0}, {'tour': array([14,  1, 57, 16,  4,  3, 38, 27, 34, 48, 33, 11,  8, 18, 22, 42,  0,
       53, 24, 21, 13, 56, 59, 45,  9, 60, 39, 51, 29, 28, 44,  7, 26, 12,
       41, 50, 63, 20, 31, 52, 58, 47, 43, 30,  6, 17, 10, 49, 19, 54, 40,
       55,  5, 64, 37, 25, 23, 35, 65, 32, 46, 62,  2, 61, 36, 15]), 'cur_cost': 104215.0}]
2025-07-03 17:19:44,332 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:19:44,332 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-03 17:19:44,332 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 17:19:44,332 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1], 'cur_cost': 10045.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 30, 2, 0, 35, 28, 45, 31, 27, 55, 59, 16, 54, 42, 57, 23, 22, 37, 43, 3, 32, 38, 64, 40, 44, 25, 26, 48, 39, 24, 20, 63, 62, 11, 17, 29, 60, 61, 19, 13, 53, 4, 50, 7, 33, 47, 14, 18, 56, 15, 34, 10, 41, 49, 8, 21, 65, 1, 51, 46, 6, 9, 5, 36, 12, 58], 'cur_cost': 100420.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}}]
2025-07-03 17:19:44,334 - __main__ - INFO - 进化阶段完成
2025-07-03 17:19:44,334 - __main__ - INFO - 开始评估阶段
2025-07-03 17:19:44,334 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 17:19:44,336 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 17:19:44,336 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 10045.0 → 10045.0 (Δ 0.0)
- Mean cost: 76132.6 → 75982.7
- Diversity: 0.762 → 0.775 (Δ 0.0138)

#### Elite metrics
- Elite count: 15 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.187 → 0.138 (Δ -0.0489)

#### Strategy performance
- Explore: 7 inds, success 0.0% , avg Δ 0.0
- Exploit: 3 inds, success 66.7% , avg Δ 499.67

#### Other indicators
- No-change individuals: 7
- Regression individuals: 1
- Historical trends: Last 1 iterations: costs [10045.0], diversity [0.9276094276094276]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 17:19:44,337 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:19:53,022 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.667
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 17:19:53,054 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 17:19:53,054 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.667
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 17:19:53,055 - __main__ - INFO - 评估阶段完成
2025-07-03 17:19:53,055 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.667
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 17:19:53,055 - __main__ - INFO - 当前最佳适应度: 10045.0
2025-07-03 17:19:53,056 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-03 17:19:53,057 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-03 17:19:53,057 - __main__ - INFO - 开始分析阶段
2025-07-03 17:19:53,057 - StatsExpert - INFO - 开始统计分析
2025-07-03 17:19:53,076 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10045.0, 'max': 116490.0, 'mean': 75982.7, 'std': 43548.09218565149}, 'diversity': 0.9309764309764309, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 17:19:53,077 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10045.0, 'max': 116490.0, 'mean': 75982.7, 'std': 43548.09218565149}, 'diversity_level': 0.9309764309764309, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 17:19:53,078 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 17:19:53,078 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 17:19:53,078 - PathExpert - INFO - 开始路径结构分析
2025-07-03 17:19:53,080 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 17:19:53,081 - PathExpert - INFO - 路径结构分析完成
2025-07-03 17:19:53,081 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 17:19:53,081 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 17:19:53,081 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 17:19:53,083 - EliteExpert - INFO - 开始精英解分析
2025-07-03 17:19:53,091 - EliteExpert - INFO - 精英解分析完成
2025-07-03 17:19:53,091 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 524.0, 'avg_gap': 62363.65238095238}, 'structure_gap': {'unique_elite_edges': 182, 'unique_pop_edges': 450, 'common_edges': 104}}, 'elite_diversity': {'diversity_score': 0.28975468975468976}}
2025-07-03 17:19:53,092 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 17:19:53,092 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 17:19:53,095 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 17:19:53,095 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 10045.0, mean 75982.7, max 116490.0, std 43548.09218565149
- diversity: 0.9309764309764309
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10045.0, 10045.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.28975468975468976}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 17:19:53,096 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:20:07,039 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9309764309764309,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "description": "Low-density cells with long edges",
      "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells with potential for exploitation",
      "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]
    }
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["mutation", "crossover"]
  }
}
```
2025-07-03 17:20:07,040 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 17:20:07,040 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9309764309764309, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells with long edges', 'cells': [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}], 'opportunity_regions': [{'description': 'High-density cells with potential for exploitation', 'cells': [[0, 0], [1, 1], [1, 2], [2, 1]]}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 17:20:07,040 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 17:20:07,040 - __main__ - INFO - 分析阶段完成
2025-07-03 17:20:07,040 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9309764309764309, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells with long edges', 'cells': [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}], 'opportunity_regions': [{'description': 'High-density cells with potential for exploitation', 'cells': [[0, 0], [1, 1], [1, 2], [2, 1]]}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 17:20:07,041 - __main__ - INFO - 开始策略分配阶段
2025-07-03 17:20:07,041 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 17:20:07,041 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.931
  • best_cost: 10045.0
  • mean_cost: 75982.7
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Low
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.667
  },
  "bal...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 17:20:07,042 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 17:20:07,042 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:20:17,836 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover new regions."
}
```
2025-07-03 17:20:17,837 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 17:20:17,837 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 17:20:17,837 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover new regions."
}
```
2025-07-03 17:20:17,838 - __main__ - INFO - 策略分配阶段完成
2025-07-03 17:20:17,838 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 17:20:17,838 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover new regions."
}
```
2025-07-03 17:20:17,838 - __main__ - INFO - 开始进化阶段
2025-07-03 17:20:17,839 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 17:20:17,839 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:20:17,840 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10328.0 (rank 20.0%)
- Population diversity: 0.7753846153846153
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells with potential for exploitation", "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]}]
- Difficult regions (full): [{"description": "Low-density cells with long edges", "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:20:17,840 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:20:17,840 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:20:33,287 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density cells with potential for exploitation",
  "strategy_comment": "Focuses on opportunity regions while maintaining diversity."
}
```
2025-07-03 17:20:33,288 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:20:33,299 - ExplorationKnowledgeBase - INFO - 已保存28条知识条目
2025-07-03 17:20:33,303 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10328.0, 路径: [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 17:20:33,303 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}
2025-07-03 17:20:33,304 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 17:20:33,304 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:20:33,305 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10045.0 (rank 0.0%)
- Population diversity: 0.7753846153846153
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells with potential for exploitation", "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]}]
- Difficult regions (full): [{"description": "Low-density cells with long edges", "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:20:33,305 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:20:33,305 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:20:49,579 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3],
  "targeted_regions": "dense_cell(0,0), dense_cell(1,1)",
  "strategy_comment": "Prioritizes high-density cells while reshuffling tail for diversity."
}
```
2025-07-03 17:20:49,579 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:20:49,598 - ExplorationKnowledgeBase - INFO - 已保存29条知识条目
2025-07-03 17:20:49,603 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10189.0, 路径: [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3]
2025-07-03 17:20:49,603 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3], 'cur_cost': 10189.0}
2025-07-03 17:20:49,604 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 17:20:49,604 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:20:49,604 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10059.0 (rank 0.0%)
- Population diversity: 0.7769230769230769
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells with potential for exploitation", "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]}]
- Difficult regions (full): [{"description": "Low-density cells with long edges", "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:20:49,605 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:20:49,605 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:21:04,802 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
  "targeted_regions": "High-density cells [0,0],[1,1],[1,2],[2,1]",
  "strategy_comment": "Prioritizes opportunity regions while maintaining diversity"
}
```
2025-07-03 17:21:04,803 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:21:04,822 - ExplorationKnowledgeBase - INFO - 已保存30条知识条目
2025-07-03 17:21:04,825 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10059.0, 路径: [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
2025-07-03 17:21:04,826 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}
2025-07-03 17:21:04,826 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 17:21:04,826 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:21:04,827 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:21:04,827 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 106264.0
2025-07-03 17:21:05,330 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 17:21:05,330 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277]
2025-07-03 17:21:05,330 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64)]
2025-07-03 17:21:05,337 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:21:05,338 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3], 'cur_cost': 10189.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': array([24, 58, 63, 18, 30, 15,  7, 46, 14, 49, 48, 57, 10, 62, 31, 22,  2,
       54, 56, 23, 38, 26, 40, 28, 47,  0, 64, 42, 16, 20, 51, 13, 59, 60,
       19, 65, 45, 34, 37,  1,  6,  5, 41,  4, 33, 25, 55, 36, 29,  8, 12,
        3, 35, 17, 21, 32, 39, 52, 61, 50, 11, 53, 27, 44,  9, 43]), 'cur_cost': 106264.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': [40, 53, 21, 38, 0, 63, 8, 4, 65, 48, 42, 39, 29, 16, 25, 57, 2, 60, 31, 15, 47, 10, 3, 50, 56, 61, 19, 30, 62, 13, 32, 49, 64, 26, 55, 18, 6, 35, 59, 51, 36, 23, 37, 43, 54, 33, 46, 20, 58, 24, 14, 22, 27, 41, 5, 44, 52, 7, 17, 12, 45, 34, 11, 28, 1, 9], 'cur_cost': 116490.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([44,  5, 62, 19, 12, 29, 48, 21,  4, 54, 40, 41, 60, 11, 39, 65, 10,
       61, 49, 51, 46, 31, 37, 36, 18, 30, 15, 22,  9, 57, 52, 27, 63, 56,
       23, 35, 38, 25,  6, 28, 50, 42, 26,  2, 20, 16, 24, 32, 58, 47,  8,
       55, 43, 34, 14, 13, 45,  0, 64, 17, 33,  1,  3, 59,  7, 53]), 'cur_cost': 95520.0}, {'tour': array([ 4, 33, 44, 10, 18, 20, 29, 41, 14, 26,  7, 42, 16,  0, 17, 48, 61,
        6,  5, 39, 54, 11, 62, 64,  9, 38, 55, 24, 47, 13, 36, 25, 19, 49,
       60, 65, 50, 63, 23, 43, 59, 51, 27, 32, 37, 15, 57, 31,  2, 52, 12,
       56,  8, 45, 46, 21, 28, 58, 53,  3, 35, 30, 34,  1, 40, 22]), 'cur_cost': 104050.0}, {'tour': array([14,  1, 57, 16,  4,  3, 38, 27, 34, 48, 33, 11,  8, 18, 22, 42,  0,
       53, 24, 21, 13, 56, 59, 45,  9, 60, 39, 51, 29, 28, 44,  7, 26, 12,
       41, 50, 63, 20, 31, 52, 58, 47, 43, 30,  6, 17, 10, 49, 19, 54, 40,
       55,  5, 64, 37, 25, 23, 35, 65, 32, 46, 62,  2, 61, 36, 15]), 'cur_cost': 104215.0}]
2025-07-03 17:21:05,339 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:21:05,339 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-03 17:21:05,339 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-03 17:21:05,339 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 17:21:05,340 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:21:05,341 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95802.0 (rank 40.0%)
- Population diversity: 0.7815384615384615
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells with potential for exploitation", "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]}]
- Difficult regions (full): [{"description": "Low-density cells with long edges", "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:21:05,342 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:21:05,342 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:21:22,387 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58],
  "targeted_regions": "High-density cells with potential for exploitation",
  "strategy_comment": "Focus on dense cells while avoiding difficult regions."
}
```
2025-07-03 17:21:22,387 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:21:22,407 - ExplorationKnowledgeBase - INFO - 已保存31条知识条目
2025-07-03 17:21:22,410 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95802.0, 路径: [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58]
2025-07-03 17:21:22,411 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}
2025-07-03 17:21:22,411 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 17:21:22,412 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:21:22,412 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:21:22,412 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 119041.0
2025-07-03 17:21:22,915 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 17:21:22,915 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277]
2025-07-03 17:21:22,915 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64)]
2025-07-03 17:21:22,923 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:21:22,923 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3], 'cur_cost': 10189.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': array([24, 58, 63, 18, 30, 15,  7, 46, 14, 49, 48, 57, 10, 62, 31, 22,  2,
       54, 56, 23, 38, 26, 40, 28, 47,  0, 64, 42, 16, 20, 51, 13, 59, 60,
       19, 65, 45, 34, 37,  1,  6,  5, 41,  4, 33, 25, 55, 36, 29,  8, 12,
        3, 35, 17, 21, 32, 39, 52, 61, 50, 11, 53, 27, 44,  9, 43]), 'cur_cost': 106264.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': array([ 4, 16,  5, 55, 19, 13, 63, 64, 27, 65, 40, 57, 37, 31, 62, 12, 34,
       48,  2, 21,  8,  7,  6, 41, 29, 60, 43, 58, 10, 22, 11, 36,  0, 15,
        1, 49, 35, 61, 28, 52, 20,  9, 54, 46, 30, 56, 26, 18, 23, 24, 47,
       32, 51, 33, 14, 25,  3, 44, 59, 50, 39, 38, 53, 45, 42, 17]), 'cur_cost': 119041.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112898.0}, {'tour': array([44,  5, 62, 19, 12, 29, 48, 21,  4, 54, 40, 41, 60, 11, 39, 65, 10,
       61, 49, 51, 46, 31, 37, 36, 18, 30, 15, 22,  9, 57, 52, 27, 63, 56,
       23, 35, 38, 25,  6, 28, 50, 42, 26,  2, 20, 16, 24, 32, 58, 47,  8,
       55, 43, 34, 14, 13, 45,  0, 64, 17, 33,  1,  3, 59,  7, 53]), 'cur_cost': 95520.0}, {'tour': array([ 4, 33, 44, 10, 18, 20, 29, 41, 14, 26,  7, 42, 16,  0, 17, 48, 61,
        6,  5, 39, 54, 11, 62, 64,  9, 38, 55, 24, 47, 13, 36, 25, 19, 49,
       60, 65, 50, 63, 23, 43, 59, 51, 27, 32, 37, 15, 57, 31,  2, 52, 12,
       56,  8, 45, 46, 21, 28, 58, 53,  3, 35, 30, 34,  1, 40, 22]), 'cur_cost': 104050.0}, {'tour': array([14,  1, 57, 16,  4,  3, 38, 27, 34, 48, 33, 11,  8, 18, 22, 42,  0,
       53, 24, 21, 13, 56, 59, 45,  9, 60, 39, 51, 29, 28, 44,  7, 26, 12,
       41, 50, 63, 20, 31, 52, 58, 47, 43, 30,  6, 17, 10, 49, 19, 54, 40,
       55,  5, 64, 37, 25, 23, 35, 65, 32, 46, 62,  2, 61, 36, 15]), 'cur_cost': 104215.0}]
2025-07-03 17:21:22,926 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:21:22,926 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-03 17:21:22,926 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-03 17:21:22,926 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 17:21:22,927 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:21:22,927 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112898.0 (rank 80.0%)
- Population diversity: 0.7769230769230769
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- Full current path sequence: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 0, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 1, 47, 17, 29, 63, 9, 58, 55]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells with potential for exploitation", "cells": [[0, 0], [1, 1], [1, 2], [2, 1]]}]
- Difficult regions (full): [{"description": "Low-density cells with long edges", "cells": [[0, 1], [0, 2], [1, 0], [2, 0], [2, 2]]}]

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 17:21:22,928 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:21:22,928 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:21:39,593 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 1, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 0, 47, 17, 29, 63, 9, 58, 55],
  "targeted_regions": "High-density cells [1,1], [1,2]",
  "strategy_comment": "Prioritizes opportunity regions while maintaining diversity."
}
```
2025-07-03 17:21:39,594 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:21:39,613 - ExplorationKnowledgeBase - INFO - 已保存32条知识条目
2025-07-03 17:21:39,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112928.0, 路径: [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 1, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 0, 47, 17, 29, 63, 9, 58, 55]
2025-07-03 17:21:39,617 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 1, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 0, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112928.0}
2025-07-03 17:21:39,618 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 17:21:39,618 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:21:39,618 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:21:39,619 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107578.0
2025-07-03 17:21:40,121 - ExploitationExpert - INFO - res_population_num: 23
2025-07-03 17:21:40,121 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9523, 9524, 9532, 9541, 9542, 9542, 9551, 9552, 9552, 9573, 9602, 95277, 9521, 9521]
2025-07-03 17:21:40,121 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 14, 15, 17, 12, 22, 23, 13, 19, 16, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 36,
       37, 25, 26, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47,
       46, 48, 49, 40, 43, 21, 20, 13, 19, 16, 18, 23, 22, 12, 17, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 14,
       15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 54, 57, 64, 53, 62,
       59, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16, 19, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 43, 65, 27, 33, 32, 18, 44, 45, 29, 25, 20,  6, 12,  3, 50, 36,
       46, 63,  4, 22, 42,  7, 57, 48, 41, 14,  5, 17, 61, 11, 30, 35, 62,
       52, 53, 38,  1, 26, 39,  8, 56, 60,  2, 19, 10, 21, 34, 49, 64, 37,
       28, 24, 31, 16, 51,  9, 15, 47, 13, 59, 55, 54, 40, 23, 58],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 17:21:40,130 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:21:40,130 - ExploitationExpert - INFO - populations: [{'tour': [11, 9, 3, 7, 1, 0, 10, 8, 2, 6, 4, 5, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10328.0}, {'tour': [0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 1, 2, 8, 5, 4, 6, 9, 11, 7, 3], 'cur_cost': 10189.0}, {'tour': [18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10059.0}, {'tour': array([24, 58, 63, 18, 30, 15,  7, 46, 14, 49, 48, 57, 10, 62, 31, 22,  2,
       54, 56, 23, 38, 26, 40, 28, 47,  0, 64, 42, 16, 20, 51, 13, 59, 60,
       19, 65, 45, 34, 37,  1,  6,  5, 41,  4, 33, 25, 55, 36, 29,  8, 12,
        3, 35, 17, 21, 32, 39, 52, 61, 50, 11, 53, 27, 44,  9, 43]), 'cur_cost': 106264.0}, {'tour': [8, 35, 29, 17, 28, 12, 52, 15, 19, 6, 44, 14, 36, 18, 34, 32, 27, 16, 30, 57, 47, 2, 61, 64, 51, 56, 42, 43, 33, 46, 22, 20, 59, 23, 45, 1, 60, 40, 37, 3, 38, 53, 10, 9, 4, 5, 24, 13, 50, 48, 25, 26, 62, 41, 49, 0, 11, 7, 65, 21, 31, 54, 63, 39, 55, 58], 'cur_cost': 95802.0}, {'tour': array([ 4, 16,  5, 55, 19, 13, 63, 64, 27, 65, 40, 57, 37, 31, 62, 12, 34,
       48,  2, 21,  8,  7,  6, 41, 29, 60, 43, 58, 10, 22, 11, 36,  0, 15,
        1, 49, 35, 61, 28, 52, 20,  9, 54, 46, 30, 56, 26, 18, 23, 24, 47,
       32, 51, 33, 14, 25,  3, 44, 59, 50, 39, 38, 53, 45, 42, 17]), 'cur_cost': 119041.0}, {'tour': [32, 49, 21, 11, 14, 12, 56, 31, 18, 26, 43, 62, 44, 23, 5, 45, 53, 46, 52, 1, 7, 57, 37, 39, 48, 28, 50, 51, 33, 15, 8, 34, 4, 2, 38, 30, 65, 25, 27, 22, 59, 13, 64, 36, 54, 61, 20, 6, 42, 24, 10, 60, 19, 3, 40, 41, 16, 35, 0, 47, 17, 29, 63, 9, 58, 55], 'cur_cost': 112928.0}, {'tour': array([17, 19, 45,  3, 36, 65, 40,  2, 31, 14, 32, 52, 12, 13, 29, 27, 38,
       57, 51, 39, 46, 53, 18, 28, 54, 56, 20, 61, 21, 42, 62,  7, 10,  8,
       35, 22, 58, 25, 24, 16, 15, 23, 60, 55, 47, 44, 64, 43,  4,  9, 26,
       59, 33, 41, 37, 34, 50,  1, 63,  6, 11, 30,  5, 48,  0, 49]), 'cur_cost': 107578.0}, {'tour': array([ 4, 33, 44, 10, 18, 20, 29, 41, 14, 26,  7, 42, 16,  0, 17, 48, 61,
        6,  5, 39, 54, 11, 62, 64,  9, 38, 55, 24, 47, 13, 36, 25, 19, 49,
       60, 65, 50, 63, 23, 43, 59, 51, 27, 32, 37, 15, 57, 31,  2, 52, 12,
       56,  8, 45, 46, 21, 28, 58, 53,  3, 35, 30, 34,  1, 40, 22]), 'cur_cost': 104050.0}, {'tour': array([14,  1, 57, 16,  4,  3, 38, 27, 34, 48, 33, 11,  8, 18, 22, 42,  0,
       53, 24, 21, 13, 56, 59, 45,  9, 60, 39, 51, 29, 28, 44,  7, 26, 12,
       41, 50, 63, 20, 31, 52, 58, 47, 43, 30,  6, 17, 10, 49, 19, 54, 40,
       55,  5, 64, 37, 25, 23, 35, 65, 32, 46, 62,  2, 61, 36, 15]), 'cur_cost': 104215.0}]
2025-07-03 17:21:40,132 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 17:21:40,133 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-03 17:21:40,133 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 17:21:40,133 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 17:21:40,133 - ExplorationExpert - INFO - 开始生成探索路径
