from __future__ import annotations

"""run_ablation.py – high-level driver for expert ablation studies.

Example
-------
Run *full* system and *disable_landscape* variant on two instances::

    python run_ablation.py \
        --variants full disable_landscape \
        --instances berlin52 st70 \
        --seeds 0 1 2 \
        --iter 50 --pop 20

A CSV summary will be created under ``ablation_results/``.
"""

from pathlib import Path
import argparse
import csv
import random
import sys
from datetime import datetime
from typing import Dict, List
from importlib import import_module

# ---------------------------------------------------------------------------
# Ensure MoE source path is importable
# ---------------------------------------------------------------------------
PROJECT_ROOT = Path(__file__).resolve().parent.parent
MOE_SRC = PROJECT_ROOT / "MoE-main" / "idea"
if str(MOE_SRC) not in sys.path:
    sys.path.insert(0, str(MOE_SRC))

# pylint: disable=wrong-import-position
import numpy as np
import pickle as pkl

from loadinstance import load_all_instances  # type: ignore
from initpop import INIT  # type: ignore
from api_general import InterfaceAPI  # type: ignore
# Note: run_ablation does not directly use gls_evol_enhanced.tour_cost or utils;
# they are therefore not imported to keep the script minimal.

# Our ablation manager
from ablation_manager import AblationExpertManager  # type: ignore

# ---------------------------------------------------------------------------
# Built-in instance list (kept in sync with moe_main)
# ---------------------------------------------------------------------------

# NOTE: If you update moe_main.py instance list, please update here as well.
FUNC_NAME_LIST = [
    "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
    "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
    "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39",
    "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59",
    "composite12_60", "composite13_66",
    "eil51", "berlin52", "st70", "pr76", "kroA100", "lin105",
]

# ---------------------------------------------------------------------------
# Helper – dataset loading
# ---------------------------------------------------------------------------

def _prepare_instances(func_names: List[str], input_root: Path, output_root: Path) -> List[Dict]:
    """Load (or cache) instances via existing helper *load_all_instances*.

    Returns a list of dicts with keys *func_name*, *coordinate*, *distance_matrix*.
    """
    input_root.mkdir(parents=True, exist_ok=True)
    output_root.mkdir(parents=True, exist_ok=True)

    load_all_instances(func_names, str(input_root), 0, len(func_names) - 1, str(output_root))

    pkl_file = output_root / "mmtsp_instances.pkl"
    if not pkl_file.exists():
        raise FileNotFoundError(f"Expected {pkl_file} after load_all_instances().")

    with pkl_file.open("rb") as fh:
        data = pkl.load(fh)

    instances = []
    for idx, name in enumerate(data["func_name"]):
        if name in func_names:
            instances.append(
                {
                    "func_name": name,
                    "coordinate": data["coordinate"][idx],
                    "distance_matrix": data["distance_matrix"][idx],
                }
            )
    return instances

# ---------------------------------------------------------------------------
# Core loop per instance / variant / seed
# ---------------------------------------------------------------------------

def _run_single(
    instance: Dict,
    variant: str,
    seed: int,
    iter_num: int,
    pop_size: int,
) -> tuple[float, list]:
    """Execute optimisation for one <instance, variant, seed> triple.

    Returns Tuple[float, list]: best cost and elite solution list
    """
    # Reproducibility
    random.seed(seed)
    np.random.seed(seed)

    # LLM interface (switch to debug=True to avoid real API calls during dry-run)
    interface_llm = InterfaceAPI(api_type="gemini", debug_mode=False)

    manager = AblationExpertManager(interface_llm, variant=variant)

    # Population init
    pop = INIT.mixed_init(instance["distance_matrix"], pop_size)
    pop = INIT.calculate_population_costs(pop, instance["distance_matrix"])

    # Dummy elite containers (simplified loop)
    elite_pool: List[Dict] = []
    # res_populations=[]
    stats_expert = manager.experts["stats"]

    for it in range(iter_num):
        # ANALYSIS
        landscape_report, old_stats_report = manager.run_analysis_phase(  # type: ignore[attr-defined]
            populations=pop,
            res_populations=elite_pool,
            coordinates=instance["coordinate"],
            distance_matrix=instance["distance_matrix"],
            iteration=it,
            total_iterations=iter_num,
        )

        # STRATEGY
        strategy_sel, _ = manager.run_strategy_phase(  # type: ignore[attr-defined]
            landscape_report=landscape_report,
            populations=pop,
            iteration=it,
            strategy_feedback=None,
        )

        # EVOLUTION
        pop = manager.run_evolution_phase(  # type: ignore[attr-defined]
            populations=pop,
            strategies=strategy_sel,
            landscape_report=landscape_report,
            distance_matrix=instance["distance_matrix"],
            res_populations=elite_pool,
        )

        # Update elite pool (top-k)
        
        # --- 在进化前记录快照 -----------------
        old_elites = elite_pool.copy()          # snapshot BEFORE extend

        # 进化后
        elite_pool.extend(pop)
        elite_pool.sort(key=lambda x: x["cur_cost"])

        # 重新计算 new_stats_report 基于最新种群
        new_stats_analysis = stats_expert.analyze(pop)
        new_stats_report = stats_expert.generate_report(
            new_stats_analysis, instance["coordinate"], instance["distance_matrix"]
        )

        # 评估阶段
        manager.run_assessment_phase(  # type: ignore[attr-defined]
            old_stats_report,
            new_stats_report,
            strategy_sel,
            it,
            iter_num,
            old_res_populations=old_elites,     # 传入 pre-evolution 精英
            new_res_populations=elite_pool,     # 传入 post-evolution 精英
        )

    # elite_pool already contains top-k, ensure sorted
    elite_pool.sort(key=lambda x: x["cur_cost"])
    return float(elite_pool[0]["cur_cost"]), elite_pool

# ---------------------------------------------------------------------------
# CLI & main dispatcher
# ---------------------------------------------------------------------------

def main(argv: List[str] | None = None) -> None:  # noqa: D401
    parser = argparse.ArgumentParser("Expert ablation runner")
    parser.add_argument("--variants", nargs="+", default=["disable_landscape + disable_strategy+ disable_exploration + disable_assessment"], help="Variant strings (see ablation_manager.py)")
    parser.add_argument("--instances", nargs="+", default=[], help="Explicit TSPLIB/MMTSP instance names (overrides index range)")
    parser.add_argument("--func_begin", type=int, default=0, help="Start index within built-in instance list (inclusive)")
    parser.add_argument("--func_end", type=int, default=30, help="End index within built-in instance list (inclusive). Use 0 to select only --func_begin item, matching moe_main behaviour.")
    parser.add_argument("--seeds", nargs="+", type=int, default=[1,2,3,4,5])
    parser.add_argument("--iter", dest="iter_num", type=int, default=1, help="Evolution iterations")
    parser.add_argument("--pop", dest="pop_size", type=int, default=20, help="Population size")
    parser.add_argument("--out_dir", default="ablation_results", help="Result CSV directory")
    args = parser.parse_args(argv)

    # ---------------------------------------------------------------------
    # Determine instance names
    # ---------------------------------------------------------------------
    if args.instances:  # explicit list has highest priority
        selected_names = args.instances
    else:
        # Adapt moe_main default: if func_end==0 or exceeds list length → single index
        func_end = args.func_end
        if func_end == 0 or func_end >= len(FUNC_NAME_LIST):
            func_end = args.func_begin

        if args.func_begin < 0 or func_end >= len(FUNC_NAME_LIST) or args.func_begin > func_end:
            raise ValueError(
                f"Invalid index range: begin={args.func_begin}, end={func_end}, valid 0-{len(FUNC_NAME_LIST)-1}"
            )

        selected_names = FUNC_NAME_LIST[args.func_begin : func_end + 1]

    # Prepare dataset
    input_root = PROJECT_ROOT / "benchmark_MMTSP"
    output_root = PROJECT_ROOT / "benchmark_MMTSP" / "instance_pkl"
    instances_data = _prepare_instances(selected_names, input_root, output_root)

    # Ensure output dir
    out_dir = Path(args.out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = out_dir / f"ablation_{timestamp}.csv"

    with csv_path.open("w", newline="", encoding="utf-8") as fh:
        writer = csv.writer(fh)
        writer.writerow(["instance", "variant", "seed", "best_cost"])

        for variant in args.variants:
            for seed in args.seeds:
                for inst in instances_data:
                    best_cost, elites = _run_single(inst, variant, seed, args.iter_num, args.pop_size)
                    writer.writerow([inst["func_name"], variant, seed, best_cost])
                    _save_solution(elites, variant, inst["func_name"], out_dir)
                    fh.flush()
                    print(f"[{variant}][{inst['func_name']}][seed={seed}] → {best_cost}")

    print("\nAll experiments finished. Results saved to", csv_path)

# ---------------------------------------------------------------------------
# Utils – save solution files
# ---------------------------------------------------------------------------

def _missing_tag(variant: str) -> str:
    """Return joined missing-module tag derived from a *variant* string."""
    parts = [p[len("disable_") :] for p in variant.split("+") if p.startswith("disable_")]
    return "_".join(parts) if parts else "full"

# def _save_solution(elite_solutions: list, variant: str, instance_name: str, out_dir: Path) -> None:
#     """Write solution file containing all elite paths with best_cost."""
#     if not elite_solutions:
#         return

#     tag = _missing_tag(variant)
#     ts = datetime.now().strftime("%Y%m%d_%H%M%S")
#     fname = f"{tag}_{instance_name}_{ts}.solution"

#     normalize_path = getattr(import_module("gls_evol_enhanced"), "normalize_path")  # type: ignore[attr-defined]
#     # Sort solutions and determine best cost
#     elite_solutions.sort(key=lambda x: x["cur_cost"])
#     best_cost_int = int(round(elite_solutions[0]["cur_cost"]))

#     sol_dir = out_dir / "solutions"
#     sol_dir.mkdir(parents=True, exist_ok=True)
#     path = sol_dir / fname

#     with path.open("w", encoding="utf-8") as fh:
#         for sol in elite_solutions:
#             cost_int = int(round(sol["cur_cost"]))
#             if cost_int > best_cost_int:
#                 break
#             tour_norm = normalize_path(sol["tour"])
#             if tour_norm is None:
#                 tour_norm = sol["tour"]
#             # ensure list for join
#             try:
#                 if isinstance(tour_norm, np.ndarray):
#                     tour_norm = tour_norm.tolist()
#             except ImportError:
#                 pass
#             tour_str = " ".join(map(str, tour_norm))
#             fh.write(f"{cost_int} {tour_str}\n")
#     print("Saved solution →", path)

def _save_solution(elite_solutions: list, variant: str, instance_name: str, out_dir: Path) -> None:
    """Write solution file containing all elite paths with best_cost (with TSPLIB threshold if needed)."""
    if not elite_solutions:
        return

    tag = _missing_tag(variant)
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    fname = f"{tag}_{instance_name}_{ts}.solution"

    normalize_path = getattr(import_module("gls_evol_enhanced"), "normalize_path")  # type: ignore[attr-defined]
    # TSPLIB实例名列表
    tsplib_instances = ["eil51", "berlin52", "st70", "pr76", "kroA100", "lin105"]
    is_tsplib = instance_name in tsplib_instances

    # Sort solutions and determine best cost
    elite_solutions.sort(key=lambda x: x["cur_cost"])
    best_cost = elite_solutions[0]["cur_cost"]

    # TSPLIB松弛阈值
    tsplib_relax = 0.01
    if is_tsplib:
        threshold = best_cost * (1 + tsplib_relax)
    else:
        threshold = best_cost

    sol_dir = out_dir / "solutions"
    sol_dir.mkdir(parents=True, exist_ok=True)
    path = sol_dir / fname

    with path.open("w", encoding="utf-8") as fh:
        for sol in elite_solutions:
            cost = sol["cur_cost"]
            cost_int = int(round(cost))
            # 只保存满足阈值的路径
            if cost > threshold:
                break
            tour_norm = normalize_path(sol["tour"])
            if tour_norm is None:
                tour_norm = sol["tour"]
            if isinstance(tour_norm, np.ndarray):
                tour_norm = tour_norm.tolist()
            tour_str = " ".join(map(str, tour_norm))
            fh.write(f"{cost_int} {tour_str}\n")
    print("Saved solution →", path)

if __name__ == "__main__":
    main() 