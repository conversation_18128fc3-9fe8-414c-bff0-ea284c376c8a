2025-06-22 18:57:44,397 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 18:57:44,397 - __main__ - INFO - 开始分析阶段
2025-06-22 18:57:44,397 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:57:44,425 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 113346.0, 'mean': 76337.3, 'std': 43472.80851992426}, 'diversity': 0.9134680134680134, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:57:44,426 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 113346.0, 'mean': 76337.3, 'std': 43472.80851992426}, 'diversity_level': 0.9134680134680134, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:57:44,436 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:57:44,436 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:57:44,436 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:57:44,441 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:57:44,442 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (59, 62), 'frequency': 0.5, 'avg_cost': 10.0}, {'edge': (43, 48), 'frequency': 0.5, 'avg_cost': 14.0}], 'common_subpaths': [{'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(16, 23)', 'frequency': 0.4}, {'edge': '(16, 18)', 'frequency': 0.4}, {'edge': '(24, 31)', 'frequency': 0.4}, {'edge': '(0, 10)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(59, 62)', 'frequency': 0.5}, {'edge': '(56, 58)', 'frequency': 0.4}, {'edge': '(46, 47)', 'frequency': 0.4}, {'edge': '(43, 48)', 'frequency': 0.5}, {'edge': '(19, 21)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(1, 3)', 'frequency': 0.3}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(14, 63)', 'frequency': 0.2}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(17, 54)', 'frequency': 0.2}, {'edge': '(25, 44)', 'frequency': 0.2}, {'edge': '(5, 51)', 'frequency': 0.2}, {'edge': '(6, 37)', 'frequency': 0.2}, {'edge': '(0, 20)', 'frequency': 0.2}, {'edge': '(21, 29)', 'frequency': 0.2}, {'edge': '(36, 58)', 'frequency': 0.2}, {'edge': '(40, 55)', 'frequency': 0.2}, {'edge': '(27, 45)', 'frequency': 0.2}, {'edge': '(4, 53)', 'frequency': 0.2}, {'edge': '(22, 30)', 'frequency': 0.2}, {'edge': '(33, 39)', 'frequency': 0.2}, {'edge': '(1, 9)', 'frequency': 0.2}, {'edge': '(13, 14)', 'frequency': 0.2}, {'edge': '(26, 35)', 'frequency': 0.2}, {'edge': '(1, 60)', 'frequency': 0.2}, {'edge': '(9, 60)', 'frequency': 0.2}, {'edge': '(40, 63)', 'frequency': 0.2}, {'edge': '(41, 65)', 'frequency': 0.2}, {'edge': '(3, 28)', 'frequency': 0.2}, {'edge': '(43, 47)', 'frequency': 0.2}, {'edge': '(23, 48)', 'frequency': 0.2}, {'edge': '(24, 59)', 'frequency': 0.2}, {'edge': '(7, 59)', 'frequency': 0.2}, {'edge': '(10, 16)', 'frequency': 0.2}, {'edge': '(30, 62)', 'frequency': 0.2}, {'edge': '(17, 32)', 'frequency': 0.2}, {'edge': '(13, 48)', 'frequency': 0.2}, {'edge': '(30, 35)', 'frequency': 0.2}, {'edge': '(2, 60)', 'frequency': 0.2}, {'edge': '(39, 46)', 'frequency': 0.2}, {'edge': '(43, 56)', 'frequency': 0.2}, {'edge': '(7, 20)', 'frequency': 0.2}, {'edge': '(5, 47)', 'frequency': 0.2}, {'edge': '(38, 58)', 'frequency': 0.2}, {'edge': '(27, 38)', 'frequency': 0.2}, {'edge': '(10, 41)', 'frequency': 0.2}, {'edge': '(37, 49)', 'frequency': 0.2}, {'edge': '(34, 49)', 'frequency': 0.2}, {'edge': '(14, 45)', 'frequency': 0.2}, {'edge': '(28, 29)', 'frequency': 0.2}, {'edge': '(36, 50)', 'frequency': 0.2}, {'edge': '(15, 51)', 'frequency': 0.2}, {'edge': '(35, 54)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [65, 24, 63, 33, 52, 50, 0], 'cost': 17248.0, 'size': 7}, {'region': [63, 40, 55, 51, 8], 'cost': 10971.0, 'size': 5}, {'region': [7, 38, 29, 55, 25], 'cost': 10555.0, 'size': 5}, {'region': [55, 50, 58, 38, 27], 'cost': 10524.0, 'size': 5}, {'region': [0, 43, 56, 33], 'cost': 8219.0, 'size': 4}]}
2025-06-22 18:57:44,442 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:57:44,443 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:57:44,443 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:57:44,443 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 18:57:44,443 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 18:57:44,443 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:57:44,443 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:57:44,443 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:57:44,443 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9948.0, max=113346.0, mean=76337.3, std=43472.80851992426
- Diversity: 0.9134680134680134, Convergence: 0.0
- Structure: 2 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Evolution Context:
- Iteration: 0/4
- Historical trend: Not available
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Few common high-quality edges
- Known difficult regions: 5 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Moderate

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:57:51,300 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely highly rugged, due to the presence of many local optima indicated by the lack of convergence, coupled with a high diversity score. The few high-quality edges suggest that even optimal solutions may not share many common components.",
    "modality": "Likely multi-modal, as indicated by the high diversity score and the presence of difficult regions. The different possible paths suggest several good solutions exist, not just one global optimum. The unknown TSP instance size means we don't know how many local optima we would expect.",
    "deceptiveness": "Potentially deceptive. The 'few common high-quality edges' suggest that promising paths might not be easily linked. The identified 'difficult regions' further emphasize the challenges of finding a globally optimal solution. The zero Elite solutions indicate that any current good path may not be present, yet.",
    "comment": "The high standard deviation of costs and zero convergence implies that the search is struggling to find a consistently good solution."
  },
  "population_state": {
    "diversity": "High (0.91), suggesting the population explores a broad range of solutions and is far from converging towards a single optimum. This also suggests that there are several viable solutions.",
    "convergence": "Zero, indicating the population is not converging and is likely stuck exploring a large area of the search space or is stuck in local optima. No Elite Solutions also supports this.",
    "clustering": "No clustering information available. This means we don't know if the population is distributed randomly or if it is already forming distinct groups.",
    "comment": "The combination of high diversity and zero convergence suggests an early stage in the search, or that the search operators are not effectively guiding the population towards better solutions."
  },
  "difficult_regions": {
    "identified_challenges": [
      "The five identified difficult regions, the nature of which is unknown, are hindering convergence.",
      "Edge crossings are a likely source of trouble given the TSP context. However, the problem instance is unknown.",
      "Isolated nodes (if they exist within the graph structure of the problem) could be a problem, slowing convergence."
    ],
    "comment": "Addressing these regions will be key to improving solution quality."
  },
  "opportunity_regions": {
    "identified_opportunities": [
      "Focusing on the two high-quality edges can potentially lead to faster convergence, and also allow more aggressive exploitation.",
      "The 10 common subpaths may suggest a common structure and provide an opportunity for exploitation or refinement.",
      "Further exploration might uncover hidden connections and improve solutions."
    ],
    "comment": "Exploiting these edges and subpaths could be a crucial step towards improving overall solutions."
  },
  "evolution_phase": "Early Exploration phase with very limited exploitation opportunities, but a high exploration bias.",
  "evolution_direction": {
    "strategy": "Continue exploring the search space with a balanced approach of exploration and exploitation.",
    "operator_suggestions": [
      "**Exploration:** Introduce exploration operators such as: Path Relinking to explore regions between promising solutions, or Mutation-based operators with a moderate mutation rate.",
      "**Exploitation:** Exploit promising paths by using operators like: Edge recombination operator to combine good edges, or 2-opt/3-opt local search on select individuals. Because convergence is not present, this will be less effective. If it's a graph based problem, graph based crossover/mutation may be of interest."
    ],
    "operator_details": "The combination of these operators, chosen carefully to balance exploration and exploitation, should drive the search from its current phase, towards finding better solutions. Focus on diversity, especially when considering the 5 difficult regions. If graph based, consider using a graph based crossover/mutation operation."
  }
}
```
2025-06-22 18:57:51,300 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:57:51,300 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely highly rugged, due to the presence of many local optima indicated by the lack of convergence, coupled with a high diversity score. The few high-quality edges suggest that even optimal solutions may not share many common components.', 'modality': "Likely multi-modal, as indicated by the high diversity score and the presence of difficult regions. The different possible paths suggest several good solutions exist, not just one global optimum. The unknown TSP instance size means we don't know how many local optima we would expect.", 'deceptiveness': "Potentially deceptive. The 'few common high-quality edges' suggest that promising paths might not be easily linked. The identified 'difficult regions' further emphasize the challenges of finding a globally optimal solution. The zero Elite solutions indicate that any current good path may not be present, yet.", 'comment': 'The high standard deviation of costs and zero convergence implies that the search is struggling to find a consistently good solution.'}, 'population_state': {'diversity': 'High (0.91), suggesting the population explores a broad range of solutions and is far from converging towards a single optimum. This also suggests that there are several viable solutions.', 'convergence': 'Zero, indicating the population is not converging and is likely stuck exploring a large area of the search space or is stuck in local optima. No Elite Solutions also supports this.', 'clustering': "No clustering information available. This means we don't know if the population is distributed randomly or if it is already forming distinct groups.", 'comment': 'The combination of high diversity and zero convergence suggests an early stage in the search, or that the search operators are not effectively guiding the population towards better solutions.'}, 'difficult_regions': {'identified_challenges': ['The five identified difficult regions, the nature of which is unknown, are hindering convergence.', 'Edge crossings are a likely source of trouble given the TSP context. However, the problem instance is unknown.', 'Isolated nodes (if they exist within the graph structure of the problem) could be a problem, slowing convergence.'], 'comment': 'Addressing these regions will be key to improving solution quality.'}, 'opportunity_regions': {'identified_opportunities': ['Focusing on the two high-quality edges can potentially lead to faster convergence, and also allow more aggressive exploitation.', 'The 10 common subpaths may suggest a common structure and provide an opportunity for exploitation or refinement.', 'Further exploration might uncover hidden connections and improve solutions.'], 'comment': 'Exploiting these edges and subpaths could be a crucial step towards improving overall solutions.'}, 'evolution_phase': 'Early Exploration phase with very limited exploitation opportunities, but a high exploration bias.', 'evolution_direction': {'strategy': 'Continue exploring the search space with a balanced approach of exploration and exploitation.', 'operator_suggestions': ['**Exploration:** Introduce exploration operators such as: Path Relinking to explore regions between promising solutions, or Mutation-based operators with a moderate mutation rate.', "**Exploitation:** Exploit promising paths by using operators like: Edge recombination operator to combine good edges, or 2-opt/3-opt local search on select individuals. Because convergence is not present, this will be less effective. If it's a graph based problem, graph based crossover/mutation may be of interest."], 'operator_details': 'The combination of these operators, chosen carefully to balance exploration and exploitation, should drive the search from its current phase, towards finding better solutions. Focus on diversity, especially when considering the 5 difficult regions. If graph based, consider using a graph based crossover/mutation operation.'}}
2025-06-22 18:57:51,300 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:57:51,300 - __main__ - INFO - 分析阶段完成
2025-06-22 18:57:51,300 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely highly rugged, due to the presence of many local optima indicated by the lack of convergence, coupled with a high diversity score. The few high-quality edges suggest that even optimal solutions may not share many common components.', 'modality': "Likely multi-modal, as indicated by the high diversity score and the presence of difficult regions. The different possible paths suggest several good solutions exist, not just one global optimum. The unknown TSP instance size means we don't know how many local optima we would expect.", 'deceptiveness': "Potentially deceptive. The 'few common high-quality edges' suggest that promising paths might not be easily linked. The identified 'difficult regions' further emphasize the challenges of finding a globally optimal solution. The zero Elite solutions indicate that any current good path may not be present, yet.", 'comment': 'The high standard deviation of costs and zero convergence implies that the search is struggling to find a consistently good solution.'}, 'population_state': {'diversity': 'High (0.91), suggesting the population explores a broad range of solutions and is far from converging towards a single optimum. This also suggests that there are several viable solutions.', 'convergence': 'Zero, indicating the population is not converging and is likely stuck exploring a large area of the search space or is stuck in local optima. No Elite Solutions also supports this.', 'clustering': "No clustering information available. This means we don't know if the population is distributed randomly or if it is already forming distinct groups.", 'comment': 'The combination of high diversity and zero convergence suggests an early stage in the search, or that the search operators are not effectively guiding the population towards better solutions.'}, 'difficult_regions': {'identified_challenges': ['The five identified difficult regions, the nature of which is unknown, are hindering convergence.', 'Edge crossings are a likely source of trouble given the TSP context. However, the problem instance is unknown.', 'Isolated nodes (if they exist within the graph structure of the problem) could be a problem, slowing convergence.'], 'comment': 'Addressing these regions will be key to improving solution quality.'}, 'opportunity_regions': {'identified_opportunities': ['Focusing on the two high-quality edges can potentially lead to faster convergence, and also allow more aggressive exploitation.', 'The 10 common subpaths may suggest a common structure and provide an opportunity for exploitation or refinement.', 'Further exploration might uncover hidden connections and improve solutions.'], 'comment': 'Exploiting these edges and subpaths could be a crucial step towards improving overall solutions.'}, 'evolution_phase': 'Early Exploration phase with very limited exploitation opportunities, but a high exploration bias.', 'evolution_direction': {'strategy': 'Continue exploring the search space with a balanced approach of exploration and exploitation.', 'operator_suggestions': ['**Exploration:** Introduce exploration operators such as: Path Relinking to explore regions between promising solutions, or Mutation-based operators with a moderate mutation rate.', "**Exploitation:** Exploit promising paths by using operators like: Edge recombination operator to combine good edges, or 2-opt/3-opt local search on select individuals. Because convergence is not present, this will be less effective. If it's a graph based problem, graph based crossover/mutation may be of interest."], 'operator_details': 'The combination of these operators, chosen carefully to balance exploration and exploitation, should drive the search from its current phase, towards finding better solutions. Focus on diversity, especially when considering the 5 difficult regions. If graph based, consider using a graph based crossover/mutation operation.'}}
2025-06-22 18:57:51,300 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:57:51,300 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:57:51,300 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High (0.91), suggesting the population explores a broad range of solutions and is far from converging towards a single optimum. This also suggests that there are several viable solutions.
- Best cost: 9948.0, Mean cost: 76337.3
- Search space: Ruggedness: Likely highly rugged, due to the presence of many local optima indicated by the lack of convergence, coupled with a high diversity score. The few high-quality edges suggest that even optimal solutions may not share many common components., Modality: Likely multi-modal, as indicated by the high diversity score and the presence of difficult regions. The different possible paths suggest several good solutions exist, not just one global optimum. The unknown TSP instance size means we don't know how many local optima we would expect.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:57:51,300 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:57:53,757 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.25,
      "step_size": 0.1,
      "region_focus": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "local_search_iterations": 15,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:57:53,757 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 18:57:53,757 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 18:57:53,757 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.25,
      "step_size": 0.1,
      "region_focus": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "local_search_iterations": 15,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:57:53,757 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:57:53,760 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 18:57:53,760 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.25,
      "step_size": 0.1,
      "region_focus": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "local_search_iterations": 15,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:57:53,760 - __main__ - INFO - 开始进化阶段
2025-06-22 18:57:53,760 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:57:53,760 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:57:53,760 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [21, 20, 13] and ending with [43, 48, 42]
- Cost: 9948.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7276923076923076
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:57:53,760 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:57:58,290 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0
  ],
  "modification_strategy": "This strategy prioritizes exploring the 'unexplored regions' and 'opportunity regions' identified in the search context.  It uses a 'divide and conquer' approach, starting with a completely different starting point and re-ordering nodes in a manner that is likely to create a diverse outcome based on the diversity weight.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 18:57:58,290 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:57:58,290 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25407.0, 路径: [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28]
2025-06-22 18:57:58,290 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}
2025-06-22 18:57:58,292 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:57:58,292 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:57:58,292 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 11, 7] and ending with [50, 41, 42]
- Cost: 10248.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7846153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:57:58,292 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:01,415 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This strategy prioritizes diversity by creating a path that deviates significantly from the original. It starts with an initial block of nodes and then systematically adds nodes, aiming to explore diverse regions while attempting to maintain some proximity within blocks.",
  "targeted_regions": "The approach aims to touch all regions while focusing on the nodes' order. Specific regions are hard to define within the constraints, but a more spread distribution would occur, leveraging the diversity weight to explore the search space."
}
```
2025-06-22 18:58:01,415 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:01,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11300.0, 路径: [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:58:01,418 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}
2025-06-22 18:58:01,418 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-22 18:58:01,418 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:01,420 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:01,423 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 111739.0
2025-06-22 18:58:04,248 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 18:58:04,248 - ExploitationExpert - INFO - res_population_costs: [9821]
2025-06-22 18:58:04,248 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:58:04,248 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:04,248 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}, {'tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}, {'tour': array([ 1,  0, 39,  6,  2, 58, 42,  9, 35, 10, 19, 60, 37, 33, 30, 11, 23,
       40, 63, 61, 62, 38, 52, 57, 50,  4, 59, 56, 54, 32, 44,  8, 12, 41,
       16, 15,  5, 20, 51, 29, 48, 25, 65, 26, 22, 47, 27, 18, 36, 49, 28,
       21, 17, 34, 64, 46, 14,  7, 13, 31,  3, 24, 55, 43, 53, 45]), 'cur_cost': 111739.0}, {'tour': [52, 41, 8, 50, 17, 54, 16, 44, 25, 35, 47, 42, 51, 5, 65, 43, 48, 61, 37, 6, 13, 2, 49, 10, 0, 20, 28, 60, 29, 21, 19, 56, 36, 58, 18, 32, 63, 12, 3, 64, 59, 62, 40, 55, 14, 38, 26, 15, 11, 31, 45, 27, 53, 4, 46, 30, 22, 33, 39, 23, 24, 1, 9, 57, 7, 34], 'cur_cost': 113346.0}, {'tour': [2, 15, 46, 61, 57, 21, 8, 5, 51, 19, 49, 45, 6, 27, 34, 18, 14, 13, 53, 26, 35, 1, 60, 9, 37, 63, 40, 22, 65, 41, 36, 58, 64, 17, 54, 3, 28, 52, 4, 11, 44, 0, 20, 47, 43, 23, 48, 24, 59, 7, 38, 29, 55, 25, 33, 32, 16, 10, 56, 12, 50, 39, 31, 42, 30, 62], 'cur_cost': 103552.0}, {'tour': [32, 17, 59, 7, 49, 38, 53, 5, 6, 21, 23, 48, 13, 14, 29, 39, 35, 30, 54, 10, 15, 37, 45, 9, 60, 2, 64, 61, 40, 18, 16, 36, 34, 41, 56, 57, 12, 31, 26, 1, 65, 24, 63, 33, 52, 50, 0, 22, 46, 47, 27, 51, 4, 19, 43, 28, 3, 11, 55, 42, 62, 58, 25, 20, 8, 44], 'cur_cost': 101519.0}, {'tour': [22, 51, 53, 46, 39, 15, 54, 60, 42, 32, 8, 25, 0, 43, 56, 33, 28, 11, 63, 7, 20, 36, 57, 2, 5, 47, 40, 13, 21, 62, 30, 64, 4, 6, 24, 59, 48, 3, 1, 9, 12, 23, 31, 61, 55, 50, 58, 38, 27, 17, 18, 65, 41, 10, 52, 29, 16, 37, 49, 34, 19, 35, 26, 45, 14, 44], 'cur_cost': 107260.0}, {'tour': [53, 35, 13, 17, 31, 55, 5, 57, 10, 30, 26, 46, 37, 49, 4, 12, 32, 61, 45, 23, 16, 58, 11, 20, 7, 29, 28, 36, 50, 63, 54, 33, 39, 0, 34, 64, 6, 1, 14, 22, 2, 60, 41, 62, 59, 9, 18, 3, 51, 15, 65, 21, 19, 25, 44, 47, 56, 43, 48, 8, 38, 27, 24, 52, 42, 40], 'cur_cost': 103868.0}, {'tour': [36, 50, 19, 16, 39, 46, 8, 62, 44, 31, 0, 5, 45, 27, 65, 2, 40, 6, 63, 47, 24, 13, 48, 17, 15, 51, 37, 4, 18, 12, 49, 34, 29, 21, 11, 41, 10, 64, 42, 23, 20, 43, 25, 57, 52, 14, 7, 53, 55, 32, 22, 30, 35, 54, 61, 33, 1, 60, 56, 28, 9, 3, 26, 59, 58, 38], 'cur_cost': 102291.0}, {'tour': [25, 24, 31, 30, 1, 7, 48, 11, 56, 58, 13, 26, 54, 35, 14, 45, 64, 50, 3, 29, 28, 23, 10, 16, 59, 63, 40, 55, 51, 8, 60, 37, 6, 18, 39, 21, 61, 38, 46, 5, 47, 43, 33, 27, 22, 19, 52, 0, 41, 20, 12, 2, 17, 32, 9, 65, 57, 44, 49, 62, 36, 42, 15, 53, 4, 34], 'cur_cost': 101152.0}]
2025-06-22 18:58:04,255 - ExploitationExpert - INFO - 局部搜索耗时: 2.83秒
2025-06-22 18:58:04,255 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 18:58:04,255 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-22 18:58:04,255 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:58:04,256 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:04,257 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [52, 41, 8] and ending with [57, 7, 34]
- Cost: 113346.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.823076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:04,257 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:07,716 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50,
    2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35,
    51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54,
    13, 21, 36, 45, 0, 57
  ],
  "modification_strategy": "The strategy prioritizes exploration of unexplored and opportunity regions by: 1. Randomly re-ordering large segments of the provided path. 2. Adding several nodes from the opportunity regions identified in the search context. 3. Ensuring a diverse node distribution to maximize coverage and minimize potential path overlap with the original.",
  "targeted_regions": "Targeted Regions include opportunity regions, and general areas not heavily visited in the initial path, identified through diversity and region analysis."
}
```
2025-06-22 18:58:07,716 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:07,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117076.0, 路径: [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61]
2025-06-22 18:58:07,716 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61], 'cur_cost': 117076.0}
2025-06-22 18:58:07,716 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 18:58:07,716 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:07,716 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:07,716 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 97890.0
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - res_population_costs: [9821, 9587]
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}, {'tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}, {'tour': array([ 1,  0, 39,  6,  2, 58, 42,  9, 35, 10, 19, 60, 37, 33, 30, 11, 23,
       40, 63, 61, 62, 38, 52, 57, 50,  4, 59, 56, 54, 32, 44,  8, 12, 41,
       16, 15,  5, 20, 51, 29, 48, 25, 65, 26, 22, 47, 27, 18, 36, 49, 28,
       21, 17, 34, 64, 46, 14,  7, 13, 31,  3, 24, 55, 43, 53, 45]), 'cur_cost': 111739.0}, {'tour': [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61], 'cur_cost': 117076.0}, {'tour': array([27,  5, 47,  7, 61, 38, 41, 24, 33, 36, 65,  3, 23, 22, 48,  4,  8,
       58, 34, 49,  0, 51, 42, 19, 16, 59, 14, 32, 44, 63, 64, 62, 17, 50,
       20, 46, 37, 31, 18,  1, 15, 45, 57, 21, 29, 53, 54, 60, 26, 40, 35,
       25, 10, 52,  9, 13, 28,  2, 39, 43, 56, 30, 11, 55,  6, 12]), 'cur_cost': 97890.0}, {'tour': [32, 17, 59, 7, 49, 38, 53, 5, 6, 21, 23, 48, 13, 14, 29, 39, 35, 30, 54, 10, 15, 37, 45, 9, 60, 2, 64, 61, 40, 18, 16, 36, 34, 41, 56, 57, 12, 31, 26, 1, 65, 24, 63, 33, 52, 50, 0, 22, 46, 47, 27, 51, 4, 19, 43, 28, 3, 11, 55, 42, 62, 58, 25, 20, 8, 44], 'cur_cost': 101519.0}, {'tour': [22, 51, 53, 46, 39, 15, 54, 60, 42, 32, 8, 25, 0, 43, 56, 33, 28, 11, 63, 7, 20, 36, 57, 2, 5, 47, 40, 13, 21, 62, 30, 64, 4, 6, 24, 59, 48, 3, 1, 9, 12, 23, 31, 61, 55, 50, 58, 38, 27, 17, 18, 65, 41, 10, 52, 29, 16, 37, 49, 34, 19, 35, 26, 45, 14, 44], 'cur_cost': 107260.0}, {'tour': [53, 35, 13, 17, 31, 55, 5, 57, 10, 30, 26, 46, 37, 49, 4, 12, 32, 61, 45, 23, 16, 58, 11, 20, 7, 29, 28, 36, 50, 63, 54, 33, 39, 0, 34, 64, 6, 1, 14, 22, 2, 60, 41, 62, 59, 9, 18, 3, 51, 15, 65, 21, 19, 25, 44, 47, 56, 43, 48, 8, 38, 27, 24, 52, 42, 40], 'cur_cost': 103868.0}, {'tour': [36, 50, 19, 16, 39, 46, 8, 62, 44, 31, 0, 5, 45, 27, 65, 2, 40, 6, 63, 47, 24, 13, 48, 17, 15, 51, 37, 4, 18, 12, 49, 34, 29, 21, 11, 41, 10, 64, 42, 23, 20, 43, 25, 57, 52, 14, 7, 53, 55, 32, 22, 30, 35, 54, 61, 33, 1, 60, 56, 28, 9, 3, 26, 59, 58, 38], 'cur_cost': 102291.0}, {'tour': [25, 24, 31, 30, 1, 7, 48, 11, 56, 58, 13, 26, 54, 35, 14, 45, 64, 50, 3, 29, 28, 23, 10, 16, 59, 63, 40, 55, 51, 8, 60, 37, 6, 18, 39, 21, 61, 38, 46, 5, 47, 43, 33, 27, 22, 19, 52, 0, 41, 20, 12, 2, 17, 32, 9, 65, 57, 44, 49, 62, 36, 42, 15, 53, 4, 34], 'cur_cost': 101152.0}]
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - 局部搜索耗时: 1.03秒
2025-06-22 18:58:08,749 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 18:58:08,753 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 18:58:08,753 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 18:58:08,753 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:08,753 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 17, 59] and ending with [20, 8, 44]
- Cost: 101519.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8292307692307692
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:08,753 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:12,009 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47
  ],
  "modification_strategy": "The strategy prioritized a complete reordering of the existing path, focusing on exploring unexplored regions indicated in the search context by shuffling the nodes and incorporating a different starting point. Emphasis was on high diversity to find a path significantly different from the original, trying to avoid the difficult regions.",
  "targeted_regions": "Unexplored regions and opportunity regions, by aiming for a high degree of path diversity. The goal was to create a path that avoids previously known difficult regions. This approach involved a significant rearrangement of nodes to maximize exploration of potential new areas."
}
```
2025-06-22 18:58:12,009 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:12,009 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126758.0, 路径: [32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47]
2025-06-22 18:58:12,009 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47], 'cur_cost': 126758.0}
2025-06-22 18:58:12,009 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:58:12,009 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:12,009 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:12,009 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 109468.0
2025-06-22 18:58:12,511 - ExploitationExpert - INFO - res_population_num: 11
2025-06-22 18:58:12,511 - ExploitationExpert - INFO - res_population_costs: [9821, 9587, 9587, 9566, 9560, 9547, 9543, 9535, 9532, 9530, 9521]
2025-06-22 18:58:12,511 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:58:12,514 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:12,514 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}, {'tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}, {'tour': array([ 1,  0, 39,  6,  2, 58, 42,  9, 35, 10, 19, 60, 37, 33, 30, 11, 23,
       40, 63, 61, 62, 38, 52, 57, 50,  4, 59, 56, 54, 32, 44,  8, 12, 41,
       16, 15,  5, 20, 51, 29, 48, 25, 65, 26, 22, 47, 27, 18, 36, 49, 28,
       21, 17, 34, 64, 46, 14,  7, 13, 31,  3, 24, 55, 43, 53, 45]), 'cur_cost': 111739.0}, {'tour': [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61], 'cur_cost': 117076.0}, {'tour': array([27,  5, 47,  7, 61, 38, 41, 24, 33, 36, 65,  3, 23, 22, 48,  4,  8,
       58, 34, 49,  0, 51, 42, 19, 16, 59, 14, 32, 44, 63, 64, 62, 17, 50,
       20, 46, 37, 31, 18,  1, 15, 45, 57, 21, 29, 53, 54, 60, 26, 40, 35,
       25, 10, 52,  9, 13, 28,  2, 39, 43, 56, 30, 11, 55,  6, 12]), 'cur_cost': 97890.0}, {'tour': [32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47], 'cur_cost': 126758.0}, {'tour': array([27,  9, 21, 34, 14, 17, 54, 33, 36, 32, 22, 31,  4,  7, 52, 13, 48,
       24,  2, 10, 35, 43, 25, 59, 51, 11, 50, 65, 56, 42, 61, 40, 44, 41,
       63, 45,  1, 23, 19,  8, 37, 57,  0, 60, 64, 18,  5, 62, 38, 15, 30,
       47,  3, 49, 58, 28, 26, 20,  6, 12, 16, 55, 29, 53, 46, 39]), 'cur_cost': 109468.0}, {'tour': [53, 35, 13, 17, 31, 55, 5, 57, 10, 30, 26, 46, 37, 49, 4, 12, 32, 61, 45, 23, 16, 58, 11, 20, 7, 29, 28, 36, 50, 63, 54, 33, 39, 0, 34, 64, 6, 1, 14, 22, 2, 60, 41, 62, 59, 9, 18, 3, 51, 15, 65, 21, 19, 25, 44, 47, 56, 43, 48, 8, 38, 27, 24, 52, 42, 40], 'cur_cost': 103868.0}, {'tour': [36, 50, 19, 16, 39, 46, 8, 62, 44, 31, 0, 5, 45, 27, 65, 2, 40, 6, 63, 47, 24, 13, 48, 17, 15, 51, 37, 4, 18, 12, 49, 34, 29, 21, 11, 41, 10, 64, 42, 23, 20, 43, 25, 57, 52, 14, 7, 53, 55, 32, 22, 30, 35, 54, 61, 33, 1, 60, 56, 28, 9, 3, 26, 59, 58, 38], 'cur_cost': 102291.0}, {'tour': [25, 24, 31, 30, 1, 7, 48, 11, 56, 58, 13, 26, 54, 35, 14, 45, 64, 50, 3, 29, 28, 23, 10, 16, 59, 63, 40, 55, 51, 8, 60, 37, 6, 18, 39, 21, 61, 38, 46, 5, 47, 43, 33, 27, 22, 19, 52, 0, 41, 20, 12, 2, 17, 32, 9, 65, 57, 44, 49, 62, 36, 42, 15, 53, 4, 34], 'cur_cost': 101152.0}]
2025-06-22 18:58:12,516 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:58:12,516 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 18:58:12,516 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:58:12,516 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 18:58:12,517 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:12,517 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [53, 35, 13] and ending with [52, 42, 40]
- Cost: 103868.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8307692307692308
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:12,517 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:16,211 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52
  ],
  "modification_strategy": "Leveraged the 'Diversity weight' and 'Risk tolerance' to create a path substantially different from the original. Focused on exploring the 'Unexplored regions' and 'Opportunity regions' indicated in the search context by incorporating nodes potentially in proximity. The strategy was to shuffle and strategically re-arrange the node sequence while maintaining the TSP constraints.",
  "targeted_regions": "Primarily aimed at exploring the two 'Unexplored regions' and 'Opportunity regions' identified, while avoiding 'Difficult regions'. Specific node indices were selected based on the search context and adjusted for path diversity."
}
```
2025-06-22 18:58:16,211 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:16,211 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117476.0, 路径: [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52]
2025-06-22 18:58:16,211 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52], 'cur_cost': 117476.0}
2025-06-22 18:58:16,211 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:58:16,211 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:16,211 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:16,211 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 112938.0
2025-06-22 18:58:16,713 - ExploitationExpert - INFO - res_population_num: 16
2025-06-22 18:58:16,713 - ExploitationExpert - INFO - res_population_costs: [9821, 9587, 9587, 9566, 9560, 9547, 9543, 9535, 9532, 9530, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:58:16,713 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:58:16,718 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:16,719 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}, {'tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}, {'tour': array([ 1,  0, 39,  6,  2, 58, 42,  9, 35, 10, 19, 60, 37, 33, 30, 11, 23,
       40, 63, 61, 62, 38, 52, 57, 50,  4, 59, 56, 54, 32, 44,  8, 12, 41,
       16, 15,  5, 20, 51, 29, 48, 25, 65, 26, 22, 47, 27, 18, 36, 49, 28,
       21, 17, 34, 64, 46, 14,  7, 13, 31,  3, 24, 55, 43, 53, 45]), 'cur_cost': 111739.0}, {'tour': [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61], 'cur_cost': 117076.0}, {'tour': array([27,  5, 47,  7, 61, 38, 41, 24, 33, 36, 65,  3, 23, 22, 48,  4,  8,
       58, 34, 49,  0, 51, 42, 19, 16, 59, 14, 32, 44, 63, 64, 62, 17, 50,
       20, 46, 37, 31, 18,  1, 15, 45, 57, 21, 29, 53, 54, 60, 26, 40, 35,
       25, 10, 52,  9, 13, 28,  2, 39, 43, 56, 30, 11, 55,  6, 12]), 'cur_cost': 97890.0}, {'tour': [32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47], 'cur_cost': 126758.0}, {'tour': array([27,  9, 21, 34, 14, 17, 54, 33, 36, 32, 22, 31,  4,  7, 52, 13, 48,
       24,  2, 10, 35, 43, 25, 59, 51, 11, 50, 65, 56, 42, 61, 40, 44, 41,
       63, 45,  1, 23, 19,  8, 37, 57,  0, 60, 64, 18,  5, 62, 38, 15, 30,
       47,  3, 49, 58, 28, 26, 20,  6, 12, 16, 55, 29, 53, 46, 39]), 'cur_cost': 109468.0}, {'tour': [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52], 'cur_cost': 117476.0}, {'tour': array([29, 16, 22, 53, 32, 27, 47, 63, 40, 19, 60, 38,  7, 13, 21, 56, 26,
       37,  5, 44, 11, 36, 15, 12, 28, 33, 24, 25,  3, 48,  8, 58, 23,  2,
        1, 55, 64, 65, 43, 20, 61, 50,  9, 35, 54, 17, 34, 49,  0, 39, 62,
       42,  4, 51, 10, 30, 46, 57, 14, 52, 59, 45, 41, 18, 31,  6]), 'cur_cost': 112938.0}, {'tour': [25, 24, 31, 30, 1, 7, 48, 11, 56, 58, 13, 26, 54, 35, 14, 45, 64, 50, 3, 29, 28, 23, 10, 16, 59, 63, 40, 55, 51, 8, 60, 37, 6, 18, 39, 21, 61, 38, 46, 5, 47, 43, 33, 27, 22, 19, 52, 0, 41, 20, 12, 2, 17, 32, 9, 65, 57, 44, 49, 62, 36, 42, 15, 53, 4, 34], 'cur_cost': 101152.0}]
2025-06-22 18:58:16,719 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:58:16,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 18:58:16,721 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:58:16,721 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 18:58:16,721 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:16,722 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [25, 24, 31] and ending with [53, 4, 34]
- Cost: 101152.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8246153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:16,722 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:20,618 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13
  ],
  "modification_strategy": "To maximize diversity, I employed a strategy that prioritizes exploring unexplored regions and avoiding difficult ones. I used a combination of random walks with strategic jumps to connect distinct regions of the graph, favoring a different starting point and order compared to the original path. I tried to distribute the nodes across the entire range more evenly, breaking away from any potential localized clusters.",
  "targeted_regions": "Unexplored regions and areas around nodes 4, 13, 34, and 53, as these were at the end of the previous path. I prioritized traversing through the nodes that seemed less explored in the provided context."
}
```
2025-06-22 18:58:20,618 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:20,618 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108360.0, 路径: [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48]
2025-06-22 18:58:20,618 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48], 'cur_cost': 108360.0}
2025-06-22 18:58:20,618 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 17, 3, 38, 5, 1, 4, 10, 12, 9, 8, 7, 6, 20, 13, 25, 26, 27, 24, 16, 15, 14, 19, 18, 23, 22, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 2, 11, 0, 28], 'cur_cost': 25407.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 11, 7, 5, 1, 3, 0, 2, 4, 6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11300.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 41, 8, 15, 23, 60, 3, 10, 47, 25, 58, 1, 17, 31, 49, 6, 38, 43, 63, 50, 2, 28, 33, 22, 14, 56, 39, 5, 44, 65, 19, 4, 27, 62, 12, 46, 30, 55, 9, 35, 51, 59, 20, 26, 48, 11, 37, 29, 42, 16, 64, 24, 53, 7, 34, 40, 32, 18, 54, 13, 21, 36, 45, 0, 57, 61], 'cur_cost': 117076.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [32, 59, 41, 5, 24, 11, 60, 29, 48, 35, 2, 17, 55, 14, 44, 62, 1, 6, 25, 49, 3, 38, 20, 12, 54, 4, 10, 39, 30, 51, 64, 16, 65, 27, 45, 58, 31, 52, 0, 23, 46, 18, 61, 34, 57, 33, 50, 7, 15, 28, 40, 26, 19, 56, 8, 42, 13, 22, 9, 43, 63, 36, 37, 21, 53, 47], 'cur_cost': 126758.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52], 'cur_cost': 117476.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48], 'cur_cost': 108360.0}}]
2025-06-22 18:58:20,618 - __main__ - INFO - 进化阶段完成
2025-06-22 18:58:20,618 - __main__ - INFO - 开始评估阶段
2025-06-22 18:58:20,618 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:58:20,618 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:58:20,618 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/4
- Population changes:
  * Cost: min 9948.0->11300.0, mean 76337.3->93841.2
  * Diversity: 0.7276923076923076->0.8276923076923077
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 6 individuals, success rate 0.0%, avg improvement -11049.33
- Exploit: 4 individuals, success rate 25.0%, avg improvement -27185.75
- Overall improvement: -1352.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:58:27,489 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Struggling to Converge",
    "features": {
      "iteration": "0/4 (Very early)",
      "cost_trend": "Cost is increasing (both min and mean), indicating a lack of optimization and possibly some individuals moving into less optimal spaces.",
      "diversity_trend": "Diversity is slightly increasing, suggesting exploration is finding new areas of the search space.",
      "elite_solutions_trend": "Number of elite solutions is decreasing, which is concerning and indicative of problems with either the strategies themselves, the fitness landscape, or both."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Very Poor",
      "success_rate": "0.0%",
      "avg_improvement": "-11049.33",
      "summary": "Exploration is failing to find improvements. This suggests either the explored regions are inherently suboptimal, the exploration strategy isn't effective in this landscape, or the exploration parameters (step size, mutation rate, etc.) are poorly tuned."
    },
    "exploit": {
      "performance": "Poor",
      "success_rate": "25.0%",
      "avg_improvement": "-27185.75",
      "summary": "Exploitation is also performing poorly, with a very low success rate and a significant negative average improvement.  This suggests that the exploitation strategy is actively making solutions worse, possibly due to large steps or an inappropriate local search algorithm.  The few successful attempts did not mitigate the overall negative impact."
    },
    "overall_improvement": {
      "performance": "Negative",
      "value": "-1352.0",
      "summary": "The overall performance of the iteration is negative, meaning the new population is, on average, worse than the starting population. This signals a serious problem that needs to be addressed immediately."
    }
  },
  "balance_state": {
    "assessment": "Highly Unbalanced",
    "details": "The system is currently heavily biased towards worsening the solutions. Both exploration and exploitation are failing. This suggests a fundamental problem with the search space and the strategies employed.",
    "adjustment_needs": "Drastic adjustments are required. The immediate focus should be on identifying why *any* individuals are improving. The balance is secondary to simply trying to move in a positive direction."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Analyze Individual Performance:  Examine the characteristics of the individuals that were selected for exploration and exploitation. Why were they chosen? Are there any commonalities between them? Also, analyze the successful exploit attempts, if any, to identify what made them work better. This may give clues about which parameters are useful for exploration and/or exploitation.  Analyze the fitness function/landscape: If the individuals are truly moving into worse areas, then there could be a landscape issue. A local optimum might be to blame."
    },
    {
      "priority": "High",
      "action": "Parameter Tuning: Review and tune the parameters for both exploration and exploitation. Consider reducing the step size or mutation rate for both, especially exploitation. Try different exploration strategies. Consider using more directed exploration if possible."
    },
    {
      "priority": "High",
      "action": "Review Strategy Selection: Evaluate the criteria for choosing exploration and exploitation candidates. Are the right individuals being selected? Is the decision-making process biased?"
    },
    {
      "priority": "Medium",
      "action": "Increase Elitism (temporary): Given the rapidly worsening performance, temporarily increase the elite population to preserve high-quality solutions. This prevents losing potentially valuable individuals while the strategies are adjusted."
    },
    {
      "priority": "Low",
      "action": "Historical Data (future): If possible, add a method to generate historical data for your population and for each individual in your populations. If an individual starts improving, its historical path might offer some clues that you could use."
    }
  ]
}
```
2025-06-22 18:58:27,519 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:58:27,519 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Struggling to Converge",
    "features": {
      "iteration": "0/4 (Very early)",
      "cost_trend": "Cost is increasing (both min and mean), indicating a lack of optimization and possibly some individuals moving into less optimal spaces.",
      "diversity_trend": "Diversity is slightly increasing, suggesting exploration is finding new areas of the search space.",
      "elite_solutions_trend": "Number of elite solutions is decreasing, which is concerning and indicative of problems with either the strategies themselves, the fitness landscape, or both."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Very Poor",
      "success_rate": "0.0%",
      "avg_improvement": "-11049.33",
      "summary": "Exploration is failing to find improvements. This suggests either the explored regions are inherently suboptimal, the exploration strategy isn't effective in this landscape, or the exploration parameters (step size, mutation rate, etc.) are poorly tuned."
    },
    "exploit": {
      "performance": "Poor",
      "success_rate": "25.0%",
      "avg_improvement": "-27185.75",
      "summary": "Exploitation is also performing poorly, with a very low success rate and a significant negative average improvement.  This suggests that the exploitation strategy is actively making solutions worse, possibly due to large steps or an inappropriate local search algorithm.  The few successful attempts did not mitigate the overall negative impact."
    },
    "overall_improvement": {
      "performance": "Negative",
      "value": "-1352.0",
      "summary": "The overall performance of the iteration is negative, meaning the new population is, on average, worse than the starting population. This signals a serious problem that needs to be addressed immediately."
    }
  },
  "balance_state": {
    "assessment": "Highly Unbalanced",
    "details": "The system is currently heavily biased towards worsening the solutions. Both exploration and exploitation are failing. This suggests a fundamental problem with the search space and the strategies employed.",
    "adjustment_needs": "Drastic adjustments are required. The immediate focus should be on identifying why *any* individuals are improving. The balance is secondary to simply trying to move in a positive direction."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Analyze Individual Performance:  Examine the characteristics of the individuals that were selected for exploration and exploitation. Why were they chosen? Are there any commonalities between them? Also, analyze the successful exploit attempts, if any, to identify what made them work better. This may give clues about which parameters are useful for exploration and/or exploitation.  Analyze the fitness function/landscape: If the individuals are truly moving into worse areas, then there could be a landscape issue. A local optimum might be to blame."
    },
    {
      "priority": "High",
      "action": "Parameter Tuning: Review and tune the parameters for both exploration and exploitation. Consider reducing the step size or mutation rate for both, especially exploitation. Try different exploration strategies. Consider using more directed exploration if possible."
    },
    {
      "priority": "High",
      "action": "Review Strategy Selection: Evaluate the criteria for choosing exploration and exploitation candidates. Are the right individuals being selected? Is the decision-making process biased?"
    },
    {
      "priority": "Medium",
      "action": "Increase Elitism (temporary): Given the rapidly worsening performance, temporarily increase the elite population to preserve high-quality solutions. This prevents losing potentially valuable individuals while the strategies are adjusted."
    },
    {
      "priority": "Low",
      "action": "Historical Data (future): If possible, add a method to generate historical data for your population and for each individual in your populations. If an individual starts improving, its historical path might offer some clues that you could use."
    }
  ]
}
```
2025-06-22 18:58:27,520 - __main__ - INFO - 评估阶段完成
2025-06-22 18:58:27,520 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Struggling to Converge",
    "features": {
      "iteration": "0/4 (Very early)",
      "cost_trend": "Cost is increasing (both min and mean), indicating a lack of optimization and possibly some individuals moving into less optimal spaces.",
      "diversity_trend": "Diversity is slightly increasing, suggesting exploration is finding new areas of the search space.",
      "elite_solutions_trend": "Number of elite solutions is decreasing, which is concerning and indicative of problems with either the strategies themselves, the fitness landscape, or both."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Very Poor",
      "success_rate": "0.0%",
      "avg_improvement": "-11049.33",
      "summary": "Exploration is failing to find improvements. This suggests either the explored regions are inherently suboptimal, the exploration strategy isn't effective in this landscape, or the exploration parameters (step size, mutation rate, etc.) are poorly tuned."
    },
    "exploit": {
      "performance": "Poor",
      "success_rate": "25.0%",
      "avg_improvement": "-27185.75",
      "summary": "Exploitation is also performing poorly, with a very low success rate and a significant negative average improvement.  This suggests that the exploitation strategy is actively making solutions worse, possibly due to large steps or an inappropriate local search algorithm.  The few successful attempts did not mitigate the overall negative impact."
    },
    "overall_improvement": {
      "performance": "Negative",
      "value": "-1352.0",
      "summary": "The overall performance of the iteration is negative, meaning the new population is, on average, worse than the starting population. This signals a serious problem that needs to be addressed immediately."
    }
  },
  "balance_state": {
    "assessment": "Highly Unbalanced",
    "details": "The system is currently heavily biased towards worsening the solutions. Both exploration and exploitation are failing. This suggests a fundamental problem with the search space and the strategies employed.",
    "adjustment_needs": "Drastic adjustments are required. The immediate focus should be on identifying why *any* individuals are improving. The balance is secondary to simply trying to move in a positive direction."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Analyze Individual Performance:  Examine the characteristics of the individuals that were selected for exploration and exploitation. Why were they chosen? Are there any commonalities between them? Also, analyze the successful exploit attempts, if any, to identify what made them work better. This may give clues about which parameters are useful for exploration and/or exploitation.  Analyze the fitness function/landscape: If the individuals are truly moving into worse areas, then there could be a landscape issue. A local optimum might be to blame."
    },
    {
      "priority": "High",
      "action": "Parameter Tuning: Review and tune the parameters for both exploration and exploitation. Consider reducing the step size or mutation rate for both, especially exploitation. Try different exploration strategies. Consider using more directed exploration if possible."
    },
    {
      "priority": "High",
      "action": "Review Strategy Selection: Evaluate the criteria for choosing exploration and exploitation candidates. Are the right individuals being selected? Is the decision-making process biased?"
    },
    {
      "priority": "Medium",
      "action": "Increase Elitism (temporary): Given the rapidly worsening performance, temporarily increase the elite population to preserve high-quality solutions. This prevents losing potentially valuable individuals while the strategies are adjusted."
    },
    {
      "priority": "Low",
      "action": "Historical Data (future): If possible, add a method to generate historical data for your population and for each individual in your populations. If an individual starts improving, its historical path might offer some clues that you could use."
    }
  ]
}
```
2025-06-22 18:58:27,520 - __main__ - INFO - 当前最佳适应度: 11300.0
2025-06-22 18:58:27,520 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 18:58:27,520 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 18:58:27,520 - __main__ - INFO - 开始分析阶段
2025-06-22 18:58:27,520 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:58:27,538 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11300.0, 'max': 126758.0, 'mean': 93841.2, 'std': 38515.800757091885}, 'diversity': 0.9572390572390573, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:58:27,538 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11300.0, 'max': 126758.0, 'mean': 93841.2, 'std': 38515.800757091885}, 'diversity_level': 0.9572390572390573, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:58:27,538 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:58:27,542 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:58:27,542 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:58:27,543 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:58:27,545 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:58:27,545 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:58:27,545 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:58:27,545 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:58:27,545 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:58:27,548 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:58:27,548 - __main__ - INFO - 精英专家分析报告: {'elite_count': 16, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1779.0, 'avg_gap': 84282.825}, 'structure_gap': {'unique_elite_edges': 184, 'unique_pop_edges': 527, 'common_edges': 45}}, 'elite_diversity': {'diversity_score': 0.2609848484848485}}
2025-06-22 18:58:27,548 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:58:27,551 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:58:27,551 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:58:27,551 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11300.0, max=126758.0, mean=93841.2, std=38515.800757091885
- Diversity: 0.9572390572390573, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 1/4
- Historical trend: Recent costs: [11300.0]
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:58:32,830 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely high, given the large cost range (min to max) and high standard deviation relative to the mean cost. This suggests a landscape with many local optima.",
    "modality": "Unknown, but the ruggedness suggests a multimodal landscape (many peaks and valleys).",
    "deceptiveness": "Unknown, but potentially present. The high exploration phase (see below) might be a sign of the search process trying to overcome local optima and navigate a complex landscape."
  },
  "population_state": {
    "diversity": "High (0.957), indicating a wide variety of solutions are present. This is expected in early iterations and especially with a low number of individuals. This is a good sign for exploration.",
    "convergence": "Very low (0.0), which is expected at the start of the optimization and in a highly exploratory phase.",
    "clustering": "No clustering information. Further analysis is required after more iterations to detect patterns in the solutions."
  },
  "difficult_regions": {
    "identified_challenges": "None identified at this stage due to the lack of solution information and iteration count. The algorithm has just started, and we haven't observed any detrimental patterns or areas of concentrated difficulty. This needs to be revised based on emerging solutions and how they evolve across iterations.",
    "node_sequences_to_avoid": "N/A",
    "edges_to_avoid": "N/A"
  },
  "opportunity_regions": {
    "identified_opportunities": "None identified with current information. The exploration phase will likely discover opportunities later.",
    "node_sequences_to_include": "N/A",
    "edges_to_include": "N/A"
  },
  "evolution_phase": "Exploration. The combination of high diversity, low convergence, and a very low iteration count indicates the algorithm is predominantly exploring the search space.",
  "evolution_direction": {
    "strategy": "Continue exploration, but with a focus on refining promising areas once identified.",
    "operator_suggestions": [
      "Mutation operators: Continue using a variety of mutation operators to maintain diversity and escape local optima. This is critical in the initial phase.",
      "Crossover operators: Focus on crossover operators that promote edge exchange, and try to generate diverse offspring (e.g., partially mapped crossover, order crossover). Since the TSP instance is unknown, we have to make assumptions.",
      "Selection pressure: Use a selection mechanism with enough pressure to favour good solutions, but not so much to lose diversity. Ensure that the selection preserves some of the diversity in the population by using a diversified selection operator (e.g. a combination of tournament and roulette).",
      "Local search (Optional): Consider incorporating a local search operator (e.g., 2-opt or 3-opt) in later iterations, especially if convergence starts to increase. For now, however, it could hurt exploration."
    ]
  }
}
```

2025-06-22 18:58:32,830 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:58:32,830 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely high, given the large cost range (min to max) and high standard deviation relative to the mean cost. This suggests a landscape with many local optima.', 'modality': 'Unknown, but the ruggedness suggests a multimodal landscape (many peaks and valleys).', 'deceptiveness': 'Unknown, but potentially present. The high exploration phase (see below) might be a sign of the search process trying to overcome local optima and navigate a complex landscape.'}, 'population_state': {'diversity': 'High (0.957), indicating a wide variety of solutions are present. This is expected in early iterations and especially with a low number of individuals. This is a good sign for exploration.', 'convergence': 'Very low (0.0), which is expected at the start of the optimization and in a highly exploratory phase.', 'clustering': 'No clustering information. Further analysis is required after more iterations to detect patterns in the solutions.'}, 'difficult_regions': {'identified_challenges': "None identified at this stage due to the lack of solution information and iteration count. The algorithm has just started, and we haven't observed any detrimental patterns or areas of concentrated difficulty. This needs to be revised based on emerging solutions and how they evolve across iterations.", 'node_sequences_to_avoid': 'N/A', 'edges_to_avoid': 'N/A'}, 'opportunity_regions': {'identified_opportunities': 'None identified with current information. The exploration phase will likely discover opportunities later.', 'node_sequences_to_include': 'N/A', 'edges_to_include': 'N/A'}, 'evolution_phase': 'Exploration. The combination of high diversity, low convergence, and a very low iteration count indicates the algorithm is predominantly exploring the search space.', 'evolution_direction': {'strategy': 'Continue exploration, but with a focus on refining promising areas once identified.', 'operator_suggestions': ['Mutation operators: Continue using a variety of mutation operators to maintain diversity and escape local optima. This is critical in the initial phase.', 'Crossover operators: Focus on crossover operators that promote edge exchange, and try to generate diverse offspring (e.g., partially mapped crossover, order crossover). Since the TSP instance is unknown, we have to make assumptions.', 'Selection pressure: Use a selection mechanism with enough pressure to favour good solutions, but not so much to lose diversity. Ensure that the selection preserves some of the diversity in the population by using a diversified selection operator (e.g. a combination of tournament and roulette).', 'Local search (Optional): Consider incorporating a local search operator (e.g., 2-opt or 3-opt) in later iterations, especially if convergence starts to increase. For now, however, it could hurt exploration.']}}
2025-06-22 18:58:32,830 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:58:32,830 - __main__ - INFO - 分析阶段完成
2025-06-22 18:58:32,830 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely high, given the large cost range (min to max) and high standard deviation relative to the mean cost. This suggests a landscape with many local optima.', 'modality': 'Unknown, but the ruggedness suggests a multimodal landscape (many peaks and valleys).', 'deceptiveness': 'Unknown, but potentially present. The high exploration phase (see below) might be a sign of the search process trying to overcome local optima and navigate a complex landscape.'}, 'population_state': {'diversity': 'High (0.957), indicating a wide variety of solutions are present. This is expected in early iterations and especially with a low number of individuals. This is a good sign for exploration.', 'convergence': 'Very low (0.0), which is expected at the start of the optimization and in a highly exploratory phase.', 'clustering': 'No clustering information. Further analysis is required after more iterations to detect patterns in the solutions.'}, 'difficult_regions': {'identified_challenges': "None identified at this stage due to the lack of solution information and iteration count. The algorithm has just started, and we haven't observed any detrimental patterns or areas of concentrated difficulty. This needs to be revised based on emerging solutions and how they evolve across iterations.", 'node_sequences_to_avoid': 'N/A', 'edges_to_avoid': 'N/A'}, 'opportunity_regions': {'identified_opportunities': 'None identified with current information. The exploration phase will likely discover opportunities later.', 'node_sequences_to_include': 'N/A', 'edges_to_include': 'N/A'}, 'evolution_phase': 'Exploration. The combination of high diversity, low convergence, and a very low iteration count indicates the algorithm is predominantly exploring the search space.', 'evolution_direction': {'strategy': 'Continue exploration, but with a focus on refining promising areas once identified.', 'operator_suggestions': ['Mutation operators: Continue using a variety of mutation operators to maintain diversity and escape local optima. This is critical in the initial phase.', 'Crossover operators: Focus on crossover operators that promote edge exchange, and try to generate diverse offspring (e.g., partially mapped crossover, order crossover). Since the TSP instance is unknown, we have to make assumptions.', 'Selection pressure: Use a selection mechanism with enough pressure to favour good solutions, but not so much to lose diversity. Ensure that the selection preserves some of the diversity in the population by using a diversified selection operator (e.g. a combination of tournament and roulette).', 'Local search (Optional): Consider incorporating a local search operator (e.g., 2-opt or 3-opt) in later iterations, especially if convergence starts to increase. For now, however, it could hurt exploration.']}}
2025-06-22 18:58:32,834 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:58:32,834 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:58:32,835 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High (0.957), indicating a wide variety of solutions are present. This is expected in early iterations and especially with a low number of individuals. This is a good sign for exploration.
- Best cost: 11300.0, Mean cost: 93841.2
- Search space: Ruggedness: Likely high, given the large cost range (min to max) and high standard deviation relative to the mean cost. This suggests a landscape with many local optima., Modality: Unknown, but the ruggedness suggests a multimodal landscape (many peaks and valleys).
- Difficult regions: 3 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Struggling to Converge",
    "features": {
      "iteration": "0/4 (Very early)",
      "cost_trend": "Cost is increasing (both min and ...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:58:32,835 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:58:35,362 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size": 0.1,
      "local_search_iterations": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 1
    }
  }
}
```

2025-06-22 18:58:35,362 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:58:35,362 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:58:35,362 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size": 0.1,
      "local_search_iterations": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 1
    }
  }
}
```

2025-06-22 18:58:35,362 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:58:35,362 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:58:35,362 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size": 0.1,
      "local_search_iterations": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 1
    }
  }
}
```

2025-06-22 18:58:35,362 - __main__ - INFO - 开始进化阶段
2025-06-22 18:58:35,362 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:58:35,362 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:35,362 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [21, 17, 3] and ending with [11, 0, 28]
- Cost: 25407.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:35,362 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:38,670 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62
  ],
  "modification_strategy": "This strategy prioritizes a complete re-ordering, aiming for high diversity.  It starts by keeping the initial few nodes to have an initial common point and then uses a less deterministic pattern for exploring nodes. This aims to diversify the path while still respecting the starting and ending points of the given path.",
  "targeted_regions": "Focus on exploring different sequences within the 66 nodes, prioritizing more 'untouched' sequences from the given path to find a different permutation."
}
```
2025-06-22 18:58:38,670 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:38,670 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27952.0, 路径: [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62]
2025-06-22 18:58:38,670 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}
2025-06-22 18:58:38,670 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:58:38,670 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:38,670 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 11, 7] and ending with [63, 64, 65]
- Cost: 11300.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8630769230769231
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:38,670 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:44,764 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This strategy prioritizes exploring unexplored regions by starting with a small cluster of nodes and then expanding to other nodes in a more diverse manner. The initial nodes are designed to ensure a varied start from the original path. The strategy then systematically includes all nodes from 0 to 65 once each.",
  "targeted_regions": "Targeted exploration of all node indices, ensuring the entire problem space is covered for maximum diversity. Emphasis on spreading the early nodes out to differentiate from the start of the prior path."
}
```
2025-06-22 18:58:44,764 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:44,764 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13812.0, 路径: [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:58:44,764 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}
2025-06-22 18:58:44,764 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:58:44,764 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:44,764 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 0, 39] and ending with [43, 53, 45]
- Cost: 111739.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.86
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:44,764 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:47,934 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65
  ],
  "modification_strategy": "This strategy prioritizes a complete overhaul of the existing path, focusing on a linear traversal through the nodes. This provides high diversity as it breaks away from the original structure. The path traverses the nodes in sequential order and ensures that all nodes are visited exactly once, complying with the TSP constraints and focusing on exploring unexplored regions effectively.",
  "targeted_regions": "All regions are targeted equally to ensure complete path exploration. This approach maximizes the likelihood of uncovering new promising regions within the given constraints."
}
```
2025-06-22 18:58:47,934 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:47,934 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19777.0, 路径: [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65]
2025-06-22 18:58:47,934 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}
2025-06-22 18:58:47,934 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:58:47,934 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:47,934 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [52, 41, 8] and ending with [0, 57, 61]
- Cost: 117076.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8476923076923077
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:47,934 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:51,683 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57
  ],
  "modification_strategy": "This strategy prioritizes diversification by reordering the path. The initial segment mirrors the original, followed by a complete shuffle of unexplored regions, and then the final segment is constructed to link the new sections back to the end. This ensures a significant departure from the original path while maintaining the necessary path constraints.",
  "targeted_regions": "All 3 Opportunity Regions were targeted for improved coverage of unexplored space. Also the beginning and the end have been kept the same to ensure that the final path still reaches the target node."
}
```
2025-06-22 18:58:51,683 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:51,683 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109038.0, 路径: [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63]
2025-06-22 18:58:51,683 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}
2025-06-22 18:58:51,683 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:58:51,683 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:51,683 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 5, 47] and ending with [55, 6, 12]
- Cost: 97890.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8553846153846154
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:51,683 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:58:56,231 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10
  ],
  "modification_strategy": "Implemented a path generation strategy that prioritized exploration of unexplored and difficult regions as identified in the search context. The path was constructed using a randomized approach, starting with the known starting point [27, 5, 47], and then incorporating nodes that are likely to lead to new regions and avoid difficult ones. This was done through a random shuffling of the nodes, biased towards nodes that were initially further away from the start and end points of the original path. ",
  "targeted_regions": "Focused on 3 opportunity regions and 3 difficult regions, strategically choosing nodes throughout to explore these regions and create a more diverse path that attempts to uncover new areas within the TSP solution space."
}
```
2025-06-22 18:58:56,231 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:58:56,231 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117605.0, 路径: [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56]
2025-06-22 18:58:56,231 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}
2025-06-22 18:58:56,231 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:58:56,231 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:56,231 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:56,231 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 111759.0
2025-06-22 18:58:58,116 - ExploitationExpert - INFO - res_population_num: 21
2025-06-22 18:58:58,116 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:58:58,116 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:58:58,125 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:58,125 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}, {'tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}, {'tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}, {'tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}, {'tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}, {'tour': array([26, 30, 52, 25, 49, 23,  8, 60, 59, 62, 33, 38, 57, 65, 24, 21, 15,
       34, 44, 40,  2, 48, 61, 18,  6, 22, 58, 20,  0,  7, 36, 11,  1, 42,
       27, 64, 46, 56, 51, 55, 12, 29, 41, 45,  3, 17, 28, 39, 53, 47, 14,
       16, 32, 43, 50, 10, 13,  5, 54,  4, 37, 63, 31, 19, 35,  9]), 'cur_cost': 111759.0}, {'tour': array([27,  9, 21, 34, 14, 17, 54, 33, 36, 32, 22, 31,  4,  7, 52, 13, 48,
       24,  2, 10, 35, 43, 25, 59, 51, 11, 50, 65, 56, 42, 61, 40, 44, 41,
       63, 45,  1, 23, 19,  8, 37, 57,  0, 60, 64, 18,  5, 62, 38, 15, 30,
       47,  3, 49, 58, 28, 26, 20,  6, 12, 16, 55, 29, 53, 46, 39]), 'cur_cost': 109468.0}, {'tour': [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52], 'cur_cost': 117476.0}, {'tour': array([29, 16, 22, 53, 32, 27, 47, 63, 40, 19, 60, 38,  7, 13, 21, 56, 26,
       37,  5, 44, 11, 36, 15, 12, 28, 33, 24, 25,  3, 48,  8, 58, 23,  2,
        1, 55, 64, 65, 43, 20, 61, 50,  9, 35, 54, 17, 34, 49,  0, 39, 62,
       42,  4, 51, 10, 30, 46, 57, 14, 52, 59, 45, 41, 18, 31,  6]), 'cur_cost': 112938.0}, {'tour': [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48], 'cur_cost': 108360.0}]
2025-06-22 18:58:58,127 - ExploitationExpert - INFO - 局部搜索耗时: 1.90秒
2025-06-22 18:58:58,127 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 18:58:58,127 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:58:58,128 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:58:58,128 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:58,128 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:58,128 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100977.0
2025-06-22 18:58:58,631 - ExploitationExpert - INFO - res_population_num: 26
2025-06-22 18:58:58,632 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:58:58,632 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:58:58,643 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:58,643 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}, {'tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}, {'tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}, {'tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}, {'tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}, {'tour': array([26, 30, 52, 25, 49, 23,  8, 60, 59, 62, 33, 38, 57, 65, 24, 21, 15,
       34, 44, 40,  2, 48, 61, 18,  6, 22, 58, 20,  0,  7, 36, 11,  1, 42,
       27, 64, 46, 56, 51, 55, 12, 29, 41, 45,  3, 17, 28, 39, 53, 47, 14,
       16, 32, 43, 50, 10, 13,  5, 54,  4, 37, 63, 31, 19, 35,  9]), 'cur_cost': 111759.0}, {'tour': array([49,  8, 40, 45, 22, 12, 34, 58, 53, 51, 20, 29, 11, 54, 15, 18,  1,
       24, 17, 50, 31, 27, 63, 33,  2, 39, 47, 56, 10, 57,  0, 61, 32, 48,
       13, 52,  7, 16, 62, 65, 55, 28, 14, 26, 41, 64, 36,  4, 60, 42, 23,
       38, 21, 19,  5,  6, 30, 44,  9, 25, 59,  3, 37, 35, 43, 46]), 'cur_cost': 100977.0}, {'tour': [53, 35, 13, 2, 60, 45, 18, 38, 5, 27, 30, 64, 43, 1, 51, 22, 55, 10, 20, 48, 33, 62, 29, 9, 16, 4, 59, 37, 15, 25, 32, 65, 3, 41, 23, 56, 11, 46, 0, 57, 26, 39, 54, 40, 44, 7, 61, 14, 12, 42, 34, 21, 63, 8, 24, 50, 19, 28, 36, 58, 49, 6, 47, 17, 31, 52], 'cur_cost': 117476.0}, {'tour': array([29, 16, 22, 53, 32, 27, 47, 63, 40, 19, 60, 38,  7, 13, 21, 56, 26,
       37,  5, 44, 11, 36, 15, 12, 28, 33, 24, 25,  3, 48,  8, 58, 23,  2,
        1, 55, 64, 65, 43, 20, 61, 50,  9, 35, 54, 17, 34, 49,  0, 39, 62,
       42,  4, 51, 10, 30, 46, 57, 14, 52, 59, 45, 41, 18, 31,  6]), 'cur_cost': 112938.0}, {'tour': [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48], 'cur_cost': 108360.0}]
2025-06-22 18:58:58,645 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:58:58,645 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 18:58:58,645 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:58:58,645 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:58:58,645 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:58:58,645 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:58:58,647 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 113451.0
2025-06-22 18:58:59,150 - ExploitationExpert - INFO - res_population_num: 30
2025-06-22 18:58:59,150 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:58:59,150 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:58:59,164 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:58:59,164 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}, {'tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}, {'tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}, {'tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}, {'tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}, {'tour': array([26, 30, 52, 25, 49, 23,  8, 60, 59, 62, 33, 38, 57, 65, 24, 21, 15,
       34, 44, 40,  2, 48, 61, 18,  6, 22, 58, 20,  0,  7, 36, 11,  1, 42,
       27, 64, 46, 56, 51, 55, 12, 29, 41, 45,  3, 17, 28, 39, 53, 47, 14,
       16, 32, 43, 50, 10, 13,  5, 54,  4, 37, 63, 31, 19, 35,  9]), 'cur_cost': 111759.0}, {'tour': array([49,  8, 40, 45, 22, 12, 34, 58, 53, 51, 20, 29, 11, 54, 15, 18,  1,
       24, 17, 50, 31, 27, 63, 33,  2, 39, 47, 56, 10, 57,  0, 61, 32, 48,
       13, 52,  7, 16, 62, 65, 55, 28, 14, 26, 41, 64, 36,  4, 60, 42, 23,
       38, 21, 19,  5,  6, 30, 44,  9, 25, 59,  3, 37, 35, 43, 46]), 'cur_cost': 100977.0}, {'tour': array([ 3, 27,  5, 36, 15,  7, 33, 45, 19,  1, 37, 44,  9, 18, 23, 54, 49,
       41, 64, 56, 63, 17, 10, 26, 50, 25, 34, 11, 51, 61, 48, 32, 22, 57,
       43, 31, 42, 14, 20, 47, 16, 40, 58, 35, 55, 62, 38, 24,  2, 60, 29,
       13, 21, 28, 53,  8, 52,  0, 30, 12, 59, 39, 65,  4, 46,  6]), 'cur_cost': 113451.0}, {'tour': array([29, 16, 22, 53, 32, 27, 47, 63, 40, 19, 60, 38,  7, 13, 21, 56, 26,
       37,  5, 44, 11, 36, 15, 12, 28, 33, 24, 25,  3, 48,  8, 58, 23,  2,
        1, 55, 64, 65, 43, 20, 61, 50,  9, 35, 54, 17, 34, 49,  0, 39, 62,
       42,  4, 51, 10, 30, 46, 57, 14, 52, 59, 45, 41, 18, 31,  6]), 'cur_cost': 112938.0}, {'tour': [25, 42, 11, 58, 2, 63, 14, 49, 37, 45, 19, 39, 51, 29, 61, 3, 32, 24, 65, 34, 22, 8, 53, 4, 6, 55, 16, 43, 12, 23, 27, 47, 5, 57, 35, 46, 0, 1, 30, 59, 62, 9, 17, 33, 44, 28, 20, 31, 54, 36, 15, 56, 64, 38, 18, 10, 50, 40, 21, 7, 41, 26, 52, 13, 60, 48], 'cur_cost': 108360.0}]
2025-06-22 18:58:59,166 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:58:59,167 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 18:58:59,167 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:58:59,167 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 18:58:59,167 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:58:59,168 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [29, 16, 22] and ending with [18, 31, 6]
- Cost: 112938.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8569230769230769
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:58:59,168 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:02,672 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17, 6],
  "modification_strategy": "Prioritized exploration of unexplored regions and diversification. The new path starts from the same starting point and maintains a relatively balanced distribution of node indices within the overall sequence. We attempt to insert nodes from unexplored regions in the beginning to prioritize the areas that the current path does not visit. The reordering is guided by the constraints and the requirements of TSP.",
  "targeted_regions": "Unexplored regions and regions with a high diversity score, specifically focusing on integrating nodes not present in the original path early on to influence later path decisions. Also, paying attention to the opportunity regions to incorporate nodes not present in the original path. Avoiding the difficult regions."
}
```
2025-06-22 18:59:02,672 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:02,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105948.0, 路径: [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17]
2025-06-22 18:59:02,672 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17], 'cur_cost': 105948.0}
2025-06-22 18:59:02,672 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:59:02,672 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:59:02,672 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:59:02,672 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 116543.0
2025-06-22 18:59:03,173 - ExploitationExpert - INFO - res_population_num: 31
2025-06-22 18:59:03,173 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:59:03,174 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:59:03,188 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:59:03,188 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}, {'tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}, {'tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}, {'tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}, {'tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}, {'tour': array([26, 30, 52, 25, 49, 23,  8, 60, 59, 62, 33, 38, 57, 65, 24, 21, 15,
       34, 44, 40,  2, 48, 61, 18,  6, 22, 58, 20,  0,  7, 36, 11,  1, 42,
       27, 64, 46, 56, 51, 55, 12, 29, 41, 45,  3, 17, 28, 39, 53, 47, 14,
       16, 32, 43, 50, 10, 13,  5, 54,  4, 37, 63, 31, 19, 35,  9]), 'cur_cost': 111759.0}, {'tour': array([49,  8, 40, 45, 22, 12, 34, 58, 53, 51, 20, 29, 11, 54, 15, 18,  1,
       24, 17, 50, 31, 27, 63, 33,  2, 39, 47, 56, 10, 57,  0, 61, 32, 48,
       13, 52,  7, 16, 62, 65, 55, 28, 14, 26, 41, 64, 36,  4, 60, 42, 23,
       38, 21, 19,  5,  6, 30, 44,  9, 25, 59,  3, 37, 35, 43, 46]), 'cur_cost': 100977.0}, {'tour': array([ 3, 27,  5, 36, 15,  7, 33, 45, 19,  1, 37, 44,  9, 18, 23, 54, 49,
       41, 64, 56, 63, 17, 10, 26, 50, 25, 34, 11, 51, 61, 48, 32, 22, 57,
       43, 31, 42, 14, 20, 47, 16, 40, 58, 35, 55, 62, 38, 24,  2, 60, 29,
       13, 21, 28, 53,  8, 52,  0, 30, 12, 59, 39, 65,  4, 46,  6]), 'cur_cost': 113451.0}, {'tour': [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17], 'cur_cost': 105948.0}, {'tour': array([10, 49, 35, 26,  3, 12,  5, 39, 28, 38, 59, 58, 42, 62, 30, 48, 41,
       25, 60, 46, 54, 44, 18, 27, 17, 56, 29, 11, 57, 34, 19,  9, 52, 31,
        8, 63, 43, 22, 50, 53, 45,  1,  6,  7,  2, 51, 13, 36, 16, 65, 32,
        0, 23, 20, 47, 40,  4, 21, 24, 61, 55, 33, 15, 64, 37, 14]), 'cur_cost': 116543.0}]
2025-06-22 18:59:03,190 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:59:03,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 18:59:03,190 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:59:03,191 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 17, 3, 6, 12, 25, 30, 35, 40, 45, 50, 55, 60, 64, 65, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 1, 0, 2, 4, 5, 7, 9, 10, 11, 14, 15, 16, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 37, 39, 41, 42, 44, 46, 47, 49, 51, 52, 54, 56, 57, 59, 61, 62], 'cur_cost': 27952.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 8, 12, 13, 11, 7, 6, 4, 5, 3, 2, 1, 0, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13812.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19777.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 41, 8, 33, 15, 2, 48, 1, 6, 39, 4, 64, 31, 26, 59, 54, 17, 5, 62, 11, 42, 28, 35, 56, 38, 45, 60, 29, 23, 10, 19, 49, 14, 21, 30, 65, 3, 9, 40, 18, 44, 37, 58, 55, 20, 43, 34, 46, 25, 16, 53, 24, 51, 32, 13, 61, 0, 22, 27, 12, 50, 36, 47, 57, 7, 63], 'cur_cost': 109038.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [27, 4, 18, 58, 33, 61, 24, 38, 41, 11, 50, 1, 35, 47, 2, 16, 37, 5, 13, 52, 65, 29, 57, 42, 0, 60, 6, 32, 48, 23, 15, 19, 9, 54, 3, 26, 36, 46, 39, 53, 17, 40, 62, 21, 45, 55, 14, 30, 20, 28, 64, 59, 49, 22, 31, 12, 63, 8, 51, 34, 44, 7, 25, 43, 10, 56], 'cur_cost': 117605.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17], 'cur_cost': 105948.0}}]
2025-06-22 18:59:03,191 - __main__ - INFO - 进化阶段完成
2025-06-22 18:59:03,191 - __main__ - INFO - 开始评估阶段
2025-06-22 18:59:03,191 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:59:03,192 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:59:03,192 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/4
- Population changes:
  * Cost: min 11300.0->13812.0, mean 93841.2->83686.2
  * Diversity: 0.8276923076923077->0.8461538461538461
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 50.0%, avg improvement 13703.0
- Exploit: 4 individuals, success rate 75.0%, avg improvement 4833.0
- Overall improvement: -2512.0

## Historical Trends:
Last 1 iterations: costs [11300.0], diversity [0.9572390572390573]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:59:08,353 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration - Initial Exploration",
    "features": [
      "Iteration 1 of 4, indicating the beginning of the evolutionary process.",
      "Cost is increasing on average and for the best solution (11300.0 -> 13812.0). This could be due to the exploration of new areas of the search space.",
      "Diversity is slightly increasing (0.8276923076923077 -> 0.8461538461538461). This suggests that the population is maintaining a good spread of solutions, though this is only based on one generation.",
      "Elite solutions remain constant (1->1), indicating the best solution from the previous generation is still in the current generation.",
      "The historical trend shows an initial cost of 11300.0 and a diversity of 0.9572390572390573 in the previous iteration."
    ]
  },
  "strategy_effectiveness": {
    "exploration": {
      "success_rate": 0.5,
      "avg_improvement": 13703.0,
      "evaluation": "Exploration is showing some improvement, with a 50% success rate and a considerable average improvement in individuals that successfully explore new solutions. However, this positive trend isn't yet strong enough to bring down overall cost."
    },
    "exploitation": {
      "success_rate": 0.75,
      "avg_improvement": 4833.0,
      "evaluation": "Exploitation shows a higher success rate (75%) and a smaller average improvement than exploration. This suggests that Exploitation is doing better at finding successful improvements."
    },
    "overall_improvement": {
      "overall": -2512.0,
      "evaluation": "Overall improvement is negative. This is likely due to the exploration pushing the cost of some individuals higher than the improvements made by exploitation. It's important to note this is only one generation of data."
    }
  },
  "balance_state": {
    "assessment": "The current balance is slightly skewed toward exploration, or a balance that is being less successful overall. While both strategies generate some success, it is not enough to generate positive overall results.",
    "adjustment_needs": "Consider increasing the weight of the exploitation strategy in the next iteration, especially if the exploration strategy isn't rapidly identifying significantly better solutions. Review the parameters of the exploration strategy to see if can be made more efficient or less costly."
  },
  "recommendations": [
    "Reduce the exploration rate slightly for this iteration.",
    "Fine-tune the exploitation strategy to make it more aggressive, and/or more diverse in the solutions it assesses.",
    "Analyze the characteristics of the failed explorations to understand why they are not improving performance.",
    "Monitor cost and diversity carefully in the next iteration to evaluate the effect of the changes.",
    "Compare the characteristics of the single elite solutions in each iteration to understand the current search bias."
  ]
}
```
2025-06-22 18:59:08,382 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:59:08,382 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration - Initial Exploration",
    "features": [
      "Iteration 1 of 4, indicating the beginning of the evolutionary process.",
      "Cost is increasing on average and for the best solution (11300.0 -> 13812.0). This could be due to the exploration of new areas of the search space.",
      "Diversity is slightly increasing (0.8276923076923077 -> 0.8461538461538461). This suggests that the population is maintaining a good spread of solutions, though this is only based on one generation.",
      "Elite solutions remain constant (1->1), indicating the best solution from the previous generation is still in the current generation.",
      "The historical trend shows an initial cost of 11300.0 and a diversity of 0.9572390572390573 in the previous iteration."
    ]
  },
  "strategy_effectiveness": {
    "exploration": {
      "success_rate": 0.5,
      "avg_improvement": 13703.0,
      "evaluation": "Exploration is showing some improvement, with a 50% success rate and a considerable average improvement in individuals that successfully explore new solutions. However, this positive trend isn't yet strong enough to bring down overall cost."
    },
    "exploitation": {
      "success_rate": 0.75,
      "avg_improvement": 4833.0,
      "evaluation": "Exploitation shows a higher success rate (75%) and a smaller average improvement than exploration. This suggests that Exploitation is doing better at finding successful improvements."
    },
    "overall_improvement": {
      "overall": -2512.0,
      "evaluation": "Overall improvement is negative. This is likely due to the exploration pushing the cost of some individuals higher than the improvements made by exploitation. It's important to note this is only one generation of data."
    }
  },
  "balance_state": {
    "assessment": "The current balance is slightly skewed toward exploration, or a balance that is being less successful overall. While both strategies generate some success, it is not enough to generate positive overall results.",
    "adjustment_needs": "Consider increasing the weight of the exploitation strategy in the next iteration, especially if the exploration strategy isn't rapidly identifying significantly better solutions. Review the parameters of the exploration strategy to see if can be made more efficient or less costly."
  },
  "recommendations": [
    "Reduce the exploration rate slightly for this iteration.",
    "Fine-tune the exploitation strategy to make it more aggressive, and/or more diverse in the solutions it assesses.",
    "Analyze the characteristics of the failed explorations to understand why they are not improving performance.",
    "Monitor cost and diversity carefully in the next iteration to evaluate the effect of the changes.",
    "Compare the characteristics of the single elite solutions in each iteration to understand the current search bias."
  ]
}
```
2025-06-22 18:59:08,382 - __main__ - INFO - 评估阶段完成
2025-06-22 18:59:08,387 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration - Initial Exploration",
    "features": [
      "Iteration 1 of 4, indicating the beginning of the evolutionary process.",
      "Cost is increasing on average and for the best solution (11300.0 -> 13812.0). This could be due to the exploration of new areas of the search space.",
      "Diversity is slightly increasing (0.8276923076923077 -> 0.8461538461538461). This suggests that the population is maintaining a good spread of solutions, though this is only based on one generation.",
      "Elite solutions remain constant (1->1), indicating the best solution from the previous generation is still in the current generation.",
      "The historical trend shows an initial cost of 11300.0 and a diversity of 0.9572390572390573 in the previous iteration."
    ]
  },
  "strategy_effectiveness": {
    "exploration": {
      "success_rate": 0.5,
      "avg_improvement": 13703.0,
      "evaluation": "Exploration is showing some improvement, with a 50% success rate and a considerable average improvement in individuals that successfully explore new solutions. However, this positive trend isn't yet strong enough to bring down overall cost."
    },
    "exploitation": {
      "success_rate": 0.75,
      "avg_improvement": 4833.0,
      "evaluation": "Exploitation shows a higher success rate (75%) and a smaller average improvement than exploration. This suggests that Exploitation is doing better at finding successful improvements."
    },
    "overall_improvement": {
      "overall": -2512.0,
      "evaluation": "Overall improvement is negative. This is likely due to the exploration pushing the cost of some individuals higher than the improvements made by exploitation. It's important to note this is only one generation of data."
    }
  },
  "balance_state": {
    "assessment": "The current balance is slightly skewed toward exploration, or a balance that is being less successful overall. While both strategies generate some success, it is not enough to generate positive overall results.",
    "adjustment_needs": "Consider increasing the weight of the exploitation strategy in the next iteration, especially if the exploration strategy isn't rapidly identifying significantly better solutions. Review the parameters of the exploration strategy to see if can be made more efficient or less costly."
  },
  "recommendations": [
    "Reduce the exploration rate slightly for this iteration.",
    "Fine-tune the exploitation strategy to make it more aggressive, and/or more diverse in the solutions it assesses.",
    "Analyze the characteristics of the failed explorations to understand why they are not improving performance.",
    "Monitor cost and diversity carefully in the next iteration to evaluate the effect of the changes.",
    "Compare the characteristics of the single elite solutions in each iteration to understand the current search bias."
  ]
}
```
2025-06-22 18:59:08,387 - __main__ - INFO - 当前最佳适应度: 13812.0
2025-06-22 18:59:08,388 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 18:59:08,388 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-22 18:59:08,390 - __main__ - INFO - 开始分析阶段
2025-06-22 18:59:08,390 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:59:08,407 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13812.0, 'max': 117605.0, 'mean': 83686.2, 'std': 41730.616704764856}, 'diversity': 0.9649831649831649, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:59:08,408 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13812.0, 'max': 117605.0, 'mean': 83686.2, 'std': 41730.616704764856}, 'diversity_level': 0.9649831649831649, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:59:08,408 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:59:08,408 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:59:08,408 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:59:08,411 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:59:08,411 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:59:08,411 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:59:08,411 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:59:08,412 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:59:08,412 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:59:08,424 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:59:08,424 - __main__ - INFO - 精英专家分析报告: {'elite_count': 31, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4291.0, 'avg_gap': 74145.90967741935}, 'structure_gap': {'unique_elite_edges': 182, 'unique_pop_edges': 533, 'common_edges': 61}}, 'elite_diversity': {'diversity_score': 0.23401759530791788}}
2025-06-22 18:59:08,424 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:59:08,429 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:59:08,429 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:59:08,429 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=13812.0, max=117605.0, mean=83686.2, std=41730.616704764856
- Diversity: 0.9649831649831649, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 2/4
- Historical trend: Recent costs: [11300.0, 13812.0]
- Improvement rate: 0.00 per iteration
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:59:14,932 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely highly rugged due to high cost variance (std=41730.616704764856), and the fact that there are only 10 individuals to represent the total solution space. The lack of elite solutions and structure (0 high quality edges, 0 common subpaths) also suggests a rugged landscape where small changes can lead to significant cost variations.",
    "modality": "Unknown, but likely multi-modal. The presence of local optima is probable given the ruggedness and the early iterations. The current population is not converged.",
    "deceptiveness": "Potentially deceptive. The combination of high cost variance, the small population size relative to the unknown TSP instance size (though it’s specified to have 0 nodes, which is clearly impossible), and the absence of any observed structure increases the chances of finding local optima instead of the global one.",
    "edge_distribution": "Unknown, and since there are zero nodes in the TSP instance, completely irrelevant at this point"
  },
  "population_state": {
    "diversity": "High (0.9649831649831649), indicating that the population explores different areas of the search space. The large cost range supports this. This level of diversity is expected in the early iterations.",
    "convergence": "Very low (0.0). The population has not converged towards a specific area of the search space, which is expected in early iterations. The two recent costs offer a clear indication of potential improvements.",
    "clustering": "No clustering information available. Further analysis would be necessary."
  },
  "difficult_regions": {
    "identified_challenges": "Cannot be determined with certainty since no problem is defined in terms of nodes or edges. At this point, this is an impossible instance. The high cost variability suggests a need to carefully evaluate and adjust solutions.",
    "node_sequences_or_edges_to_avoid": "Impossible to state given the zero-node TSP instance."
  },
  "opportunity_regions": {
    "promising_areas": "Cannot be determined with certainty due to the lack of a defined problem. Based on the history of [11300.0, 13812.0] it seems the algorithm is making progress on identifying the lowest cost found so far. Continuing to explore these areas, as long as the improvement continues is highly advised. However, the definition of the problem is currently lacking, so it would be more beneficial to fix this first.",
    "node_sequences_or_edges_to_include": "Impossible to state given the zero-node TSP instance."
  },
  "evolution_phase": "Primarily exploration, with a transition to a refinement phase likely to occur soon, given the iteration is 2/4. There is still significant diversity and minimal convergence, but cost improvement trend indicates refining could be an option.",
  "evolution_direction": {
    "strategy": "Continue with a high exploration focus while slightly shifting towards refinement. This is important because there are only four iterations and the low cost has a great chance of being improved upon. Due to the iteration being 2/4, the exploration phase is still relevant as the search is still not converged, and only two iterations have been performed.",
    "operator_suggestions": [
      "Maintain the use of mutation operators to explore the search space. Focus on operators that introduce significant changes to the solution.",
      "Implement and favor crossover operators to combine potentially good parts of solutions, which will allow for refining to take place. Apply crossover operator early.",
       "Carefully evaluate the feasibility of the problem, and ensure the TSP instance is well defined, and that the constraints are applied correctly.",
        "Refinement: Consider a local search operator to make small modifications to promising solutions. Consider implementing 2-opt or 3-opt operators after the issue of zero nodes in the TSP is resolved."
    ]
  }
}
```
2025-06-22 18:59:14,932 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:59:14,932 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely highly rugged due to high cost variance (std=41730.616704764856), and the fact that there are only 10 individuals to represent the total solution space. The lack of elite solutions and structure (0 high quality edges, 0 common subpaths) also suggests a rugged landscape where small changes can lead to significant cost variations.', 'modality': 'Unknown, but likely multi-modal. The presence of local optima is probable given the ruggedness and the early iterations. The current population is not converged.', 'deceptiveness': 'Potentially deceptive. The combination of high cost variance, the small population size relative to the unknown TSP instance size (though it’s specified to have 0 nodes, which is clearly impossible), and the absence of any observed structure increases the chances of finding local optima instead of the global one.', 'edge_distribution': 'Unknown, and since there are zero nodes in the TSP instance, completely irrelevant at this point'}, 'population_state': {'diversity': 'High (0.9649831649831649), indicating that the population explores different areas of the search space. The large cost range supports this. This level of diversity is expected in the early iterations.', 'convergence': 'Very low (0.0). The population has not converged towards a specific area of the search space, which is expected in early iterations. The two recent costs offer a clear indication of potential improvements.', 'clustering': 'No clustering information available. Further analysis would be necessary.'}, 'difficult_regions': {'identified_challenges': 'Cannot be determined with certainty since no problem is defined in terms of nodes or edges. At this point, this is an impossible instance. The high cost variability suggests a need to carefully evaluate and adjust solutions.', 'node_sequences_or_edges_to_avoid': 'Impossible to state given the zero-node TSP instance.'}, 'opportunity_regions': {'promising_areas': 'Cannot be determined with certainty due to the lack of a defined problem. Based on the history of [11300.0, 13812.0] it seems the algorithm is making progress on identifying the lowest cost found so far. Continuing to explore these areas, as long as the improvement continues is highly advised. However, the definition of the problem is currently lacking, so it would be more beneficial to fix this first.', 'node_sequences_or_edges_to_include': 'Impossible to state given the zero-node TSP instance.'}, 'evolution_phase': 'Primarily exploration, with a transition to a refinement phase likely to occur soon, given the iteration is 2/4. There is still significant diversity and minimal convergence, but cost improvement trend indicates refining could be an option.', 'evolution_direction': {'strategy': 'Continue with a high exploration focus while slightly shifting towards refinement. This is important because there are only four iterations and the low cost has a great chance of being improved upon. Due to the iteration being 2/4, the exploration phase is still relevant as the search is still not converged, and only two iterations have been performed.', 'operator_suggestions': ['Maintain the use of mutation operators to explore the search space. Focus on operators that introduce significant changes to the solution.', 'Implement and favor crossover operators to combine potentially good parts of solutions, which will allow for refining to take place. Apply crossover operator early.', 'Carefully evaluate the feasibility of the problem, and ensure the TSP instance is well defined, and that the constraints are applied correctly.', 'Refinement: Consider a local search operator to make small modifications to promising solutions. Consider implementing 2-opt or 3-opt operators after the issue of zero nodes in the TSP is resolved.']}}
2025-06-22 18:59:14,932 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:59:14,932 - __main__ - INFO - 分析阶段完成
2025-06-22 18:59:14,932 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely highly rugged due to high cost variance (std=41730.616704764856), and the fact that there are only 10 individuals to represent the total solution space. The lack of elite solutions and structure (0 high quality edges, 0 common subpaths) also suggests a rugged landscape where small changes can lead to significant cost variations.', 'modality': 'Unknown, but likely multi-modal. The presence of local optima is probable given the ruggedness and the early iterations. The current population is not converged.', 'deceptiveness': 'Potentially deceptive. The combination of high cost variance, the small population size relative to the unknown TSP instance size (though it’s specified to have 0 nodes, which is clearly impossible), and the absence of any observed structure increases the chances of finding local optima instead of the global one.', 'edge_distribution': 'Unknown, and since there are zero nodes in the TSP instance, completely irrelevant at this point'}, 'population_state': {'diversity': 'High (0.9649831649831649), indicating that the population explores different areas of the search space. The large cost range supports this. This level of diversity is expected in the early iterations.', 'convergence': 'Very low (0.0). The population has not converged towards a specific area of the search space, which is expected in early iterations. The two recent costs offer a clear indication of potential improvements.', 'clustering': 'No clustering information available. Further analysis would be necessary.'}, 'difficult_regions': {'identified_challenges': 'Cannot be determined with certainty since no problem is defined in terms of nodes or edges. At this point, this is an impossible instance. The high cost variability suggests a need to carefully evaluate and adjust solutions.', 'node_sequences_or_edges_to_avoid': 'Impossible to state given the zero-node TSP instance.'}, 'opportunity_regions': {'promising_areas': 'Cannot be determined with certainty due to the lack of a defined problem. Based on the history of [11300.0, 13812.0] it seems the algorithm is making progress on identifying the lowest cost found so far. Continuing to explore these areas, as long as the improvement continues is highly advised. However, the definition of the problem is currently lacking, so it would be more beneficial to fix this first.', 'node_sequences_or_edges_to_include': 'Impossible to state given the zero-node TSP instance.'}, 'evolution_phase': 'Primarily exploration, with a transition to a refinement phase likely to occur soon, given the iteration is 2/4. There is still significant diversity and minimal convergence, but cost improvement trend indicates refining could be an option.', 'evolution_direction': {'strategy': 'Continue with a high exploration focus while slightly shifting towards refinement. This is important because there are only four iterations and the low cost has a great chance of being improved upon. Due to the iteration being 2/4, the exploration phase is still relevant as the search is still not converged, and only two iterations have been performed.', 'operator_suggestions': ['Maintain the use of mutation operators to explore the search space. Focus on operators that introduce significant changes to the solution.', 'Implement and favor crossover operators to combine potentially good parts of solutions, which will allow for refining to take place. Apply crossover operator early.', 'Carefully evaluate the feasibility of the problem, and ensure the TSP instance is well defined, and that the constraints are applied correctly.', 'Refinement: Consider a local search operator to make small modifications to promising solutions. Consider implementing 2-opt or 3-opt operators after the issue of zero nodes in the TSP is resolved.']}}
2025-06-22 18:59:14,932 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:59:14,932 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:59:14,932 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 2
- Population: 10 individuals, diversity=High (0.9649831649831649), indicating that the population explores different areas of the search space. The large cost range supports this. This level of diversity is expected in the early iterations.
- Best cost: 13812.0, Mean cost: 83686.2
- Search space: Ruggedness: Likely highly rugged due to high cost variance (std=41730.616704764856), and the fact that there are only 10 individuals to represent the total solution space. The lack of elite solutions and structure (0 high quality edges, 0 common subpaths) also suggests a rugged landscape where small changes can lead to significant cost variations., Modality: Unknown, but likely multi-modal. The presence of local optima is probable given the ruggedness and the early iterations. The current population is not converged.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration - Initial Exploration",
    "features": [
      "Iteration 1 of 4, indicating the beginning of the evolutionary process.",
      "Cost is...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:59:14,932 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:59:17,705 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_intensity": 0.7,
      "step_size": 0.3,
      "region_focus": true,
      "difficult_region_bias": 0.5,
      "opportunity_region_bias": 0.6
    },
    "exploit": {
      "mutation_intensity": 0.3,
      "step_size": 0.1,
      "local_search_radius": 0.2,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:59:17,706 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:59:17,706 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:59:17,706 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_intensity": 0.7,
      "step_size": 0.3,
      "region_focus": true,
      "difficult_region_bias": 0.5,
      "opportunity_region_bias": 0.6
    },
    "exploit": {
      "mutation_intensity": 0.3,
      "step_size": 0.1,
      "local_search_radius": 0.2,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:59:17,707 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:59:17,707 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:59:17,707 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_intensity": 0.7,
      "step_size": 0.3,
      "region_focus": true,
      "difficult_region_bias": 0.5,
      "opportunity_region_bias": 0.6
    },
    "exploit": {
      "mutation_intensity": 0.3,
      "step_size": 0.1,
      "local_search_radius": 0.2,
      "region_focus": false
    }
  }
}
```

2025-06-22 18:59:17,708 - __main__ - INFO - 开始进化阶段
2025-06-22 18:59:17,708 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:59:17,708 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:17,708 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [21, 17, 3] and ending with [59, 61, 62]
- Cost: 27952.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8461538461538461
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:17,708 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:20,659 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34,
    36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8,
    10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51,
    53, 55, 57, 59, 61, 63, 65
  ],
  "modification_strategy": "Prioritized a more diverse path by strategically altering the initial and final segments and introducing a more even distribution across the node space. Focused on exploring unexplored regions indicated in the search context.",
  "targeted_regions": "Specifically targeted unexplored regions. The path attempts to uniformly visit the regions, trying to cover them in a balanced manner and avoiding the difficult regions as specified."
}
```
2025-06-22 18:59:20,659 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:20,659 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21852.0, 路径: [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65]
2025-06-22 18:59:20,659 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}
2025-06-22 18:59:20,659 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:59:20,659 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:20,659 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 10, 8] and ending with [63, 64, 65]
- Cost: 13812.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:20,659 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:23,832 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This approach prioritizes exploration by starting with a different initial segment and then systematically traversing the remaining nodes in a slightly different order. This re-orders the nodes while maintaining a full permutation and attempting to avoid the most difficult regions while maintaining diversity.",
  "targeted_regions": "This path attempts to explore regions starting from node indices that have not been explored in the earlier path. The specific targets depend on the distribution of opportunities and difficult regions, but the strategy aims to cover diverse segments of the node space to reduce the risk of missing an optimized area."
}
```
2025-06-22 18:59:23,832 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:23,832 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11456.0, 路径: [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:59:23,832 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}
2025-06-22 18:59:23,832 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:59:23,832 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:23,832 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 2, 4] and ending with [61, 63, 65]
- Cost: 19777.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.823076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:23,836 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:27,106 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63
  ],
  "modification_strategy": "A hybrid approach leveraging both diversification and quality preservation. The new path prioritizes exploring unexplored regions (primarily those hinted at by 'opportunity regions' in the search context) by alternating the starting and ending points in the previous path, and by using a more evenly distributed progression in the initial path section. The last part of the path involves reversing the traversal to create an interesting route. The indices are verified to meet the TSP constraints.",
  "targeted_regions": "The primary goal is to explore the 'opportunity regions' which would likely involve nodes which are at the endpoints and middle areas of the current path. The path is constructed so the opportunity regions get explored, and some exploration is done around the difficult to avoid regions to ensure that a diverse and valid path is constructed."
}
```
2025-06-22 18:59:27,109 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:27,109 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25900.0, 路径: [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63]
2025-06-22 18:59:27,109 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}
2025-06-22 18:59:27,109 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:59:27,109 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:27,109 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [52, 41, 8] and ending with [57, 7, 63]
- Cost: 109038.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8923076923076924
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:27,109 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:30,685 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32
  ],
  "modification_strategy": "This strategy prioritizes diversification from the original path while still maintaining a degree of coherence. It involves several key modifications: 1) Shuffling the starting section of the original path significantly. 2) Randomly re-ordering intermediate segments of the path to ensure nodes are visited once. 3) Ensuring the new path ends with different indices than the original. This approach increases the probability of hitting unexplored regions and reduces the cost to reach those regions while maintaining diversity.",
  "targeted_regions": "Focus on exploration of all regions by introducing a completely different sequence of nodes throughout the path and targeted both unexplored regions and difficult to avoid regions. The path is also designed to avoid potential cost from similar subpaths in the original path."
}
```
2025-06-22 18:59:30,685 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:30,686 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116559.0, 路径: [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61]
2025-06-22 18:59:30,686 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}
2025-06-22 18:59:30,686 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:59:30,686 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:30,687 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 4, 18] and ending with [43, 10, 56]
- Cost: 117605.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8892307692307693
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:30,687 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:33,797 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17
  ],
  "modification_strategy": "This strategy prioritizes diversity by breaking away from the original path early and incorporating nodes from unexplored regions based on the 'search context'. The start and end points are maintained but the middle section is radically altered to explore potential opportunity regions and avoid difficult ones indicated.",
  "targeted_regions": "Opportunity regions and areas identified as not being visited in previous paths."
}
```
2025-06-22 18:59:33,797 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:33,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105560.0, 路径: [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32]
2025-06-22 18:59:33,797 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}
2025-06-22 18:59:33,797 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 18:59:33,797 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:33,797 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [26, 30, 52] and ending with [19, 35, 9]
- Cost: 111759.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8830769230769231
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:33,801 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:38,768 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19
  ],
  "modification_strategy": "This strategy prioritizes diversification. I have employed a random walk with a significant number of large jumps, specifically attempting to move away from the beginning and end of the current path, and exploring the identified opportunity regions by including nodes within their approximate locations. I've also scrambled the current path in several sections to increase the likelihood of generating a highly divergent path.",
  "targeted_regions": "Opportunity Regions (implicitly, by incorporating nodes generally in their vicinity) and nodes furthest away from the original starting and ending nodes to ensure maximum difference in path sequence."
}
```
2025-06-22 18:59:38,768 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:38,768 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118096.0, 路径: [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52]
2025-06-22 18:59:38,768 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}
2025-06-22 18:59:38,768 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:59:38,771 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:59:38,771 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:59:38,771 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 107684.0
2025-06-22 18:59:39,272 - ExploitationExpert - INFO - res_population_num: 31
2025-06-22 18:59:39,272 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821]
2025-06-22 18:59:39,273 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:59:39,287 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:59:39,287 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}, {'tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}, {'tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}, {'tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}, {'tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}, {'tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}, {'tour': array([12, 63, 29, 20, 16, 47, 25, 13, 48, 65, 43, 58, 52, 30,  8, 38, 18,
       10, 15, 28, 19, 55, 33, 32, 22,  4, 37, 42, 64, 45, 51, 59, 24, 46,
       40, 11, 60,  9,  3,  2, 26, 31,  7,  5, 56,  6, 62, 17,  1, 23, 44,
       54, 27, 41, 39, 35, 21, 36, 50, 61, 34, 53,  0, 57, 14, 49]), 'cur_cost': 107684.0}, {'tour': array([ 3, 27,  5, 36, 15,  7, 33, 45, 19,  1, 37, 44,  9, 18, 23, 54, 49,
       41, 64, 56, 63, 17, 10, 26, 50, 25, 34, 11, 51, 61, 48, 32, 22, 57,
       43, 31, 42, 14, 20, 47, 16, 40, 58, 35, 55, 62, 38, 24,  2, 60, 29,
       13, 21, 28, 53,  8, 52,  0, 30, 12, 59, 39, 65,  4, 46,  6]), 'cur_cost': 113451.0}, {'tour': [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17], 'cur_cost': 105948.0}, {'tour': array([10, 49, 35, 26,  3, 12,  5, 39, 28, 38, 59, 58, 42, 62, 30, 48, 41,
       25, 60, 46, 54, 44, 18, 27, 17, 56, 29, 11, 57, 34, 19,  9, 52, 31,
        8, 63, 43, 22, 50, 53, 45,  1,  6,  7,  2, 51, 13, 36, 16, 65, 32,
        0, 23, 20, 47, 40,  4, 21, 24, 61, 55, 33, 15, 64, 37, 14]), 'cur_cost': 116543.0}]
2025-06-22 18:59:39,289 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:59:39,289 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-22 18:59:39,289 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:59:39,289 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:59:39,289 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:59:39,289 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:59:39,289 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 89646.0
2025-06-22 18:59:39,794 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:59:39,794 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521]
2025-06-22 18:59:39,794 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:59:39,809 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:59:39,810 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}, {'tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}, {'tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}, {'tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}, {'tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}, {'tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}, {'tour': array([12, 63, 29, 20, 16, 47, 25, 13, 48, 65, 43, 58, 52, 30,  8, 38, 18,
       10, 15, 28, 19, 55, 33, 32, 22,  4, 37, 42, 64, 45, 51, 59, 24, 46,
       40, 11, 60,  9,  3,  2, 26, 31,  7,  5, 56,  6, 62, 17,  1, 23, 44,
       54, 27, 41, 39, 35, 21, 36, 50, 61, 34, 53,  0, 57, 14, 49]), 'cur_cost': 107684.0}, {'tour': array([19, 39, 42, 44, 63, 54, 38, 36, 40, 14,  5, 52, 11, 49, 27, 22, 20,
       41, 15, 30, 26, 65, 61, 60, 58, 64, 37, 28, 29, 21, 32, 16, 43, 59,
       56, 25, 50, 46,  3,  8,  1, 51, 57,  9, 13, 53, 17, 12,  2, 33, 45,
       47,  7, 34, 10,  0, 24, 55,  6, 48, 23,  4, 35, 31, 18, 62]), 'cur_cost': 89646.0}, {'tour': [29, 3, 45, 12, 6, 28, 43, 1, 5, 59, 13, 62, 33, 39, 21, 15, 4, 34, 11, 61, 23, 24, 53, 47, 0, 14, 36, 54, 55, 56, 32, 57, 30, 64, 44, 41, 9, 10, 2, 60, 58, 50, 40, 18, 20, 65, 51, 42, 16, 52, 37, 38, 48, 25, 35, 46, 63, 22, 49, 19, 26, 7, 27, 31, 8, 17], 'cur_cost': 105948.0}, {'tour': array([10, 49, 35, 26,  3, 12,  5, 39, 28, 38, 59, 58, 42, 62, 30, 48, 41,
       25, 60, 46, 54, 44, 18, 27, 17, 56, 29, 11, 57, 34, 19,  9, 52, 31,
        8, 63, 43, 22, 50, 53, 45,  1,  6,  7,  2, 51, 13, 36, 16, 65, 32,
        0, 23, 20, 47, 40,  4, 21, 24, 61, 55, 33, 15, 64, 37, 14]), 'cur_cost': 116543.0}]
2025-06-22 18:59:39,812 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:59:39,812 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-22 18:59:39,812 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:59:39,812 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:59:39,812 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:59:39,813 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:59:39,813 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 112628.0
2025-06-22 18:59:40,315 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:59:40,315 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521]
2025-06-22 18:59:40,315 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:59:40,331 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:59:40,331 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}, {'tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}, {'tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}, {'tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}, {'tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}, {'tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}, {'tour': array([12, 63, 29, 20, 16, 47, 25, 13, 48, 65, 43, 58, 52, 30,  8, 38, 18,
       10, 15, 28, 19, 55, 33, 32, 22,  4, 37, 42, 64, 45, 51, 59, 24, 46,
       40, 11, 60,  9,  3,  2, 26, 31,  7,  5, 56,  6, 62, 17,  1, 23, 44,
       54, 27, 41, 39, 35, 21, 36, 50, 61, 34, 53,  0, 57, 14, 49]), 'cur_cost': 107684.0}, {'tour': array([19, 39, 42, 44, 63, 54, 38, 36, 40, 14,  5, 52, 11, 49, 27, 22, 20,
       41, 15, 30, 26, 65, 61, 60, 58, 64, 37, 28, 29, 21, 32, 16, 43, 59,
       56, 25, 50, 46,  3,  8,  1, 51, 57,  9, 13, 53, 17, 12,  2, 33, 45,
       47,  7, 34, 10,  0, 24, 55,  6, 48, 23,  4, 35, 31, 18, 62]), 'cur_cost': 89646.0}, {'tour': array([64, 11,  2, 20, 24, 63, 47, 15, 40, 33, 14, 61, 35, 60, 54, 25, 46,
       59,  3,  6, 42,  8, 57, 44, 34,  9, 55, 32, 10, 56, 52, 48, 21, 41,
        4, 37, 26, 51, 19, 49,  1, 53, 58, 38, 29, 43, 18, 12, 23, 45, 13,
        7, 22, 28, 62, 27, 31, 39, 16, 30, 65, 50,  0, 17,  5, 36]), 'cur_cost': 112628.0}, {'tour': array([10, 49, 35, 26,  3, 12,  5, 39, 28, 38, 59, 58, 42, 62, 30, 48, 41,
       25, 60, 46, 54, 44, 18, 27, 17, 56, 29, 11, 57, 34, 19,  9, 52, 31,
        8, 63, 43, 22, 50, 53, 45,  1,  6,  7,  2, 51, 13, 36, 16, 65, 32,
        0, 23, 20, 47, 40,  4, 21, 24, 61, 55, 33, 15, 64, 37, 14]), 'cur_cost': 116543.0}]
2025-06-22 18:59:40,333 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:59:40,333 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-06-22 18:59:40,333 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:59:40,333 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:59:40,335 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:59:40,335 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:59:40,335 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 91162.0
2025-06-22 18:59:40,840 - ExploitationExpert - INFO - res_population_num: 35
2025-06-22 18:59:40,841 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9530, 9532, 9535, 9543, 9547, 9560, 9566, 9587, 9587, 9821, 9521, 9521, 9521, 9521]
2025-06-22 18:59:40,841 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16,
       19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 40, 43,
       48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 24, 29, 32, 33, 31, 37, 36, 26, 25, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 20, 21, 13, 23, 22, 15, 17, 12, 18, 16,
       19, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 33, 31, 25, 28, 30, 34, 35, 26, 36, 37,
       27, 43, 48, 46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  3,  7, 11,  9,  4,  5,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 33, 31, 24,
       29, 32, 34, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42,
       63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:59:40,856 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:59:40,857 - ExploitationExpert - INFO - populations: [{'tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}, {'tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}, {'tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}, {'tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}, {'tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}, {'tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}, {'tour': array([12, 63, 29, 20, 16, 47, 25, 13, 48, 65, 43, 58, 52, 30,  8, 38, 18,
       10, 15, 28, 19, 55, 33, 32, 22,  4, 37, 42, 64, 45, 51, 59, 24, 46,
       40, 11, 60,  9,  3,  2, 26, 31,  7,  5, 56,  6, 62, 17,  1, 23, 44,
       54, 27, 41, 39, 35, 21, 36, 50, 61, 34, 53,  0, 57, 14, 49]), 'cur_cost': 107684.0}, {'tour': array([19, 39, 42, 44, 63, 54, 38, 36, 40, 14,  5, 52, 11, 49, 27, 22, 20,
       41, 15, 30, 26, 65, 61, 60, 58, 64, 37, 28, 29, 21, 32, 16, 43, 59,
       56, 25, 50, 46,  3,  8,  1, 51, 57,  9, 13, 53, 17, 12,  2, 33, 45,
       47,  7, 34, 10,  0, 24, 55,  6, 48, 23,  4, 35, 31, 18, 62]), 'cur_cost': 89646.0}, {'tour': array([64, 11,  2, 20, 24, 63, 47, 15, 40, 33, 14, 61, 35, 60, 54, 25, 46,
       59,  3,  6, 42,  8, 57, 44, 34,  9, 55, 32, 10, 56, 52, 48, 21, 41,
        4, 37, 26, 51, 19, 49,  1, 53, 58, 38, 29, 43, 18, 12, 23, 45, 13,
        7, 22, 28, 62, 27, 31, 39, 16, 30, 65, 50,  0, 17,  5, 36]), 'cur_cost': 112628.0}, {'tour': array([ 4,  1, 50, 20, 35, 36, 30, 26, 58, 63, 61, 15, 39, 12, 24,  8, 45,
       47, 13,  6, 48, 23, 29,  9, 25,  5, 22, 32, 33, 21, 60, 55, 17, 27,
       65, 37, 40, 51, 31, 43, 42, 28, 18, 49, 11, 52, 10, 46, 38,  2, 59,
       53, 54,  0, 34,  3, 64, 19, 62, 44, 57, 56,  7, 41, 16, 14]), 'cur_cost': 91162.0}]
2025-06-22 18:59:40,858 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:59:40,859 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-06-22 18:59:40,860 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:59:40,860 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 17, 3, 1, 5, 7, 9, 11, 13, 15, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 0, 2, 4, 6, 8, 10, 12, 14, 19, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 21852.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25900.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 4, 15, 28, 39, 50, 2, 19, 55, 43, 9, 26, 1, 35, 47, 6, 33, 60, 21, 12, 23, 48, 11, 3, 27, 41, 8, 14, 24, 62, 31, 59, 54, 45, 0, 10, 36, 65, 44, 5, 13, 22, 16, 38, 64, 30, 53, 18, 37, 40, 46, 20, 58, 49, 25, 29, 51, 7, 63, 34, 42, 56, 17, 32, 57, 61], 'cur_cost': 116559.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [27, 31, 10, 56, 43, 5, 12, 24, 60, 19, 3, 33, 41, 45, 1, 64, 2, 58, 30, 48, 53, 20, 35, 9, 62, 50, 15, 57, 38, 29, 18, 4, 16, 6, 55, 23, 40, 39, 22, 46, 42, 11, 65, 7, 14, 13, 37, 63, 34, 21, 59, 8, 52, 49, 26, 28, 36, 54, 44, 47, 61, 51, 25, 0, 17, 32], 'cur_cost': 105560.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 59, 41, 5, 12, 48, 2, 54, 62, 27, 34, 60, 1, 37, 4, 14, 39, 53, 44, 17, 32, 63, 10, 28, 6, 20, 56, 43, 3, 38, 21, 49, 15, 31, 46, 9, 0, 33, 50, 51, 13, 55, 25, 64, 11, 36, 23, 61, 18, 58, 8, 40, 24, 47, 7, 16, 65, 57, 45, 30, 42, 22, 29, 35, 19, 52], 'cur_cost': 118096.0}}]
2025-06-22 18:59:40,860 - __main__ - INFO - 进化阶段完成
2025-06-22 18:59:40,861 - __main__ - INFO - 开始评估阶段
2025-06-22 18:59:40,861 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:59:40,862 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:59:40,862 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 2/4
- Population changes:
  * Cost: min 13812.0->11456.0, mean 83686.2->80054.3
  * Diversity: 0.8461538461538461->0.8953846153846153
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 50.0%, avg improvement 86.67
- Exploit: 4 individuals, success rate 50.0%, avg improvement 8949.75
- Overall improvement: 2356.0

## Historical Trends:
Last 2 iterations: costs [11300.0, 13812.0], diversity [0.9572390572390573, 0.9649831649831649]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:59:45,941 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Intermediate Stage of Optimization",
    "features": "The evolution is showing improvement in both cost and diversity, indicating the search is successfully refining solutions. However, the rate of improvement is slowing compared to the previous iteration, suggesting we are approaching local optima or the solution space is becoming more complex. Elite solutions are stable at 1, indicating the algorithm is converging towards a good, but potentially not optimal, solution."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Good (50%)",
      "improvement": "Moderate (86.67 - indicating small incremental gains).",
      "assessment": "Exploration is yielding some positive results, indicating that the search is still able to find new solutions, albeit with small improvements. The success rate shows that it's still useful."
    },
    "exploit": {
      "success_rate": "Good (50%)",
      "improvement": "High (8949.75 - indicating significant gains when successful).",
      "assessment": "Exploitation is very effective when it finds improvements, but with a lower sample size. This suggests that the exploitation might be getting trapped in local optima more frequently. This needs to be verified with more information."
    },
    "overall": {
      "improvement": "Significant (2356.0)",
      "assessment": "Overall improvement is still strong, indicating that the current combination of explore and exploit is still effective, but the rate of improvement might be decreasing."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance appears to be shifted towards exploitation. Although both exploration and exploitation have the same success rate, the exploitation leads to a better individual improvement. Considering the slowing rate of cost improvement and the high exploit improvement, the balance could be tested for adjustment.",
    "adjustment_needs": "Consider slightly increasing exploration to escape potential local optima. Increasing exploration should be done carefully to avoid degrading the current good solutions."
  },
  "recommendations": {
    "next_iteration": [
      "Increase exploration slightly: Increase the number of individuals in the exploration phase by 1-2.",
      "Adjust mutation rate (if applicable): If using mutation, slightly increase the mutation rate in the next generation. This could aid in escaping local optima.",
      "Monitor diversity closely: Continue to track diversity to ensure the population does not prematurely converge. If diversity starts to decline, increase exploration or apply more aggressive mutation.",
      "Analyze Elite solutions: Analyze the elite solution and the solutions obtained from the exploit phase to find commonalities. Identify characteristics that lead to better solutions. Also analyze the exploration failures to avoid repeating the mistakes.",
      "Re-evaluate the number of individuals for exploitation based on the previous iterations, and possibly reduce or maintain its proportion."
    ]
  }
}
```
2025-06-22 18:59:45,972 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:59:45,973 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Intermediate Stage of Optimization",
    "features": "The evolution is showing improvement in both cost and diversity, indicating the search is successfully refining solutions. However, the rate of improvement is slowing compared to the previous iteration, suggesting we are approaching local optima or the solution space is becoming more complex. Elite solutions are stable at 1, indicating the algorithm is converging towards a good, but potentially not optimal, solution."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Good (50%)",
      "improvement": "Moderate (86.67 - indicating small incremental gains).",
      "assessment": "Exploration is yielding some positive results, indicating that the search is still able to find new solutions, albeit with small improvements. The success rate shows that it's still useful."
    },
    "exploit": {
      "success_rate": "Good (50%)",
      "improvement": "High (8949.75 - indicating significant gains when successful).",
      "assessment": "Exploitation is very effective when it finds improvements, but with a lower sample size. This suggests that the exploitation might be getting trapped in local optima more frequently. This needs to be verified with more information."
    },
    "overall": {
      "improvement": "Significant (2356.0)",
      "assessment": "Overall improvement is still strong, indicating that the current combination of explore and exploit is still effective, but the rate of improvement might be decreasing."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance appears to be shifted towards exploitation. Although both exploration and exploitation have the same success rate, the exploitation leads to a better individual improvement. Considering the slowing rate of cost improvement and the high exploit improvement, the balance could be tested for adjustment.",
    "adjustment_needs": "Consider slightly increasing exploration to escape potential local optima. Increasing exploration should be done carefully to avoid degrading the current good solutions."
  },
  "recommendations": {
    "next_iteration": [
      "Increase exploration slightly: Increase the number of individuals in the exploration phase by 1-2.",
      "Adjust mutation rate (if applicable): If using mutation, slightly increase the mutation rate in the next generation. This could aid in escaping local optima.",
      "Monitor diversity closely: Continue to track diversity to ensure the population does not prematurely converge. If diversity starts to decline, increase exploration or apply more aggressive mutation.",
      "Analyze Elite solutions: Analyze the elite solution and the solutions obtained from the exploit phase to find commonalities. Identify characteristics that lead to better solutions. Also analyze the exploration failures to avoid repeating the mistakes.",
      "Re-evaluate the number of individuals for exploitation based on the previous iterations, and possibly reduce or maintain its proportion."
    ]
  }
}
```
2025-06-22 18:59:45,974 - __main__ - INFO - 评估阶段完成
2025-06-22 18:59:45,974 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Intermediate Stage of Optimization",
    "features": "The evolution is showing improvement in both cost and diversity, indicating the search is successfully refining solutions. However, the rate of improvement is slowing compared to the previous iteration, suggesting we are approaching local optima or the solution space is becoming more complex. Elite solutions are stable at 1, indicating the algorithm is converging towards a good, but potentially not optimal, solution."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Good (50%)",
      "improvement": "Moderate (86.67 - indicating small incremental gains).",
      "assessment": "Exploration is yielding some positive results, indicating that the search is still able to find new solutions, albeit with small improvements. The success rate shows that it's still useful."
    },
    "exploit": {
      "success_rate": "Good (50%)",
      "improvement": "High (8949.75 - indicating significant gains when successful).",
      "assessment": "Exploitation is very effective when it finds improvements, but with a lower sample size. This suggests that the exploitation might be getting trapped in local optima more frequently. This needs to be verified with more information."
    },
    "overall": {
      "improvement": "Significant (2356.0)",
      "assessment": "Overall improvement is still strong, indicating that the current combination of explore and exploit is still effective, but the rate of improvement might be decreasing."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance appears to be shifted towards exploitation. Although both exploration and exploitation have the same success rate, the exploitation leads to a better individual improvement. Considering the slowing rate of cost improvement and the high exploit improvement, the balance could be tested for adjustment.",
    "adjustment_needs": "Consider slightly increasing exploration to escape potential local optima. Increasing exploration should be done carefully to avoid degrading the current good solutions."
  },
  "recommendations": {
    "next_iteration": [
      "Increase exploration slightly: Increase the number of individuals in the exploration phase by 1-2.",
      "Adjust mutation rate (if applicable): If using mutation, slightly increase the mutation rate in the next generation. This could aid in escaping local optima.",
      "Monitor diversity closely: Continue to track diversity to ensure the population does not prematurely converge. If diversity starts to decline, increase exploration or apply more aggressive mutation.",
      "Analyze Elite solutions: Analyze the elite solution and the solutions obtained from the exploit phase to find commonalities. Identify characteristics that lead to better solutions. Also analyze the exploration failures to avoid repeating the mistakes.",
      "Re-evaluate the number of individuals for exploitation based on the previous iterations, and possibly reduce or maintain its proportion."
    ]
  }
}
```
2025-06-22 18:59:45,974 - __main__ - INFO - 当前最佳适应度: 11456.0
2025-06-22 18:59:45,974 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-22 18:59:45,974 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-22 18:59:45,974 - __main__ - INFO - 开始分析阶段
2025-06-22 18:59:45,974 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:59:45,994 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11456.0, 'max': 118096.0, 'mean': 80054.3, 'std': 40615.33633013519}, 'diversity': 0.9744107744107743, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:59:45,995 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11456.0, 'max': 118096.0, 'mean': 80054.3, 'std': 40615.33633013519}, 'diversity_level': 0.9744107744107743, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:59:45,995 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:59:45,995 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:59:45,995 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:59:45,997 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:59:45,998 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:59:45,998 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:59:45,998 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:59:45,998 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:59:45,999 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:59:46,019 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:59:46,020 - __main__ - INFO - 精英专家分析报告: {'elite_count': 35, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1935.0, 'avg_gap': 70516.21428571429}, 'structure_gap': {'unique_elite_edges': 184, 'unique_pop_edges': 561, 'common_edges': 60}}, 'elite_diversity': {'diversity_score': 0.2288770053475936}}
2025-06-22 18:59:46,020 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:59:46,020 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:59:46,021 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:59:46,021 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11456.0, max=118096.0, mean=80054.3, std=40615.33633013519
- Diversity: 0.9744107744107743, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 3/4
- Historical trend: Recent costs: [11300.0, 13812.0, 11456.0]
- Improvement rate: 1178.00 per iteration
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:59:53,383 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely rugged. The high standard deviation in costs (40615.3) and the presence of local optima (indicated by the iteration's cost fluctuation) suggest a landscape with many peaks and valleys. This ruggedness makes finding the global optimum challenging.",
    "modality": "Potentially multimodal. While not definitively confirmed, the ruggedness suggests the presence of multiple local optima, implying a multimodal search space.",
    "deceptiveness": "Unknown.  The lack of information on edge distribution or known difficult regions makes assessing deceptiveness challenging. However, the high exploration rate and the low convergence suggest that either the solution is difficult to find, or the problem is indeed deceptive and the search is currently focusing on sub-optimal solutions."
  },
  "population_state": {
    "diversity": "High (0.9744).  The population is diverse, meaning solutions are varied, which is positive for exploration in the early stages.",
    "convergence": "Very low (0.0).  This indicates a lack of convergence toward a specific region of the search space and aligns with the high diversity.  The search is either still broadly exploring or is experiencing high variance in solution quality.",
    "clustering": "No clustering information.  This suggests no clear groups of similar solutions within the population, further reflecting the high diversity."
  },
  "difficult_regions": {
    "identified_challenges": "None explicitly identified. With the provided data, it's impossible to identify specific difficult regions.  The 'Structure' data indicating 0 high-quality edges and 0 common subpaths gives no hints about problematic sub-routes or edge connections. The lack of any known difficult regions also supports this.",
    "specific_node_sequences_or_edges_to_avoid": "None identified. Without information on the TSP instance (number of nodes) or specifics on sub-routes, the current information does not allow to provide this information."
  },
  "opportunity_regions": {
    "promising_areas": "Unknown.  Due to the lack of information on the problem's structure, it's impossible to suggest promising areas. There are no common subpaths or edges identified from which potential promising areas could be identified.",
    "specific_node_sequences_or_edges_to_include": "None identified. Similar to difficult regions, without problem-specific knowledge, we cannot identify favorable sub-routes or edges."
  },
  "evolution_phase": "Exploration. The high diversity, low convergence, the cost fluctuations (high standard deviation) and the improvement rate per iteration indicates that the algorithm is primarily in an exploratory phase, searching for promising regions.",
  "evolution_direction": {
    "recommended_strategy": "Continue with a strong exploration focus, but consider starting to incorporate more exploitation after the next iteration.",
    "operator_suggestions": [
      {
        "operator": "Mutation",
        "details": "Continue using mutation operators that introduce significant changes to the solutions (e.g., 2-opt, insertion, swap). Maintain a high mutation rate to preserve diversity and facilitate broad exploration. Consider adding a small rate of mutations for the next iteration."
      },
      {
        "operator": "Crossover",
        "details": "Maintain or slightly reduce the crossover rate to balance between exploration and early exploitation.  Use crossover operators that are designed for exploration and that generate diverse offspring (e.g., order crossover, partially mapped crossover) that can generate new edges."
      },
      {
        "operator": "Selection",
        "details": "Ensure that elitism is used to preserve the best solutions.  Consider using a selection strategy that favors exploration to keep the solutions diverse, to increase the chances of finding a better solution."
      },
       {
        "operator": "Local Search (optional)",
        "details": "Consider incorporating local search operators (e.g., 2-opt, 3-opt) with a low probability to explore the neighborhood of good solutions and attempt to refine solutions.  But be careful about applying too much local search at this stage, as it could lead to premature convergence in a local optima. These should only be considered once a good set of candidates is identified and refined."
      }

    ]
  }
}
```
2025-06-22 18:59:53,383 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:59:53,383 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': "Likely rugged. The high standard deviation in costs (40615.3) and the presence of local optima (indicated by the iteration's cost fluctuation) suggest a landscape with many peaks and valleys. This ruggedness makes finding the global optimum challenging.", 'modality': 'Potentially multimodal. While not definitively confirmed, the ruggedness suggests the presence of multiple local optima, implying a multimodal search space.', 'deceptiveness': 'Unknown.  The lack of information on edge distribution or known difficult regions makes assessing deceptiveness challenging. However, the high exploration rate and the low convergence suggest that either the solution is difficult to find, or the problem is indeed deceptive and the search is currently focusing on sub-optimal solutions.'}, 'population_state': {'diversity': 'High (0.9744).  The population is diverse, meaning solutions are varied, which is positive for exploration in the early stages.', 'convergence': 'Very low (0.0).  This indicates a lack of convergence toward a specific region of the search space and aligns with the high diversity.  The search is either still broadly exploring or is experiencing high variance in solution quality.', 'clustering': 'No clustering information.  This suggests no clear groups of similar solutions within the population, further reflecting the high diversity.'}, 'difficult_regions': {'identified_challenges': "None explicitly identified. With the provided data, it's impossible to identify specific difficult regions.  The 'Structure' data indicating 0 high-quality edges and 0 common subpaths gives no hints about problematic sub-routes or edge connections. The lack of any known difficult regions also supports this.", 'specific_node_sequences_or_edges_to_avoid': 'None identified. Without information on the TSP instance (number of nodes) or specifics on sub-routes, the current information does not allow to provide this information.'}, 'opportunity_regions': {'promising_areas': "Unknown.  Due to the lack of information on the problem's structure, it's impossible to suggest promising areas. There are no common subpaths or edges identified from which potential promising areas could be identified.", 'specific_node_sequences_or_edges_to_include': 'None identified. Similar to difficult regions, without problem-specific knowledge, we cannot identify favorable sub-routes or edges.'}, 'evolution_phase': 'Exploration. The high diversity, low convergence, the cost fluctuations (high standard deviation) and the improvement rate per iteration indicates that the algorithm is primarily in an exploratory phase, searching for promising regions.', 'evolution_direction': {'recommended_strategy': 'Continue with a strong exploration focus, but consider starting to incorporate more exploitation after the next iteration.', 'operator_suggestions': [{'operator': 'Mutation', 'details': 'Continue using mutation operators that introduce significant changes to the solutions (e.g., 2-opt, insertion, swap). Maintain a high mutation rate to preserve diversity and facilitate broad exploration. Consider adding a small rate of mutations for the next iteration.'}, {'operator': 'Crossover', 'details': 'Maintain or slightly reduce the crossover rate to balance between exploration and early exploitation.  Use crossover operators that are designed for exploration and that generate diverse offspring (e.g., order crossover, partially mapped crossover) that can generate new edges.'}, {'operator': 'Selection', 'details': 'Ensure that elitism is used to preserve the best solutions.  Consider using a selection strategy that favors exploration to keep the solutions diverse, to increase the chances of finding a better solution.'}, {'operator': 'Local Search (optional)', 'details': 'Consider incorporating local search operators (e.g., 2-opt, 3-opt) with a low probability to explore the neighborhood of good solutions and attempt to refine solutions.  But be careful about applying too much local search at this stage, as it could lead to premature convergence in a local optima. These should only be considered once a good set of candidates is identified and refined.'}]}}
2025-06-22 18:59:53,383 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:59:53,383 - __main__ - INFO - 分析阶段完成
2025-06-22 18:59:53,383 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': "Likely rugged. The high standard deviation in costs (40615.3) and the presence of local optima (indicated by the iteration's cost fluctuation) suggest a landscape with many peaks and valleys. This ruggedness makes finding the global optimum challenging.", 'modality': 'Potentially multimodal. While not definitively confirmed, the ruggedness suggests the presence of multiple local optima, implying a multimodal search space.', 'deceptiveness': 'Unknown.  The lack of information on edge distribution or known difficult regions makes assessing deceptiveness challenging. However, the high exploration rate and the low convergence suggest that either the solution is difficult to find, or the problem is indeed deceptive and the search is currently focusing on sub-optimal solutions.'}, 'population_state': {'diversity': 'High (0.9744).  The population is diverse, meaning solutions are varied, which is positive for exploration in the early stages.', 'convergence': 'Very low (0.0).  This indicates a lack of convergence toward a specific region of the search space and aligns with the high diversity.  The search is either still broadly exploring or is experiencing high variance in solution quality.', 'clustering': 'No clustering information.  This suggests no clear groups of similar solutions within the population, further reflecting the high diversity.'}, 'difficult_regions': {'identified_challenges': "None explicitly identified. With the provided data, it's impossible to identify specific difficult regions.  The 'Structure' data indicating 0 high-quality edges and 0 common subpaths gives no hints about problematic sub-routes or edge connections. The lack of any known difficult regions also supports this.", 'specific_node_sequences_or_edges_to_avoid': 'None identified. Without information on the TSP instance (number of nodes) or specifics on sub-routes, the current information does not allow to provide this information.'}, 'opportunity_regions': {'promising_areas': "Unknown.  Due to the lack of information on the problem's structure, it's impossible to suggest promising areas. There are no common subpaths or edges identified from which potential promising areas could be identified.", 'specific_node_sequences_or_edges_to_include': 'None identified. Similar to difficult regions, without problem-specific knowledge, we cannot identify favorable sub-routes or edges.'}, 'evolution_phase': 'Exploration. The high diversity, low convergence, the cost fluctuations (high standard deviation) and the improvement rate per iteration indicates that the algorithm is primarily in an exploratory phase, searching for promising regions.', 'evolution_direction': {'recommended_strategy': 'Continue with a strong exploration focus, but consider starting to incorporate more exploitation after the next iteration.', 'operator_suggestions': [{'operator': 'Mutation', 'details': 'Continue using mutation operators that introduce significant changes to the solutions (e.g., 2-opt, insertion, swap). Maintain a high mutation rate to preserve diversity and facilitate broad exploration. Consider adding a small rate of mutations for the next iteration.'}, {'operator': 'Crossover', 'details': 'Maintain or slightly reduce the crossover rate to balance between exploration and early exploitation.  Use crossover operators that are designed for exploration and that generate diverse offspring (e.g., order crossover, partially mapped crossover) that can generate new edges.'}, {'operator': 'Selection', 'details': 'Ensure that elitism is used to preserve the best solutions.  Consider using a selection strategy that favors exploration to keep the solutions diverse, to increase the chances of finding a better solution.'}, {'operator': 'Local Search (optional)', 'details': 'Consider incorporating local search operators (e.g., 2-opt, 3-opt) with a low probability to explore the neighborhood of good solutions and attempt to refine solutions.  But be careful about applying too much local search at this stage, as it could lead to premature convergence in a local optima. These should only be considered once a good set of candidates is identified and refined.'}]}}
2025-06-22 18:59:53,383 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:59:53,383 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:59:53,383 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 3
- Population: 10 individuals, diversity=High (0.9744).  The population is diverse, meaning solutions are varied, which is positive for exploration in the early stages.
- Best cost: 11456.0, Mean cost: 80054.3
- Search space: Ruggedness: Likely rugged. The high standard deviation in costs (40615.3) and the presence of local optima (indicated by the iteration's cost fluctuation) suggest a landscape with many peaks and valleys. This ruggedness makes finding the global optimum challenging., Modality: Potentially multimodal. While not definitively confirmed, the ruggedness suggests the presence of multiple local optima, implying a multimodal search space.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Intermediate Stage of Optimization",
    "features": "The evolution is showing improvement in both cost and diversity, indicating the search is successfu...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:59:53,383 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:59:55,995 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "exploit",
    "individual_6": "exploit",
    "individual_7": "exploit",
    "individual_8": "exploit",
    "individual_9": "explore",
    "individual_10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size_factor": 0.1,
      "region_focus_enabled": true,
      "region_focus_strength": 0.75
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.2,
      "step_size_factor": 0.01,
      "local_search_radius_factor": 0.2
    }
  }
}
```

2025-06-22 18:59:55,995 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 18:59:55,995 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 18:59:55,995 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "exploit",
    "individual_6": "exploit",
    "individual_7": "exploit",
    "individual_8": "exploit",
    "individual_9": "explore",
    "individual_10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size_factor": 0.1,
      "region_focus_enabled": true,
      "region_focus_strength": 0.75
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.2,
      "step_size_factor": 0.01,
      "local_search_radius_factor": 0.2
    }
  }
}
```

2025-06-22 18:59:55,995 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:59:55,995 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 18:59:55,995 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "exploit",
    "individual_6": "exploit",
    "individual_7": "exploit",
    "individual_8": "exploit",
    "individual_9": "explore",
    "individual_10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_rate": 0.8,
      "step_size_factor": 0.1,
      "region_focus_enabled": true,
      "region_focus_strength": 0.75
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_rate": 0.2,
      "step_size_factor": 0.01,
      "local_search_radius_factor": 0.2
    }
  }
}
```

2025-06-22 18:59:55,995 - __main__ - INFO - 开始进化阶段
2025-06-22 18:59:55,995 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:59:55,995 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:55,995 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [21, 17, 3] and ending with [61, 63, 65]
- Cost: 21852.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8953846153846153
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:55,995 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:59:59,546 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    21, 17, 3, 5, 7, 9, 11, 13, 15, 1, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18,
    20, 19, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52,
    54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43,
    41, 39, 37, 35, 33, 31, 29, 27, 25, 23
  ],
  "modification_strategy": "Implemented a strategy focusing on a zig-zag pattern for diversity, alternating between exploring the lower and upper parts of the node index range. This aimed to visit unexplored regions early and late in the path. Specifically, after an initial section following the given start, the path alternates between traversing the even and odd indices to achieve diversity and cover the available region. Once the main area is visited, the strategy navigates the higher indices, ensuring all nodes are visited.",
  "targeted_regions": "The strategy targeted all regions. It prioritized diversity by visiting a range of low indices, then higher indices, then the last section. This approach is intended to explore all regions, including the ones considered 'opportunity' and 'difficult' during the exploration."
}
```
2025-06-22 18:59:59,546 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:59:59,546 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20044.0, 路径: [21, 17, 3, 5, 7, 9, 11, 13, 15, 1, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 19, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23]
2025-06-22 18:59:59,546 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [21, 17, 3, 5, 7, 9, 11, 13, 15, 1, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 19, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23], 'cur_cost': 20044.0}
2025-06-22 18:59:59,546 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:59:59,548 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:59:59,548 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 10, 8] and ending with [63, 64, 65]
- Cost: 11456.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8907692307692308
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:59:59,549 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:02,640 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Completely restructured the path by reversing the order of the initial nodes, then establishing a linear progression across the rest of the nodes. This prioritizes visiting the earlier nodes, potentially impacting exploration of the 'opportunity' and 'difficult' regions in a different order.",
  "targeted_regions": "The early nodes (0-10) are now visited very early. The goal is to have this path significantly different from the previous one, exploring earlier nodes first."
}
```
2025-06-22 19:00:02,640 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:02,640 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11456.0, 路径: [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 19:00:02,640 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}
2025-06-22 19:00:02,643 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 19:00:02,643 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:02,643 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [57, 60, 63]
- Cost: 25900.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8907692307692308
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:02,643 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:06,773 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57,
    60, 63, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52,
    55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47,
    50, 53, 56, 59, 62, 65
  ],
  "modification_strategy": "This path is generated with an interleaving approach. It starts with a sequence of nodes which are multiples of 3 and then introduces a node which is not a multiple of 3, creating a zig-zag pattern to cover a larger area to explore diversity. Then it shifts to the next node that are not multiples of 3, and so forth. This strategy is designed to prioritize exploration of different regions and create a more diverse path. It aims to avoid the difficult regions and utilize the opportunity regions by spreading out nodes across different regions.",
  "targeted_regions": "The strategy targets exploring the entire space by spreading the nodes evenly to avoid difficult regions and focusing on the unexplored areas."
}
```
2025-06-22 19:00:06,773 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:06,773 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27899.0, 路径: [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65]
2025-06-22 19:00:06,775 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65], 'cur_cost': 27899.0}
2025-06-22 19:00:06,775 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 19:00:06,775 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:06,775 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [52, 4, 15] and ending with [32, 57, 61]
- Cost: 116559.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8938461538461538
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:06,775 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:10,250 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    52, 3, 10, 45, 60, 1, 28, 39, 48, 65, 12, 2, 56, 17, 5, 22, 35, 64, 25, 31, 53, 8, 41, 19, 59, 46, 14, 57, 21, 37, 6, 43, 32, 20, 15, 4, 27, 50, 36, 16, 29, 55, 63, 11, 40, 62, 30, 54, 34, 44, 42, 13, 24, 51, 7, 26, 33, 9, 18, 23, 38, 49, 58, 0, 61
  ],
  "modification_strategy": "A path was generated by starting with a similar structure (initial node) to the original path, but then making significant detours early on to explore the unexplored regions. Further, I used a combination of random selection and prioritizing nodes that are further away to ensure a diverse path. The later part of the path was structured to explore nodes around the end points to ensure proper coverage.",
  "targeted_regions": "Unexplored Regions and regions with higher node dispersion, focusing on nodes around the starting and ending points of the original path, as well as the opportunity regions identified in the search context."
}
```
2025-06-22 19:00:10,250 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:10,250 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112140.0, 路径: [52, 3, 10, 45, 60, 1, 28, 39, 48, 65, 12, 2, 56, 17, 5, 22, 35, 64, 25, 31, 53, 8, 41, 19, 59, 46, 14, 57, 21, 37, 6, 43, 32, 20, 15, 4, 27, 50, 36, 16, 29, 55, 63, 11, 40, 62, 30, 54, 34, 44, 42, 13, 24, 51, 7, 26, 33, 9, 18, 23, 38, 49, 58, 0, 61, 47]
2025-06-22 19:00:10,250 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [52, 3, 10, 45, 60, 1, 28, 39, 48, 65, 12, 2, 56, 17, 5, 22, 35, 64, 25, 31, 53, 8, 41, 19, 59, 46, 14, 57, 21, 37, 6, 43, 32, 20, 15, 4, 27, 50, 36, 16, 29, 55, 63, 11, 40, 62, 30, 54, 34, 44, 42, 13, 24, 51, 7, 26, 33, 9, 18, 23, 38, 49, 58, 0, 61, 47], 'cur_cost': 112140.0}
2025-06-22 19:00:10,250 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 19:00:10,250 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:10,250 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 31, 10] and ending with [0, 17, 32]
- Cost: 105560.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8907692307692308
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:10,250 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:13,368 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    27, 31, 10, 45, 5, 22, 38, 13, 60, 41, 2, 57, 3, 54, 16, 50, 34, 1, 61, 11, 20, 29, 47, 30, 33, 40, 62, 52, 9, 25, 14, 48, 65, 4, 58, 35, 18, 26, 42, 12, 59, 55, 7, 49, 37, 15, 28, 64, 21, 44, 39, 6, 53, 36, 8, 23, 63, 43, 51, 17, 32, 24, 0, 19, 46
  ],
  "modification_strategy": "Shuffled and re-routed the path by focusing on exploring the opportunity regions and difficult regions, attempting to change the order of the nodes in a way that promotes diversity and avoids difficult areas while maintaining a valid path.",
  "targeted_regions": "Opportunity regions and difficult regions were prioritized to create a more diverse path, using diversity weight to influence path generation while not being trapped in difficult regions"
}
```
2025-06-22 19:00:13,368 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:13,370 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106452.0, 路径: [27, 31, 10, 45, 5, 22, 38, 13, 60, 41, 2, 57, 3, 54, 16, 50, 34, 1, 61, 11, 20, 29, 47, 30, 33, 40, 62, 52, 9, 25, 14, 48, 65, 4, 58, 35, 18, 26, 42, 12, 59, 55, 7, 49, 37, 15, 28, 64, 21, 44, 39, 6, 53, 36, 8, 23, 63, 43, 51, 17, 32, 24, 0, 19, 46, 56]
2025-06-22 19:00:13,370 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [27, 31, 10, 45, 5, 22, 38, 13, 60, 41, 2, 57, 3, 54, 16, 50, 34, 1, 61, 11, 20, 29, 47, 30, 33, 40, 62, 52, 9, 25, 14, 48, 65, 4, 58, 35, 18, 26, 42, 12, 59, 55, 7, 49, 37, 15, 28, 64, 21, 44, 39, 6, 53, 36, 8, 23, 63, 43, 51, 17, 32, 24, 0, 19, 46, 56], 'cur_cost': 106452.0}
2025-06-22 19:00:13,371 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 19:00:13,371 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:13,372 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [26, 59, 41] and ending with [35, 19, 52]
- Cost: 118096.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:13,372 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:16,476 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    26, 59, 41, 1, 60, 18, 47, 38, 2, 58, 49, 56, 4, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 48, 50, 51, 52, 53, 54, 55, 57, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "This strategy prioritizes diversity by substantially altering the initial path segment and incorporating a more distributed exploration across the node space. The initial portion is shuffled to create a divergent start. The remaining nodes are ordered, with key nodes strategically placed within the sequence.",
  "targeted_regions": "This path targets the two opportunity regions and seeks to avoid the two difficult regions by ensuring the nodes are spread around the node indices."
}
```
2025-06-22 19:00:16,476 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:16,476 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35737.0, 路径: [26, 59, 41, 1, 60, 18, 47, 38, 2, 58, 49, 56, 4, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 48, 50, 51, 52, 53, 54, 55, 57, 61, 62, 63, 64, 65, 0]
2025-06-22 19:00:16,476 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 59, 41, 1, 60, 18, 47, 38, 2, 58, 49, 56, 4, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 48, 50, 51, 52, 53, 54, 55, 57, 61, 62, 63, 64, 65, 0], 'cur_cost': 35737.0}
2025-06-22 19:00:16,476 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 19:00:16,476 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:16,476 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 63, 29] and ending with [57, 14, 49]
- Cost: 107684.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8338461538461538
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:16,476 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:20,409 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 63, 29, 5, 42, 3, 18, 55, 60, 20, 1, 58, 14, 49, 31, 35, 25, 40, 10, 2, 51, 61, 37, 4, 33, 22, 45, 15, 65, 59, 38, 28, 50, 48, 24, 34, 39, 19, 56, 7, 64, 26, 30, 17, 43, 9, 32, 41, 11, 46, 52, 6, 54, 27, 23, 47, 13, 8, 16, 44, 36, 53, 21, 0, 62, 57
  ],
  "modification_strategy": "Implemented a 're-routing' approach. Starting from the original path's beginning, I randomized segments, incorporating nodes from potential unexplored regions indicated in the search context. This involved: 1) Keeping the first few nodes similar, 2) Randomly reordering segments focusing on regions flagged as unexplored or offering opportunities. 3) Ensuring complete coverage by adding nodes not included in the randomizations and the end segment of the original path. Emphasis on exploring unexplored regions and targeting the difficult regions by avoiding them, to maximize diversity.",
  "targeted_regions": "Targeted the opportunity regions and attempted to circumnavigate the difficult regions by strategically randomizing node sequences, specifically attempting to visit previously unvisited or less frequently visited regions."
}
```
2025-06-22 19:00:20,409 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:20,409 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109198.0, 路径: [12, 63, 29, 5, 42, 3, 18, 55, 60, 20, 1, 58, 14, 49, 31, 35, 25, 40, 10, 2, 51, 61, 37, 4, 33, 22, 45, 15, 65, 59, 38, 28, 50, 48, 24, 34, 39, 19, 56, 7, 64, 26, 30, 17, 43, 9, 32, 41, 11, 46, 52, 6, 54, 27, 23, 47, 13, 8, 16, 44, 36, 53, 21, 0, 62, 57]
2025-06-22 19:00:20,409 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 63, 29, 5, 42, 3, 18, 55, 60, 20, 1, 58, 14, 49, 31, 35, 25, 40, 10, 2, 51, 61, 37, 4, 33, 22, 45, 15, 65, 59, 38, 28, 50, 48, 24, 34, 39, 19, 56, 7, 64, 26, 30, 17, 43, 9, 32, 41, 11, 46, 52, 6, 54, 27, 23, 47, 13, 8, 16, 44, 36, 53, 21, 0, 62, 57], 'cur_cost': 109198.0}
2025-06-22 19:00:20,409 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 19:00:20,409 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:20,409 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [19, 39, 42] and ending with [31, 18, 62]
- Cost: 89646.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8384615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:20,409 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:24,076 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 54, 53, 52, 51, 49, 48, 47, 46, 44, 43, 41, 39, 37, 36, 34, 33, 32, 29, 27, 26, 24, 23, 22, 21, 19, 18, 17, 16, 14, 13, 12, 11, 9, 8, 7, 6, 4, 3, 2, 1, 38, 28, 42, 50
  ],
  "modification_strategy": "Implemented a strategy based on a more radical re-ordering of the original path, while still incorporating some elements to avoid significant jumps. First I created a more linear path. Then I created a reverse loop in order to address the end requirement of the path",
  "targeted_regions": "Attempted to explore a new ordering of node indices. Focused on spreading the path across the graph to better explore unexplored regions."
}
```
2025-06-22 19:00:24,079 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:24,079 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28430.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 54, 53, 52, 51, 49, 48, 47, 46, 44, 43, 41, 39, 37, 36, 34, 33, 32, 29, 27, 26, 24, 23, 22, 21, 19, 18, 17, 16, 14, 13, 12, 11, 9, 8, 7, 6, 4, 3, 2, 1, 38, 28, 42, 31]
2025-06-22 19:00:24,079 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 54, 53, 52, 51, 49, 48, 47, 46, 44, 43, 41, 39, 37, 36, 34, 33, 32, 29, 27, 26, 24, 23, 22, 21, 19, 18, 17, 16, 14, 13, 12, 11, 9, 8, 7, 6, 4, 3, 2, 1, 38, 28, 42, 31], 'cur_cost': 28430.0}
2025-06-22 19:00:24,080 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 19:00:24,080 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:24,081 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [64, 11, 2] and ending with [17, 5, 36]
- Cost: 112628.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7861538461538462
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:24,081 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:27,959 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    64, 11, 5, 22, 30, 45, 58, 1, 33, 2, 6, 3, 4, 7, 9, 8, 10, 12, 13, 14,
    15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31, 32, 34, 35,
    36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54,
    55, 56, 57, 59, 60, 61, 62, 63, 65, 17
  ],
  "modification_strategy": "Implemented a more randomized approach focusing on exploring unexplored regions indicated in the search context. Starting with the original path's beginning nodes, I introduced significant shifts in the sequence to visit regions that the prior path likely avoided, leveraging the diversity weight and risk tolerance parameters to guide the alterations. Used an iterative swapping of node indices to shift the path significantly, prioritizing the opportunity regions in the path. Added the ending node at the very end to comply with the constraints.",
  "targeted_regions": "Targeted the two unexplored regions and two opportunity regions, aiming to diversify the route's structure. Specifically, the shifts focus on areas where the previous path showed limited exploration based on the provided search context. I aimed for a path that is less similar to the original one in terms of the order of nodes while still conforming to the TSP constraints."
}
```
2025-06-22 19:00:27,960 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:27,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25608.0, 路径: [64, 11, 5, 22, 30, 45, 58, 1, 33, 2, 6, 3, 4, 7, 9, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 65, 0]
2025-06-22 19:00:27,960 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [64, 11, 5, 22, 30, 45, 58, 1, 33, 2, 6, 3, 4, 7, 9, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 65, 0], 'cur_cost': 25608.0}
2025-06-22 19:00:27,961 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 19:00:27,961 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 19:00:27,961 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [4, 1, 50] and ending with [41, 16, 14]
- Cost: 91162.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7123076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 19:00:27,961 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 19:00:31,493 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    4, 1, 50, 52, 54, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 55, 53, 51, 49, 47,
    45, 43, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8,
    6, 2, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37,
    39, 42, 44, 46, 48, 41, 16, 14
  ],
  "modification_strategy": "This path was constructed to maximize diversity by starting and ending with nodes from the original path, but then attempting to visit previously unexplored regions. I alternated visiting nodes, incorporating new regions that were identified as opportunity regions based on the search context. The strategy involves a zig-zag exploration approach, ensuring a diverse spread across the graph space while incorporating the given starting and ending constraints of [4,1,50] and [41,16,14].",
  "targeted_regions": "Opportunity regions identified in the search context (Specific regions unknown based on given information, but this strategy prioritizes exploration of potential new areas within the context's specifications.)"
}
```
2025-06-22 19:00:31,493 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 19:00:31,493 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22587.0, 路径: [4, 1, 50, 52, 54, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 55, 53, 51, 49, 47, 45, 43, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 2, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 42, 44, 46, 48]
2025-06-22 19:00:31,493 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [4, 1, 50, 52, 54, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 55, 53, 51, 49, 47, 45, 43, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 2, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 42, 44, 46, 48], 'cur_cost': 22587.0}
2025-06-22 19:00:31,493 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [21, 17, 3, 5, 7, 9, 11, 13, 15, 1, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 19, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23], 'cur_cost': 20044.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [9, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11456.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65], 'cur_cost': 27899.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [52, 3, 10, 45, 60, 1, 28, 39, 48, 65, 12, 2, 56, 17, 5, 22, 35, 64, 25, 31, 53, 8, 41, 19, 59, 46, 14, 57, 21, 37, 6, 43, 32, 20, 15, 4, 27, 50, 36, 16, 29, 55, 63, 11, 40, 62, 30, 54, 34, 44, 42, 13, 24, 51, 7, 26, 33, 9, 18, 23, 38, 49, 58, 0, 61, 47], 'cur_cost': 112140.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [27, 31, 10, 45, 5, 22, 38, 13, 60, 41, 2, 57, 3, 54, 16, 50, 34, 1, 61, 11, 20, 29, 47, 30, 33, 40, 62, 52, 9, 25, 14, 48, 65, 4, 58, 35, 18, 26, 42, 12, 59, 55, 7, 49, 37, 15, 28, 64, 21, 44, 39, 6, 53, 36, 8, 23, 63, 43, 51, 17, 32, 24, 0, 19, 46, 56], 'cur_cost': 106452.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 59, 41, 1, 60, 18, 47, 38, 2, 58, 49, 56, 4, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 48, 50, 51, 52, 53, 54, 55, 57, 61, 62, 63, 64, 65, 0], 'cur_cost': 35737.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 63, 29, 5, 42, 3, 18, 55, 60, 20, 1, 58, 14, 49, 31, 35, 25, 40, 10, 2, 51, 61, 37, 4, 33, 22, 45, 15, 65, 59, 38, 28, 50, 48, 24, 34, 39, 19, 56, 7, 64, 26, 30, 17, 43, 9, 32, 41, 11, 46, 52, 6, 54, 27, 23, 47, 13, 8, 16, 44, 36, 53, 21, 0, 62, 57], 'cur_cost': 109198.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 59, 58, 57, 56, 54, 53, 52, 51, 49, 48, 47, 46, 44, 43, 41, 39, 37, 36, 34, 33, 32, 29, 27, 26, 24, 23, 22, 21, 19, 18, 17, 16, 14, 13, 12, 11, 9, 8, 7, 6, 4, 3, 2, 1, 38, 28, 42, 31], 'cur_cost': 28430.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [64, 11, 5, 22, 30, 45, 58, 1, 33, 2, 6, 3, 4, 7, 9, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 65, 0], 'cur_cost': 25608.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [4, 1, 50, 52, 54, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 55, 53, 51, 49, 47, 45, 43, 41, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 2, 0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 42, 44, 46, 48], 'cur_cost': 22587.0}}]
2025-06-22 19:00:31,493 - __main__ - INFO - 进化阶段完成
2025-06-22 19:00:31,493 - __main__ - INFO - 开始评估阶段
2025-06-22 19:00:31,493 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 19:00:31,497 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 19:00:31,497 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 3/4
- Population changes:
  * Cost: min 11456.0->11456.0, mean 80054.3->49955.1
  * Diversity: 0.8953846153846153->0.6446153846153846
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 10 individuals, success rate 60.0%, avg improvement 30099.2
- Exploit: 0 individuals, success rate 0%, avg improvement 0
- Overall improvement: 0.0

## Historical Trends:
Last 3 iterations: costs [11300.0, 13812.0, 11456.0], diversity [0.9572390572390573, 0.9649831649831649, 0.9744107744107743]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 19:00:37,503 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Convergence Phase / Stagnation",
    "features": "The cost is stable at the minimum value found in the population. Diversity is decreasing.  The exploration phase shows some success with a high success rate, but overall the system is not showing any improvement. The historical data points to a potential premature convergence. The lack of exploitation suggests the algorithm is focusing on exploration with decreasing diversity and with no further improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 30099.2,
      "evaluation": "The explore phase shows success (60%) and a high average improvement. The exploration is still useful, but the lack of improvement overall suggests that the current exploration is not finding significant improvements or that the explored solutions are not being incorporated into the population effectively. There's potential for further exploration in the existing space, or that the current individuals in the population are close to optimal. "
    },
    "exploit": {
      "success_rate": 0.0,
      "avg_improvement": 0,
      "evaluation": "The exploit phase is completely inactive. This is expected in a converged state, but might be premature. Without exploitation, the algorithm cannot refine promising solutions further."
    },
    "overall_improvement": {
      "value": 0.0,
      "evaluation": "No overall improvement is observed.  This confirms the potential for stagnation."
    }
  },
  "balance_state": {
    "assessment": "The balance heavily favors exploration (10 individuals vs 0 exploitation), given the stagnation and decrease in diversity and overall no improvement. The algorithm is likely focusing on different regions of the search space but without consolidating and optimizing a promising candidate. The lack of exploitation needs immediate attention.",
    "adjustment_needs": "The balance needs to shift *towards* exploitation. The algorithm should focus on refine promising solutions found during exploration. Diversity reduction must also be addressed in order to prevent falling into a local minimum. "
  },
  "recommendations": [
    {
      "action": "Increase Exploitation, Decrease Exploration",
      "details": "Introduce exploitation: increase the number of individuals for Exploitation.  Consider including exploitation operators, like local search, elitism to refine promising solutions. If the current best solutions do not improve further, they should be kept in the population. Reduce the number of exploration individuals (e.g., by 50%)."
    },
    {
      "action": "Analyze Exploration Successes",
      "details": "Examine the 'successful' exploration individuals.  What are their common characteristics?  Is the exploration consistently finding similar solutions?  This might inform which parameters or regions of the search space are most promising."
    },
    {
      "action": "Diversity Management",
      "details": "Implement diversity mechanisms. Explore strategies to increase diversity (if the minimum hasn't been found, then a premature convergence may occur), e.g., by introducing new mutations with a higher probability (e.g., changing mutation parameters), or introducing a small amount of random solutions. Monitor the change in diversity and if convergence occurs after the increase, consider re-evaluating it."
    },
    {
      "action": "Review Parameterization",
      "details": "Review the exploration parameters (mutation rate, step size, etc.). Are they appropriate given the current search space and convergence stage? Experiment with adjusting these parameters. If the previous search was optimal, consider maintaining the current values."
    }
  ]
}
```
2025-06-22 19:00:37,533 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 19:00:37,533 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Convergence Phase / Stagnation",
    "features": "The cost is stable at the minimum value found in the population. Diversity is decreasing.  The exploration phase shows some success with a high success rate, but overall the system is not showing any improvement. The historical data points to a potential premature convergence. The lack of exploitation suggests the algorithm is focusing on exploration with decreasing diversity and with no further improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 30099.2,
      "evaluation": "The explore phase shows success (60%) and a high average improvement. The exploration is still useful, but the lack of improvement overall suggests that the current exploration is not finding significant improvements or that the explored solutions are not being incorporated into the population effectively. There's potential for further exploration in the existing space, or that the current individuals in the population are close to optimal. "
    },
    "exploit": {
      "success_rate": 0.0,
      "avg_improvement": 0,
      "evaluation": "The exploit phase is completely inactive. This is expected in a converged state, but might be premature. Without exploitation, the algorithm cannot refine promising solutions further."
    },
    "overall_improvement": {
      "value": 0.0,
      "evaluation": "No overall improvement is observed.  This confirms the potential for stagnation."
    }
  },
  "balance_state": {
    "assessment": "The balance heavily favors exploration (10 individuals vs 0 exploitation), given the stagnation and decrease in diversity and overall no improvement. The algorithm is likely focusing on different regions of the search space but without consolidating and optimizing a promising candidate. The lack of exploitation needs immediate attention.",
    "adjustment_needs": "The balance needs to shift *towards* exploitation. The algorithm should focus on refine promising solutions found during exploration. Diversity reduction must also be addressed in order to prevent falling into a local minimum. "
  },
  "recommendations": [
    {
      "action": "Increase Exploitation, Decrease Exploration",
      "details": "Introduce exploitation: increase the number of individuals for Exploitation.  Consider including exploitation operators, like local search, elitism to refine promising solutions. If the current best solutions do not improve further, they should be kept in the population. Reduce the number of exploration individuals (e.g., by 50%)."
    },
    {
      "action": "Analyze Exploration Successes",
      "details": "Examine the 'successful' exploration individuals.  What are their common characteristics?  Is the exploration consistently finding similar solutions?  This might inform which parameters or regions of the search space are most promising."
    },
    {
      "action": "Diversity Management",
      "details": "Implement diversity mechanisms. Explore strategies to increase diversity (if the minimum hasn't been found, then a premature convergence may occur), e.g., by introducing new mutations with a higher probability (e.g., changing mutation parameters), or introducing a small amount of random solutions. Monitor the change in diversity and if convergence occurs after the increase, consider re-evaluating it."
    },
    {
      "action": "Review Parameterization",
      "details": "Review the exploration parameters (mutation rate, step size, etc.). Are they appropriate given the current search space and convergence stage? Experiment with adjusting these parameters. If the previous search was optimal, consider maintaining the current values."
    }
  ]
}
```
2025-06-22 19:00:37,533 - __main__ - INFO - 评估阶段完成
2025-06-22 19:00:37,533 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Convergence Phase / Stagnation",
    "features": "The cost is stable at the minimum value found in the population. Diversity is decreasing.  The exploration phase shows some success with a high success rate, but overall the system is not showing any improvement. The historical data points to a potential premature convergence. The lack of exploitation suggests the algorithm is focusing on exploration with decreasing diversity and with no further improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 30099.2,
      "evaluation": "The explore phase shows success (60%) and a high average improvement. The exploration is still useful, but the lack of improvement overall suggests that the current exploration is not finding significant improvements or that the explored solutions are not being incorporated into the population effectively. There's potential for further exploration in the existing space, or that the current individuals in the population are close to optimal. "
    },
    "exploit": {
      "success_rate": 0.0,
      "avg_improvement": 0,
      "evaluation": "The exploit phase is completely inactive. This is expected in a converged state, but might be premature. Without exploitation, the algorithm cannot refine promising solutions further."
    },
    "overall_improvement": {
      "value": 0.0,
      "evaluation": "No overall improvement is observed.  This confirms the potential for stagnation."
    }
  },
  "balance_state": {
    "assessment": "The balance heavily favors exploration (10 individuals vs 0 exploitation), given the stagnation and decrease in diversity and overall no improvement. The algorithm is likely focusing on different regions of the search space but without consolidating and optimizing a promising candidate. The lack of exploitation needs immediate attention.",
    "adjustment_needs": "The balance needs to shift *towards* exploitation. The algorithm should focus on refine promising solutions found during exploration. Diversity reduction must also be addressed in order to prevent falling into a local minimum. "
  },
  "recommendations": [
    {
      "action": "Increase Exploitation, Decrease Exploration",
      "details": "Introduce exploitation: increase the number of individuals for Exploitation.  Consider including exploitation operators, like local search, elitism to refine promising solutions. If the current best solutions do not improve further, they should be kept in the population. Reduce the number of exploration individuals (e.g., by 50%)."
    },
    {
      "action": "Analyze Exploration Successes",
      "details": "Examine the 'successful' exploration individuals.  What are their common characteristics?  Is the exploration consistently finding similar solutions?  This might inform which parameters or regions of the search space are most promising."
    },
    {
      "action": "Diversity Management",
      "details": "Implement diversity mechanisms. Explore strategies to increase diversity (if the minimum hasn't been found, then a premature convergence may occur), e.g., by introducing new mutations with a higher probability (e.g., changing mutation parameters), or introducing a small amount of random solutions. Monitor the change in diversity and if convergence occurs after the increase, consider re-evaluating it."
    },
    {
      "action": "Review Parameterization",
      "details": "Review the exploration parameters (mutation rate, step size, etc.). Are they appropriate given the current search space and convergence stage? Experiment with adjusting these parameters. If the previous search was optimal, consider maintaining the current values."
    }
  ]
}
```
2025-06-22 19:00:37,535 - __main__ - INFO - 当前最佳适应度: 11456.0
2025-06-22 19:00:37,535 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-22 19:00:37,551 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 19:00:37,551 - __main__ - INFO - 实例 composite13_66 处理完成
