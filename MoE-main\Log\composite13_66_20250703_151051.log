2025-07-03 15:10:51,578 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 15:10:51,580 - __main__ - INFO - 开始分析阶段
2025-07-03 15:10:51,580 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:10:51,599 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10074.0, 'max': 116172.0, 'mean': 79224.1, 'std': 45469.917806061625}, 'diversity': 0.9080808080808082, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:10:51,600 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10074.0, 'max': 116172.0, 'mean': 79224.1, 'std': 45469.917806061625}, 'diversity_level': 0.9080808080808082, 'convergence_level': 0.0, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:10:51,601 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:10:51,601 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:10:51,602 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:10:51,608 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:10:51,609 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (35, 36), 'frequency': 0.5, 'avg_cost': 39.0}, {'edge': (44, 45), 'frequency': 0.5, 'avg_cost': 15.0}, {'edge': (57, 64), 'frequency': 0.5, 'avg_cost': 23.0}], 'common_subpaths': [{'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (15, 22, 12), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (12, 17, 18), 'frequency': 0.3}, {'subpath': (17, 18, 16), 'frequency': 0.3}, {'subpath': (18, 16, 23), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(9, 11)', 'frequency': 0.4}, {'edge': '(15, 22)', 'frequency': 0.4}, {'edge': '(16, 18)', 'frequency': 0.4}, {'edge': '(35, 36)', 'frequency': 0.5}, {'edge': '(33, 34)', 'frequency': 0.4}, {'edge': '(24, 29)', 'frequency': 0.4}, {'edge': '(29, 32)', 'frequency': 0.4}, {'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(46, 48)', 'frequency': 0.4}, {'edge': '(39, 43)', 'frequency': 0.4}, {'edge': '(44, 45)', 'frequency': 0.5}, {'edge': '(50, 51)', 'frequency': 0.4}, {'edge': '(57, 64)', 'frequency': 0.5}], 'low_frequency_edges': [{'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(53, 64)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(55, 60)', 'frequency': 0.3}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(61, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(2, 63)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.3}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(14, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(42, 57)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(16, 50)', 'frequency': 0.2}, {'edge': '(49, 59)', 'frequency': 0.2}, {'edge': '(9, 21)', 'frequency': 0.3}, {'edge': '(11, 65)', 'frequency': 0.2}, {'edge': '(35, 58)', 'frequency': 0.2}, {'edge': '(26, 58)', 'frequency': 0.2}, {'edge': '(19, 40)', 'frequency': 0.2}, {'edge': '(2, 53)', 'frequency': 0.2}, {'edge': '(37, 47)', 'frequency': 0.2}, {'edge': '(32, 60)', 'frequency': 0.2}, {'edge': '(49, 62)', 'frequency': 0.2}, {'edge': '(28, 52)', 'frequency': 0.2}, {'edge': '(21, 44)', 'frequency': 0.2}, {'edge': '(47, 48)', 'frequency': 0.2}, {'edge': '(15, 43)', 'frequency': 0.2}, {'edge': '(1, 37)', 'frequency': 0.2}, {'edge': '(16, 56)', 'frequency': 0.2}, {'edge': '(18, 63)', 'frequency': 0.2}, {'edge': '(8, 27)', 'frequency': 0.2}, {'edge': '(9, 38)', 'frequency': 0.3}, {'edge': '(3, 50)', 'frequency': 0.2}, {'edge': '(25, 62)', 'frequency': 0.2}, {'edge': '(12, 53)', 'frequency': 0.2}, {'edge': '(49, 52)', 'frequency': 0.2}, {'edge': '(24, 60)', 'frequency': 0.3}, {'edge': '(4, 44)', 'frequency': 0.2}, {'edge': '(15, 23)', 'frequency': 0.2}, {'edge': '(15, 54)', 'frequency': 0.2}, {'edge': '(34, 43)', 'frequency': 0.2}, {'edge': '(29, 61)', 'frequency': 0.2}, {'edge': '(10, 42)', 'frequency': 0.2}, {'edge': '(38, 62)', 'frequency': 0.2}, {'edge': '(31, 40)', 'frequency': 0.2}, {'edge': '(10, 41)', 'frequency': 0.2}, {'edge': '(14, 64)', 'frequency': 0.2}, {'edge': '(39, 56)', 'frequency': 0.2}, {'edge': '(17, 23)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [33, 52, 35, 58, 26, 63, 45], 'cost': 17605.0, 'size': 7}, {'region': [63, 48, 65, 49, 62, 25, 51], 'cost': 16404.0, 'size': 7}, {'region': [45, 8, 49, 52, 26, 53], 'cost': 13719.0, 'size': 6}, {'region': [59, 36, 58, 30], 'cost': 8764.0, 'size': 4}, {'region': [25, 61, 44, 64], 'cost': 8556.0, 'size': 4}]}
2025-07-03 15:10:51,609 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:10:51,610 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:10:51,610 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:10:51,610 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 15:10:51,610 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 15:10:51,610 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:10:51,610 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:10:52,160 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:10:52,160 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 10074.0, mean 79224.1, max 116172.0, std 45469.917806061625
- diversity: 0.9080808080808082
- convergence: 0.0
- clustering: 8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (35, 36), 'frequency': 0.5, 'avg_cost': 39.0}, {'edge': (44, 45), 'frequency': 0.5, 'avg_cost': 15.0}, {'edge': (57, 64), 'frequency': 0.5, 'avg_cost': 23.0}]
- common_subpaths_sample: [{'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [33, 52, 35, 58, 26, 63, 45], 'cost': 17605.0, 'size': 7}, {'region': [63, 48, 65, 49, 62, 25, 51], 'cost': 16404.0, 'size': 7}, {'region': [45, 8, 49, 52, 26, 53], 'cost': 13719.0, 'size': 6}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 15:10:52,162 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:11:23,725 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误: HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-07-03 15:11:25,726 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-07-03 15:11:57,091 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误: HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-07-03 15:11:59,092 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 3/3)
2025-07-03 15:12:16,129 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误: Response ended prematurely
2025-07-03 15:12:16,129 - LandscapeExpert - INFO - LLM返回的分析结果: API请求失败: Response ended prematurely
2025-07-03 15:12:16,130 - LandscapeExpert - WARNING - 解析景观分析结果失败: Unable to extract valid JSON data from the response
2025-07-03 15:12:16,130 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 15:12:16,130 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: Response ended prematurely'}
2025-07-03 15:12:16,130 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 15:12:16,130 - __main__ - INFO - 分析阶段完成
2025-07-03 15:12:16,131 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': 'API请求失败: Response ended prematurely'}
2025-07-03 15:12:16,131 - __main__ - INFO - 开始策略分配阶段
2025-07-03 15:12:16,131 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 15:12:16,131 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 10074.0
  • mean_cost: 79224.1
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 15:12:16,132 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 15:12:16,132 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
