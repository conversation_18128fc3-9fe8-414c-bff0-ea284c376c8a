from __future__ import annotations

"""ablation_manager.py

Utility classes to facilitate *ablation studies* for the MoE-TSP framework.

The core idea is to subclass ``ExpertCollaborationManager`` so we can
selectively disable or replace individual experts (Landscape / Strategy /
Exploration / Exploitation / Assessment) without touching the original
`moe_main.py` implementation.  Each disabled expert is substituted with a
``DummyExpert`` that fulfils the required interface but performs *no* heavy
computation or LLM calls.  For some experiments we also provide lightweight
*rule-based* experts that implement a simple heuristic, allowing us to keep
the overall search loop functional while measuring the pure contribution of
LLM-powered reasoning.

Typical usage (see ``run_ablation.py``)::

    mgr = AblationExpertManager(interface_llm,
                                variant="disable_landscape")

Available variant strings
-------------------------
full (default)                – keep all original experts

disable_landscape             – replace ``LandscapeExpert`` with ``DummyExpert``
rule_landscape                – replace ``LandscapeExpert`` with
                                 ``RuleBasedLandscapeExpert``

disable_strategy              – replace ``<PERSON>Ex<PERSON>`` with ``DummyExpert``
rule_strategy                 – replace ``StrategyExpert`` with
                                 ``RuleBasedStrategyExpert``

disable_exploration           – replace ``ExplorationExpert`` with ``DummyExpert``

disable_exploitation          – replace ``ExploitationExpert`` with ``DummyExpert``

disable_assessment            – replace ``EvolutionAssessmentExpert`` with ``DummyExpert``

You can also combine flags with ``+`` (e.g. ``disable_landscape+disable_strategy``).
"""

from typing import Dict, List, Set
import logging
import re
import importlib  # noqa: F401

# ---------------------------------------------------------------------------
# Import original framework symbols
# ---------------------------------------------------------------------------

# The MoE codebase lives in ``MoE-main/idea`` which may not be on the import
# path when the ablation package (located one directory up) is imported from
# elsewhere.  We arrange the path dynamically so that ``import moe_main`` works
# regardless of the current working directory.

import os, sys
_PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
_MOE_SRC = os.path.join(_PROJECT_ROOT, "MoE-main", "idea")
if _MOE_SRC not in sys.path:
    sys.path.append(_MOE_SRC)

# Now we can safely import
# The import may still fail during static analysis – we guard against that so
# linters do not raise "missing-import" diagnostics.
try:
    moe_main = importlib.import_module("moe_main")  # type: ignore

    # Fetch original expert classes. If any are missing we will overwrite them
    # later with stub fallbacks.
    StatsExpert = getattr(moe_main, "StatsExpert", None)  # type: ignore[assignment]
    PathExpert = getattr(moe_main, "PathExpert", None)  # type: ignore[assignment]
    EliteExpert = getattr(moe_main, "EliteExpert", None)  # type: ignore[assignment]
    LandscapeExpert = getattr(moe_main, "LandscapeExpert", None)  # type: ignore[assignment]
    StrategyExpert = getattr(moe_main, "StrategyExpert", None)  # type: ignore[assignment]
    ExplorationExpert = getattr(moe_main, "ExplorationExpert", None)  # type: ignore[assignment]
    ExploitationExpert = getattr(moe_main, "ExploitationExpert", None)  # type: ignore[assignment]
    EvolutionAssessmentExpert = getattr(moe_main, "EvolutionAssessmentExpert", None)  # type: ignore[assignment]
    _RealExpertBase = getattr(moe_main, "ExpertBase", None)  # type: ignore[assignment]

    # Guarantee a fallback base class.
    if _RealExpertBase is None:
        _RealExpertBase = type("_DynamicBase", (), {})

    # Replace any missing expert classes with the base stub so names always refer to *types*.
    for _name in [
        "StatsExpert",
        "PathExpert",
        "EliteExpert",
        "LandscapeExpert",
        "StrategyExpert",
        "ExplorationExpert",
        "ExploitationExpert",
        "EvolutionAssessmentExpert",
    ]:
        if globals().get(_name) is None:
            globals()[_name] = _RealExpertBase  # type: ignore[assignment]

except ModuleNotFoundError:  # pragma: no cover
    # Provide minimal fallbacks so static checkers stay silent.
    DEFAULT_BASE = type("_DefaultBase", (), {})
    _RealExpertBase = DEFAULT_BASE  # type: ignore[assignment]

    # Ensure every expert symbol refers to *some* class object so that static
    # analysers do not see ``None`` values.
    for _sym in [
        "StatsExpert",
        "PathExpert",
        "EliteExpert",
        "LandscapeExpert",
        "StrategyExpert",
        "ExplorationExpert",
        "ExploitationExpert",
        "EvolutionAssessmentExpert",
    ]:
        if globals().get(_sym) is None:
            globals()[_sym] = _RealExpertBase  # type: ignore[assignment]

    # Create a dummy *moe_main* module-like object to satisfy attribute lookups.
    class _MoeStub:  # noqa: D401
        ExpertCollaborationManager = _RealExpertBase  # type: ignore

    moe_main = _MoeStub()  # type: ignore

# Determine parent manager class (real one if present, else stub)
if hasattr(moe_main, "ExpertCollaborationManager"):
    _BaseManager = moe_main.ExpertCollaborationManager  # type: ignore
else:  # pragma: no cover
    class _BaseManager:  # noqa: D401
        def __init__(self, *_, **__):
            pass

# ---------------------------------------------------------------------------
# Fallback / rule-based experts
# ---------------------------------------------------------------------------

# Use the resolved base expert (real or stub)
BaseExpert = _RealExpertBase

class DummyExpert(BaseExpert):  # type: ignore[misc]
    """A minimal replacement for any expert –*does nothing* but keeps the
    contract intact so that the rest of the pipeline does not crash."""

    def analyze(self, *args, **kwargs):
        # StrategyExpert expects a tuple, others a dict.
        if "populations" in kwargs or (len(args) >= 2 and isinstance(args[1], list)):
            # Heuristic to guess this might be the StrategyExpert.
            populations = kwargs.get("populations", []) or (args[1] if len(args) > 1 else [])
            return ["exploit"] * len(populations), {}
        return {}

    # Path / Stats / Landscape experts may call generate_report – we just echo.
    def generate_report(self, analysis_result, *args, **kwargs):  # noqa: D401
        return analysis_result

    # Exploration / Exploitation experts need *generate_path* – we return None
    def generate_path(self, *args, **kwargs):  # noqa: D401
        return None

    # Assessment expert uses *evaluate*
    def evaluate(self, *args, **kwargs):
        return {}


class RuleBasedLandscapeExpert(DummyExpert):
    """A **very** simple heuristic landscape expert: if population diversity is
    below 0.3 → *exploit*, otherwise → *explore*."""

    def analyze(
        self,
        stats_report: Dict,
        path_report: Dict,
        elite_report: Dict,
        iteration: int = 0,
        total_iterations: int = 10,
        history_data: Dict | None = None,
    ) -> Dict:
        diversity = stats_report.get("diversity_level", 0.5)
        phase = "exploit" if diversity < 0.3 else "explore"
        return {
            "diversity_level": diversity,
            "evolution_phase": phase,
            "search_space_features": {},
            "population_state": {},
            "difficult_regions": path_report.get("low_quality_regions", []),
            "opportunity_regions": path_report.get("high_quality_edges", []),
        }


class RuleBasedStrategyExpert(DummyExpert):
    """Assign *explore* or *exploit* to individuals based on landscape phase."""

    def analyze(
        self,
        landscape_report: Dict,
        populations: List[Dict],
        iteration: int,
        strategy_feedback=None,
    ):
        phase = landscape_report.get("evolution_phase", "explore")
        if phase == "exploit":
            selection = ["exploit"] * len(populations)
        else:
            # Alternate explore/exploit to keep diversity
            selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
        # Minimal response payload
        return selection, {"rule": "simple_phase_based", "phase": phase}


# Mapping helper -------------------------------------------------------------
_EXPERT_KEY_TO_CLASS = {
    "stats": StatsExpert,
    "path": PathExpert,
    "elite": EliteExpert,
    "landscape": LandscapeExpert,
    "strategy": StrategyExpert,
    "exploration": ExplorationExpert,
    "exploitation": ExploitationExpert,
    "assessment": EvolutionAssessmentExpert,
}

_RULE_BASED_MAP = {
    "landscape": RuleBasedLandscapeExpert,
    "strategy": RuleBasedStrategyExpert,
}


# ---------------------------------------------------------------------------
# Ablation-aware manager
# ---------------------------------------------------------------------------

class AblationExpertManager(_BaseManager):  # type: ignore[misc]
    """Drop-in replacement for the original manager that honours *variant* flags.

    Parameters
    ----------
    interface_llm
        Same as original manager.
    variant
        String describing which experts to disable / replace.  Flags are joined
        with ``+``.  Examples: ``"disable_landscape"``, ``"rule_strategy"``.
    """

    #: Regex patterns for variant fragments
    _DISABLE_RE = re.compile(r"^disable_(\w+)$")
    _RULE_RE = re.compile(r"^rule_(\w+)$")

    def __init__(self, interface_llm, *, variant: str = "full") -> None:  # noqa: D401
        self.variant = variant.lower() if variant else "full"
        # Parse variant string into sets
        self._disabled: Set[str] = set()
        self._rule_based: Set[str] = set()
        for part in self.variant.split("+"):
            part = part.strip()
            if m := self._DISABLE_RE.match(part):
                self._disabled.add(m.group(1))
            elif m := self._RULE_RE.match(part):
                self._rule_based.add(m.group(1))
        super().__init__(interface_llm, config={"variant": self.variant})
        logging.getLogger(__name__).info(
            "Ablation manager initialised – disabled=%s, rule_based=%s",
            sorted(self._disabled),
            sorted(self._rule_based),
        )

    # ---------------------------------------------------------------------
    # Overridden hooks
    # ---------------------------------------------------------------------

    def _initialize_experts(self):  # noqa: D401
        """Instantiate experts honouring *disabled* / *rule_based* flags."""
        self.experts = {}
        for key, default_cls in _EXPERT_KEY_TO_CLASS.items():
            if key in self._disabled:
                self.experts[key] = DummyExpert()
            elif key in self._rule_based and key in _RULE_BASED_MAP:
                self.experts[key] = _RULE_BASED_MAP[key]()
            else:
                # Use original implementation (may require interface_llm)
                if key in {"landscape", "strategy", "exploration", "exploitation", "assessment"}:
                    # those have a constructor signature (interface_llm)
                    self.experts[key] = default_cls(self.interface_llm)  # type: ignore[arg-type]
                else:
                    self.experts[key] = default_cls()  # type: ignore[call-arg]

    # (class body ends) 