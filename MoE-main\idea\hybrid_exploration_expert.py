"""
混合探索专家
实现启发式算法与LLM引导相结合的路径生成策略
"""

import numpy as np
import random
import logging
from typing import List, Tuple, Dict, Any
import copy


class HybridExplorationExpert:
    """混合探索专家，结合启发式算法和LLM引导"""
    
    def __init__(self, interface_llm=None, heuristic_ratio=0.7):
        self.logger = logging.getLogger(__name__)
        self.interface_llm = interface_llm
        self.heuristic_ratio = heuristic_ratio  # 启发式方法使用比例
        
        # 启发式生成器列表
        self.heuristic_generators = [
            self.nearest_neighbor_with_perturbation,
            self.random_insertion_with_guidance,
            self.cluster_based_construction,
            self.greedy_with_diversification
        ]
        
        # 路径验证和修复工具
        self.path_validator = PathValidator()
        
    def generate_path(self, individual, landscape_report, populations, distance_matrix, **kwargs):
        """混合路径生成：启发式 + LLM引导"""
        
        # 决定使用启发式还是LLM方法
        if random.random() < self.heuristic_ratio or not self.interface_llm:
            return self.heuristic_exploration(individual, landscape_report, distance_matrix, populations)
        else:
            return self.llm_guided_exploration(individual, landscape_report, populations, distance_matrix)
    
    def heuristic_exploration(self, individual, landscape_report, distance_matrix, populations):
        """启发式探索方法"""
        try:
            # 解析景观报告获取指导信息
            guidance = self._extract_guidance_from_landscape(landscape_report)
            
            # 随机选择一个启发式生成器
            generator = random.choice(self.heuristic_generators)
            
            # 生成新路径
            new_path = generator(individual, distance_matrix, guidance, populations)
            
            # 验证和修复路径
            new_path = self.path_validator.validate_and_repair(new_path, distance_matrix.shape[0])
            
            # 计算路径成本
            new_cost = self._calculate_path_cost(new_path, distance_matrix)
            
            self.logger.info(f"启发式探索生成路径，成本: {new_cost}")
            
            return {
                "new_tour": new_path,
                "cur_cost": new_cost,
                "generation_method": "heuristic",
                "generator_used": generator.__name__
            }
            
        except Exception as e:
            self.logger.error(f"启发式探索失败: {e}")
            return self._fallback_generation(individual, distance_matrix)
    
    def llm_guided_exploration(self, individual, landscape_report, populations, distance_matrix):
        """LLM引导的探索方法"""
        try:
            if not self.interface_llm:
                return self.heuristic_exploration(individual, landscape_report, distance_matrix, populations)
            
            # 生成LLM提示词
            from experts_prompt import generate_exploration_expert_prompt
            prompt = generate_exploration_expert_prompt(
                individual, populations, landscape_report, {}, distance_matrix
            )
            
            # 调用LLM
            response = self.interface_llm.get_response(prompt)
            
            # 解析LLM响应
            llm_result = self._parse_llm_response(response, distance_matrix.shape[0])
            
            if llm_result and self.path_validator.is_valid_tsp_path(llm_result["new_path"]):
                new_cost = self._calculate_path_cost(llm_result["new_path"], distance_matrix)
                return {
                    "new_tour": llm_result["new_path"],
                    "cur_cost": new_cost,
                    "generation_method": "llm_guided",
                    "llm_strategy": llm_result.get("strategy_comment", "")
                }
            else:
                # LLM生成失败，回退到启发式方法
                self.logger.warning("LLM生成路径无效，回退到启发式方法")
                return self.heuristic_exploration(individual, landscape_report, distance_matrix, populations)
                
        except Exception as e:
            self.logger.error(f"LLM引导探索失败: {e}")
            return self.heuristic_exploration(individual, landscape_report, distance_matrix, populations)
    
    def nearest_neighbor_with_perturbation(self, individual, distance_matrix, guidance, populations):
        """最近邻算法加扰动"""
        n_nodes = distance_matrix.shape[0]
        
        # 随机选择起始节点
        start_node = random.randint(0, n_nodes - 1)
        path = [start_node]
        unvisited = set(range(n_nodes)) - {start_node}
        
        current = start_node
        while unvisited:
            # 找到最近的未访问节点
            nearest = min(unvisited, key=lambda x: distance_matrix[current][x])
            
            # 添加扰动：有一定概率选择次近邻
            if random.random() < 0.3 and len(unvisited) > 1:
                unvisited_list = list(unvisited)
                unvisited_list.sort(key=lambda x: distance_matrix[current][x])
                if len(unvisited_list) > 1:
                    nearest = unvisited_list[1]  # 选择次近邻
            
            path.append(nearest)
            unvisited.remove(nearest)
            current = nearest
        
        return np.array(path)
    
    def random_insertion_with_guidance(self, individual, distance_matrix, guidance, populations):
        """随机插入算法加引导"""
        n_nodes = distance_matrix.shape[0]
        
        # 从机会区域开始构建路径
        opportunity_nodes = guidance.get("opportunity_nodes", [])
        if opportunity_nodes:
            start_nodes = random.sample(opportunity_nodes, min(3, len(opportunity_nodes)))
        else:
            start_nodes = random.sample(range(n_nodes), min(3, n_nodes))
        
        path = start_nodes.copy()
        remaining = [i for i in range(n_nodes) if i not in path]
        
        while remaining:
            # 随机选择一个未访问节点
            node_to_insert = random.choice(remaining)
            
            # 找到最佳插入位置
            best_position = 0
            best_cost_increase = float('inf')
            
            for i in range(len(path)):
                # 计算在位置i插入节点的成本增加
                if i == 0:
                    cost_increase = (distance_matrix[node_to_insert][path[0]] + 
                                   distance_matrix[path[-1]][node_to_insert] - 
                                   distance_matrix[path[-1]][path[0]])
                else:
                    cost_increase = (distance_matrix[path[i-1]][node_to_insert] + 
                                   distance_matrix[node_to_insert][path[i]] - 
                                   distance_matrix[path[i-1]][path[i]])
                
                if cost_increase < best_cost_increase:
                    best_cost_increase = cost_increase
                    best_position = i
            
            path.insert(best_position, node_to_insert)
            remaining.remove(node_to_insert)
        
        return np.array(path)
    
    def cluster_based_construction(self, individual, distance_matrix, guidance, populations):
        """基于聚类的路径构建"""
        n_nodes = distance_matrix.shape[0]
        
        # 简单的基于距离的聚类
        clusters = self._simple_clustering(distance_matrix, n_clusters=min(5, n_nodes//3))
        
        # 为每个聚类构建子路径
        cluster_paths = []
        for cluster in clusters:
            if len(cluster) > 1:
                sub_path = self._build_cluster_path(cluster, distance_matrix)
                cluster_paths.append(sub_path)
            else:
                cluster_paths.append(cluster)
        
        # 连接聚类路径
        path = self._connect_cluster_paths(cluster_paths, distance_matrix)
        
        return np.array(path)
    
    def greedy_with_diversification(self, individual, distance_matrix, guidance, populations):
        """贪心算法加多样化"""
        n_nodes = distance_matrix.shape[0]
        
        # 避免与现有种群路径过于相似
        existing_edges = self._extract_common_edges(populations)
        
        path = [random.randint(0, n_nodes - 1)]
        unvisited = set(range(n_nodes)) - set(path)
        
        while unvisited:
            current = path[-1]
            
            # 计算到所有未访问节点的成本，考虑多样化
            candidates = []
            for node in unvisited:
                edge = (min(current, node), max(current, node))
                base_cost = distance_matrix[current][node]
                
                # 如果边在现有种群中很常见，增加惩罚
                penalty = existing_edges.get(edge, 0) * 0.1
                adjusted_cost = base_cost + penalty
                
                candidates.append((node, adjusted_cost))
            
            # 选择调整后成本最小的节点
            next_node = min(candidates, key=lambda x: x[1])[0]
            
            path.append(next_node)
            unvisited.remove(next_node)
        
        return np.array(path)
    
    def _extract_guidance_from_landscape(self, landscape_report):
        """从景观报告中提取指导信息"""
        guidance = {
            "opportunity_nodes": [],
            "avoid_nodes": [],
            "preferred_strategy": "explore"
        }
        
        try:
            if isinstance(landscape_report, dict):
                target_regions = landscape_report.get("target_regions", {})
                guidance["opportunity_nodes"] = target_regions.get("opportunities", [])
                guidance["avoid_nodes"] = target_regions.get("avoid", [])
                
                recommended_action = landscape_report.get("recommended_action", {})
                guidance["preferred_strategy"] = recommended_action.get("focus", "explore")
        except Exception as e:
            self.logger.warning(f"解析景观报告失败: {e}")
        
        return guidance
    
    def _calculate_path_cost(self, path, distance_matrix):
        """计算路径总成本"""
        if len(path) < 2:
            return float('inf')
        
        total_cost = 0
        for i in range(len(path)):
            next_i = (i + 1) % len(path)
            total_cost += distance_matrix[path[i]][path[next_i]]
        
        return total_cost
    
    def _parse_llm_response(self, response, n_nodes):
        """解析LLM响应"""
        try:
            import re
            import json
            
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                data = json.loads(json_match.group(1))
                new_path = data.get("new_path", [])
                
                # 验证路径
                if (isinstance(new_path, list) and 
                    len(new_path) == n_nodes and 
                    set(new_path) == set(range(n_nodes))):
                    return data
            
            return None
        except Exception:
            return None
    
    def _simple_clustering(self, distance_matrix, n_clusters):
        """简单的基于距离的聚类"""
        n_nodes = distance_matrix.shape[0]
        
        # 随机选择聚类中心
        centers = random.sample(range(n_nodes), min(n_clusters, n_nodes))
        clusters = [[] for _ in centers]
        
        # 将每个节点分配到最近的聚类中心
        for node in range(n_nodes):
            if node in centers:
                cluster_idx = centers.index(node)
            else:
                distances = [distance_matrix[node][center] for center in centers]
                cluster_idx = distances.index(min(distances))
            clusters[cluster_idx].append(node)
        
        return [cluster for cluster in clusters if cluster]
    
    def _build_cluster_path(self, cluster, distance_matrix):
        """为聚类构建路径"""
        if len(cluster) <= 2:
            return cluster
        
        # 使用最近邻算法构建聚类内路径
        start = cluster[0]
        path = [start]
        remaining = set(cluster[1:])
        
        current = start
        while remaining:
            nearest = min(remaining, key=lambda x: distance_matrix[current][x])
            path.append(nearest)
            remaining.remove(nearest)
            current = nearest
        
        return path
    
    def _connect_cluster_paths(self, cluster_paths, distance_matrix):
        """连接聚类路径"""
        if not cluster_paths:
            return []
        
        if len(cluster_paths) == 1:
            return cluster_paths[0]
        
        # 简单连接：找到聚类间最短距离
        connected_path = cluster_paths[0].copy()
        remaining_clusters = cluster_paths[1:]
        
        while remaining_clusters:
            last_node = connected_path[-1]
            
            # 找到距离最近的聚类
            best_cluster_idx = 0
            best_distance = float('inf')
            best_start_node = None
            
            for i, cluster in enumerate(remaining_clusters):
                for node in cluster:
                    dist = distance_matrix[last_node][node]
                    if dist < best_distance:
                        best_distance = dist
                        best_cluster_idx = i
                        best_start_node = node
            
            # 连接最近的聚类
            next_cluster = remaining_clusters.pop(best_cluster_idx)
            start_idx = next_cluster.index(best_start_node)
            
            # 重新排列聚类路径，使其从最近节点开始
            reordered_cluster = next_cluster[start_idx:] + next_cluster[:start_idx]
            connected_path.extend(reordered_cluster)
        
        return connected_path
    
    def _extract_common_edges(self, populations):
        """提取种群中的常见边"""
        edge_count = {}
        
        for individual in populations:
            tour = individual.get("tour", [])
            if len(tour) > 1:
                for i in range(len(tour)):
                    next_i = (i + 1) % len(tour)
                    edge = (min(tour[i], tour[next_i]), max(tour[i], tour[next_i]))
                    edge_count[edge] = edge_count.get(edge, 0) + 1
        
        return edge_count
    
    def _fallback_generation(self, individual, distance_matrix):
        """回退生成方法"""
        n_nodes = distance_matrix.shape[0]
        path = list(range(n_nodes))
        random.shuffle(path)
        cost = self._calculate_path_cost(path, distance_matrix)
        
        return {
            "new_tour": np.array(path),
            "cur_cost": cost,
            "generation_method": "fallback"
        }


class PathValidator:
    """路径验证和修复工具"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def is_valid_tsp_path(self, path):
        """检查路径是否为有效的TSP路径"""
        if not isinstance(path, (list, np.ndarray)):
            return False
        
        if len(path) == 0:
            return False
        
        # 检查是否包含所有节点且无重复
        unique_nodes = set(path)
        expected_nodes = set(range(len(path)))
        
        return unique_nodes == expected_nodes
    
    def validate_and_repair(self, path, n_nodes):
        """验证并修复路径"""
        if self.is_valid_tsp_path(path) and len(path) == n_nodes:
            return path
        
        # 修复路径
        return self.repair_path(path, n_nodes)
    
    def repair_path(self, path, n_nodes):
        """修复无效路径"""
        try:
            # 确保路径包含所有节点
            path_set = set(path) if path else set()
            missing_nodes = set(range(n_nodes)) - path_set
            duplicate_nodes = []
            
            # 找出重复节点
            seen = set()
            for node in path:
                if node in seen:
                    duplicate_nodes.append(node)
                else:
                    seen.add(node)
            
            # 替换重复节点
            repaired_path = list(path)
            for i, node in enumerate(repaired_path):
                if node in duplicate_nodes and missing_nodes:
                    replacement = missing_nodes.pop()
                    repaired_path[i] = replacement
                    duplicate_nodes.remove(node)
            
            # 添加缺失节点
            repaired_path.extend(list(missing_nodes))
            
            # 确保长度正确
            if len(repaired_path) > n_nodes:
                repaired_path = repaired_path[:n_nodes]
            elif len(repaired_path) < n_nodes:
                remaining = [i for i in range(n_nodes) if i not in repaired_path]
                repaired_path.extend(remaining)
            
            return np.array(repaired_path)
            
        except Exception as e:
            self.logger.error(f"路径修复失败: {e}")
            # 返回简单的顺序路径
            return np.array(list(range(n_nodes)))
