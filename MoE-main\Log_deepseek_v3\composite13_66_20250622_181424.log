2025-06-22 18:14:24,467 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 18:14:24,467 - __main__ - INFO - 开始分析阶段
2025-06-22 18:14:24,468 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:14:24,485 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9903.0, 'max': 114553.0, 'mean': 77540.4, 'std': 44428.72865883065}, 'diversity': 0.9296296296296295, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:14:24,486 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9903.0, 'max': 114553.0, 'mean': 77540.4, 'std': 44428.72865883065}, 'diversity_level': 0.9296296296296295, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:14:24,487 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:14:24,487 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:14:24,488 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:14:24,493 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:14:24,494 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(28, 30)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(28, 32)', 'frequency': 0.2}, {'edge': '(30, 35)', 'frequency': 0.2}, {'edge': '(34, 35)', 'frequency': 0.2}, {'edge': '(26, 34)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(25, 31)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(29, 33)', 'frequency': 0.2}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(24, 37)', 'frequency': 0.2}, {'edge': '(36, 37)', 'frequency': 0.3}, {'edge': '(27, 36)', 'frequency': 0.2}, {'edge': '(18, 27)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(14, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 21)', 'frequency': 0.3}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(9, 19)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(8, 10)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(32, 42)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.3}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(48, 63)', 'frequency': 0.2}, {'edge': '(17, 56)', 'frequency': 0.2}, {'edge': '(17, 21)', 'frequency': 0.2}, {'edge': '(24, 27)', 'frequency': 0.2}, {'edge': '(8, 12)', 'frequency': 0.2}, {'edge': '(45, 63)', 'frequency': 0.3}, {'edge': '(5, 35)', 'frequency': 0.2}, {'edge': '(20, 57)', 'frequency': 0.2}, {'edge': '(11, 14)', 'frequency': 0.2}, {'edge': '(2, 26)', 'frequency': 0.2}, {'edge': '(29, 30)', 'frequency': 0.2}, {'edge': '(7, 37)', 'frequency': 0.3}, {'edge': '(11, 36)', 'frequency': 0.2}, {'edge': '(56, 60)', 'frequency': 0.2}, {'edge': '(12, 60)', 'frequency': 0.2}, {'edge': '(48, 57)', 'frequency': 0.2}, {'edge': '(46, 55)', 'frequency': 0.2}, {'edge': '(15, 34)', 'frequency': 0.2}, {'edge': '(16, 28)', 'frequency': 0.2}, {'edge': '(16, 58)', 'frequency': 0.2}, {'edge': '(52, 58)', 'frequency': 0.2}, {'edge': '(30, 49)', 'frequency': 0.2}, {'edge': '(21, 60)', 'frequency': 0.2}, {'edge': '(51, 54)', 'frequency': 0.2}, {'edge': '(11, 63)', 'frequency': 0.2}, {'edge': '(24, 56)', 'frequency': 0.2}, {'edge': '(0, 7)', 'frequency': 0.2}, {'edge': '(0, 65)', 'frequency': 0.2}, {'edge': '(44, 47)', 'frequency': 0.2}, {'edge': '(22, 54)', 'frequency': 0.2}, {'edge': '(48, 50)', 'frequency': 0.2}, {'edge': '(9, 31)', 'frequency': 0.2}, {'edge': '(14, 23)', 'frequency': 0.2}, {'edge': '(25, 43)', 'frequency': 0.2}, {'edge': '(33, 40)', 'frequency': 0.2}, {'edge': '(44, 59)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [27, 55, 38, 6, 50, 60], 'cost': 13467.0, 'size': 6}, {'region': [35, 60, 32, 38, 65], 'cost': 10988.0, 'size': 5}, {'region': [4, 44, 63, 45, 35], 'cost': 10327.0, 'size': 5}, {'region': [41, 9, 39, 37, 62], 'cost': 10002.0, 'size': 5}, {'region': [35, 39, 33, 46, 62], 'cost': 9162.0, 'size': 5}]}
2025-06-22 18:14:24,495 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:14:24,495 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:14:24,495 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:14:24,496 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 18:14:24,496 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 18:14:24,497 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:14:24,497 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:14:24,497 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:14:24,497 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9903.0, max=114553.0, mean=77540.4, std=44428.72865883065
- Diversity: 0.9296296296296295, Convergence: 0.0
- Structure: 0 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 18:14:30,499 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Potentially high. The significant cost range (min to max) and large standard deviation (std) of the costs suggest a non-uniform fitness landscape. The lack of elite solutions (0) and high diversity (0.93) further hints at a potentially complex and rugged landscape where solutions are scattered and hard to converge towards an optimal solution.",
    "modality": "Potentially multimodal. Although not explicitly stated, the presence of 5 difficult regions and the wide range of costs might indicate multiple local optima. The lack of convergence also supports the possibility of multiple areas with high quality solutions but the algorithm is unable to pinpoint on them."
  },
  "population_state": {
    "diversity": "High. A diversity of 0.93 suggests a widely distributed population across the search space, indicating that the solutions are dissimilar to each other.",
    "convergence": "Zero. The convergence of 0 indicates that the population has not focused on any particular area or optimal solutions. There are no solutions with shared edges or fixed nodes and no elite solutions."
  },
  "difficult_regions": {
    "challenges": "The 5 identified difficult regions highlight areas of the search space where the optimization process struggles. These regions likely present complex interactions or constraints that impede finding superior solutions. The fact no solutions are in an 'elite' state implies these difficult regions are a major roadblock, perhaps containing local optima or areas with particularly complex fitness dependencies. The high cost range suggests these difficult regions may have widely varying costs associated with them, which make comparisons and improvements difficult.",
    "impact": "These regions are likely preventing convergence and the formation of elite solutions. They are consuming a high amount of resources and preventing the population from identifying superior solutions."
  },
  "opportunity_regions": {
    "potential": "No opportunity regions were identified, meaning this information is not available from the provided data. The absence indicates no obvious areas in the search space stand out as being particularly promising based on the given information.",
    "impact": "The inability to identify these regions means that it's difficult to predict the exact direction or area in which the algorithm should improve. "
  },
  "evolution_direction": {
    "strategy": "Explore and Exploit with a Bias toward Exploration. Given high diversity, low convergence, and the identification of difficult regions, the current focus should be on exploring the search space to overcome those hurdles. Specifically:",
    "recommendations": [
      "Increase Exploration: Consider using mutation operators with larger step sizes or increasing the mutation rate to help the population jump across difficult regions and explore broader areas of the search space.",
      "Refine Evaluation (if possible): Examine the evaluation function for potential issues that might be making the search more difficult. Check if there are any inconsistencies or noisy information in the fitness values which could be contributing to the challenge in the difficult regions.",
      "Diversify Population: Further diversification might be helpful. Use a selection scheme biased to less fitted individuals to avoid premature convergence.",
      "Monitor Convergence: The population should be kept moving, so the convergence and diversity metrics should be continuously monitored to determine the direction of the movement of the population. Monitor the state of the difficult regions as exploration proceeds.",
      "Consider Problem Decomposition: If possible, consider decomposing the problem into smaller subproblems to simplify the search. This can help break down the complex interactions within the difficult regions.",
	  "Check for edge quality to see if any can be included in the current best candidates."
    ]
  }
}
```
2025-06-22 18:14:30,501 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:14:30,501 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Potentially high. The significant cost range (min to max) and large standard deviation (std) of the costs suggest a non-uniform fitness landscape. The lack of elite solutions (0) and high diversity (0.93) further hints at a potentially complex and rugged landscape where solutions are scattered and hard to converge towards an optimal solution.', 'modality': 'Potentially multimodal. Although not explicitly stated, the presence of 5 difficult regions and the wide range of costs might indicate multiple local optima. The lack of convergence also supports the possibility of multiple areas with high quality solutions but the algorithm is unable to pinpoint on them.'}, 'population_state': {'diversity': 'High. A diversity of 0.93 suggests a widely distributed population across the search space, indicating that the solutions are dissimilar to each other.', 'convergence': 'Zero. The convergence of 0 indicates that the population has not focused on any particular area or optimal solutions. There are no solutions with shared edges or fixed nodes and no elite solutions.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions highlight areas of the search space where the optimization process struggles. These regions likely present complex interactions or constraints that impede finding superior solutions. The fact no solutions are in an 'elite' state implies these difficult regions are a major roadblock, perhaps containing local optima or areas with particularly complex fitness dependencies. The high cost range suggests these difficult regions may have widely varying costs associated with them, which make comparisons and improvements difficult.", 'impact': 'These regions are likely preventing convergence and the formation of elite solutions. They are consuming a high amount of resources and preventing the population from identifying superior solutions.'}, 'opportunity_regions': {'potential': 'No opportunity regions were identified, meaning this information is not available from the provided data. The absence indicates no obvious areas in the search space stand out as being particularly promising based on the given information.', 'impact': "The inability to identify these regions means that it's difficult to predict the exact direction or area in which the algorithm should improve. "}, 'evolution_direction': {'strategy': 'Explore and Exploit with a Bias toward Exploration. Given high diversity, low convergence, and the identification of difficult regions, the current focus should be on exploring the search space to overcome those hurdles. Specifically:', 'recommendations': ['Increase Exploration: Consider using mutation operators with larger step sizes or increasing the mutation rate to help the population jump across difficult regions and explore broader areas of the search space.', 'Refine Evaluation (if possible): Examine the evaluation function for potential issues that might be making the search more difficult. Check if there are any inconsistencies or noisy information in the fitness values which could be contributing to the challenge in the difficult regions.', 'Diversify Population: Further diversification might be helpful. Use a selection scheme biased to less fitted individuals to avoid premature convergence.', 'Monitor Convergence: The population should be kept moving, so the convergence and diversity metrics should be continuously monitored to determine the direction of the movement of the population. Monitor the state of the difficult regions as exploration proceeds.', 'Consider Problem Decomposition: If possible, consider decomposing the problem into smaller subproblems to simplify the search. This can help break down the complex interactions within the difficult regions.', 'Check for edge quality to see if any can be included in the current best candidates.']}}
2025-06-22 18:14:30,501 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:14:30,502 - __main__ - INFO - 分析阶段完成
2025-06-22 18:14:30,502 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Potentially high. The significant cost range (min to max) and large standard deviation (std) of the costs suggest a non-uniform fitness landscape. The lack of elite solutions (0) and high diversity (0.93) further hints at a potentially complex and rugged landscape where solutions are scattered and hard to converge towards an optimal solution.', 'modality': 'Potentially multimodal. Although not explicitly stated, the presence of 5 difficult regions and the wide range of costs might indicate multiple local optima. The lack of convergence also supports the possibility of multiple areas with high quality solutions but the algorithm is unable to pinpoint on them.'}, 'population_state': {'diversity': 'High. A diversity of 0.93 suggests a widely distributed population across the search space, indicating that the solutions are dissimilar to each other.', 'convergence': 'Zero. The convergence of 0 indicates that the population has not focused on any particular area or optimal solutions. There are no solutions with shared edges or fixed nodes and no elite solutions.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions highlight areas of the search space where the optimization process struggles. These regions likely present complex interactions or constraints that impede finding superior solutions. The fact no solutions are in an 'elite' state implies these difficult regions are a major roadblock, perhaps containing local optima or areas with particularly complex fitness dependencies. The high cost range suggests these difficult regions may have widely varying costs associated with them, which make comparisons and improvements difficult.", 'impact': 'These regions are likely preventing convergence and the formation of elite solutions. They are consuming a high amount of resources and preventing the population from identifying superior solutions.'}, 'opportunity_regions': {'potential': 'No opportunity regions were identified, meaning this information is not available from the provided data. The absence indicates no obvious areas in the search space stand out as being particularly promising based on the given information.', 'impact': "The inability to identify these regions means that it's difficult to predict the exact direction or area in which the algorithm should improve. "}, 'evolution_direction': {'strategy': 'Explore and Exploit with a Bias toward Exploration. Given high diversity, low convergence, and the identification of difficult regions, the current focus should be on exploring the search space to overcome those hurdles. Specifically:', 'recommendations': ['Increase Exploration: Consider using mutation operators with larger step sizes or increasing the mutation rate to help the population jump across difficult regions and explore broader areas of the search space.', 'Refine Evaluation (if possible): Examine the evaluation function for potential issues that might be making the search more difficult. Check if there are any inconsistencies or noisy information in the fitness values which could be contributing to the challenge in the difficult regions.', 'Diversify Population: Further diversification might be helpful. Use a selection scheme biased to less fitted individuals to avoid premature convergence.', 'Monitor Convergence: The population should be kept moving, so the convergence and diversity metrics should be continuously monitored to determine the direction of the movement of the population. Monitor the state of the difficult regions as exploration proceeds.', 'Consider Problem Decomposition: If possible, consider decomposing the problem into smaller subproblems to simplify the search. This can help break down the complex interactions within the difficult regions.', 'Check for edge quality to see if any can be included in the current best candidates.']}}
2025-06-22 18:14:30,502 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:14:30,502 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:14:30,502 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High. A diversity of 0.93 suggests a widely distributed population across the search space, indicating that the solutions are dissimilar to each other.
- Best cost: 9903.0, Mean cost: 77540.4
- Search space: Ruggedness: Potentially high. The significant cost range (min to max) and large standard deviation (std) of the costs suggest a non-uniform fitness landscape. The lack of elite solutions (0) and high diversity (0.93) further hints at a potentially complex and rugged landscape where solutions are scattered and hard to converge towards an optimal solution., Modality: Potentially multimodal. Although not explicitly stated, the presence of 5 difficult regions and the wide range of costs might indicate multiple local optima. The lack of convergence also supports the possibility of multiple areas with high quality solutions but the algorithm is unable to pinpoint on them.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:14:30,502 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:14:32,980 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.1,
      "local_search_iterations": 2
    },
    "exploit": {
      "crossover_rate": 0.8,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 18:14:32,980 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:14:32,980 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:14:32,980 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.1,
      "local_search_iterations": 2
    },
    "exploit": {
      "crossover_rate": 0.8,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 18:14:32,980 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:14:32,980 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 18:14:32,980 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.1,
      "local_search_iterations": 2
    },
    "exploit": {
      "crossover_rate": 0.8,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 18:14:32,980 - __main__ - INFO - 开始进化阶段
2025-06-22 18:14:32,980 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:14:32,980 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:14:32,980 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 28, 30] and ending with [43, 48, 42]
- Cost: 9903.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7646153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:14:32,980 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:14:36,190 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50,
    51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4,
    5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
    25, 26, 27, 28, 29, 30
  ],
  "modification_strategy": "Reversed the original path's beginning and end sections to increase diversity. Then I split the remaining nodes into roughly two halves to create a new path with increased distance from the original path.",
  "targeted_regions": "All regions, as the approach involves broad adjustments to the path and emphasizes node reordering to explore different areas effectively. The reordering allows the path to traverse a much broader distribution of nodes initially which is a good approach for diversity."
}
```
2025-06-22 18:14:36,190 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:14:36,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11468.0, 路径: [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
2025-06-22 18:14:36,190 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}
2025-06-22 18:14:36,190 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:14:36,190 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:14:36,190 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [48, 49, 40] and ending with [65, 52, 63]
- Cost: 9971.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:14:36,190 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:14:39,352 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This path focuses on a more linear progression to maximize coverage and explore unexplored regions as indicated in the search context. It attempts to create a more diverse path by changing node order and incorporating elements that were not present in the initial path.",
  "targeted_regions": "Prioritizing a contiguous exploration of nodes from 0 to 65, with an initial start different from the original path's start. This strategy aims to broadly cover the entire search space, specifically including the unexplored opportunity regions by creating a complete permutation of all nodes."
}
```
2025-06-22 18:14:39,354 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:14:39,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15776.0, 路径: [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:14:39,354 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}
2025-06-22 18:14:39,355 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:14:39,355 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:14:39,355 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 28, 30] and ending with [43, 48, 42]
- Cost: 9903.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8046153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:14:39,355 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:14:43,059 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61
  ],
  "modification_strategy": "This path prioritizes diversity by introducing a substantial number of swaps and reordering segments. It begins by exploring nodes near the original start, but then diversifies significantly, jumping around the graph to explore different regions. The placement of potentially difficult regions is strategically distributed throughout the path to mitigate risk.",
  "targeted_regions": "This path targets the two unexplored opportunity regions and aims to avoid the difficult regions by distributing their nodes throughout the path, rather than concentrating them together. The path also spreads out nodes that are far apart in the original path to further improve diversity."
}
```
2025-06-22 18:14:43,059 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:14:43,059 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86288.0, 路径: [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61]
2025-06-22 18:14:43,059 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}
2025-06-22 18:14:43,059 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:14:43,059 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:14:43,059 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 2, 42] and ending with [28, 62, 48]
- Cost: 109046.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8153846153846154
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:14:43,059 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:14:47,088 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This strategy focuses on swapping the initial segment of the existing path with a modified segment that includes more exploration of potentially beneficial regions. We aim for high diversity by altering the beginning of the path significantly, exploring previously unvisited areas while respecting the TSP constraints.",
  "targeted_regions": "We target the beginning of the path to explore the opportunity regions and avoid the difficult regions."
}
```
2025-06-22 18:14:47,088 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:14:47,088 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25567.0, 路径: [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:14:47,089 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}
2025-06-22 18:14:47,089 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:14:47,089 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:14:47,089 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 13, 9] and ending with [15, 34, 51]
- Cost: 98645.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7430769230769231
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:14:47,089 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:14:50,334 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11
  ],
  "modification_strategy": "I prioritized exploring unexplored regions by creating a path that zig-zags across the nodes, focusing on groups of adjacent nodes to ensure diversity and a broader exploration footprint. I aimed to move away from the starting node, then reversed direction, and repeat, which should result in a path substantially different from the current one.",
  "targeted_regions": "I aimed to visit a selection of regions. Nodes with higher indices were targeted to try and reach those unexplored regions and difficult regions identified in the search context."
}
```
2025-06-22 18:14:50,334 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:14:50,334 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65694.0, 路径: [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43]
2025-06-22 18:14:50,334 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}
2025-06-22 18:14:50,334 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:14:50,334 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:14:50,334 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:14:50,341 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 119182.0
2025-06-22 18:14:53,145 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 18:14:53,145 - ExploitationExpert - INFO - res_population_costs: [93875]
2025-06-22 18:14:53,145 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64)]
2025-06-22 18:14:53,153 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:14:53,153 - ExploitationExpert - INFO - populations: [{'tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}, {'tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}, {'tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}, {'tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}, {'tour': array([23, 25, 57, 27, 63, 52, 29, 34, 12, 51,  7,  0,  9, 65, 16, 14, 46,
       11, 53, 45, 15, 39, 18, 61, 38, 32, 49,  2, 36, 30, 44, 62,  3, 21,
       56, 20, 17, 35, 59,  4,  6,  1, 33, 64, 48, 28, 10, 13,  5, 50, 22,
       54, 24, 58, 31, 43, 55, 42, 19, 41,  8, 26, 47, 60, 37, 40]), 'cur_cost': 119182.0}, {'tour': [26, 57, 1, 4, 33, 62, 16, 58, 46, 55, 51, 21, 39, 23, 9, 5, 19, 35, 29, 59, 25, 32, 31, 50, 64, 10, 8, 40, 6, 37, 43, 15, 48, 14, 41, 17, 34, 53, 38, 20, 45, 63, 11, 27, 52, 28, 30, 49, 36, 12, 60, 56, 24, 7, 0, 65, 18, 42, 47, 44, 3, 61, 22, 54, 13, 2], 'cur_cost': 108290.0}, {'tour': [56, 45, 65, 24, 30, 29, 38, 1, 18, 55, 35, 3, 12, 48, 50, 39, 51, 22, 8, 17, 21, 60, 57, 6, 58, 36, 4, 34, 15, 28, 16, 46, 0, 27, 41, 59, 9, 31, 47, 44, 26, 49, 23, 14, 52, 13, 42, 62, 7, 37, 2, 53, 19, 61, 43, 25, 64, 5, 20, 63, 11, 32, 33, 40, 10, 54], 'cur_cost': 103595.0}, {'tour': [20, 47, 6, 12, 8, 49, 25, 43, 23, 54, 51, 3, 5, 35, 60, 32, 38, 65, 0, 7, 55, 2, 30, 10, 29, 52, 64, 11, 14, 46, 28, 40, 33, 42, 27, 1, 63, 17, 19, 41, 9, 39, 37, 62, 56, 24, 36, 26, 34, 18, 15, 21, 53, 44, 59, 61, 16, 13, 58, 50, 48, 45, 4, 31, 22, 57], 'cur_cost': 105961.0}, {'tour': [4, 5, 43, 13, 45, 63, 48, 20, 34, 9, 31, 14, 23, 21, 28, 10, 25, 40, 32, 56, 17, 51, 2, 27, 55, 38, 6, 50, 60, 16, 37, 7, 64, 8, 30, 58, 52, 53, 65, 47, 1, 24, 61, 42, 54, 22, 18, 3, 15, 26, 35, 39, 33, 46, 62, 12, 29, 41, 0, 49, 44, 59, 57, 11, 36, 19], 'cur_cost': 114553.0}]
2025-06-22 18:14:53,153 - ExploitationExpert - INFO - 局部搜索耗时: 2.82秒
2025-06-22 18:14:53,153 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 18:14:53,153 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:14:53,153 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:14:53,156 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:14:53,156 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:14:53,156 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 112720.0
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - res_population_costs: [93875, 9572]
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64)]
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - populations: [{'tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}, {'tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}, {'tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}, {'tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}, {'tour': array([23, 25, 57, 27, 63, 52, 29, 34, 12, 51,  7,  0,  9, 65, 16, 14, 46,
       11, 53, 45, 15, 39, 18, 61, 38, 32, 49,  2, 36, 30, 44, 62,  3, 21,
       56, 20, 17, 35, 59,  4,  6,  1, 33, 64, 48, 28, 10, 13,  5, 50, 22,
       54, 24, 58, 31, 43, 55, 42, 19, 41,  8, 26, 47, 60, 37, 40]), 'cur_cost': 119182.0}, {'tour': array([55, 15, 16, 45, 57, 32, 17, 14, 42, 65, 63, 39, 58, 28, 51, 52, 62,
       25, 59, 24, 64, 53, 27, 11, 21, 33,  1, 46, 30, 41,  7, 40, 43,  4,
       54, 31, 19, 13, 61, 60, 18, 12, 22,  0,  9,  3, 37, 44,  5, 35, 49,
       36, 50, 10, 29, 56,  6, 47, 26, 48, 23, 34, 38, 20,  2,  8]), 'cur_cost': 112720.0}, {'tour': [56, 45, 65, 24, 30, 29, 38, 1, 18, 55, 35, 3, 12, 48, 50, 39, 51, 22, 8, 17, 21, 60, 57, 6, 58, 36, 4, 34, 15, 28, 16, 46, 0, 27, 41, 59, 9, 31, 47, 44, 26, 49, 23, 14, 52, 13, 42, 62, 7, 37, 2, 53, 19, 61, 43, 25, 64, 5, 20, 63, 11, 32, 33, 40, 10, 54], 'cur_cost': 103595.0}, {'tour': [20, 47, 6, 12, 8, 49, 25, 43, 23, 54, 51, 3, 5, 35, 60, 32, 38, 65, 0, 7, 55, 2, 30, 10, 29, 52, 64, 11, 14, 46, 28, 40, 33, 42, 27, 1, 63, 17, 19, 41, 9, 39, 37, 62, 56, 24, 36, 26, 34, 18, 15, 21, 53, 44, 59, 61, 16, 13, 58, 50, 48, 45, 4, 31, 22, 57], 'cur_cost': 105961.0}, {'tour': [4, 5, 43, 13, 45, 63, 48, 20, 34, 9, 31, 14, 23, 21, 28, 10, 25, 40, 32, 56, 17, 51, 2, 27, 55, 38, 6, 50, 60, 16, 37, 7, 64, 8, 30, 58, 52, 53, 65, 47, 1, 24, 61, 42, 54, 22, 18, 3, 15, 26, 35, 39, 33, 46, 62, 12, 29, 41, 0, 49, 44, 59, 57, 11, 36, 19], 'cur_cost': 114553.0}]
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - 局部搜索耗时: 1.04秒
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 18:14:54,192 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:14:54,192 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:14:54,192 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105584.0
2025-06-22 18:14:54,700 - ExploitationExpert - INFO - res_population_num: 5
2025-06-22 18:14:54,700 - ExploitationExpert - INFO - res_population_costs: [93875, 9572, 9524, 9522, 9522]
2025-06-22 18:14:54,700 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:14:54,700 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:14:54,700 - ExploitationExpert - INFO - populations: [{'tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}, {'tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}, {'tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}, {'tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}, {'tour': array([23, 25, 57, 27, 63, 52, 29, 34, 12, 51,  7,  0,  9, 65, 16, 14, 46,
       11, 53, 45, 15, 39, 18, 61, 38, 32, 49,  2, 36, 30, 44, 62,  3, 21,
       56, 20, 17, 35, 59,  4,  6,  1, 33, 64, 48, 28, 10, 13,  5, 50, 22,
       54, 24, 58, 31, 43, 55, 42, 19, 41,  8, 26, 47, 60, 37, 40]), 'cur_cost': 119182.0}, {'tour': array([55, 15, 16, 45, 57, 32, 17, 14, 42, 65, 63, 39, 58, 28, 51, 52, 62,
       25, 59, 24, 64, 53, 27, 11, 21, 33,  1, 46, 30, 41,  7, 40, 43,  4,
       54, 31, 19, 13, 61, 60, 18, 12, 22,  0,  9,  3, 37, 44,  5, 35, 49,
       36, 50, 10, 29, 56,  6, 47, 26, 48, 23, 34, 38, 20,  2,  8]), 'cur_cost': 112720.0}, {'tour': array([23, 59, 25, 36, 38, 24,  3, 52, 55, 17, 18, 16, 14, 41,  6, 49, 61,
       11, 28,  0, 34, 20,  4, 12, 64, 57, 43, 42, 44, 35, 51, 10,  1, 39,
       26,  5, 54, 46, 32, 62, 30, 15, 63,  9, 60, 45, 40, 65, 53, 50,  8,
       13, 21, 22, 31, 33, 48,  7, 19, 29, 47, 58, 27,  2, 56, 37]), 'cur_cost': 105584.0}, {'tour': [20, 47, 6, 12, 8, 49, 25, 43, 23, 54, 51, 3, 5, 35, 60, 32, 38, 65, 0, 7, 55, 2, 30, 10, 29, 52, 64, 11, 14, 46, 28, 40, 33, 42, 27, 1, 63, 17, 19, 41, 9, 39, 37, 62, 56, 24, 36, 26, 34, 18, 15, 21, 53, 44, 59, 61, 16, 13, 58, 50, 48, 45, 4, 31, 22, 57], 'cur_cost': 105961.0}, {'tour': [4, 5, 43, 13, 45, 63, 48, 20, 34, 9, 31, 14, 23, 21, 28, 10, 25, 40, 32, 56, 17, 51, 2, 27, 55, 38, 6, 50, 60, 16, 37, 7, 64, 8, 30, 58, 52, 53, 65, 47, 1, 24, 61, 42, 54, 22, 18, 3, 15, 26, 35, 39, 33, 46, 62, 12, 29, 41, 0, 49, 44, 59, 57, 11, 36, 19], 'cur_cost': 114553.0}]
2025-06-22 18:14:54,704 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:14:54,704 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 18:14:54,705 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:14:54,706 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:14:54,706 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:14:54,706 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:14:54,706 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 103348.0
2025-06-22 18:14:55,208 - ExploitationExpert - INFO - res_population_num: 7
2025-06-22 18:14:55,209 - ExploitationExpert - INFO - res_population_costs: [93875, 9572, 9524, 9522, 9522, 9521, 9521]
2025-06-22 18:14:55,209 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:14:55,211 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:14:55,211 - ExploitationExpert - INFO - populations: [{'tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}, {'tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}, {'tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}, {'tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}, {'tour': array([23, 25, 57, 27, 63, 52, 29, 34, 12, 51,  7,  0,  9, 65, 16, 14, 46,
       11, 53, 45, 15, 39, 18, 61, 38, 32, 49,  2, 36, 30, 44, 62,  3, 21,
       56, 20, 17, 35, 59,  4,  6,  1, 33, 64, 48, 28, 10, 13,  5, 50, 22,
       54, 24, 58, 31, 43, 55, 42, 19, 41,  8, 26, 47, 60, 37, 40]), 'cur_cost': 119182.0}, {'tour': array([55, 15, 16, 45, 57, 32, 17, 14, 42, 65, 63, 39, 58, 28, 51, 52, 62,
       25, 59, 24, 64, 53, 27, 11, 21, 33,  1, 46, 30, 41,  7, 40, 43,  4,
       54, 31, 19, 13, 61, 60, 18, 12, 22,  0,  9,  3, 37, 44,  5, 35, 49,
       36, 50, 10, 29, 56,  6, 47, 26, 48, 23, 34, 38, 20,  2,  8]), 'cur_cost': 112720.0}, {'tour': array([23, 59, 25, 36, 38, 24,  3, 52, 55, 17, 18, 16, 14, 41,  6, 49, 61,
       11, 28,  0, 34, 20,  4, 12, 64, 57, 43, 42, 44, 35, 51, 10,  1, 39,
       26,  5, 54, 46, 32, 62, 30, 15, 63,  9, 60, 45, 40, 65, 53, 50,  8,
       13, 21, 22, 31, 33, 48,  7, 19, 29, 47, 58, 27,  2, 56, 37]), 'cur_cost': 105584.0}, {'tour': array([12, 18, 63,  4, 11,  9, 41, 56, 48, 60, 19, 13, 25, 61, 58, 21, 39,
       64, 45, 28, 10, 20,  3, 46, 32,  1, 40, 16, 34, 59, 65,  2, 57, 29,
       24, 53,  5, 51, 52, 23, 49, 42, 47, 37,  6, 27, 15, 17, 22, 38,  0,
        7, 55, 33, 35, 30, 31, 43, 26, 14, 50, 62, 44, 54,  8, 36]), 'cur_cost': 103348.0}, {'tour': [4, 5, 43, 13, 45, 63, 48, 20, 34, 9, 31, 14, 23, 21, 28, 10, 25, 40, 32, 56, 17, 51, 2, 27, 55, 38, 6, 50, 60, 16, 37, 7, 64, 8, 30, 58, 52, 53, 65, 47, 1, 24, 61, 42, 54, 22, 18, 3, 15, 26, 35, 39, 33, 46, 62, 12, 29, 41, 0, 49, 44, 59, 57, 11, 36, 19], 'cur_cost': 114553.0}]
2025-06-22 18:14:55,213 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:14:55,213 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 18:14:55,213 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:14:55,213 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:14:55,213 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:14:55,214 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:14:55,214 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99587.0
2025-06-22 18:14:55,715 - ExploitationExpert - INFO - res_population_num: 15
2025-06-22 18:14:55,715 - ExploitationExpert - INFO - res_population_costs: [93875, 9572, 9524, 9522, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:14:55,716 - ExploitationExpert - INFO - res_populations: [array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:14:55,721 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:14:55,721 - ExploitationExpert - INFO - populations: [{'tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}, {'tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}, {'tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}, {'tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}, {'tour': array([23, 25, 57, 27, 63, 52, 29, 34, 12, 51,  7,  0,  9, 65, 16, 14, 46,
       11, 53, 45, 15, 39, 18, 61, 38, 32, 49,  2, 36, 30, 44, 62,  3, 21,
       56, 20, 17, 35, 59,  4,  6,  1, 33, 64, 48, 28, 10, 13,  5, 50, 22,
       54, 24, 58, 31, 43, 55, 42, 19, 41,  8, 26, 47, 60, 37, 40]), 'cur_cost': 119182.0}, {'tour': array([55, 15, 16, 45, 57, 32, 17, 14, 42, 65, 63, 39, 58, 28, 51, 52, 62,
       25, 59, 24, 64, 53, 27, 11, 21, 33,  1, 46, 30, 41,  7, 40, 43,  4,
       54, 31, 19, 13, 61, 60, 18, 12, 22,  0,  9,  3, 37, 44,  5, 35, 49,
       36, 50, 10, 29, 56,  6, 47, 26, 48, 23, 34, 38, 20,  2,  8]), 'cur_cost': 112720.0}, {'tour': array([23, 59, 25, 36, 38, 24,  3, 52, 55, 17, 18, 16, 14, 41,  6, 49, 61,
       11, 28,  0, 34, 20,  4, 12, 64, 57, 43, 42, 44, 35, 51, 10,  1, 39,
       26,  5, 54, 46, 32, 62, 30, 15, 63,  9, 60, 45, 40, 65, 53, 50,  8,
       13, 21, 22, 31, 33, 48,  7, 19, 29, 47, 58, 27,  2, 56, 37]), 'cur_cost': 105584.0}, {'tour': array([12, 18, 63,  4, 11,  9, 41, 56, 48, 60, 19, 13, 25, 61, 58, 21, 39,
       64, 45, 28, 10, 20,  3, 46, 32,  1, 40, 16, 34, 59, 65,  2, 57, 29,
       24, 53,  5, 51, 52, 23, 49, 42, 47, 37,  6, 27, 15, 17, 22, 38,  0,
        7, 55, 33, 35, 30, 31, 43, 26, 14, 50, 62, 44, 54,  8, 36]), 'cur_cost': 103348.0}, {'tour': array([ 0, 55, 21, 57, 58, 14, 13,  6, 19, 50, 20, 36,  2, 59, 22, 54, 62,
       61, 53, 45, 60, 26,  7, 37, 17, 39, 16, 64, 25, 24, 18, 44, 23, 29,
       30, 38, 34, 52, 40, 33, 42,  8, 47, 51, 12, 28,  3,  1, 32,  4, 11,
       49, 41, 43, 10, 15, 48, 56,  9, 31, 27, 46,  5, 65, 63, 35]), 'cur_cost': 99587.0}]
2025-06-22 18:14:55,723 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:14:55,723 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 18:14:55,723 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:14:55,723 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [32, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 11468.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [48, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15776.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 40, 50, 56, 1, 10, 20, 3, 24, 37, 46, 60, 8, 17, 27, 34, 44, 54, 0, 5, 15, 25, 38, 48, 65, 12, 19, 29, 39, 49, 62, 6, 16, 26, 33, 43, 53, 2, 9, 18, 28, 35, 45, 55, 7, 13, 23, 30, 41, 51, 63, 4, 14, 22, 31, 42, 52, 59, 11, 21, 36, 47, 57, 64, 58, 61], 'cur_cost': 86288.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 0, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 25567.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 9, 15, 23, 31, 39, 47, 55, 63, 61, 53, 45, 37, 29, 21, 13, 5, 0, 2, 10, 18, 26, 34, 42, 50, 58, 64, 62, 54, 46, 38, 30, 22, 14, 6, 3, 4, 12, 20, 28, 36, 44, 52, 60, 65, 57, 49, 41, 33, 25, 17, 7, 8, 16, 24, 32, 40, 48, 56, 59, 51, 35, 27, 19, 11, 43], 'cur_cost': 65694.0}}]
2025-06-22 18:14:55,724 - __main__ - INFO - 进化阶段完成
2025-06-22 18:14:55,724 - __main__ - INFO - 开始评估阶段
2025-06-22 18:14:55,724 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:14:55,725 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:14:55,725 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/2
- Population changes:
  * Cost: min 9903.0->11468.0, mean 77540.4->74521.4
  * Diversity: 0.7646153846153846->0.7123076923076923
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 5 individuals, success rate 40.0%, avg improvement 6535.0
- Exploit: 5 individuals, success rate 40.0%, avg improvement -497.0
- Overall improvement: -1565.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:15:01,029 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potentially Stagnating",
    "features": "The population cost is increasing (worsening), while diversity is decreasing. The number of elite solutions has also dropped significantly. This suggests potential stagnation and a need to re-evaluate the search direction."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately Successful",
      "details": "Explore achieved a 40% success rate with a significant average improvement of 6535.0.  This indicates that exploration is finding beneficial solutions, but potentially at a low rate or with a high degree of randomness."
    },
    "exploit": {
      "performance": "Unsuccessful",
      "details": "Exploit also achieved a 40% success rate, but the average improvement is negative (-497.0). This suggests that exploitation is not effective and potentially damaging the overall population quality, as improvements made are not substantial enough to offset the individuals that did not improve."
    },
    "overall": {
      "performance": "Negative",
      "details": "The overall population cost increased.  The combined effect of exploration and exploitation resulted in a negative overall improvement of -1565.0, worsening population quality."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards Exploration, but needs adjustments.",
    "adjustment_needs": "While the exploration phase is yielding significant improvements when successful, it is not sustaining the overall population quality due to the ineffectiveness of the exploitation.  The exploitation needs adjustment as it is dragging the population cost upwards."
  },
  "recommendations": [
    {
      "type": "Re-evaluate Exploitation",
      "details": "Thoroughly examine the exploitation strategy to identify and fix the root cause of its poor performance. Consider more focused local search methods.  Are parameters set incorrectly?  Is the selection method for exploitation flawed? Consider a complete removal of the exploit strategy in favor of increased exploration."
    },
    {
      "type": "Fine-tune Exploration",
      "details": "While exploration is generating improvements, consider refining it to potentially increase the success rate. This could involve adjusting the mutation/variation parameters, or adding a form of selection based on explored parameters to increase the chance of retaining promising solutions. Explore parameters that consistently yielded improvement during the exploration phase."
    },
    {
      "type": "Balance Exploration and Exploitation",
      "details": "Re-balance the allocation of individuals between explore and exploit, or remove exploit strategy altogether. For the next iteration, experiment with a greater emphasis on the exploration phase. Try reducing the number of individuals allocated for exploitation to see if this can yield some improvement."
    },
    {
      "type": "Elitist Retention",
      "details": "Increase the elitism to preserve high-performing solutions and increase the rate of discovery. Ensure that the top performing solutions are retained to preserve improvements. Consider increasing the number of elite solutions retained."
    }
  ]
}
```
2025-06-22 18:15:01,059 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:15:01,059 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potentially Stagnating",
    "features": "The population cost is increasing (worsening), while diversity is decreasing. The number of elite solutions has also dropped significantly. This suggests potential stagnation and a need to re-evaluate the search direction."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately Successful",
      "details": "Explore achieved a 40% success rate with a significant average improvement of 6535.0.  This indicates that exploration is finding beneficial solutions, but potentially at a low rate or with a high degree of randomness."
    },
    "exploit": {
      "performance": "Unsuccessful",
      "details": "Exploit also achieved a 40% success rate, but the average improvement is negative (-497.0). This suggests that exploitation is not effective and potentially damaging the overall population quality, as improvements made are not substantial enough to offset the individuals that did not improve."
    },
    "overall": {
      "performance": "Negative",
      "details": "The overall population cost increased.  The combined effect of exploration and exploitation resulted in a negative overall improvement of -1565.0, worsening population quality."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards Exploration, but needs adjustments.",
    "adjustment_needs": "While the exploration phase is yielding significant improvements when successful, it is not sustaining the overall population quality due to the ineffectiveness of the exploitation.  The exploitation needs adjustment as it is dragging the population cost upwards."
  },
  "recommendations": [
    {
      "type": "Re-evaluate Exploitation",
      "details": "Thoroughly examine the exploitation strategy to identify and fix the root cause of its poor performance. Consider more focused local search methods.  Are parameters set incorrectly?  Is the selection method for exploitation flawed? Consider a complete removal of the exploit strategy in favor of increased exploration."
    },
    {
      "type": "Fine-tune Exploration",
      "details": "While exploration is generating improvements, consider refining it to potentially increase the success rate. This could involve adjusting the mutation/variation parameters, or adding a form of selection based on explored parameters to increase the chance of retaining promising solutions. Explore parameters that consistently yielded improvement during the exploration phase."
    },
    {
      "type": "Balance Exploration and Exploitation",
      "details": "Re-balance the allocation of individuals between explore and exploit, or remove exploit strategy altogether. For the next iteration, experiment with a greater emphasis on the exploration phase. Try reducing the number of individuals allocated for exploitation to see if this can yield some improvement."
    },
    {
      "type": "Elitist Retention",
      "details": "Increase the elitism to preserve high-performing solutions and increase the rate of discovery. Ensure that the top performing solutions are retained to preserve improvements. Consider increasing the number of elite solutions retained."
    }
  ]
}
```
2025-06-22 18:15:01,060 - __main__ - INFO - 评估阶段完成
2025-06-22 18:15:01,060 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potentially Stagnating",
    "features": "The population cost is increasing (worsening), while diversity is decreasing. The number of elite solutions has also dropped significantly. This suggests potential stagnation and a need to re-evaluate the search direction."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately Successful",
      "details": "Explore achieved a 40% success rate with a significant average improvement of 6535.0.  This indicates that exploration is finding beneficial solutions, but potentially at a low rate or with a high degree of randomness."
    },
    "exploit": {
      "performance": "Unsuccessful",
      "details": "Exploit also achieved a 40% success rate, but the average improvement is negative (-497.0). This suggests that exploitation is not effective and potentially damaging the overall population quality, as improvements made are not substantial enough to offset the individuals that did not improve."
    },
    "overall": {
      "performance": "Negative",
      "details": "The overall population cost increased.  The combined effect of exploration and exploitation resulted in a negative overall improvement of -1565.0, worsening population quality."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards Exploration, but needs adjustments.",
    "adjustment_needs": "While the exploration phase is yielding significant improvements when successful, it is not sustaining the overall population quality due to the ineffectiveness of the exploitation.  The exploitation needs adjustment as it is dragging the population cost upwards."
  },
  "recommendations": [
    {
      "type": "Re-evaluate Exploitation",
      "details": "Thoroughly examine the exploitation strategy to identify and fix the root cause of its poor performance. Consider more focused local search methods.  Are parameters set incorrectly?  Is the selection method for exploitation flawed? Consider a complete removal of the exploit strategy in favor of increased exploration."
    },
    {
      "type": "Fine-tune Exploration",
      "details": "While exploration is generating improvements, consider refining it to potentially increase the success rate. This could involve adjusting the mutation/variation parameters, or adding a form of selection based on explored parameters to increase the chance of retaining promising solutions. Explore parameters that consistently yielded improvement during the exploration phase."
    },
    {
      "type": "Balance Exploration and Exploitation",
      "details": "Re-balance the allocation of individuals between explore and exploit, or remove exploit strategy altogether. For the next iteration, experiment with a greater emphasis on the exploration phase. Try reducing the number of individuals allocated for exploitation to see if this can yield some improvement."
    },
    {
      "type": "Elitist Retention",
      "details": "Increase the elitism to preserve high-performing solutions and increase the rate of discovery. Ensure that the top performing solutions are retained to preserve improvements. Consider increasing the number of elite solutions retained."
    }
  ]
}
```
2025-06-22 18:15:01,061 - __main__ - INFO - 当前最佳适应度: 11468.0
2025-06-22 18:15:01,061 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 18:15:01,061 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 18:15:01,061 - __main__ - INFO - 开始分析阶段
2025-06-22 18:15:01,061 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:15:01,081 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11468.0, 'max': 119182.0, 'mean': 74521.4, 'std': 39896.75042205818}, 'diversity': 0.9077441077441076, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:15:01,081 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11468.0, 'max': 119182.0, 'mean': 74521.4, 'std': 39896.75042205818}, 'diversity_level': 0.9077441077441076, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:15:01,081 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:15:01,081 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:15:01,082 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:15:01,084 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:15:01,084 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:15:01,084 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:15:01,085 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:15:01,085 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:15:01,085 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:15:01,089 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:15:01,089 - __main__ - INFO - 精英专家分析报告: {'elite_count': 15, 'elite_common_features': {'common_edges': {'(55, 61)': 0.7333333333333333, '(62, 59)': 0.7333333333333333}, 'common_edge_ratio': 0.030303030303030304}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1947.0, 'avg_gap': 59373.06666666666}, 'structure_gap': {'unique_elite_edges': 206, 'unique_pop_edges': 447, 'common_edges': 57}}, 'elite_diversity': {'diversity_score': 0.3246753246753247}}
2025-06-22 18:15:01,089 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:15:01,090 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:15:01,090 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:15:01,090 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11468.0, max=119182.0, mean=74521.4, std=39896.75042205818
- Diversity: 0.9077441077441076, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 18:15:07,280 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Potentially high, but hard to definitively assess with only 10 individuals and no identified high-quality edges or structure. The wide cost range (min=11468, max=119182, mean=74521.4, std=39896.75) hints at a varied fitness landscape.",
    "modality": "Unknown. The presence of a significant standard deviation in cost suggests the possibility of multiple optima, but the lack of elite solutions or any identified structure makes it impossible to confirm. The low convergence and the diversity metrics provide a clue for multi-modal problem but not a strong confirmation.",
    "other_features": "The absence of any shared high-quality edges or common subpaths and the number of individuals suggest the population is not exploiting any existing structure."
  },
  "population_state": {
    "diversity": "High (0.907744). This suggests a good exploration of the search space, but it's also coupled with no convergence. The current set of population contains diverse solutions.",
    "convergence": "Extremely low (0.0). This indicates that the population is not moving towards a specific area and is not converging on a solution." ,
    "elite_status": "No elite solutions are found."
  },
  "difficult_regions": {
    "identified_challenges": "Difficult regions are not explicitly identified. The lack of structure, high cost variability and lack of convergence and elite solutions suggest that the search space might contain complex patterns or the search may be stuck at a plateaus. The population might also be exploring a large portion of the space without finding useful information to improve convergence." ,
    "possible_explanations": [
      "A very flat fitness landscape in some regions.",
      "A deceptive landscape that lures the population away from the optimal solution.",
      "The search has not explored some of the important areas of the search space."
    ]
  },
  "opportunity_regions": {
    "promising_areas": "Opportunity regions are not explicitly identified. However, the high diversity suggests unexplored potential across the search space. Given the diversity and the potentially large number of optima, the population is unlikely to have sampled the whole search space with a population of 10 individuals. There might be some local optimal solutions which the algorithm could exploit.",
    "potential_strategies": [
      "Focus exploration in regions with lower cost, where the algorithm can focus its search in some promising areas.",
      "Investigate whether the cost function can be simplified to remove any misleading information."
    ]
  },
  "evolution_direction": {
    "recommended_strategy": "Increase exploitation alongside exploration, or improve local search.",
    "specific_actions": [
      "Consider techniques for increasing convergence: This could involve introducing pressure toward better solutions through elitism.",
      "Implement a local search phase to quickly move the solutions found to local optimal solutions.",
      "Explore different mutation/crossover/replacement strategies to promote exploitation of promising areas and accelerate convergence.",
      "Implement a mechanism for preserving good solutions, even if they are not yet elite.",
      "Check if there are regions in the cost function or in the solutions themselves which need to be preprocessed or optimized beforehand.",
      "Increase the population size. A larger population can provide more information about the search space.",
       "Try different parameter settings and operators."
    ]
  }
}
```
2025-06-22 18:15:07,280 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:15:07,280 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Potentially high, but hard to definitively assess with only 10 individuals and no identified high-quality edges or structure. The wide cost range (min=11468, max=119182, mean=74521.4, std=39896.75) hints at a varied fitness landscape.', 'modality': 'Unknown. The presence of a significant standard deviation in cost suggests the possibility of multiple optima, but the lack of elite solutions or any identified structure makes it impossible to confirm. The low convergence and the diversity metrics provide a clue for multi-modal problem but not a strong confirmation.', 'other_features': 'The absence of any shared high-quality edges or common subpaths and the number of individuals suggest the population is not exploiting any existing structure.'}, 'population_state': {'diversity': "High (0.907744). This suggests a good exploration of the search space, but it's also coupled with no convergence. The current set of population contains diverse solutions.", 'convergence': 'Extremely low (0.0). This indicates that the population is not moving towards a specific area and is not converging on a solution.', 'elite_status': 'No elite solutions are found.'}, 'difficult_regions': {'identified_challenges': 'Difficult regions are not explicitly identified. The lack of structure, high cost variability and lack of convergence and elite solutions suggest that the search space might contain complex patterns or the search may be stuck at a plateaus. The population might also be exploring a large portion of the space without finding useful information to improve convergence.', 'possible_explanations': ['A very flat fitness landscape in some regions.', 'A deceptive landscape that lures the population away from the optimal solution.', 'The search has not explored some of the important areas of the search space.']}, 'opportunity_regions': {'promising_areas': 'Opportunity regions are not explicitly identified. However, the high diversity suggests unexplored potential across the search space. Given the diversity and the potentially large number of optima, the population is unlikely to have sampled the whole search space with a population of 10 individuals. There might be some local optimal solutions which the algorithm could exploit.', 'potential_strategies': ['Focus exploration in regions with lower cost, where the algorithm can focus its search in some promising areas.', 'Investigate whether the cost function can be simplified to remove any misleading information.']}, 'evolution_direction': {'recommended_strategy': 'Increase exploitation alongside exploration, or improve local search.', 'specific_actions': ['Consider techniques for increasing convergence: This could involve introducing pressure toward better solutions through elitism.', 'Implement a local search phase to quickly move the solutions found to local optimal solutions.', 'Explore different mutation/crossover/replacement strategies to promote exploitation of promising areas and accelerate convergence.', 'Implement a mechanism for preserving good solutions, even if they are not yet elite.', 'Check if there are regions in the cost function or in the solutions themselves which need to be preprocessed or optimized beforehand.', 'Increase the population size. A larger population can provide more information about the search space.', 'Try different parameter settings and operators.']}}
2025-06-22 18:15:07,280 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:15:07,280 - __main__ - INFO - 分析阶段完成
2025-06-22 18:15:07,280 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Potentially high, but hard to definitively assess with only 10 individuals and no identified high-quality edges or structure. The wide cost range (min=11468, max=119182, mean=74521.4, std=39896.75) hints at a varied fitness landscape.', 'modality': 'Unknown. The presence of a significant standard deviation in cost suggests the possibility of multiple optima, but the lack of elite solutions or any identified structure makes it impossible to confirm. The low convergence and the diversity metrics provide a clue for multi-modal problem but not a strong confirmation.', 'other_features': 'The absence of any shared high-quality edges or common subpaths and the number of individuals suggest the population is not exploiting any existing structure.'}, 'population_state': {'diversity': "High (0.907744). This suggests a good exploration of the search space, but it's also coupled with no convergence. The current set of population contains diverse solutions.", 'convergence': 'Extremely low (0.0). This indicates that the population is not moving towards a specific area and is not converging on a solution.', 'elite_status': 'No elite solutions are found.'}, 'difficult_regions': {'identified_challenges': 'Difficult regions are not explicitly identified. The lack of structure, high cost variability and lack of convergence and elite solutions suggest that the search space might contain complex patterns or the search may be stuck at a plateaus. The population might also be exploring a large portion of the space without finding useful information to improve convergence.', 'possible_explanations': ['A very flat fitness landscape in some regions.', 'A deceptive landscape that lures the population away from the optimal solution.', 'The search has not explored some of the important areas of the search space.']}, 'opportunity_regions': {'promising_areas': 'Opportunity regions are not explicitly identified. However, the high diversity suggests unexplored potential across the search space. Given the diversity and the potentially large number of optima, the population is unlikely to have sampled the whole search space with a population of 10 individuals. There might be some local optimal solutions which the algorithm could exploit.', 'potential_strategies': ['Focus exploration in regions with lower cost, where the algorithm can focus its search in some promising areas.', 'Investigate whether the cost function can be simplified to remove any misleading information.']}, 'evolution_direction': {'recommended_strategy': 'Increase exploitation alongside exploration, or improve local search.', 'specific_actions': ['Consider techniques for increasing convergence: This could involve introducing pressure toward better solutions through elitism.', 'Implement a local search phase to quickly move the solutions found to local optimal solutions.', 'Explore different mutation/crossover/replacement strategies to promote exploitation of promising areas and accelerate convergence.', 'Implement a mechanism for preserving good solutions, even if they are not yet elite.', 'Check if there are regions in the cost function or in the solutions themselves which need to be preprocessed or optimized beforehand.', 'Increase the population size. A larger population can provide more information about the search space.', 'Try different parameter settings and operators.']}}
2025-06-22 18:15:07,280 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:15:07,280 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:15:07,286 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High (0.907744). This suggests a good exploration of the search space, but it's also coupled with no convergence. The current set of population contains diverse solutions.
- Best cost: 11468.0, Mean cost: 74521.4
- Search space: Ruggedness: Potentially high, but hard to definitively assess with only 10 individuals and no identified high-quality edges or structure. The wide cost range (min=11468, max=119182, mean=74521.4, std=39896.75) hints at a varied fitness landscape., Modality: Unknown. The presence of a significant standard deviation in cost suggests the possibility of multiple optima, but the lack of elite solutions or any identified structure makes it impossible to confirm. The low convergence and the diversity metrics provide a clue for multi-modal problem but not a strong confirmation.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early, Potentially Stagnating",
    "features": "The population cost is increasing (worsening), while diversity is decreasing. The number of elite soluti...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:15:07,286 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:15:09,622 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "local_search_iterations": 10
    },
    "exploit": {
      "crossover_rate": 0.3,
      "mutation_rate": 0.05,
      "local_search_iterations": 20
    }
  }
}
```

2025-06-22 18:15:09,622 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-22 18:15:09,622 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-22 18:15:09,622 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "local_search_iterations": 10
    },
    "exploit": {
      "crossover_rate": 0.3,
      "mutation_rate": 0.05,
      "local_search_iterations": 20
    }
  }
}
```

2025-06-22 18:15:09,622 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:15:09,625 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-22 18:15:09,625 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "local_search_iterations": 10
    },
    "exploit": {
      "crossover_rate": 0.3,
      "mutation_rate": 0.05,
      "local_search_iterations": 20
    }
  }
}
```

2025-06-22 18:15:09,625 - __main__ - INFO - 开始进化阶段
2025-06-22 18:15:09,625 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:15:09,625 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:09,626 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 31, 33] and ending with [28, 29, 30]
- Cost: 11468.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7123076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:09,626 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:13,304 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28
  ],
  "modification_strategy": "This strategy prioritizes exploration of potentially under-visited regions by reordering nodes to include a greater variety of areas in early path segments. Specifically, I attempted to distribute visits more evenly across the node space by interleaving nodes that might have been clustered together in the original path. This strategy uses a degree of random sampling to maximize diversity within the given constraints. This includes starting with a different node, then diversifying by picking nodes more randomly than following the current path.",
  "targeted_regions": "The primary focus was to spread out the node visits, aiming to cover the two unexplored opportunity regions indicated by the context, and also minimizing potential clustering in the original path."
}
```
2025-06-22 18:15:13,304 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:13,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109909.0, 路径: [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35]
2025-06-22 18:15:13,304 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35], 'cur_cost': 109909.0}
2025-06-22 18:15:13,304 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:15:13,304 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:13,304 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [48, 0, 1] and ending with [63, 64, 65]
- Cost: 15776.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.796923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:13,304 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:16,785 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "This path prioritizes exploration of different regions by altering the initial sequence and partially reversing the order. Specifically, it starts with node 48 and explores a sequence of nodes that were previously traversed later. It then integrates the original starting point and continues to the end in the given order. This strategy increases diversity by deviating from the original path early on, hitting key nodes previously explored and incorporating the end of the original path, thus maintaining a full and unique permutation.",
  "targeted_regions": "Opportunity and unexplored regions were targeted by changing the start and traversing of a new cluster of nodes."
}
```
2025-06-22 18:15:16,785 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:16,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20657.0, 路径: [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:15:16,788 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 20657.0}
2025-06-22 18:15:16,788 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:15:16,788 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:16,789 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 40, 50] and ending with [64, 58, 61]
- Cost: 86288.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7953846153846154
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:16,789 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:20,002 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Completely reordered the path to maximize diversity. This involves creating a completely new sequence rather than local modifications. This strategy prioritizes exploration and provides a baseline that is significantly different from the current path. The path utilizes the full node range, visiting each node index once.",
  "targeted_regions": "All regions are targeted equally. This is an exploratory approach to create a very different path. The goal is to uncover previously unexplored connections."
}
```
2025-06-22 18:15:20,002 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:20,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:15:20,004 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-22 18:15:20,004 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:15:20,004 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:20,004 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 2, 42] and ending with [63, 64, 65]
- Cost: 25567.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7246153846153847
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:20,004 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:23,947 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
    37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56,
    57, 58, 59, 60, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "The strategy prioritizes exploration of unexplored regions identified in the search context. The initial sequence from the original path is preserved to maintain a degree of existing quality. Then the remaining nodes are arranged in a seemingly arbitrary but legal sequence, ensuring that all nodes are visited, and all requirements of the TSP are met. The aim is to create a path that balances diversity with a minimum expected disruption to path quality.  This is accomplished by strategically integrating new regions from the given information into the path sequence while minimizing the drastic alterations that would result from total random shuffling.",
  "targeted_regions": "Unexplored regions and opportunity regions are indirectly targeted by ensuring all nodes are included in the new path. "
}
```
2025-06-22 18:15:23,947 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:23,947 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21213.0, 路径: [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0]
2025-06-22 18:15:23,947 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 21213.0}
2025-06-22 18:15:23,947 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:15:23,947 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:23,947 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 9, 15] and ending with [19, 11, 43]
- Cost: 65694.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7246153846153847
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:23,947 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:27,243 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38,
    39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56,
    57, 58, 59, 60, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "Completely randomized path generation. This strategy prioritizes diversity by creating a fundamentally different path structure compared to the existing path.  The indices were shuffled randomly to avoid any bias towards the original path's structure. This approach avoids path dependencies.",
  "targeted_regions": "By generating a fully randomized path, all regions (including opportunity and unexplored regions) are implicitly targeted for exploration. This strategy directly addresses the need to visit all 66 nodes while striving for maximal diversity."
}
```
2025-06-22 18:15:27,243 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:27,243 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0]
2025-06-22 18:15:27,243 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}
2025-06-22 18:15:27,243 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 18:15:27,244 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:27,244 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [23, 25, 57] and ending with [60, 37, 40]
- Cost: 119182.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6446153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:27,244 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:30,850 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60
  ],
  "modification_strategy": "This strategy prioritizes exploration of unexplored regions and increases diversity by swapping and reordering larger segments of the current path. I aimed to avoid difficult regions using the risk tolerance parameter and diversity weight. The initial segments are also modified to ensure a significant shift from the original path. The high diversity weight encourages significant changes in the path ordering, while considering the problem constraints.",
  "targeted_regions": "The strategy focused on exploring the unexplored regions identified in the search context. Significant reordering of segments from the original path allows to hopefully reach those regions more efficiently. The shuffling is designed to make sure the traversal is significantly different"
}
```
2025-06-22 18:15:30,850 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:30,850 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114032.0, 路径: [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22]
2025-06-22 18:15:30,850 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22], 'cur_cost': 114032.0}
2025-06-22 18:15:30,850 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 18:15:30,850 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:15:30,850 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [55, 15, 16] and ending with [20, 2, 8]
- Cost: 112720.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6523076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:15:30,850 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:15:36,142 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25
  ],
  "modification_strategy": "This strategy prioritizes exploring unexplored regions and increasing diversity. The path was reconstructed by interweaving nodes to create a more diverse path, and avoiding areas previously identified as difficult.  The beginning and ending nodes were kept the same, but the bulk of the path was modified to diversify the search. This focuses on a high diversity score.",
  "targeted_regions": "Unexplored regions and those indicated as potential opportunity areas were targeted.  Specifically, the new path reorders the path to visit regions previously unexplored and avoids the difficult regions indicated in the search context."
}
```
2025-06-22 18:15:36,142 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:15:36,142 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109523.0, 路径: [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56]
2025-06-22 18:15:36,142 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56], 'cur_cost': 109523.0}
2025-06-22 18:15:36,142 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:15:36,142 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:15:36,146 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:15:36,146 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 96206.0
2025-06-22 18:15:38,104 - ExploitationExpert - INFO - res_population_num: 21
2025-06-22 18:15:38,104 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9522, 9524, 9572, 93875, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:15:38,106 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:15:38,115 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:15:38,116 - ExploitationExpert - INFO - populations: [{'tour': [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35], 'cur_cost': 109909.0}, {'tour': [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 20657.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 21213.0}, {'tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}, {'tour': [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22], 'cur_cost': 114032.0}, {'tour': [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56], 'cur_cost': 109523.0}, {'tour': array([ 6, 30, 62, 64, 55, 17, 40, 20, 47, 44, 59, 26, 21, 23, 54, 60, 43,
       58,  7, 39, 41,  2, 12, 22, 19,  9,  5, 10, 14,  4, 28,  1, 57, 29,
       56, 52, 38, 42, 45, 46, 61, 63, 24, 13, 50, 37, 15,  0, 16, 27, 31,
       65, 11, 18, 33, 34,  8, 32,  3, 49, 36, 35, 51, 25, 53, 48]), 'cur_cost': 96206.0}, {'tour': array([12, 18, 63,  4, 11,  9, 41, 56, 48, 60, 19, 13, 25, 61, 58, 21, 39,
       64, 45, 28, 10, 20,  3, 46, 32,  1, 40, 16, 34, 59, 65,  2, 57, 29,
       24, 53,  5, 51, 52, 23, 49, 42, 47, 37,  6, 27, 15, 17, 22, 38,  0,
        7, 55, 33, 35, 30, 31, 43, 26, 14, 50, 62, 44, 54,  8, 36]), 'cur_cost': 103348.0}, {'tour': array([ 0, 55, 21, 57, 58, 14, 13,  6, 19, 50, 20, 36,  2, 59, 22, 54, 62,
       61, 53, 45, 60, 26,  7, 37, 17, 39, 16, 64, 25, 24, 18, 44, 23, 29,
       30, 38, 34, 52, 40, 33, 42,  8, 47, 51, 12, 28,  3,  1, 32,  4, 11,
       49, 41, 43, 10, 15, 48, 56,  9, 31, 27, 46,  5, 65, 63, 35]), 'cur_cost': 99587.0}]
2025-06-22 18:15:38,119 - ExploitationExpert - INFO - 局部搜索耗时: 1.97秒
2025-06-22 18:15:38,119 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 18:15:38,119 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:15:38,119 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:15:38,119 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:15:38,120 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:15:38,120 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 127603.0
2025-06-22 18:15:38,623 - ExploitationExpert - INFO - res_population_num: 23
2025-06-22 18:15:38,623 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9522, 9524, 9572, 93875, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:15:38,623 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:15:38,633 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:15:38,633 - ExploitationExpert - INFO - populations: [{'tour': [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35], 'cur_cost': 109909.0}, {'tour': [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 20657.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 21213.0}, {'tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}, {'tour': [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22], 'cur_cost': 114032.0}, {'tour': [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56], 'cur_cost': 109523.0}, {'tour': array([ 6, 30, 62, 64, 55, 17, 40, 20, 47, 44, 59, 26, 21, 23, 54, 60, 43,
       58,  7, 39, 41,  2, 12, 22, 19,  9,  5, 10, 14,  4, 28,  1, 57, 29,
       56, 52, 38, 42, 45, 46, 61, 63, 24, 13, 50, 37, 15,  0, 16, 27, 31,
       65, 11, 18, 33, 34,  8, 32,  3, 49, 36, 35, 51, 25, 53, 48]), 'cur_cost': 96206.0}, {'tour': array([ 1, 48, 55, 16, 15,  4, 38, 42, 20, 64, 33, 21, 65, 14,  8, 34, 17,
        3, 47, 37, 63, 31,  7, 35, 12, 52, 19, 44, 60, 49, 24, 58, 43, 53,
       29, 54, 27, 56, 59, 13, 18, 61, 32, 57, 46,  2, 41, 40, 28,  9, 11,
       22, 39, 10, 51, 25, 45, 23, 26,  0, 30, 50, 62, 36,  6,  5]), 'cur_cost': 127603.0}, {'tour': array([ 0, 55, 21, 57, 58, 14, 13,  6, 19, 50, 20, 36,  2, 59, 22, 54, 62,
       61, 53, 45, 60, 26,  7, 37, 17, 39, 16, 64, 25, 24, 18, 44, 23, 29,
       30, 38, 34, 52, 40, 33, 42,  8, 47, 51, 12, 28,  3,  1, 32,  4, 11,
       49, 41, 43, 10, 15, 48, 56,  9, 31, 27, 46,  5, 65, 63, 35]), 'cur_cost': 99587.0}]
2025-06-22 18:15:38,635 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:15:38,635 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 18:15:38,635 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:15:38,636 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:15:38,636 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:15:38,636 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:15:38,637 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 100420.0
2025-06-22 18:15:39,140 - ExploitationExpert - INFO - res_population_num: 24
2025-06-22 18:15:39,140 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9522, 9524, 9572, 93875, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:15:39,140 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 23,
       13, 21, 20, 14, 15,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 23,
       22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 43, 40, 49,
       58, 56, 55, 61, 53, 62, 59, 60, 54, 57, 64, 65, 52, 63, 10],
      dtype=int64), array([ 0, 32, 35, 31,  7,  5, 22, 41, 64, 55, 29, 20,  4, 24, 25, 47, 52,
       58, 16, 28,  9,  3, 43, 65, 39, 18, 53,  8, 45, 50, 15, 11, 59, 48,
       57, 62, 61,  1, 46, 26, 38, 13, 21, 60, 56, 40, 12, 19, 27, 44, 14,
       17,  6, 10,  2, 54, 51, 23, 36, 33, 30, 49, 63, 34, 42, 37],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:15:39,150 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:15:39,151 - ExploitationExpert - INFO - populations: [{'tour': [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35], 'cur_cost': 109909.0}, {'tour': [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 20657.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 21213.0}, {'tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}, {'tour': [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22], 'cur_cost': 114032.0}, {'tour': [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56], 'cur_cost': 109523.0}, {'tour': array([ 6, 30, 62, 64, 55, 17, 40, 20, 47, 44, 59, 26, 21, 23, 54, 60, 43,
       58,  7, 39, 41,  2, 12, 22, 19,  9,  5, 10, 14,  4, 28,  1, 57, 29,
       56, 52, 38, 42, 45, 46, 61, 63, 24, 13, 50, 37, 15,  0, 16, 27, 31,
       65, 11, 18, 33, 34,  8, 32,  3, 49, 36, 35, 51, 25, 53, 48]), 'cur_cost': 96206.0}, {'tour': array([ 1, 48, 55, 16, 15,  4, 38, 42, 20, 64, 33, 21, 65, 14,  8, 34, 17,
        3, 47, 37, 63, 31,  7, 35, 12, 52, 19, 44, 60, 49, 24, 58, 43, 53,
       29, 54, 27, 56, 59, 13, 18, 61, 32, 57, 46,  2, 41, 40, 28,  9, 11,
       22, 39, 10, 51, 25, 45, 23, 26,  0, 30, 50, 62, 36,  6,  5]), 'cur_cost': 127603.0}, {'tour': array([15, 63, 29, 11, 31, 17, 49, 48,  2, 21, 22, 52, 10, 62, 42, 50, 19,
       56,  9,  1,  6, 54, 32, 26, 25, 44, 53, 27, 40,  4, 37, 12, 23, 13,
       60,  5, 45, 39, 61, 58, 38, 20, 30, 59, 41, 46, 16, 33, 55, 35, 43,
       34,  3,  0, 28, 36,  7, 65, 24, 51, 47, 14, 64,  8, 18, 57]), 'cur_cost': 100420.0}]
2025-06-22 18:15:39,153 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:15:39,153 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 18:15:39,153 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:15:39,153 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [32, 5, 17, 44, 21, 6, 43, 14, 36, 60, 23, 45, 31, 53, 46, 1, 12, 63, 38, 24, 34, 0, 18, 50, 55, 22, 48, 2, 41, 19, 39, 13, 33, 3, 51, 29, 10, 26, 54, 58, 47, 40, 8, 64, 11, 65, 4, 57, 20, 59, 52, 27, 37, 30, 25, 62, 56, 9, 16, 7, 42, 15, 49, 28, 61, 35], 'cur_cost': 109909.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [48, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 0, 1, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 20657.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [43, 2, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 21213.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [23, 57, 25, 18, 42, 5, 31, 4, 29, 50, 6, 34, 64, 62, 30, 49, 28, 14, 17, 21, 51, 11, 47, 13, 33, 52, 2, 45, 3, 39, 43, 20, 16, 54, 1, 56, 12, 55, 24, 0, 61, 41, 27, 32, 44, 15, 36, 53, 9, 8, 46, 63, 26, 10, 58, 65, 38, 35, 19, 59, 7, 40, 48, 60, 37, 22], 'cur_cost': 114032.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [55, 4, 10, 23, 36, 59, 28, 1, 60, 33, 50, 37, 15, 16, 53, 62, 29, 63, 13, 30, 44, 20, 3, 48, 40, 26, 39, 24, 27, 41, 57, 43, 64, 52, 35, 65, 14, 8, 45, 19, 61, 11, 5, 17, 58, 32, 47, 34, 2, 54, 31, 12, 7, 46, 22, 6, 9, 51, 42, 21, 0, 18, 38, 49, 25, 56], 'cur_cost': 109523.0}}]
2025-06-22 18:15:39,154 - __main__ - INFO - 进化阶段完成
2025-06-22 18:15:39,154 - __main__ - INFO - 开始评估阶段
2025-06-22 18:15:39,154 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:15:39,154 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:15:39,154 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/2
- Population changes:
  * Cost: min 11468.0->11479.0, mean 74521.4->72252.1
  * Diversity: 0.7123076923076923->0.6553846153846153
  * Elite solutions: 1->2

## Strategy Performance:
- Explore: 7 individuals, success rate 71.4%, avg improvement 5486.14
- Exploit: 3 individuals, success rate 33.3%, avg improvement -5236.67
- Overall improvement: -11.0

## Historical Trends:
Last 1 iterations: costs [11468.0], diversity [0.9077441077441076]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:15:44,113 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Refinement",
    "features": "The initial population shows signs of refinement with slight cost increases in both the minimum and mean. The decrease in diversity, the addition of elite solutions, and the overall negative improvement indicate a challenging initial phase, possibly requiring more robust exploration."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High",
      "avg_improvement": "Positive",
      "assessment": "Exploration shows good success with a high success rate and significant average improvement. This suggests the exploration strategy is effectively discovering promising regions of the search space."
    },
    "exploit": {
      "success_rate": "Low",
      "avg_improvement": "Negative",
      "assessment": "Exploitation is failing. The negative average improvement and low success rate indicate that the exploitation strategy is likely converging towards sub-optimal solutions or overfitting to the initial population."
    },
    "overall_improvement": {
      "value": "Negative",
      "assessment": "Overall negative improvement indicates the optimization process is not yet successful. The gains from exploration have been offset by failures in exploitation, or there may be an inherent noise that needs to be addressed through different settings."
    }
  },
  "balance_state": {
    "assessment": "Unbalanced towards Exploration; needed is a refinement of the exploitation strategy or adjustment to the exploration strategy to better use information that has been gained",
    "adjustment_needs": "Prioritize refining or strengthening the exploitation strategy or tuning the exploration strategy to make use of the initial information. Reduce the frequency/intensity of exploitation in the next iteration until a significant improvement is observed"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine Exploitation Strategy: Review and revise the exploitation strategy. Possible actions include:",
      "details": [
        "Adjust exploitation parameters (e.g., mutation rate, step size) to be more cautious.",
        "Implement a more sophisticated exploitation strategy (e.g., using a local search algorithm around promising exploration results).",
        "Increase the number of exploration attempts to avoid getting trapped into local optimum."
      ]
    },
    {
      "priority": "Medium",
      "action": "Fine-tune Exploration Strategy: Review parameters to see if there is room for improvement.",
      "details": [
        "Maintain or increase exploration attempts to avoid a local minimum or slow down the process"
      ]
    },
    {
      "priority": "Low",
      "action": "Monitor Diversity: Keep a close eye on the diversity metrics over the next few iterations. If diversity continues to decrease drastically, consider increasing the exploration intensity in future iterations to encourage more diverse solutions.",
      "details": []
    }
  ]
}
```
2025-06-22 18:15:44,141 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:15:44,142 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Refinement",
    "features": "The initial population shows signs of refinement with slight cost increases in both the minimum and mean. The decrease in diversity, the addition of elite solutions, and the overall negative improvement indicate a challenging initial phase, possibly requiring more robust exploration."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High",
      "avg_improvement": "Positive",
      "assessment": "Exploration shows good success with a high success rate and significant average improvement. This suggests the exploration strategy is effectively discovering promising regions of the search space."
    },
    "exploit": {
      "success_rate": "Low",
      "avg_improvement": "Negative",
      "assessment": "Exploitation is failing. The negative average improvement and low success rate indicate that the exploitation strategy is likely converging towards sub-optimal solutions or overfitting to the initial population."
    },
    "overall_improvement": {
      "value": "Negative",
      "assessment": "Overall negative improvement indicates the optimization process is not yet successful. The gains from exploration have been offset by failures in exploitation, or there may be an inherent noise that needs to be addressed through different settings."
    }
  },
  "balance_state": {
    "assessment": "Unbalanced towards Exploration; needed is a refinement of the exploitation strategy or adjustment to the exploration strategy to better use information that has been gained",
    "adjustment_needs": "Prioritize refining or strengthening the exploitation strategy or tuning the exploration strategy to make use of the initial information. Reduce the frequency/intensity of exploitation in the next iteration until a significant improvement is observed"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine Exploitation Strategy: Review and revise the exploitation strategy. Possible actions include:",
      "details": [
        "Adjust exploitation parameters (e.g., mutation rate, step size) to be more cautious.",
        "Implement a more sophisticated exploitation strategy (e.g., using a local search algorithm around promising exploration results).",
        "Increase the number of exploration attempts to avoid getting trapped into local optimum."
      ]
    },
    {
      "priority": "Medium",
      "action": "Fine-tune Exploration Strategy: Review parameters to see if there is room for improvement.",
      "details": [
        "Maintain or increase exploration attempts to avoid a local minimum or slow down the process"
      ]
    },
    {
      "priority": "Low",
      "action": "Monitor Diversity: Keep a close eye on the diversity metrics over the next few iterations. If diversity continues to decrease drastically, consider increasing the exploration intensity in future iterations to encourage more diverse solutions.",
      "details": []
    }
  ]
}
```
2025-06-22 18:15:44,143 - __main__ - INFO - 评估阶段完成
2025-06-22 18:15:44,143 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Refinement",
    "features": "The initial population shows signs of refinement with slight cost increases in both the minimum and mean. The decrease in diversity, the addition of elite solutions, and the overall negative improvement indicate a challenging initial phase, possibly requiring more robust exploration."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High",
      "avg_improvement": "Positive",
      "assessment": "Exploration shows good success with a high success rate and significant average improvement. This suggests the exploration strategy is effectively discovering promising regions of the search space."
    },
    "exploit": {
      "success_rate": "Low",
      "avg_improvement": "Negative",
      "assessment": "Exploitation is failing. The negative average improvement and low success rate indicate that the exploitation strategy is likely converging towards sub-optimal solutions or overfitting to the initial population."
    },
    "overall_improvement": {
      "value": "Negative",
      "assessment": "Overall negative improvement indicates the optimization process is not yet successful. The gains from exploration have been offset by failures in exploitation, or there may be an inherent noise that needs to be addressed through different settings."
    }
  },
  "balance_state": {
    "assessment": "Unbalanced towards Exploration; needed is a refinement of the exploitation strategy or adjustment to the exploration strategy to better use information that has been gained",
    "adjustment_needs": "Prioritize refining or strengthening the exploitation strategy or tuning the exploration strategy to make use of the initial information. Reduce the frequency/intensity of exploitation in the next iteration until a significant improvement is observed"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Refine Exploitation Strategy: Review and revise the exploitation strategy. Possible actions include:",
      "details": [
        "Adjust exploitation parameters (e.g., mutation rate, step size) to be more cautious.",
        "Implement a more sophisticated exploitation strategy (e.g., using a local search algorithm around promising exploration results).",
        "Increase the number of exploration attempts to avoid getting trapped into local optimum."
      ]
    },
    {
      "priority": "Medium",
      "action": "Fine-tune Exploration Strategy: Review parameters to see if there is room for improvement.",
      "details": [
        "Maintain or increase exploration attempts to avoid a local minimum or slow down the process"
      ]
    },
    {
      "priority": "Low",
      "action": "Monitor Diversity: Keep a close eye on the diversity metrics over the next few iterations. If diversity continues to decrease drastically, consider increasing the exploration intensity in future iterations to encourage more diverse solutions.",
      "details": []
    }
  ]
}
```
2025-06-22 18:15:44,143 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-22 18:15:44,143 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 18:15:44,158 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 18:15:44,159 - __main__ - INFO - 实例 composite13_66 处理完成
