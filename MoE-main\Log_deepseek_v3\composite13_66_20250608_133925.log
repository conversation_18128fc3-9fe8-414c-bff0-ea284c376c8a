2025-06-08 13:39:25,140 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-08 13:39:25,142 - __main__ - INFO - 开始分析阶段
2025-06-08 13:39:25,142 - StatsExpert - INFO - 开始统计分析
2025-06-08 13:39:25,161 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10093.0, 'max': 114139.0, 'mean': 75975.3, 'std': 43392.33724069263}, 'diversity': 0.9127946127946128, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 13:39:25,162 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10093.0, 'max': 114139.0, 'mean': 75975.3, 'std': 43392.33724069263}, 'diversity_level': 0.9127946127946128, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 13:39:25,162 - PathExpert - INFO - 开始路径结构分析
2025-06-08 13:39:25,167 - PathExpert - INFO - 路径结构分析完成
2025-06-08 13:39:25,168 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (43, 48), 'frequency': 0.5, 'avg_cost': 14.0}], 'common_subpaths': [{'subpath': (13, 20, 21), 'frequency': 0.4}, {'subpath': (15, 22, 12), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (12, 17, 18), 'frequency': 0.3}, {'subpath': (17, 18, 16), 'frequency': 0.3}, {'subpath': (18, 16, 23), 'frequency': 0.3}, {'subpath': (16, 23, 13), 'frequency': 0.3}, {'subpath': (23, 13, 20), 'frequency': 0.3}, {'subpath': (20, 21, 19), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(16, 23)', 'frequency': 0.4}, {'edge': '(13, 20)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(45, 38)', 'frequency': 0.4}, {'edge': '(50, 41)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(22, 12)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(18, 16)', 'frequency': 0.3}, {'edge': '(23, 13)', 'frequency': 0.3}, {'edge': '(21, 19)', 'frequency': 0.3}, {'edge': '(19, 14)', 'frequency': 0.2}, {'edge': '(14, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(37, 25)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(36, 35)', 'frequency': 0.3}, {'edge': '(35, 28)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(34, 33)', 'frequency': 0.3}, {'edge': '(33, 31)', 'frequency': 0.3}, {'edge': '(31, 24)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.2}, {'edge': '(7, 1)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(11, 9)', 'frequency': 0.3}, {'edge': '(9, 5)', 'frequency': 0.2}, {'edge': '(5, 4)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(8, 2)', 'frequency': 0.2}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(10, 0)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.2}, {'edge': '(61, 53)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(62, 59)', 'frequency': 0.3}, {'edge': '(59, 56)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(64, 57)', 'frequency': 0.3}, {'edge': '(57, 54)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(65, 52)', 'frequency': 0.2}, {'edge': '(52, 63)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(51, 50)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.2}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.2}, {'edge': '(49, 47)', 'frequency': 0.2}, {'edge': '(47, 46)', 'frequency': 0.2}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(48, 43)', 'frequency': 0.3}, {'edge': '(43, 39)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(3, 1)', 'frequency': 0.2}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(49, 13)', 'frequency': 0.2}, {'edge': '(42, 39)', 'frequency': 0.2}, {'edge': '(7, 31)', 'frequency': 0.2}, {'edge': '(20, 51)', 'frequency': 0.2}, {'edge': '(65, 26)', 'frequency': 0.2}, {'edge': '(26, 48)', 'frequency': 0.2}, {'edge': '(44, 8)', 'frequency': 0.2}, {'edge': '(26, 31)', 'frequency': 0.2}, {'edge': '(30, 63)', 'frequency': 0.2}, {'edge': '(14, 18)', 'frequency': 0.2}, {'edge': '(24, 53)', 'frequency': 0.2}, {'edge': '(52, 45)', 'frequency': 0.2}, {'edge': '(38, 5)', 'frequency': 0.2}, {'edge': '(9, 40)', 'frequency': 0.2}, {'edge': '(37, 64)', 'frequency': 0.2}, {'edge': '(21, 44)', 'frequency': 0.2}, {'edge': '(42, 60)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(40, 39)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [63, 50, 53, 25, 60, 33, 56], 'cost': 17489.0, 'size': 7}, {'region': [55, 35, 48, 53, 37, 46, 7], 'cost': 15333.0, 'size': 7}, {'region': [27, 55, 51, 37, 64, 41], 'cost': 13591.0, 'size': 6}, {'region': [61, 33, 57, 42, 60], 'cost': 11680.0, 'size': 5}, {'region': [29, 61, 42, 60, 50], 'cost': 11497.0, 'size': 5}]}
2025-06-08 13:39:25,169 - EliteExpert - INFO - 开始精英解分析
2025-06-08 13:39:25,169 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-08 13:39:25,170 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-08 13:39:25,170 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 13:39:25,170 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 13:39:25,170 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=10093.0, Max=114139.0, Mean=75975.3, Std=43392.33724069263
- Diversity Level: 0.9127946127946128
- Convergence Level: 0.0
- Clustering Information: {"clusters": 8, "cluster_sizes": [3, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: [{"edge": [43, 48], "frequency": 0.5, "avg_cost": 14.0}]
- Common Subpaths: [{"subpath": [13, 20, 21], "frequency": 0.4}, {"subpath": [15, 22, 12], "frequency": 0.3}, {"subpath": [22, 12, 17], "frequency": 0.3}, {"subpath": [12, 17, 18], "frequency": 0.3}, {"subpath": [17, 18, 16], "frequency": 0.3}, {"subpath": [18, 16, 23], "frequency": 0.3}, {"subpath": [16, 23, 13], "frequency": 0.3}, {"subpath": [23, 13, 20], "frequency": 0.3}, {"subpath": [20, 21, 19], "frequency": 0.3}, {"subpath": [27, 37, 25], "frequency": 0.3}]
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [{"edge": "(16, 23)", "frequency": 0.4}, {"edge": "(13, 20)", "frequency": 0.4}, {"edge": "(20, 21)", "frequency": 0.4}, {"edge": "(45, 38)", "frequency": 0.4}, {"edge": "(50, 41)", "frequency": 0.4}], "low_frequency_edges": [{"edge": "(15, 22)", "frequency": 0.3}, {"edge": "(22, 12)", "frequency": 0.3}, {"edge": "(12, 17)", "frequency": 0.3}, {"edge": "(17, 18)", "frequency": 0.3}, {"edge": "(18, 16)", "frequency": 0.3}, {"edge": "(23, 13)", "frequency": 0.3}, {"edge": "(21, 19)", "frequency": 0.3}, {"edge": "(19, 14)", "frequency": 0.2}, {"edge": "(14, 27)", "frequency": 0.2}, {"edge": "(27, 37)", "frequency": 0.3}, {"edge": "(37, 25)", "frequency": 0.3}, {"edge": "(25, 26)", "frequency": 0.3}, {"edge": "(26, 36)", "frequency": 0.3}, {"edge": "(36, 35)", "frequency": 0.3}, {"edge": "(35, 28)", "frequency": 0.3}, {"edge": "(28, 30)", "frequency": 0.3}, {"edge": "(30, 34)", "frequency": 0.3}, {"edge": "(34, 33)", "frequency": 0.3}, {"edge": "(33, 31)", "frequency": 0.3}, {"edge": "(31, 24)", "frequency": 0.3}, {"edge": "(24, 29)", "frequency": 0.3}, {"edge": "(29, 32)", "frequency": 0.3}, {"edge": "(3, 7)", "frequency": 0.2}, {"edge": "(7, 1)", "frequency": 0.2}, {"edge": "(1, 11)", "frequency": 0.2}, {"edge": "(11, 9)", "frequency": 0.3}, {"edge": "(9, 5)", "frequency": 0.2}, {"edge": "(5, 4)", "frequency": 0.3}, {"edge": "(4, 8)", "frequency": 0.2}, {"edge": "(8, 2)", "frequency": 0.2}, {"edge": "(2, 6)", "frequency": 0.2}, {"edge": "(6, 10)", "frequency": 0.2}, {"edge": "(10, 0)", "frequency": 0.2}, {"edge": "(0, 55)", "frequency": 0.2}, {"edge": "(55, 61)", "frequency": 0.2}, {"edge": "(61, 53)", "frequency": 0.2}, {"edge": "(53, 62)", "frequency": 0.3}, {"edge": "(62, 59)", "frequency": 0.3}, {"edge": "(59, 56)", "frequency": 0.3}, {"edge": "(56, 58)", "frequency": 0.3}, {"edge": "(58, 60)", "frequency": 0.3}, {"edge": "(60, 64)", "frequency": 0.3}, {"edge": "(64, 57)", "frequency": 0.3}, {"edge": "(57, 54)", "frequency": 0.3}, {"edge": "(54, 65)", "frequency": 0.2}, {"edge": "(65, 52)", "frequency": 0.2}, {"edge": "(52, 63)", "frequency": 0.2}, {"edge": "(39, 44)", "frequency": 0.3}, {"edge": "(44, 45)", "frequency": 0.3}, {"edge": "(38, 51)", "frequency": 0.3}, {"edge": "(51, 50)", "frequency": 0.3}, {"edge": "(47, 49)", "frequency": 0.2}, {"edge": "(43, 48)", "frequency": 0.2}, {"edge": "(32, 40)", "frequency": 0.2}, {"edge": "(40, 49)", "frequency": 0.2}, {"edge": "(49, 47)", "frequency": 0.2}, {"edge": "(47, 46)", "frequency": 0.2}, {"edge": "(46, 48)", "frequency": 0.2}, {"edge": "(48, 43)", "frequency": 0.3}, {"edge": "(43, 39)", "frequency": 0.2}, {"edge": "(41, 42)", "frequency": 0.2}, {"edge": "(4, 6)", "frequency": 0.2}, {"edge": "(3, 1)", "frequency": 0.2}, {"edge": "(10, 15)", "frequency": 0.2}, {"edge": "(49, 13)", "frequency": 0.2}, {"edge": "(42, 39)", "frequency": 0.2}, {"edge": "(7, 31)", "frequency": 0.2}, {"edge": "(20, 51)", "frequency": 0.2}, {"edge": "(65, 26)", "frequency": 0.2}, {"edge": "(26, 48)", "frequency": 0.2}, {"edge": "(44, 8)", "frequency": 0.2}, {"edge": "(26, 31)", "frequency": 0.2}, {"edge": "(30, 63)", "frequency": 0.2}, {"edge": "(14, 18)", "frequency": 0.2}, {"edge": "(24, 53)", "frequency": 0.2}, {"edge": "(52, 45)", "frequency": 0.2}, {"edge": "(38, 5)", "frequency": 0.2}, {"edge": "(9, 40)", "frequency": 0.2}, {"edge": "(37, 64)", "frequency": 0.2}, {"edge": "(21, 44)", "frequency": 0.2}, {"edge": "(42, 60)", "frequency": 0.2}, {"edge": "(5, 55)", "frequency": 0.2}, {"edge": "(40, 39)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [63, 50, 53, 25, 60, 33, 56], "cost": 17489.0, "size": 7}, {"region": [55, 35, 48, 53, 37, 46, 7], "cost": 15333.0, "size": 7}, {"region": [27, 55, 51, 37, 64, 41], "cost": 13591.0, "size": 6}, {"region": [61, 33, 57, 42, 60], "cost": 11680.0, "size": 5}, {"region": [29, 61, 42, 60, 50], "cost": 11497.0, "size": 5}]

## Elite Solution Analysis
- Number of Elite Solutions: 0
- Common Features: {}
- Fixed Nodes: []
- Population Gap: {}
- Elite Diversity: {}

## Historical Trends
No historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

2025-06-08 13:39:34,925 - LandscapeExpert - INFO - LLM返回的分析结果: Okay, let's dive into this landscape analysis.

**1. Overall Search Space Characteristics**

Based on the data, we can infer the following about the search space:

*   **High Variability (Cost Variance):** The cost statistics reveal a significant spread in solution quality. The large standard deviation (43392.33724069263) indicates a highly rugged landscape with potentially many local optima and/or a considerable difference between the quality of solutions.
*   **Multi-Modality (Clustering):**  The clustering information ("clusters": 8, "cluster\_sizes": \[3, 1, 1, 1, 1, 1, 1, 1]) suggests a multi-modal landscape. This means there are likely multiple distinct regions of attraction, possibly corresponding to different "solution types" or structures, which are separated across the search space. The uneven cluster sizes indicate a skewed distribution, with one dominant cluster containing 3 solutions and several singletons.
*   **Diverse Population:** A diversity level of 0.913 is indicative of a very diverse population. This is expected due to the variance in cost. The diversity level could be an indication that the algorithm is exploring large areas of the search space.
*   **Lack of Convergence:** A convergence level of 0.0 suggests the population is not converging towards a single solution or region. This aligns with the high cost variance and indicates exploration is still active.

**2. Current Population State Assessment**

*   **Poor Solution Quality, but diverse:** The minimum cost (10093.0) is significantly lower than the mean (75975.3), but all the costs are fairly low (10093.0-114139.0). This suggests a very poor initial population. The wide cost range and high diversity, combined with the lack of convergence, indicates the algorithm is actively exploring.
*   **Path Structure:** Path structure analysis reveals:
    *   **Common subpaths:** The presence of several common subpaths (e.g., \[13, 20, 21], \[15, 22, 12]) suggests that the algorithm has discovered some building blocks or useful sequences of elements. The relatively high frequency (0.3-0.4) indicates these subpaths are often present in the population.
    *   **High Quality Edges:** The high quality edge \[43, 48] suggests it could be an important edge, i.e. a potential building block.
    *   **Edge Frequency Distribution:** The edge frequency distribution highlights a large number of low-frequency edges. These edges do not appear very often across the population. The edges in the medium frequency class, which are present in 40% of the solutions, may represent potential building blocks for better solutions.
*   **Elite Solution Deficiency:** The absence of elite solutions signifies that the search has not yet found any high-quality solutions and/or that the definition of "elite" is too strict for the current population. There is no information on which features the elite solutions have in common.

**3. Identification of Difficult Regions and Search Challenges**

*   **Ruggedness:** The high cost variance, coupled with a highly diverse population and lack of convergence suggest a highly rugged search space. The presence of multiple local optima likely makes it challenging for the algorithm to find good solutions.
*   **Difficult Regions (Low Quality Regions):** The low quality regions (e.g., \[63, 50, 53, 25, 60, 33, 56]) identified in the analysis are likely problematic areas where solutions tend to perform poorly. These areas warrant further investigation to understand what makes them difficult. One option would be to use a local search strategy to find better solutions for these specific regions.
*   **Exploration vs. Exploitation Balance:** The current state of the population suggests a strong emphasis on exploration. With such diversity and the lack of elite solutions, the algorithm is likely exploring a wide area of the search space but may not be effectively exploiting promising regions.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Common Subpaths:** The presence of common subpaths offers opportunities to leverage these discovered building blocks. The algorithm should be steered towards exploiting these subpaths more aggressively (e.g., by favoring them in mutation or crossover operations).
*   **Medium Frequency Edges:** These edges are present in several solutions. Focusing on these edges may help to improve the convergence.
*   **High Quality Edges:** Focusing on the edges \[43, 48] may help to find better solutions.
*   **Clustering Information:** The clustering information suggests the existence of distinct solution structures. It could be beneficial to analyze solutions within each cluster to identify unique features.

**5. Recommended Evolution Direction and Strategy Balance**

Based on this analysis, I recommend the following evolutionary direction and strategy balance:

*   **Increased Exploitation (But with Caution):**
    *   **Exploitation of Common Structures:** Introduce mechanisms to favor the formation of common subpaths, high quality edges, and medium frequency edges. For instance, in crossover, prioritize recombination that preserves or reinforces these structures.
    *   **Local Search (Potentially):** Consider incorporating a local search component to refine solutions in the vicinity of promising regions, and to improve solutions in the low quality regions. This could help jump out of local optima and converge more quickly.
*   **Maintain Diversity (Initially):**
    *   **Avoid Premature Convergence:** Although exploitation is important, over-emphasizing it could lead to premature convergence on a local optimum. Continue to maintain a moderate level of diversity by using appropriate mutation operators.
    *   **Adaptive Strategies:** Implement adaptive mechanisms to adjust the balance between exploration and exploitation. For example, decrease mutation rates and increase crossover rates if the population is converging to a small cost range. Increase mutation rates when convergence stalls, to encourage exploration.
*   **Population Management:**
    *   **Elite Strategy:** Re-evaluate the "elite" solution definition. It might be too restrictive given the current state. Consider reducing the criteria to facilitate selection of solutions with better costs.
    *   **Consider elitism (if not implemented):** Ensure that the best solutions from each generation are carried over to the next.
*   **Analysis and Monitoring:**
    *   **Track Building Blocks:** Continuously monitor the frequency of common subpaths and high quality edges to assess their impact.
    *   **Evaluate Region Quality:** Track the performance of solutions that traverse the identified low-quality regions to understand why they are problematic.

**In summary:** The current population is exploring a diverse and likely rugged landscape. The algorithm needs to exploit the information it has already discovered (common subpaths, edges, and building blocks) while remaining diverse enough to avoid getting trapped in local optima. A balance between exploration and exploitation, and continuous monitoring of solution features, is essential for successful evolution.

2025-06-08 13:39:34,925 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-08 13:39:34,925 - __main__ - INFO - 景观专家分析报告: Okay, let's dive into this landscape analysis.

**1. Overall Search Space Characteristics**

Based on the data, we can infer the following about the search space:

*   **High Variability (Cost Variance):** The cost statistics reveal a significant spread in solution quality. The large standard deviation (43392.33724069263) indicates a highly rugged landscape with potentially many local optima and/or a considerable difference between the quality of solutions.
*   **Multi-Modality (Clustering):**  The clustering information ("clusters": 8, "cluster\_sizes": \[3, 1, 1, 1, 1, 1, 1, 1]) suggests a multi-modal landscape. This means there are likely multiple distinct regions of attraction, possibly corresponding to different "solution types" or structures, which are separated across the search space. The uneven cluster sizes indicate a skewed distribution, with one dominant cluster containing 3 solutions and several singletons.
*   **Diverse Population:** A diversity level of 0.913 is indicative of a very diverse population. This is expected due to the variance in cost. The diversity level could be an indication that the algorithm is exploring large areas of the search space.
*   **Lack of Convergence:** A convergence level of 0.0 suggests the population is not converging towards a single solution or region. This aligns with the high cost variance and indicates exploration is still active.

**2. Current Population State Assessment**

*   **Poor Solution Quality, but diverse:** The minimum cost (10093.0) is significantly lower than the mean (75975.3), but all the costs are fairly low (10093.0-114139.0). This suggests a very poor initial population. The wide cost range and high diversity, combined with the lack of convergence, indicates the algorithm is actively exploring.
*   **Path Structure:** Path structure analysis reveals:
    *   **Common subpaths:** The presence of several common subpaths (e.g., \[13, 20, 21], \[15, 22, 12]) suggests that the algorithm has discovered some building blocks or useful sequences of elements. The relatively high frequency (0.3-0.4) indicates these subpaths are often present in the population.
    *   **High Quality Edges:** The high quality edge \[43, 48] suggests it could be an important edge, i.e. a potential building block.
    *   **Edge Frequency Distribution:** The edge frequency distribution highlights a large number of low-frequency edges. These edges do not appear very often across the population. The edges in the medium frequency class, which are present in 40% of the solutions, may represent potential building blocks for better solutions.
*   **Elite Solution Deficiency:** The absence of elite solutions signifies that the search has not yet found any high-quality solutions and/or that the definition of "elite" is too strict for the current population. There is no information on which features the elite solutions have in common.

**3. Identification of Difficult Regions and Search Challenges**

*   **Ruggedness:** The high cost variance, coupled with a highly diverse population and lack of convergence suggest a highly rugged search space. The presence of multiple local optima likely makes it challenging for the algorithm to find good solutions.
*   **Difficult Regions (Low Quality Regions):** The low quality regions (e.g., \[63, 50, 53, 25, 60, 33, 56]) identified in the analysis are likely problematic areas where solutions tend to perform poorly. These areas warrant further investigation to understand what makes them difficult. One option would be to use a local search strategy to find better solutions for these specific regions.
*   **Exploration vs. Exploitation Balance:** The current state of the population suggests a strong emphasis on exploration. With such diversity and the lack of elite solutions, the algorithm is likely exploring a wide area of the search space but may not be effectively exploiting promising regions.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Common Subpaths:** The presence of common subpaths offers opportunities to leverage these discovered building blocks. The algorithm should be steered towards exploiting these subpaths more aggressively (e.g., by favoring them in mutation or crossover operations).
*   **Medium Frequency Edges:** These edges are present in several solutions. Focusing on these edges may help to improve the convergence.
*   **High Quality Edges:** Focusing on the edges \[43, 48] may help to find better solutions.
*   **Clustering Information:** The clustering information suggests the existence of distinct solution structures. It could be beneficial to analyze solutions within each cluster to identify unique features.

**5. Recommended Evolution Direction and Strategy Balance**

Based on this analysis, I recommend the following evolutionary direction and strategy balance:

*   **Increased Exploitation (But with Caution):**
    *   **Exploitation of Common Structures:** Introduce mechanisms to favor the formation of common subpaths, high quality edges, and medium frequency edges. For instance, in crossover, prioritize recombination that preserves or reinforces these structures.
    *   **Local Search (Potentially):** Consider incorporating a local search component to refine solutions in the vicinity of promising regions, and to improve solutions in the low quality regions. This could help jump out of local optima and converge more quickly.
*   **Maintain Diversity (Initially):**
    *   **Avoid Premature Convergence:** Although exploitation is important, over-emphasizing it could lead to premature convergence on a local optimum. Continue to maintain a moderate level of diversity by using appropriate mutation operators.
    *   **Adaptive Strategies:** Implement adaptive mechanisms to adjust the balance between exploration and exploitation. For example, decrease mutation rates and increase crossover rates if the population is converging to a small cost range. Increase mutation rates when convergence stalls, to encourage exploration.
*   **Population Management:**
    *   **Elite Strategy:** Re-evaluate the "elite" solution definition. It might be too restrictive given the current state. Consider reducing the criteria to facilitate selection of solutions with better costs.
    *   **Consider elitism (if not implemented):** Ensure that the best solutions from each generation are carried over to the next.
*   **Analysis and Monitoring:**
    *   **Track Building Blocks:** Continuously monitor the frequency of common subpaths and high quality edges to assess their impact.
    *   **Evaluate Region Quality:** Track the performance of solutions that traverse the identified low-quality regions to understand why they are problematic.

**In summary:** The current population is exploring a diverse and likely rugged landscape. The algorithm needs to exploit the information it has already discovered (common subpaths, edges, and building blocks) while remaining diverse enough to avoid getting trapped in local optima. A balance between exploration and exploitation, and continuous monitoring of solution features, is essential for successful evolution.

2025-06-08 13:39:34,925 - __main__ - INFO - 分析阶段完成
2025-06-08 13:39:34,925 - __main__ - INFO - 景观分析完整报告: Okay, let's dive into this landscape analysis.

**1. Overall Search Space Characteristics**

Based on the data, we can infer the following about the search space:

*   **High Variability (Cost Variance):** The cost statistics reveal a significant spread in solution quality. The large standard deviation (43392.33724069263) indicates a highly rugged landscape with potentially many local optima and/or a considerable difference between the quality of solutions.
*   **Multi-Modality (Clustering):**  The clustering information ("clusters": 8, "cluster\_sizes": \[3, 1, 1, 1, 1, 1, 1, 1]) suggests a multi-modal landscape. This means there are likely multiple distinct regions of attraction, possibly corresponding to different "solution types" or structures, which are separated across the search space. The uneven cluster sizes indicate a skewed distribution, with one dominant cluster containing 3 solutions and several singletons.
*   **Diverse Population:** A diversity level of 0.913 is indicative of a very diverse population. This is expected due to the variance in cost. The diversity level could be an indication that the algorithm is exploring large areas of the search space.
*   **Lack of Convergence:** A convergence level of 0.0 suggests the population is not converging towards a single solution or region. This aligns with the high cost variance and indicates exploration is still active.

**2. Current Population State Assessment**

*   **Poor Solution Quality, but diverse:** The minimum cost (10093.0) is significantly lower than the mean (75975.3), but all the costs are fairly low (10093.0-114139.0). This suggests a very poor initial population. The wide cost range and high diversity, combined with the lack of convergence, indicates the algorithm is actively exploring.
*   **Path Structure:** Path structure analysis reveals:
    *   **Common subpaths:** The presence of several common subpaths (e.g., \[13, 20, 21], \[15, 22, 12]) suggests that the algorithm has discovered some building blocks or useful sequences of elements. The relatively high frequency (0.3-0.4) indicates these subpaths are often present in the population.
    *   **High Quality Edges:** The high quality edge \[43, 48] suggests it could be an important edge, i.e. a potential building block.
    *   **Edge Frequency Distribution:** The edge frequency distribution highlights a large number of low-frequency edges. These edges do not appear very often across the population. The edges in the medium frequency class, which are present in 40% of the solutions, may represent potential building blocks for better solutions.
*   **Elite Solution Deficiency:** The absence of elite solutions signifies that the search has not yet found any high-quality solutions and/or that the definition of "elite" is too strict for the current population. There is no information on which features the elite solutions have in common.

**3. Identification of Difficult Regions and Search Challenges**

*   **Ruggedness:** The high cost variance, coupled with a highly diverse population and lack of convergence suggest a highly rugged search space. The presence of multiple local optima likely makes it challenging for the algorithm to find good solutions.
*   **Difficult Regions (Low Quality Regions):** The low quality regions (e.g., \[63, 50, 53, 25, 60, 33, 56]) identified in the analysis are likely problematic areas where solutions tend to perform poorly. These areas warrant further investigation to understand what makes them difficult. One option would be to use a local search strategy to find better solutions for these specific regions.
*   **Exploration vs. Exploitation Balance:** The current state of the population suggests a strong emphasis on exploration. With such diversity and the lack of elite solutions, the algorithm is likely exploring a wide area of the search space but may not be effectively exploiting promising regions.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Common Subpaths:** The presence of common subpaths offers opportunities to leverage these discovered building blocks. The algorithm should be steered towards exploiting these subpaths more aggressively (e.g., by favoring them in mutation or crossover operations).
*   **Medium Frequency Edges:** These edges are present in several solutions. Focusing on these edges may help to improve the convergence.
*   **High Quality Edges:** Focusing on the edges \[43, 48] may help to find better solutions.
*   **Clustering Information:** The clustering information suggests the existence of distinct solution structures. It could be beneficial to analyze solutions within each cluster to identify unique features.

**5. Recommended Evolution Direction and Strategy Balance**

Based on this analysis, I recommend the following evolutionary direction and strategy balance:

*   **Increased Exploitation (But with Caution):**
    *   **Exploitation of Common Structures:** Introduce mechanisms to favor the formation of common subpaths, high quality edges, and medium frequency edges. For instance, in crossover, prioritize recombination that preserves or reinforces these structures.
    *   **Local Search (Potentially):** Consider incorporating a local search component to refine solutions in the vicinity of promising regions, and to improve solutions in the low quality regions. This could help jump out of local optima and converge more quickly.
*   **Maintain Diversity (Initially):**
    *   **Avoid Premature Convergence:** Although exploitation is important, over-emphasizing it could lead to premature convergence on a local optimum. Continue to maintain a moderate level of diversity by using appropriate mutation operators.
    *   **Adaptive Strategies:** Implement adaptive mechanisms to adjust the balance between exploration and exploitation. For example, decrease mutation rates and increase crossover rates if the population is converging to a small cost range. Increase mutation rates when convergence stalls, to encourage exploration.
*   **Population Management:**
    *   **Elite Strategy:** Re-evaluate the "elite" solution definition. It might be too restrictive given the current state. Consider reducing the criteria to facilitate selection of solutions with better costs.
    *   **Consider elitism (if not implemented):** Ensure that the best solutions from each generation are carried over to the next.
*   **Analysis and Monitoring:**
    *   **Track Building Blocks:** Continuously monitor the frequency of common subpaths and high quality edges to assess their impact.
    *   **Evaluate Region Quality:** Track the performance of solutions that traverse the identified low-quality regions to understand why they are problematic.

**In summary:** The current population is exploring a diverse and likely rugged landscape. The algorithm needs to exploit the information it has already discovered (common subpaths, edges, and building blocks) while remaining diverse enough to avoid getting trapped in local optima. A balance between exploration and exploitation, and continuous monitoring of solution features, is essential for successful evolution.

2025-06-08 13:39:34,925 - __main__ - INFO - 开始策略分配阶段
2025-06-08 13:39:34,925 - StrategyExpert - INFO - 开始策略分配分析
2025-06-08 13:39:34,925 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
# Strategy Selection Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization. You excel at analyzing search space characteristics and population states to select the optimal balance between exploration and exploitation, and to assign appropriate strategies to different individuals.

## Current Iteration: 0

## Population Information
[{"id": 0, "cost": 10097.0, "diversity_contribution": null}, {"id": 1, "cost": 10304.0, "diversity_contribution": null}, {"id": 2, "cost": 10093.0, "diversity_contribution": null}, {"id": 3, "cost": 98789.0, "diversity_contribution": null}, {"id": 4, "cost": 112114.0, "diversity_contribution": null}, {"id": 5, "cost": 103719.0, "diversity_contribution": null}, {"id": 6, "cost": 99300.0, "diversity_contribution": null}, {"id": 7, "cost": 114139.0, "diversity_contribution": null}, {"id": 8, "cost": 104236.0, "diversity_contribution": null}, {"id": 9, "cost": 96962.0, "diversity_contribution": null}]

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Population State: Population status extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Recommended Evolution Direction: Suggestions on evolutionary direction extracted from landscape analysis

## Previous Feedback (if available)
None

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data comes from the landscape analysis expert's analysis results and the previous generation's assessment feedback. We need you to determine the optimal exploration/exploitation balance for the current iteration and assign appropriate strategies to each individual.

# Strategy Selection Request
Please determine the optimal strategy allocation plan for the current iteration. Please follow these steps in your thinking:

## Step 1: Analyze Current Search State
- Evaluate search space characteristics (ruggedness, modality, deceptiveness, etc.)
- Analyze the current state of the population (exploration phase, diversity, convergence, etc.)
- Consider feedback and effects from the previous generation (if available)
- Identify the main challenges and opportunities currently faced

## Step 2: Determine Global Exploration Ratio
- Based on search space characteristics, determine the base exploration ratio
- Adjust the exploration ratio based on population state
- Consider the impact of evolutionary stage on exploration needs
- Analyze the effects of the previous generation's strategy to further adjust the ratio
- Determine the final global exploration ratio (between 0.0 and 1.0)

## Step 3: Build Strategy Profiles for Individuals
- Analyze the characteristics of each individual (quality, diversity contribution, historical performance, etc.)
- Evaluate the degree to which each individual is suitable for exploration or exploitation
- Consider the relationship between individuals and difficult/opportunity regions
- Build strategy tendency profiles for each individual

## Step 4: Assign Individual Strategies
- Based on the global exploration ratio, determine the number of individuals to assign exploration strategies
- Prioritize assigning exploration strategies to individuals most suitable for exploration
- Assign exploitation strategies to the remaining individuals
- Ensure strategy allocation complies with the global exploration ratio
- Record the rationale for each allocation decision

## Step 5: Determine Strategy Parameters
- Based on search space characteristics and population state, determine exploration strategy parameters
  - Diversity weight: controls the diversity level of generated solutions
  - Knowledge usage weight: controls the degree of historical knowledge utilization
  - Risk tolerance: controls the possibility of accepting suboptimal solutions
- Based on search space characteristics and population state, determine exploitation strategy parameters
  - Local search depth: controls the depth of local search
  - Quality edge usage: controls the degree of utilizing known high-quality edges
  - Elite influence: controls the influence of elite solutions on new solution generation

Please return your analysis results in JSON format with the following sections:
```json
{
  "global_explore_ratio": float,
  "justification": string,
  "individual_assignments": {
    "individual_id": "explore|exploit",
    ...
  },
  "assignment_rationale": {
    "individual_id": string,
    ...
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": float,
      "knowledge_usage_weight": float,
      "risk_tolerance": float
    },
    "exploit": {
      "local_search_depth": int,
      "quality_edge_usage": float,
      "elite_influence": float
    }
  }
}
```

Additionally, please provide a brief summary explaining your strategy selection logic and its expected effects.

2025-06-08 13:39:34,925 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-08 13:39:39,962 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The population shows early signs of convergence, with some individuals performing significantly better than others. However, the cost range suggests that the search space has not been thoroughly explored, as there is a clear distinction between better and worse solutions. It is important to maintain some exploration to prevent premature convergence and discover potentially superior solutions. Therefore, a moderate exploration ratio is selected. ",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Relatively good cost. Prioritizing exploitation.",
    "1": "Poor cost. Prioritizing exploitation to see if small modifications will lead to improvements. ",
    "2": "Relatively good cost. Prioritizing exploitation.",
    "3": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "4": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "5": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "6": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "7": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "8": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "9": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.2
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy adopts a mixed exploration-exploitation approach. The global exploration ratio is set to 30% to balance the need for diversity with the need to refine promising solutions.  Individuals with better costs are assigned exploitation strategies to refine their solutions through local search. Individuals with poorer costs are assigned to exploration strategies. This is implemented by varying diversity and risk parameters. The intent is to maintain population diversity by encouraging individuals to explore new regions, while also leveraging the information from better solutions.  The exploration strategy will use a higher diversity weight to encourage exploration and a moderate risk tolerance to accept potentially suboptimal solutions, but allowing for potential discovery of better locations. The exploitation strategy will utilize a moderate local search depth and favor information from good solutions to improve solution quality quickly. The combined effect should prevent premature convergence, allow for improved exploration, and drive the population towards higher-quality solutions.

2025-06-08 13:39:39,962 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 13:39:39,962 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The population shows early signs of convergence, with some individuals performing significantly better than others. However, the cost range suggests that the search space has not been thoroughly explored, as there is a clear distinction between better and worse solutions. It is important to maintain some exploration to prevent premature convergence and discover potentially superior solutions. Therefore, a moderate exploration ratio is selected. ",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Relatively good cost. Prioritizing exploitation.",
    "1": "Poor cost. Prioritizing exploitation to see if small modifications will lead to improvements. ",
    "2": "Relatively good cost. Prioritizing exploitation.",
    "3": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "4": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "5": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "6": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "7": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "8": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "9": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.2
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy adopts a mixed exploration-exploitation approach. The global exploration ratio is set to 30% to balance the need for diversity with the need to refine promising solutions.  Individuals with better costs are assigned exploitation strategies to refine their solutions through local search. Individuals with poorer costs are assigned to exploration strategies. This is implemented by varying diversity and risk parameters. The intent is to maintain population diversity by encouraging individuals to explore new regions, while also leveraging the information from better solutions.  The exploration strategy will use a higher diversity weight to encourage exploration and a moderate risk tolerance to accept potentially suboptimal solutions, but allowing for potential discovery of better locations. The exploitation strategy will utilize a moderate local search depth and favor information from good solutions to improve solution quality quickly. The combined effect should prevent premature convergence, allow for improved exploration, and drive the population towards higher-quality solutions.

2025-06-08 13:39:39,962 - __main__ - INFO - 策略分配阶段完成
2025-06-08 13:39:39,962 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 13:39:39,962 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The population shows early signs of convergence, with some individuals performing significantly better than others. However, the cost range suggests that the search space has not been thoroughly explored, as there is a clear distinction between better and worse solutions. It is important to maintain some exploration to prevent premature convergence and discover potentially superior solutions. Therefore, a moderate exploration ratio is selected. ",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Relatively good cost. Prioritizing exploitation.",
    "1": "Poor cost. Prioritizing exploitation to see if small modifications will lead to improvements. ",
    "2": "Relatively good cost. Prioritizing exploitation.",
    "3": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "4": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "5": "High Cost. This individual is assigned an exploration strategy to explore different regions of the search space. ",
    "6": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "7": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "8": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements.",
    "9": "High cost. Prioritizing exploitation to see if small modifications will lead to improvements."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.2
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy adopts a mixed exploration-exploitation approach. The global exploration ratio is set to 30% to balance the need for diversity with the need to refine promising solutions.  Individuals with better costs are assigned exploitation strategies to refine their solutions through local search. Individuals with poorer costs are assigned to exploration strategies. This is implemented by varying diversity and risk parameters. The intent is to maintain population diversity by encouraging individuals to explore new regions, while also leveraging the information from better solutions.  The exploration strategy will use a higher diversity weight to encourage exploration and a moderate risk tolerance to accept potentially suboptimal solutions, but allowing for potential discovery of better locations. The exploitation strategy will utilize a moderate local search depth and favor information from good solutions to improve solution quality quickly. The combined effect should prevent premature convergence, allow for improved exploration, and drive the population towards higher-quality solutions.

2025-06-08 13:39:39,962 - __main__ - INFO - 开始进化阶段
2025-06-08 13:39:39,962 - __main__ - INFO - 为个体 0 生成利用路径
2025-06-08 13:39:39,962 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:39:39,962 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:39:39,962 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 102304.0
2025-06-08 13:39:42,785 - ExploitationExpert - INFO - res_population_num: 1
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - res_population_costs: [9922]
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64)]
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': [3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10304.0}, {'tour': [63, 52, 65, 53, 62, 59, 56, 58, 60, 64, 57, 54, 61, 55, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10093.0}, {'tour': [48, 46, 40, 38, 11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 65, 26], 'cur_cost': 98789.0}, {'tour': [44, 8, 4, 46, 16, 45, 38, 62, 25, 51, 32, 23, 42, 24, 27, 52, 15, 40, 5, 43, 60, 17, 6, 35, 9, 0, 59, 2, 53, 41, 34, 13, 57, 14, 37, 47, 55, 3, 21, 48, 28, 20, 7, 29, 1, 49, 56, 22, 18, 50, 64, 58, 10, 61, 39, 65, 26, 31, 36, 11, 54, 19, 30, 63, 12, 33], 'cur_cost': 112114.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': [13, 26, 31, 37, 36, 3, 19, 46, 35, 65, 6, 34, 25, 38, 47, 54, 8, 58, 29, 64, 55, 28, 5, 0, 14, 51, 22, 49, 10, 1, 41, 18, 12, 24, 53, 39, 50, 2, 40, 20, 52, 45, 32, 30, 21, 44, 48, 43, 7, 16, 27, 17, 62, 11, 61, 33, 57, 42, 60, 9, 23, 4, 59, 15, 63, 56], 'cur_cost': 99300.0}, {'tour': [65, 54, 26, 20, 32, 28, 62, 44, 24, 1, 33, 19, 29, 61, 42, 60, 50, 41, 11, 10, 21, 25, 9, 34, 56, 18, 45, 6, 4, 2, 31, 59, 27, 36, 52, 38, 5, 55, 35, 48, 53, 37, 46, 7, 23, 64, 17, 12, 57, 22, 8, 14, 16, 3, 51, 0, 43, 40, 39, 13, 30, 63, 58, 15, 47, 49], 'cur_cost': 114139.0}, {'tour': [46, 29, 23, 9, 40, 39, 49, 13, 20, 21, 38, 54, 4, 5, 55, 27, 32, 22, 47, 43, 59, 52, 44, 8, 37, 64, 42, 10, 18, 17, 16, 26, 48, 12, 7, 57, 51, 14, 28, 65, 36, 0, 1, 62, 63, 50, 53, 25, 60, 33, 56, 3, 15, 24, 30, 11, 41, 31, 35, 58, 45, 19, 6, 2, 61, 34], 'cur_cost': 104236.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - 局部搜索耗时: 2.82秒
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-08 13:39:42,786 - __main__ - WARNING - 个体 0 的利用路径生成失败，保留原个体
2025-06-08 13:39:42,786 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:39:42,786 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 105840.0
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - res_population_num: 2
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - res_population_costs: [9922, 9551]
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': [63, 52, 65, 53, 62, 59, 56, 58, 60, 64, 57, 54, 61, 55, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10093.0}, {'tour': [48, 46, 40, 38, 11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 65, 26], 'cur_cost': 98789.0}, {'tour': [44, 8, 4, 46, 16, 45, 38, 62, 25, 51, 32, 23, 42, 24, 27, 52, 15, 40, 5, 43, 60, 17, 6, 35, 9, 0, 59, 2, 53, 41, 34, 13, 57, 14, 37, 47, 55, 3, 21, 48, 28, 20, 7, 29, 1, 49, 56, 22, 18, 50, 64, 58, 10, 61, 39, 65, 26, 31, 36, 11, 54, 19, 30, 63, 12, 33], 'cur_cost': 112114.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': [13, 26, 31, 37, 36, 3, 19, 46, 35, 65, 6, 34, 25, 38, 47, 54, 8, 58, 29, 64, 55, 28, 5, 0, 14, 51, 22, 49, 10, 1, 41, 18, 12, 24, 53, 39, 50, 2, 40, 20, 52, 45, 32, 30, 21, 44, 48, 43, 7, 16, 27, 17, 62, 11, 61, 33, 57, 42, 60, 9, 23, 4, 59, 15, 63, 56], 'cur_cost': 99300.0}, {'tour': [65, 54, 26, 20, 32, 28, 62, 44, 24, 1, 33, 19, 29, 61, 42, 60, 50, 41, 11, 10, 21, 25, 9, 34, 56, 18, 45, 6, 4, 2, 31, 59, 27, 36, 52, 38, 5, 55, 35, 48, 53, 37, 46, 7, 23, 64, 17, 12, 57, 22, 8, 14, 16, 3, 51, 0, 43, 40, 39, 13, 30, 63, 58, 15, 47, 49], 'cur_cost': 114139.0}, {'tour': [46, 29, 23, 9, 40, 39, 49, 13, 20, 21, 38, 54, 4, 5, 55, 27, 32, 22, 47, 43, 59, 52, 44, 8, 37, 64, 42, 10, 18, 17, 16, 26, 48, 12, 7, 57, 51, 14, 28, 65, 36, 0, 1, 62, 63, 50, 53, 25, 60, 33, 56, 3, 15, 24, 30, 11, 41, 31, 35, 58, 45, 19, 6, 2, 61, 34], 'cur_cost': 104236.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - 局部搜索耗时: 1.01秒
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-08 13:39:43,800 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-08 13:39:43,800 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:39:43,800 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:39:43,804 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 103244.0
2025-06-08 13:39:43,981 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,122 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,131 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,139 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,142 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:39:44,151 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,156 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,159 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,163 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:39:44,165 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,168 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,180 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,181 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,183 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,185 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:39:44,191 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,193 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,195 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,201 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:39:44,202 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,205 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,206 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:39:44,207 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:39:44,207 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:39:44,213 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:39:44,221 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,225 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,225 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,228 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,240 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:39:44,244 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:39:44,247 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,251 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:39:44,257 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 13:39:44,262 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,270 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:39:44,273 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,274 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,275 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:39:44,276 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,277 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,280 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,281 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,283 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:39:44,286 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,287 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:39:44,290 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,291 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:39:44,292 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:39:44,293 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,294 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,294 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:39:44,294 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,299 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,300 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,303 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,304 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:39:44,304 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:39:44,306 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:39:44,308 - ExploitationExpert - INFO - res_population_num: 6
2025-06-08 13:39:44,308 - ExploitationExpert - INFO - res_population_costs: [9922, 9551, 9529, 9521, 9521, 9521]
2025-06-08 13:39:44,308 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-08 13:39:44,309 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:39:44,309 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': array([ 4, 17, 41, 31, 52, 29,  1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55,
       12, 49, 60, 15, 25, 40,  9, 59,  2, 64,  7, 44, 21, 43, 33, 30, 51,
       27, 62, 34, 63, 61, 65, 54, 42, 19,  5, 23, 14, 28, 50, 37,  0, 39,
       20, 24, 36,  8, 46, 47,  3, 11, 10, 16, 13, 45,  6, 57, 56]), 'cur_cost': 103244.0}, {'tour': [48, 46, 40, 38, 11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 65, 26], 'cur_cost': 98789.0}, {'tour': [44, 8, 4, 46, 16, 45, 38, 62, 25, 51, 32, 23, 42, 24, 27, 52, 15, 40, 5, 43, 60, 17, 6, 35, 9, 0, 59, 2, 53, 41, 34, 13, 57, 14, 37, 47, 55, 3, 21, 48, 28, 20, 7, 29, 1, 49, 56, 22, 18, 50, 64, 58, 10, 61, 39, 65, 26, 31, 36, 11, 54, 19, 30, 63, 12, 33], 'cur_cost': 112114.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': [13, 26, 31, 37, 36, 3, 19, 46, 35, 65, 6, 34, 25, 38, 47, 54, 8, 58, 29, 64, 55, 28, 5, 0, 14, 51, 22, 49, 10, 1, 41, 18, 12, 24, 53, 39, 50, 2, 40, 20, 52, 45, 32, 30, 21, 44, 48, 43, 7, 16, 27, 17, 62, 11, 61, 33, 57, 42, 60, 9, 23, 4, 59, 15, 63, 56], 'cur_cost': 99300.0}, {'tour': [65, 54, 26, 20, 32, 28, 62, 44, 24, 1, 33, 19, 29, 61, 42, 60, 50, 41, 11, 10, 21, 25, 9, 34, 56, 18, 45, 6, 4, 2, 31, 59, 27, 36, 52, 38, 5, 55, 35, 48, 53, 37, 46, 7, 23, 64, 17, 12, 57, 22, 8, 14, 16, 3, 51, 0, 43, 40, 39, 13, 30, 63, 58, 15, 47, 49], 'cur_cost': 114139.0}, {'tour': [46, 29, 23, 9, 40, 39, 49, 13, 20, 21, 38, 54, 4, 5, 55, 27, 32, 22, 47, 43, 59, 52, 44, 8, 37, 64, 42, 10, 18, 17, 16, 26, 48, 12, 7, 57, 51, 14, 28, 65, 36, 0, 1, 62, 63, 50, 53, 25, 60, 33, 56, 3, 15, 24, 30, 11, 41, 31, 35, 58, 45, 19, 6, 2, 61, 34], 'cur_cost': 104236.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:39:44,309 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-08 13:39:44,309 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-08 13:39:44,309 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-08 13:39:44,309 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-08 13:39:44,309 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:39:44,324 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[48, 46, 40, 38, 11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 65, 26]

## Path Cost: 98789.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9696969696969698

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:39:44,324 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:39:50,230 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46],
  "modification_strategy": "Large-scale restructuring with focus on regional shuffling and re-ordering.",
  "targeted_regions": ["Early part of the path (nodes 0-20)", "Central part of the path (nodes 20-40)", "Late part of the path (nodes 40-65)"],
  "avoided_regions": ["High-cost edge combinations, if identifiable from landscape analysis (not available in this prompt, but would be considered in a real-world scenario)"],
  "knowledge_application": "No knowledge was retrieved, therefore none applied.",
  "diversity_contribution": "High. The new path prioritizes re-ordering large segments of the existing path, ensuring substantial deviation from the current path. This promotes increased population diversity.",
  "expected_benefits": "The new path aims to explore significantly different areas of the search space. By reshuffling the path structure, it is designed to discover new solution patterns and avoid potential local optima. This helps to break free from any bias in the current individual. The resulting increased diversity will benefit the overall performance of the evolutionary algorithm."
}
```

**Explanation of Strategy and Expected Effects:**

The core of this exploration strategy is a large-scale restructuring of the existing path. The diversity weight is high (0.7), so the new path focuses on significant changes. Since no specific landscape analysis or knowledge retrieval was provided, I prioritize reshuffling larger segments of the current path to encourage exploration of a different search space.

**Step-by-Step Breakdown:**

1.  **Analyze Current Path:** The current path is treated as a starting point, but its specific features and weaknesses are unknown (due to a lack of analysis data in the prompt). The strategy assumes the need for a broad exploration to increase diversity, which is suitable given the constraints.

2.  **Determine Exploration Strategy:**  With a high diversity weight (0.7), the chosen strategy is a large-scale restructuring using the following logic: The existing path is dissected into multiple segments, and those segments are re-ordered.

3.  **Select Target Regions:**  Without explicit opportunity regions, the path is broken into early, middle, and late sections to be reordered. This provides a broad exploration.

4.  **Apply Historical Knowledge:** Because no historical knowledge was available, no knowledge was applied.

5.  **Generate New Path:**  The new path is created by randomly re-ordering different sections of the existing path and rearranging some of the individual nodes to avoid potentially high-cost combinations. This method generates a path substantially different from the original path, maximizing the diversity contribution. The process, while not strictly optimal without landscape details, fulfills the goal of broad exploration and diversity enhancement.

2025-06-08 13:39:50,231 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:39:50,231 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:39:50,231 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:39:50,231 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100456.0, 路径: [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46]
2025-06-08 13:39:50,231 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_path': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'modification_strategy': 'Large-scale restructuring with focus on regional shuffling and re-ordering.', 'targeted_regions': ['Early part of the path (nodes 0-20)', 'Central part of the path (nodes 20-40)', 'Late part of the path (nodes 40-65)'], 'avoided_regions': ['High-cost edge combinations, if identifiable from landscape analysis (not available in this prompt, but would be considered in a real-world scenario)'], 'knowledge_application': 'No knowledge was retrieved, therefore none applied.', 'diversity_contribution': 'High. The new path prioritizes re-ordering large segments of the existing path, ensuring substantial deviation from the current path. This promotes increased population diversity.', 'expected_benefits': 'The new path aims to explore significantly different areas of the search space. By reshuffling the path structure, it is designed to discover new solution patterns and avoid potential local optima. This helps to break free from any bias in the current individual. The resulting increased diversity will benefit the overall performance of the evolutionary algorithm.', 'new_tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}
2025-06-08 13:39:50,231 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-08 13:39:50,234 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:39:50,247 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[44, 8, 4, 46, 16, 45, 38, 62, 25, 51, 32, 23, 42, 24, 27, 52, 15, 40, 5, 43, 60, 17, 6, 35, 9, 0, 59, 2, 53, 41, 34, 13, 57, 14, 37, 47, 55, 3, 21, 48, 28, 20, 7, 29, 1, 49, 56, 22, 18, 50, 64, 58, 10, 61, 39, 65, 26, 31, 36, 11, 54, 19, 30, 63, 12, 33]

## Path Cost: 112114.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9696969696969698

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:39:50,247 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:39:55,752 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19],
  "modification_strategy": "Large-scale restructuring with regional refinement.  We prioritize a significant reshuffling of the existing path while also considering specific regions for improved exploration.",
  "targeted_regions": ["Under-explored regions as suggested by landscape analysis (details unavailable, but assumed to be regions far from current solution), focus on the latter half of the original path's nodes."],
  "avoided_regions": ["Difficult regions as defined by landscape analysis (details unavailable, but assumed to be regions where current solutions are trapped), avoiding regions that were heavily visited in the original path's traversal."],
  "knowledge_application": "No knowledge was provided, so no explicit knowledge application. The path aims for general exploration guided by a diversity-focused strategy.",
  "diversity_contribution": "High. The new path is designed to be significantly different from the original path, aiming for a high degree of novelty and exploration in previously unvisited areas of the search space.",
  "expected_benefits": "Increased population diversity, potential discovery of better solutions by exploring different solution structures and opening exploration to previously missed regions. The reshuffling seeks to quickly assess different structural configurations in the search space."
}
```

**Explanation:**

The exploration strategy aims for a balance between diversity and general area exploration.  Given the provided weights, we prioritize a significant degree of mutation (diversity weight = 0.7).  Since no knowledge was available, the strategy relies on general exploration principles.

*   **Step 1 (Analysis):** The existing path is complex, and we assume it has inherent weaknesses that limit it to the local optima. We want to "break free" of these weaknesses and introduce new combinations.
*   **Step 2 (Exploration Strategy):** The strategy is large-scale restructuring, meaning a significant reordering of the nodes.
*   **Step 3 (Target Regions):** We target under-explored regions. We assume the landscape analysis has highlighted potentially fruitful areas and difficult areas.  The restructuring aims to broadly explore areas that haven't been touched.
*   **Step 4 (Knowledge Application):** No knowledge to apply, so we default to the default strategy.
*   **Step 5 (Generate New Path):** The new path is a substantially different permutation of the original path. This is done through a shuffle. The focus is to explore different structural configurations quickly, increasing the diversity and covering the search area.

2025-06-08 13:39:55,752 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:39:55,752 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:39:55,752 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:39:55,752 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112339.0, 路径: [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19]
2025-06-08 13:39:55,752 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_path': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'modification_strategy': 'Large-scale restructuring with regional refinement.  We prioritize a significant reshuffling of the existing path while also considering specific regions for improved exploration.', 'targeted_regions': ["Under-explored regions as suggested by landscape analysis (details unavailable, but assumed to be regions far from current solution), focus on the latter half of the original path's nodes."], 'avoided_regions': ["Difficult regions as defined by landscape analysis (details unavailable, but assumed to be regions where current solutions are trapped), avoiding regions that were heavily visited in the original path's traversal."], 'knowledge_application': 'No knowledge was provided, so no explicit knowledge application. The path aims for general exploration guided by a diversity-focused strategy.', 'diversity_contribution': 'High. The new path is designed to be significantly different from the original path, aiming for a high degree of novelty and exploration in previously unvisited areas of the search space.', 'expected_benefits': 'Increased population diversity, potential discovery of better solutions by exploring different solution structures and opening exploration to previously missed regions. The reshuffling seeks to quickly assess different structural configurations in the search space.', 'new_tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}
2025-06-08 13:39:55,755 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-08 13:39:55,755 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:39:55,770 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44]

## Path Cost: 103719.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9693602693602694

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:39:55,770 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:40:04,227 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [44, 2, 25, 53, 51, 20, 17, 32, 10, 4, 14, 37, 52, 24, 59, 33, 38, 6, 15, 61, 27, 39, 29, 48, 13, 36, 31, 18, 30, 62, 28, 5, 9, 1, 21, 41, 40, 23, 12, 42, 46, 7, 19, 63, 56, 43, 8, 60, 3, 47, 57, 50, 54, 64, 58, 26, 35, 55, 45, 16, 22, 49, 34, 0, 11],
  "modification_strategy": "Large-scale restructuring with regional refinement.  A combination of random segment swaps and targeted mutations within selected sub-paths.",
  "targeted_regions": ["Opportunity Regions (Assumed to be distinct clusters based on general TSP knowledge).  Targeted sub-path re-ordering and refinement within the current path's structure to visit potential clusters.", "Regions with low coverage (based on inferred spatial distances not present in the landscape analysis)],
  "avoided_regions": ["Difficult Regions (assumed to be high-cost areas where travel is very expensive).  The strategy avoids these indirectly by using segment swapping that has a high chance of excluding such problematic edges."],
  "knowledge_application": "No knowledge retrieved, but a generic strategy of using segment swaps to re-order the path and explore different cluster-based structures is used, which is common in TSP solvers when no problem-specific knowledge is available.",
  "diversity_contribution": "High. The restructuring strategy significantly alters the path's structure, promoting exploration of new solution spaces. The segment swap approach can provide considerable diversity benefits in TSP solutions by reordering node visits.",
  "expected_benefits": "Increased diversity, potentially improved solution quality by discovering new tour structures.  The segment-swap strategy can allow for faster navigation of the space while searching for better combinations of sub-paths.  By reordering the nodes the algorithm can jump around to look at different areas of the problem space more rapidly, which aids in diversification. Improved overall population diversity, potentially leading to the discovery of superior solutions."
}
```

**Explanation of Exploration Strategy and Expected Effects:**

The exploration strategy employs a large-scale restructuring approach to maximize diversity, while also considering the limited knowledge available.  Given the high diversity weight (0.7), the primary goal is to generate a significantly different path. The absence of retrieved knowledge necessitates relying on general TSP problem-solving techniques.

1.  **Step 1: Analyze Current Path:**  The current path is a complete tour through all nodes. The strengths of the current path are unknown without landscape information. The weakness is that it's one specific path. The goal is to explore new path structures.

2.  **Step 2: Determine Exploration Strategy:**  The high diversity weight (0.7) dictates a more aggressive approach. The strategy combines large-scale restructuring (segment swaps) with some regional refinement to increase diversity and explore new solution structures. The risk tolerance of 0.6 supports this aggressive restructuring strategy.

3.  **Step 3: Select Target Regions:**  Without explicit information on opportunity and difficult regions, a generalized approach is taken. "Opportunity Regions" are inferred as areas with potentially beneficial clusterings of nodes. "Regions with low coverage" are inferred based on the assumption that the current path has "covered" areas in its initial path and those regions are visited more often. The path avoids difficult regions by not targeting regions with expensive edges.  The segment-swap strategy reduces the likelihood of including problematic edges.

4.  **Step 4: Apply Historical Knowledge:**  No specific knowledge was retrieved. However, the use of segment swaps, a common technique in TSP, leverages a general strategy of reordering the path to explore different tour structures.

5.  **Step 5: Generate New Path:** The new path is generated using a combination of:
    *   **Segment Swaps:** Randomly select segments (sub-paths) within the original path and swap their positions. This is done to significantly alter the overall structure and explore different tour configurations. The starting point of the swap is designed to be distributed through the current path to increase the chance of covering all parts.
    *   **Regional Refinement (Implicit):** While the strategy is primarily restructuring, the selection of segment start and end points implicitly allows for exploring regions.

The expected effects are:

*   **High Diversity:**  The segment swap strategy will significantly alter the path's structure.
*   **Potentially Improved Solution Quality:** Exploring different path structures can lead to discovering improved tours.
*   **Enhanced Population Diversity:** This will provide a wider search coverage for the evolutionary algorithm.

2025-06-08 13:40:04,228 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:40:04,228 - IdeaExtractor - ERROR - 提取探索路径时出错: Invalid control character at: line 4 column 328 (char 766)
2025-06-08 13:40:04,228 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:40:04,228 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103719.0, 路径: [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44]
2025-06-08 13:40:04,228 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}
2025-06-08 13:40:04,228 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-08 13:40:04,228 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:40:04,228 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:40:04,228 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 122048.0
2025-06-08 13:40:04,232 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,247 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,254 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,258 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,261 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:40:04,264 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,270 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,285 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,287 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:04,291 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,298 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,311 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,318 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,322 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,323 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:04,332 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,334 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,341 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,350 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,355 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,362 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,369 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:04,376 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,378 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,380 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,381 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,382 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,383 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,386 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,388 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,389 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,394 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:04,395 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,397 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,398 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,398 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,399 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,402 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,403 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,405 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,406 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 13:40:04,407 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,408 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,408 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,410 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,411 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,412 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,414 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,414 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,415 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,417 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,418 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,420 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,420 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,421 - root - INFO - 拓扑感知扰动用时: 0.0016秒，使用策略: pattern_based
2025-06-08 13:40:04,421 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,422 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,423 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,424 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,425 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,426 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:04,428 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,434 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,435 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:04,436 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,437 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,438 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,438 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,438 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,442 - root - INFO - 拓扑感知扰动用时: 0.0039秒，使用策略: pattern_based
2025-06-08 13:40:04,443 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,443 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,445 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,446 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,447 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,449 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,450 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,452 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,452 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,453 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,456 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,457 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,460 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,462 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,465 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,468 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 13:40:04,470 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,472 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,473 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,475 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,477 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,479 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,481 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,481 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,482 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,484 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,487 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,487 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,488 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,489 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,492 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,493 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,493 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,495 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,496 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,498 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:04,499 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,500 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,502 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,504 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,505 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,505 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,509 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,510 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,512 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,515 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,516 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,517 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,521 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:04,521 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,522 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,525 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,526 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,527 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,529 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,529 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,532 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:04,533 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,533 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,534 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,535 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: segment_preservation
2025-06-08 13:40:04,535 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,537 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,537 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,537 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,538 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,540 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,540 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,540 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,543 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,548 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,549 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,550 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,552 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,553 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,554 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,554 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,555 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:04,556 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,558 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,559 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,560 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,561 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,563 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,564 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,565 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,566 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:04,567 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,568 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 13:40:04,569 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: critical_edge
2025-06-08 13:40:04,572 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,574 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,575 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,577 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,578 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,580 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,580 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,583 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:04,584 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,585 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,589 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,589 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,590 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,591 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,592 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,593 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,595 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,595 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,596 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,597 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,601 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,606 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,607 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,609 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,609 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,610 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,611 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,613 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,614 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:04,615 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:40:04,616 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,617 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:40:04,618 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,620 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,622 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,623 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,625 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:04,626 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,630 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,633 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,635 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,636 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,640 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,644 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,644 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,646 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,652 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:04,653 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,654 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,655 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,656 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,656 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,656 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,663 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,665 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,666 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,668 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,668 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,669 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,670 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,673 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,674 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,677 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,677 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,679 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,681 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,685 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,685 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,687 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,688 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,688 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,690 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,694 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,695 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,696 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,697 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,698 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,700 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,701 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,703 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,706 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,708 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 13:40:04,709 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,710 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,715 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,717 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,718 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,724 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:04,726 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,728 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,730 - ExploitationExpert - INFO - res_population_num: 9
2025-06-08 13:40:04,730 - ExploitationExpert - INFO - res_population_costs: [9922, 9551, 9529, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:40:04,731 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-08 13:40:04,734 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:40:04,734 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': array([ 4, 17, 41, 31, 52, 29,  1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55,
       12, 49, 60, 15, 25, 40,  9, 59,  2, 64,  7, 44, 21, 43, 33, 30, 51,
       27, 62, 34, 63, 61, 65, 54, 42, 19,  5, 23, 14, 28, 50, 37,  0, 39,
       20, 24, 36,  8, 46, 47,  3, 11, 10, 16, 13, 45,  6, 57, 56]), 'cur_cost': 103244.0}, {'tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}, {'tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': array([27, 54,  5, 57, 43, 61, 34, 12, 56, 25, 44, 58,  1, 51, 65, 31, 55,
       20, 28, 53, 50, 49, 62, 24, 14,  7, 35, 60, 15, 16,  9, 32, 59, 42,
       38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30,  6, 19, 41, 46,  8, 21,
       52, 17, 47, 37, 45,  3,  2, 33,  0,  4, 48, 11, 22, 10, 23]), 'cur_cost': 122048.0}, {'tour': [65, 54, 26, 20, 32, 28, 62, 44, 24, 1, 33, 19, 29, 61, 42, 60, 50, 41, 11, 10, 21, 25, 9, 34, 56, 18, 45, 6, 4, 2, 31, 59, 27, 36, 52, 38, 5, 55, 35, 48, 53, 37, 46, 7, 23, 64, 17, 12, 57, 22, 8, 14, 16, 3, 51, 0, 43, 40, 39, 13, 30, 63, 58, 15, 47, 49], 'cur_cost': 114139.0}, {'tour': [46, 29, 23, 9, 40, 39, 49, 13, 20, 21, 38, 54, 4, 5, 55, 27, 32, 22, 47, 43, 59, 52, 44, 8, 37, 64, 42, 10, 18, 17, 16, 26, 48, 12, 7, 57, 51, 14, 28, 65, 36, 0, 1, 62, 63, 50, 53, 25, 60, 33, 56, 3, 15, 24, 30, 11, 41, 31, 35, 58, 45, 19, 6, 2, 61, 34], 'cur_cost': 104236.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:40:04,736 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 13:40:04,736 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-08 13:40:04,736 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-08 13:40:04,736 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-08 13:40:04,736 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:40:04,736 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:40:04,736 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 113637.0
2025-06-08 13:40:04,756 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,765 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,769 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,773 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,775 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,784 - root - INFO - 拓扑感知扰动用时: 0.0015秒，使用策略: segment_preservation
2025-06-08 13:40:04,790 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,795 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,798 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,811 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,819 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,824 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,828 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,840 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,843 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,847 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,848 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,856 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:04,865 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,870 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,876 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,877 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,882 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,886 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,887 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,889 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,893 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,894 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,895 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,904 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,904 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,907 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,909 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,910 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:04,912 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,912 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,915 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,915 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,916 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,918 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,919 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:04,921 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:04,921 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,922 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,924 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,926 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,927 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,932 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,933 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,934 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,938 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,940 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,940 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,941 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,942 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,943 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,945 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,945 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,953 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,954 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,955 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,956 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,957 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,961 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,964 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,965 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,966 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,966 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,967 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,967 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,968 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,969 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,970 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:04,970 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,970 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,975 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:04,976 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,978 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,981 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:04,986 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:04,986 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,989 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:04,989 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,990 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,992 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:04,992 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,997 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:04,998 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,000 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:05,002 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,005 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,005 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,007 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,008 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,011 - root - INFO - 拓扑感知扰动用时: 0.0014秒，使用策略: segment_preservation
2025-06-08 13:40:05,014 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:05,015 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,016 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,021 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,023 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,024 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,029 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,031 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,035 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:40:05,036 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,039 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,041 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,042 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,042 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,043 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,045 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,046 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,047 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,047 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,049 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,052 - root - INFO - 拓扑感知扰动用时: 0.0025秒，使用策略: segment_preservation
2025-06-08 13:40:05,052 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,053 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,055 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,056 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,062 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,063 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,065 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,066 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,067 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,068 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,069 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,069 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,070 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,070 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,074 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,074 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,076 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,077 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,079 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,079 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,079 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,083 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,084 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,084 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,086 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,088 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,088 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,090 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,090 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,090 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,095 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,097 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,100 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,101 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,105 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:05,106 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,107 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,108 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,109 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,109 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,109 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,111 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,112 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,115 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,115 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,116 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,117 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,118 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,119 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,121 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,121 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,122 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,122 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,123 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,124 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,125 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,129 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,129 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,136 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,136 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,139 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,139 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,141 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,141 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,141 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,143 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,144 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,144 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,145 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,148 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,152 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,152 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,155 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,156 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,158 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,160 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,162 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:40:05,163 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,164 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,167 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,169 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,169 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,171 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,172 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,173 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,173 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,175 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,178 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,180 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,181 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,185 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,186 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: critical_edge
2025-06-08 13:40:05,187 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,188 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,190 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,192 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,203 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:05,205 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,206 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,207 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,207 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,208 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,210 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,212 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,213 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,213 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,213 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,221 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,223 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,224 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,224 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,224 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,229 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,230 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,232 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,234 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,236 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,238 - ExploitationExpert - INFO - res_population_num: 11
2025-06-08 13:40:05,238 - ExploitationExpert - INFO - res_population_costs: [9922, 9551, 9529, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:40:05,239 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-08 13:40:05,242 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:40:05,242 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': array([ 4, 17, 41, 31, 52, 29,  1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55,
       12, 49, 60, 15, 25, 40,  9, 59,  2, 64,  7, 44, 21, 43, 33, 30, 51,
       27, 62, 34, 63, 61, 65, 54, 42, 19,  5, 23, 14, 28, 50, 37,  0, 39,
       20, 24, 36,  8, 46, 47,  3, 11, 10, 16, 13, 45,  6, 57, 56]), 'cur_cost': 103244.0}, {'tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}, {'tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': array([27, 54,  5, 57, 43, 61, 34, 12, 56, 25, 44, 58,  1, 51, 65, 31, 55,
       20, 28, 53, 50, 49, 62, 24, 14,  7, 35, 60, 15, 16,  9, 32, 59, 42,
       38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30,  6, 19, 41, 46,  8, 21,
       52, 17, 47, 37, 45,  3,  2, 33,  0,  4, 48, 11, 22, 10, 23]), 'cur_cost': 122048.0}, {'tour': array([ 8, 52, 58, 42, 24, 37, 54, 12, 56, 43, 33, 53, 28,  6, 63, 59, 10,
       26,  3, 23, 20,  2, 41, 61, 44, 17, 16, 32, 51,  9, 46, 35,  0, 11,
       31, 50, 15, 48, 40, 39,  5, 34, 21, 57, 25, 13, 55, 27, 60, 19, 18,
       36, 64, 38, 65, 29,  1, 30, 62,  7, 49, 45, 47, 14,  4, 22]), 'cur_cost': 113637.0}, {'tour': [46, 29, 23, 9, 40, 39, 49, 13, 20, 21, 38, 54, 4, 5, 55, 27, 32, 22, 47, 43, 59, 52, 44, 8, 37, 64, 42, 10, 18, 17, 16, 26, 48, 12, 7, 57, 51, 14, 28, 65, 36, 0, 1, 62, 63, 50, 53, 25, 60, 33, 56, 3, 15, 24, 30, 11, 41, 31, 35, 58, 45, 19, 6, 2, 61, 34], 'cur_cost': 104236.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:40:05,245 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 13:40:05,245 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-08 13:40:05,246 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-08 13:40:05,246 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-08 13:40:05,246 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:40:05,246 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:40:05,246 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 101039.0
2025-06-08 13:40:05,265 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,266 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,269 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,280 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,291 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,294 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:05,297 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,304 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,308 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,312 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,312 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,316 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,321 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,326 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,340 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,341 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,357 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,359 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,364 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,366 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,366 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,388 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,392 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:05,398 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:05,399 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,401 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,403 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,404 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,406 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,408 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,409 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,409 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,410 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,411 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:05,415 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,417 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,421 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,422 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,424 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,425 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,426 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,428 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,429 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:05,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,434 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,436 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:05,438 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:05,439 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,441 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,442 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,445 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:05,446 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:05,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,449 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,451 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,454 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: pattern_based
2025-06-08 13:40:05,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,457 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:05,457 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,458 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:05,460 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,462 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,463 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,463 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 13:40:05,463 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,464 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,466 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,469 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,470 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,475 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,476 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,479 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,480 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,482 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,485 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,487 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,488 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,489 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,490 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,492 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,493 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,495 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,497 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,498 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,498 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,500 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,501 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,502 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,504 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,505 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,511 - root - INFO - 拓扑感知扰动用时: 0.0051秒，使用策略: pattern_based
2025-06-08 13:40:05,512 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:05,513 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,513 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,516 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,518 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,519 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,519 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,520 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,521 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,522 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,522 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,522 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,528 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,530 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,531 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,533 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,535 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,538 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,540 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,542 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,544 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,548 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,549 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,552 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,552 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,553 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,554 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,555 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,558 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,561 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,563 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 13:40:05,564 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,566 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,567 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,570 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,571 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,573 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 13:40:05,575 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,575 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,580 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,581 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,581 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,582 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,583 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,584 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,586 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,586 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,588 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,589 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,591 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,592 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,592 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,596 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,597 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,598 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,601 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,603 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,606 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,609 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,610 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,613 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:05,615 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,616 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,620 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,623 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,624 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,627 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,627 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,628 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,630 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,634 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,634 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,636 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,638 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,639 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,639 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,641 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,648 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 13:40:05,648 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,650 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,651 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,652 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,653 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,655 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,655 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,658 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,660 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,661 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,664 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,665 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,665 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,667 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,667 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,669 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,672 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,672 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,675 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,677 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,683 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,685 - root - INFO - 拓扑感知扰动用时: 0.0028秒，使用策略: segment_preservation
2025-06-08 13:40:05,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,693 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,694 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,695 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,695 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,698 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,699 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,699 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,703 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,705 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,706 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,707 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,709 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,710 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,710 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,714 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,717 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:05,722 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,724 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,726 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,727 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,728 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,728 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,733 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:40:05,734 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,734 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,735 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,736 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,739 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,739 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,741 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,745 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,749 - ExploitationExpert - INFO - res_population_num: 13
2025-06-08 13:40:05,749 - ExploitationExpert - INFO - res_population_costs: [9922, 9551, 9529, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:40:05,749 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-08 13:40:05,755 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:40:05,755 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': array([ 4, 17, 41, 31, 52, 29,  1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55,
       12, 49, 60, 15, 25, 40,  9, 59,  2, 64,  7, 44, 21, 43, 33, 30, 51,
       27, 62, 34, 63, 61, 65, 54, 42, 19,  5, 23, 14, 28, 50, 37,  0, 39,
       20, 24, 36,  8, 46, 47,  3, 11, 10, 16, 13, 45,  6, 57, 56]), 'cur_cost': 103244.0}, {'tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}, {'tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': array([27, 54,  5, 57, 43, 61, 34, 12, 56, 25, 44, 58,  1, 51, 65, 31, 55,
       20, 28, 53, 50, 49, 62, 24, 14,  7, 35, 60, 15, 16,  9, 32, 59, 42,
       38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30,  6, 19, 41, 46,  8, 21,
       52, 17, 47, 37, 45,  3,  2, 33,  0,  4, 48, 11, 22, 10, 23]), 'cur_cost': 122048.0}, {'tour': array([ 8, 52, 58, 42, 24, 37, 54, 12, 56, 43, 33, 53, 28,  6, 63, 59, 10,
       26,  3, 23, 20,  2, 41, 61, 44, 17, 16, 32, 51,  9, 46, 35,  0, 11,
       31, 50, 15, 48, 40, 39,  5, 34, 21, 57, 25, 13, 55, 27, 60, 19, 18,
       36, 64, 38, 65, 29,  1, 30, 62,  7, 49, 45, 47, 14,  4, 22]), 'cur_cost': 113637.0}, {'tour': array([44, 28, 23, 63, 21, 64, 25,  8, 55, 38, 22, 13, 31, 39, 40,  6, 11,
       17, 50, 43, 32, 41,  0, 45, 19,  1, 29, 37, 30,  7,  2, 49, 42, 51,
        9, 61,  5, 56, 33, 35, 53,  3, 54, 27, 34, 58,  4, 24, 59, 36, 48,
       57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52, 18, 47]), 'cur_cost': 101039.0}, {'tour': [18, 20, 51, 24, 42, 39, 40, 22, 17, 58, 27, 53, 52, 13, 25, 54, 63, 33, 61, 41, 49, 21, 11, 64, 36, 29, 2, 7, 31, 60, 28, 47, 65, 56, 10, 15, 59, 16, 23, 55, 9, 1, 34, 38, 57, 45, 5, 19, 0, 12, 50, 44, 32, 37, 43, 48, 4, 6, 26, 3, 62, 8, 35, 30, 46, 14], 'cur_cost': 96962.0}]
2025-06-08 13:40:05,759 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 13:40:05,759 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-08 13:40:05,759 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-08 13:40:05,759 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-08 13:40:05,759 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:40:05,760 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:40:05,760 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 102177.0
2025-06-08 13:40:05,767 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 13:40:05,770 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,777 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,783 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,784 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,795 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,799 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,806 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,816 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,829 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,834 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,839 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,839 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,857 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:05,860 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,862 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,864 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,867 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,868 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,872 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,874 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,885 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,887 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 13:40:05,889 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,897 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,899 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,913 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,914 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,916 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:05,924 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,928 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,931 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,932 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,932 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,933 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,935 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,936 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,937 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:05,939 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:05,939 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,942 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,947 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:40:05,948 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,949 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 13:40:05,950 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,953 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,954 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,955 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:05,956 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,958 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,959 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,960 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,962 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,962 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,966 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,968 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,970 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,972 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,973 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,973 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,976 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,979 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,981 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,982 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,983 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,986 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:05,987 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,989 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,990 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:05,990 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,995 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:05,996 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:40:05,998 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:05,999 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,000 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,001 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:06,001 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,002 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:06,004 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,005 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,007 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,008 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,012 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,013 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,015 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,017 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,019 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:06,022 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,024 - root - INFO - 拓扑感知扰动用时: 0.0017秒，使用策略: segment_preservation
2025-06-08 13:40:06,027 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:06,028 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,029 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,030 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,031 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,032 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,035 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,038 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,039 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:06,042 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,043 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,044 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,046 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,049 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:06,051 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,052 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,053 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,058 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,061 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:40:06,065 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,066 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:06,070 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,073 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:06,074 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,076 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,076 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,078 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,083 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,085 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: critical_edge
2025-06-08 13:40:06,086 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,088 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 13:40:06,089 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,092 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:40:06,092 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,095 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,096 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,100 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,102 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,107 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:40:06,109 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,110 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:06,111 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,115 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,117 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,117 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,124 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,126 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:06,127 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,130 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 13:40:06,133 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,135 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:06,136 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:06,152 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,156 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,157 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: critical_edge
2025-06-08 13:40:06,160 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:40:06,165 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,167 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,170 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,173 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:40:06,176 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:40:06,177 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,178 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,179 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,181 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,182 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,185 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,187 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:06,188 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,188 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:40:06,190 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,192 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,192 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,195 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,197 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,199 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:40:06,200 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,208 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,210 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,213 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,214 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,217 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,217 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:40:06,220 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:40:06,220 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,224 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,227 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,230 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,233 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,235 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,240 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,240 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,244 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,246 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,248 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 13:40:06,249 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,253 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:40:06,256 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:40:06,259 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:40:06,261 - ExploitationExpert - INFO - res_population_num: 14
2025-06-08 13:40:06,262 - ExploitationExpert - INFO - res_population_costs: [9922, 9551, 9529, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:40:06,262 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-08 13:40:06,267 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:40:06,267 - ExploitationExpert - INFO - populations: [{'tour': array([ 7, 28, 11, 41, 62, 50, 16, 30,  6,  4, 39, 54, 60, 26, 47, 25, 44,
       13,  1, 17, 37, 34, 45,  0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43,
       63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61,  5,
        2, 48, 55, 20, 33,  8, 21, 19, 51,  3, 59, 42, 22,  9, 29]), 'cur_cost': 102304.0}, {'tour': array([ 8, 27, 35, 40,  0,  6, 21, 61, 12, 49, 57, 48, 18, 23, 58,  2, 52,
       28, 10, 34, 41,  9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11,  4,  7,
       43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20,  3, 22, 55, 39, 53,
        5, 65, 46,  1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]), 'cur_cost': 105840.0}, {'tour': array([ 4, 17, 41, 31, 52, 29,  1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55,
       12, 49, 60, 15, 25, 40,  9, 59,  2, 64,  7, 44, 21, 43, 33, 30, 51,
       27, 62, 34, 63, 61, 65, 54, 42, 19,  5, 23, 14, 28, 50, 37,  0, 39,
       20, 24, 36,  8, 46, 47,  3, 11, 10, 16, 13, 45,  6, 57, 56]), 'cur_cost': 103244.0}, {'tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}, {'tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': array([27, 54,  5, 57, 43, 61, 34, 12, 56, 25, 44, 58,  1, 51, 65, 31, 55,
       20, 28, 53, 50, 49, 62, 24, 14,  7, 35, 60, 15, 16,  9, 32, 59, 42,
       38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30,  6, 19, 41, 46,  8, 21,
       52, 17, 47, 37, 45,  3,  2, 33,  0,  4, 48, 11, 22, 10, 23]), 'cur_cost': 122048.0}, {'tour': array([ 8, 52, 58, 42, 24, 37, 54, 12, 56, 43, 33, 53, 28,  6, 63, 59, 10,
       26,  3, 23, 20,  2, 41, 61, 44, 17, 16, 32, 51,  9, 46, 35,  0, 11,
       31, 50, 15, 48, 40, 39,  5, 34, 21, 57, 25, 13, 55, 27, 60, 19, 18,
       36, 64, 38, 65, 29,  1, 30, 62,  7, 49, 45, 47, 14,  4, 22]), 'cur_cost': 113637.0}, {'tour': array([44, 28, 23, 63, 21, 64, 25,  8, 55, 38, 22, 13, 31, 39, 40,  6, 11,
       17, 50, 43, 32, 41,  0, 45, 19,  1, 29, 37, 30,  7,  2, 49, 42, 51,
        9, 61,  5, 56, 33, 35, 53,  3, 54, 27, 34, 58,  4, 24, 59, 36, 48,
       57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52, 18, 47]), 'cur_cost': 101039.0}, {'tour': array([40, 34, 28, 10, 14, 30, 33, 13, 11, 22,  3, 20, 64,  5, 25, 16, 44,
        0, 45, 46, 61, 52, 32, 43, 58, 23,  7, 49, 47,  8, 59, 42, 51, 65,
       57, 41, 54, 35, 27, 18,  4, 37, 48, 17,  6, 53, 62, 24, 15,  2, 60,
       36, 31, 29, 56, 26, 12, 21, 19, 55, 38, 50,  9,  1, 39, 63]), 'cur_cost': 102177.0}]
2025-06-08 13:40:06,269 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 13:40:06,269 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-08 13:40:06,270 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-08 13:40:06,270 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 3, 'strategy': 'explore', 'path_data': {'new_path': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'modification_strategy': 'Large-scale restructuring with focus on regional shuffling and re-ordering.', 'targeted_regions': ['Early part of the path (nodes 0-20)', 'Central part of the path (nodes 20-40)', 'Late part of the path (nodes 40-65)'], 'avoided_regions': ['High-cost edge combinations, if identifiable from landscape analysis (not available in this prompt, but would be considered in a real-world scenario)'], 'knowledge_application': 'No knowledge was retrieved, therefore none applied.', 'diversity_contribution': 'High. The new path prioritizes re-ordering large segments of the existing path, ensuring substantial deviation from the current path. This promotes increased population diversity.', 'expected_benefits': 'The new path aims to explore significantly different areas of the search space. By reshuffling the path structure, it is designed to discover new solution patterns and avoid potential local optima. This helps to break free from any bias in the current individual. The resulting increased diversity will benefit the overall performance of the evolutionary algorithm.', 'new_tour': [48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46], 'cur_cost': 100456.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_path': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'modification_strategy': 'Large-scale restructuring with regional refinement.  We prioritize a significant reshuffling of the existing path while also considering specific regions for improved exploration.', 'targeted_regions': ["Under-explored regions as suggested by landscape analysis (details unavailable, but assumed to be regions far from current solution), focus on the latter half of the original path's nodes."], 'avoided_regions': ["Difficult regions as defined by landscape analysis (details unavailable, but assumed to be regions where current solutions are trapped), avoiding regions that were heavily visited in the original path's traversal."], 'knowledge_application': 'No knowledge was provided, so no explicit knowledge application. The path aims for general exploration guided by a diversity-focused strategy.', 'diversity_contribution': 'High. The new path is designed to be significantly different from the original path, aiming for a high degree of novelty and exploration in previously unvisited areas of the search space.', 'expected_benefits': 'Increased population diversity, potential discovery of better solutions by exploring different solution structures and opening exploration to previously missed regions. The reshuffling seeks to quickly assess different structural configurations in the search space.', 'new_tour': [33, 54, 11, 36, 31, 26, 65, 39, 61, 10, 58, 64, 50, 18, 22, 56, 49, 1, 29, 7, 20, 28, 48, 21, 3, 55, 47, 37, 14, 57, 13, 34, 41, 53, 2, 59, 0, 9, 35, 6, 17, 60, 43, 5, 40, 15, 52, 27, 24, 42, 23, 32, 51, 25, 62, 38, 45, 16, 46, 4, 8, 44, 12, 63, 30, 19], 'cur_cost': 112339.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}}]
2025-06-08 13:40:06,271 - __main__ - INFO - 进化阶段完成
2025-06-08 13:40:06,271 - __main__ - INFO - 开始评估阶段
2025-06-08 13:40:06,271 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-08 13:40:06,298 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
# Evolution Assessment Task

## Role Definition
You are an expert in evolutionary algorithms and performance assessment. You excel at analyzing the evolutionary state of algorithms, evaluating strategy effectiveness, optimizing the balance between exploration and exploitation, and providing specific improvement recommendations.

## Iteration Information
- Current Iteration: 0
- Total Iterations: 5

## Population Changes
- Old Population Statistics: {"min_cost": 10093.0, "max_cost": 114139.0, "mean_cost": 75975.3, "diversity": 0.9127946127946128}
- New Population Statistics: {"min_cost": 100456.0, "max_cost": 122048.0, "mean_cost": 106680.3, "diversity": 0.9670033670033669}
- Elite Solution Changes: {"old_best_cost": 10093.0, "new_best_cost": 9521, "improvement": 572.0, "old_elite_count": 3, "new_elite_count": 14, "old_elite_diversity": 0.18181818181818177, "new_elite_diversity": 0.16161616161616166, "new_elite_solutions_count": 0}

## Strategy Allocation
- Global Exploration Ratio: 0.3
- Individual Strategy Assignments: {"0": "exploit", "1": "exploit", "2": "exploit", "3": "explore", "4": "explore", "5": "explore", "6": "exploit", "7": "exploit", "8": "exploit", "9": "exploit"}
- Strategy Parameters: {"explore": {"diversity_weight": 0.7, "knowledge_usage_weight": 0.3, "risk_tolerance": 0.6}, "exploit": {"local_search_depth": 2, "quality_edge_usage": 0.8, "elite_influence": 0.7}}

## Strategy Results
- Exploration Strategy Results: {"success_count": 0, "improvement_sum": -1892.0}
- Exploitation Strategy Results: {"success_count": 2, "improvement_sum": -305158.0}

## Historical Trends
无历史趋势数据

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data shows the population changes, strategy allocation, and strategy results in the current iteration. We need you to assess the evolutionary effects of the current iteration, analyze the evolutionary stage, evaluate strategy effectiveness, and provide improvement recommendations for the next iteration.

# Assessment Request
Please conduct a comprehensive assessment of the evolutionary effects in the current iteration. Please follow these steps in your thinking:

## Step 1: Evolution Stage Identification and Analysis
- Analyze the current population's diversity level, convergence degree, and elite solution stability
- Evaluate the improvement rate and effectiveness of various strategies
- Determine the current evolutionary stage of the algorithm (early exploration, middle transition, late fine-tuning)
- Assess the confidence level of the stage determination
- Detect if approaching a stage transition point and estimate the transition time
- Provide recommendations for preparing for stage transitions

## Step 2: Strategy Effectiveness Evaluation
- Analyze the effectiveness of exploration strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to diversity
  - Evaluate new region discovery capability
  - Evaluate ability to escape local optima
  - Analyze individual-level effectiveness
- Analyze the effectiveness of exploitation strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to convergence
  - Evaluate elite solution improvement capability
  - Evaluate local fine-tuning capability
  - Analyze individual-level effectiveness
- Evaluate overall strategy effectiveness:
  - Analyze strategy synergy effects
  - Evaluate balance effectiveness
  - Calculate iteration efficiency

## Step 3: Exploration vs. Exploitation Balance Analysis
- Calculate the current exploration/exploitation ratio
- Based on the evolutionary stage and strategy effectiveness, predict the optimal ratio
- Calculate the gap between the current ratio and the optimal ratio
- Evaluate the current balance state
- Determine if adjustment is needed, and the direction and magnitude of adjustment
- Analyze the trend of balance state changes

## Step 4: Improvement Recommendation Generation
- Recommend exploration ratio for the next iteration
- Propose strategy parameter adjustment recommendations
- Identify areas that need special attention
- Provide strategy recommendations for individuals

## Step 5: Long-term Trend Analysis
- Analyze convergence trends
- Analyze diversity trends
- Analyze efficiency trends
- Identify potential problems
- Discover optimization opportunities

Please return your assessment results in JSON format with the following sections:
```json
{
  "evolution_stage": {
    "stage": string,
    "confidence": float,
    "stage_features": {
      "diversity_level": float,
      "convergence_level": float,
      "elite_stability": float,
      "improvement_rate": float,
      "exploration_effectiveness": float,
      "exploitation_effectiveness": float
    },
    "transition_indicators": {
      "approaching_next_stage": bool,
      "estimated_iterations_to_transition": int,
      "recommended_preparation": string
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": float,
      "avg_improvement": float,
      "diversity_contribution": float,
      "new_region_discovery": float,
      "escape_local_optima": float,
      "individual_effectiveness": {}
    },
    "exploit": {
      "success_rate": float,
      "avg_improvement": float,
      "convergence_contribution": float,
      "elite_improvement": float,
      "local_refinement": float,
      "individual_effectiveness": {}
    },
    "overall": {
      "strategy_synergy": float,
      "balance_effectiveness": float,
      "iteration_efficiency": float
    }
  },
  "balance_state": {
    "current_ratio": float,
    "optimal_ratio": float,
    "ratio_gap": float,
    "balance_score": float,
    "adjustment_needed": bool,
    "adjustment_magnitude": float,
    "adjustment_direction": string,
    "balance_trend": {
      "direction": string,
      "rate": float,
      "prediction": string
    }
  },
  "recommendations": {
    "explore_ratio": float,
    "strategy_parameters": {},
    "focus_areas": [string, ...],
    "individual_recommendations": {}
  },
  "long_term_analysis": {
    "convergence_trend": string,
    "diversity_trend": string,
    "efficiency_trend": string,
    "potential_issues": [string, ...],
    "optimization_opportunities": [string, ...]
  }
}
```

Additionally, please provide a brief summary explaining your main findings and their implications for the next iteration's strategy.

2025-06-08 13:40:06,303 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-08 13:40:15,934 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "early transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.967,
      "convergence_level": 0.05,
      "elite_stability": 0.16,
      "improvement_rate": -0.01,
      "exploration_effectiveness": 0.0,
      "exploitation_effectiveness": -0.98
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Increase exploration, consider adjusting strategy parameters for both explore and exploit to increase overall improvement."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -473,
      "diversity_contribution": 0.0,
      "new_region_discovery": 0.0,
      "escape_local_optima": 0.0,
      "individual_effectiveness": {
        "3": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "4": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "5": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'"
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -152579,
      "convergence_contribution": -0.14,
      "elite_improvement": 0.05,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "1": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "2": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "6": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "7": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "8": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "9": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'"
      }
    },
    "overall": {
      "strategy_synergy": 0.1,
      "balance_effectiveness": 0.3,
      "iteration_efficiency": -0.98
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.5,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "increase exploration",
    "balance_trend": {
      "direction": "moving toward more exploration",
      "rate": 0.01,
      "prediction": "The algorithm is likely to benefit from a temporary boost in exploration as it transitions from an early stage"
    }
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.8,
        "knowledge_usage_weight": 0.2,
        "risk_tolerance": 0.7
      },
      "exploit": {
        "local_search_depth": 1,
        "quality_edge_usage": 0.7,
        "elite_influence": 0.6
      }
    },
    "focus_areas": [
      "Improving the effectiveness of both explore and exploit strategies.",
      "Monitoring the elite solution's stability.",
      "Analyzing why exploitation is highly ineffective"
    ],
    "individual_recommendations": {
      "0": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "1": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "2": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "3": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "4": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "5": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "6": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "7": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "8": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "9": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Initial improvement in elite cost, but with a decline in the mean cost and very negative total improvement. Indicates a high degree of instability.",
    "diversity_trend": "Slight increase in population diversity, but elite diversity decreased. Indicates an elite instability and potential premature focus on sub-optimal regions.",
    "efficiency_trend": "Overall iteration efficiency is very low, due to the poor performance of both exploration and exploitation strategies.",
    "potential_issues": [
      "Ineffective exploitation strategies. Possibly stuck in local optima.",
      "Poorly chosen strategy parameters.",
      "Balance leaning too heavily towards exploitation given the lack of improvement.",
      "Elite instability - new best cost is much better than previous elite but then the mean cost becomes worse."
    ],
    "optimization_opportunities": [
      "Fine-tuning exploration and exploitation parameter values.",
      "Adjusting the explore/exploit balance to favour exploration more in the next iteration.",
      "Investigating the reasons for the ineffectiveness of exploit strategies (e.g., local optima, improper parameter settings).",
        "Careful monitoring of the elite solutions."
    ]
  }
}
```

**Summary:**

The algorithm is currently in an early transition phase. While the best solution saw a small improvement, overall performance is declining, suggesting the algorithm is struggling to find better solutions. The exploitation strategies are highly ineffective, leading to a large negative improvement, while exploration also lacks positive results. The balance between exploration and exploitation is currently skewed towards exploitation.  For the next iteration, the strategy needs to shift to a more exploratory focus.  We recommend increasing the exploration ratio, adjusting the exploration and exploitation strategy parameters, and monitoring the stability of elite solutions, as well as re-evaluating the individuals currently using exploit.

2025-06-08 13:40:15,934 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-08 13:40:15,934 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "early transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.967,
      "convergence_level": 0.05,
      "elite_stability": 0.16,
      "improvement_rate": -0.01,
      "exploration_effectiveness": 0.0,
      "exploitation_effectiveness": -0.98
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Increase exploration, consider adjusting strategy parameters for both explore and exploit to increase overall improvement."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -473,
      "diversity_contribution": 0.0,
      "new_region_discovery": 0.0,
      "escape_local_optima": 0.0,
      "individual_effectiveness": {
        "3": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "4": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "5": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'"
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -152579,
      "convergence_contribution": -0.14,
      "elite_improvement": 0.05,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "1": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "2": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "6": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "7": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "8": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "9": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'"
      }
    },
    "overall": {
      "strategy_synergy": 0.1,
      "balance_effectiveness": 0.3,
      "iteration_efficiency": -0.98
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.5,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "increase exploration",
    "balance_trend": {
      "direction": "moving toward more exploration",
      "rate": 0.01,
      "prediction": "The algorithm is likely to benefit from a temporary boost in exploration as it transitions from an early stage"
    }
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.8,
        "knowledge_usage_weight": 0.2,
        "risk_tolerance": 0.7
      },
      "exploit": {
        "local_search_depth": 1,
        "quality_edge_usage": 0.7,
        "elite_influence": 0.6
      }
    },
    "focus_areas": [
      "Improving the effectiveness of both explore and exploit strategies.",
      "Monitoring the elite solution's stability.",
      "Analyzing why exploitation is highly ineffective"
    ],
    "individual_recommendations": {
      "0": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "1": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "2": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "3": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "4": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "5": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "6": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "7": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "8": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "9": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Initial improvement in elite cost, but with a decline in the mean cost and very negative total improvement. Indicates a high degree of instability.",
    "diversity_trend": "Slight increase in population diversity, but elite diversity decreased. Indicates an elite instability and potential premature focus on sub-optimal regions.",
    "efficiency_trend": "Overall iteration efficiency is very low, due to the poor performance of both exploration and exploitation strategies.",
    "potential_issues": [
      "Ineffective exploitation strategies. Possibly stuck in local optima.",
      "Poorly chosen strategy parameters.",
      "Balance leaning too heavily towards exploitation given the lack of improvement.",
      "Elite instability - new best cost is much better than previous elite but then the mean cost becomes worse."
    ],
    "optimization_opportunities": [
      "Fine-tuning exploration and exploitation parameter values.",
      "Adjusting the explore/exploit balance to favour exploration more in the next iteration.",
      "Investigating the reasons for the ineffectiveness of exploit strategies (e.g., local optima, improper parameter settings).",
        "Careful monitoring of the elite solutions."
    ]
  }
}
```

**Summary:**

The algorithm is currently in an early transition phase. While the best solution saw a small improvement, overall performance is declining, suggesting the algorithm is struggling to find better solutions. The exploitation strategies are highly ineffective, leading to a large negative improvement, while exploration also lacks positive results. The balance between exploration and exploitation is currently skewed towards exploitation.  For the next iteration, the strategy needs to shift to a more exploratory focus.  We recommend increasing the exploration ratio, adjusting the exploration and exploitation strategy parameters, and monitoring the stability of elite solutions, as well as re-evaluating the individuals currently using exploit.

2025-06-08 13:40:15,934 - __main__ - INFO - 评估阶段完成
2025-06-08 13:40:15,934 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "early transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.967,
      "convergence_level": 0.05,
      "elite_stability": 0.16,
      "improvement_rate": -0.01,
      "exploration_effectiveness": 0.0,
      "exploitation_effectiveness": -0.98
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Increase exploration, consider adjusting strategy parameters for both explore and exploit to increase overall improvement."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -473,
      "diversity_contribution": 0.0,
      "new_region_discovery": 0.0,
      "escape_local_optima": 0.0,
      "individual_effectiveness": {
        "3": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "4": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "5": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'"
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -152579,
      "convergence_contribution": -0.14,
      "elite_improvement": 0.05,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "1": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "2": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "6": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "7": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "8": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "9": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'"
      }
    },
    "overall": {
      "strategy_synergy": 0.1,
      "balance_effectiveness": 0.3,
      "iteration_efficiency": -0.98
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.5,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "increase exploration",
    "balance_trend": {
      "direction": "moving toward more exploration",
      "rate": 0.01,
      "prediction": "The algorithm is likely to benefit from a temporary boost in exploration as it transitions from an early stage"
    }
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.8,
        "knowledge_usage_weight": 0.2,
        "risk_tolerance": 0.7
      },
      "exploit": {
        "local_search_depth": 1,
        "quality_edge_usage": 0.7,
        "elite_influence": 0.6
      }
    },
    "focus_areas": [
      "Improving the effectiveness of both explore and exploit strategies.",
      "Monitoring the elite solution's stability.",
      "Analyzing why exploitation is highly ineffective"
    ],
    "individual_recommendations": {
      "0": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "1": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "2": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "3": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "4": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "5": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "6": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "7": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "8": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "9": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Initial improvement in elite cost, but with a decline in the mean cost and very negative total improvement. Indicates a high degree of instability.",
    "diversity_trend": "Slight increase in population diversity, but elite diversity decreased. Indicates an elite instability and potential premature focus on sub-optimal regions.",
    "efficiency_trend": "Overall iteration efficiency is very low, due to the poor performance of both exploration and exploitation strategies.",
    "potential_issues": [
      "Ineffective exploitation strategies. Possibly stuck in local optima.",
      "Poorly chosen strategy parameters.",
      "Balance leaning too heavily towards exploitation given the lack of improvement.",
      "Elite instability - new best cost is much better than previous elite but then the mean cost becomes worse."
    ],
    "optimization_opportunities": [
      "Fine-tuning exploration and exploitation parameter values.",
      "Adjusting the explore/exploit balance to favour exploration more in the next iteration.",
      "Investigating the reasons for the ineffectiveness of exploit strategies (e.g., local optima, improper parameter settings).",
        "Careful monitoring of the elite solutions."
    ]
  }
}
```

**Summary:**

The algorithm is currently in an early transition phase. While the best solution saw a small improvement, overall performance is declining, suggesting the algorithm is struggling to find better solutions. The exploitation strategies are highly ineffective, leading to a large negative improvement, while exploration also lacks positive results. The balance between exploration and exploitation is currently skewed towards exploitation.  For the next iteration, the strategy needs to shift to a more exploratory focus.  We recommend increasing the exploration ratio, adjusting the exploration and exploitation strategy parameters, and monitoring the stability of elite solutions, as well as re-evaluating the individuals currently using exploit.

2025-06-08 13:40:15,934 - __main__ - INFO - 当前最佳适应度: 100456.0
2025-06-08 13:40:15,940 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0509_0bug-fa-feedback\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-08 13:40:15,940 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-08 13:40:15,940 - __main__ - INFO - 开始分析阶段
2025-06-08 13:40:15,941 - StatsExpert - INFO - 开始统计分析
2025-06-08 13:40:15,959 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 100456.0, 'max': 122048.0, 'mean': 106680.3, 'std': 6692.265775505333}, 'diversity': 0.9670033670033669, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 13:40:15,959 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 100456.0, 'max': 122048.0, 'mean': 106680.3, 'std': 6692.265775505333}, 'diversity_level': 0.9670033670033669, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 13:40:15,960 - PathExpert - INFO - 开始路径结构分析
2025-06-08 13:40:15,966 - PathExpert - INFO - 路径结构分析完成
2025-06-08 13:40:15,966 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': [{'edge': '(30, 6)', 'frequency': 0.2}, {'edge': '(60, 26)', 'frequency': 0.2}, {'edge': '(25, 44)', 'frequency': 0.2}, {'edge': '(1, 17)', 'frequency': 0.2}, {'edge': '(40, 46)', 'frequency': 0.2}, {'edge': '(56, 43)', 'frequency': 0.2}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(35, 27)', 'frequency': 0.2}, {'edge': '(61, 5)', 'frequency': 0.2}, {'edge': '(55, 20)', 'frequency': 0.2}, {'edge': '(8, 21)', 'frequency': 0.2}, {'edge': '(21, 19)', 'frequency': 0.2}, {'edge': '(59, 42)', 'frequency': 0.3}, {'edge': '(29, 7)', 'frequency': 0.2}, {'edge': '(12, 49)', 'frequency': 0.2}, {'edge': '(48, 18)', 'frequency': 0.2}, {'edge': '(2, 52)', 'frequency': 0.2}, {'edge': '(28, 10)', 'frequency': 0.2}, {'edge': '(34, 41)', 'frequency': 0.2}, {'edge': '(44, 14)', 'frequency': 0.2}, {'edge': '(37, 30)', 'frequency': 0.2}, {'edge': '(36, 8)', 'frequency': 0.2}, {'edge': '(29, 1)', 'frequency': 0.2}, {'edge': '(35, 53)', 'frequency': 0.2}, {'edge': '(60, 15)', 'frequency': 0.2}, {'edge': '(43, 33)', 'frequency': 0.2}, {'edge': '(0, 39)', 'frequency': 0.2}, {'edge': '(44, 17)', 'frequency': 0.2}, {'edge': '(17, 47)', 'frequency': 0.2}, {'edge': '(41, 21)', 'frequency': 0.2}, {'edge': '(19, 63)', 'frequency': 0.2}, {'edge': '(36, 31)', 'frequency': 0.2}, {'edge': '(1, 29)', 'frequency': 0.2}, {'edge': '(20, 28)', 'frequency': 0.2}, {'edge': '(47, 37)', 'frequency': 0.2}, {'edge': '(59, 0)', 'frequency': 0.2}, {'edge': '(32, 51)', 'frequency': 0.2}, {'edge': '(35, 60)', 'frequency': 0.2}, {'edge': '(60, 19)', 'frequency': 0.2}, {'edge': '(7, 2)', 'frequency': 0.2}, {'edge': '(65, 57)', 'frequency': 0.2}, {'edge': '(46, 20)', 'frequency': 0.2}, {'edge': '(12, 56)', 'frequency': 0.2}, {'edge': '(51, 65)', 'frequency': 0.2}, {'edge': '(62, 24)', 'frequency': 0.2}, {'edge': '(11, 22)', 'frequency': 0.2}, {'edge': '(51, 9)', 'frequency': 0.2}, {'edge': '(7, 49)', 'frequency': 0.2}, {'edge': '(55, 38)', 'frequency': 0.2}, {'edge': '(0, 45)', 'frequency': 0.2}, {'edge': '(42, 51)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [36, 64, 38, 65, 29], 'cost': 11614.0, 'size': 5}, {'region': [33, 63, 45, 60, 26], 'cost': 11569.0, 'size': 5}, {'region': [1, 51, 65, 31, 55], 'cost': 11271.0, 'size': 5}, {'region': [27, 62, 34, 63], 'cost': 8809.0, 'size': 4}, {'region': [57, 41, 54, 35], 'cost': 8684.0, 'size': 4}]}
2025-06-08 13:40:15,968 - EliteExpert - INFO - 开始精英解分析
2025-06-08 13:40:15,972 - EliteExpert - INFO - 精英解分析完成
2025-06-08 13:40:15,972 - __main__ - INFO - 精英专家分析报告: {'elite_count': 14, 'elite_common_features': {'common_edges': {'(24, 31)': 0.7142857142857143, '(37, 27)': 0.7142857142857143, '(17, 12)': 0.7142857142857143, '(12, 22)': 0.7142857142857143}, 'common_edge_ratio': 0.06060606060606061}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 90935.0, 'avg_gap': 97127.94285714286}, 'structure_gap': {'unique_elite_edges': 184, 'unique_pop_edges': 580, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.22377622377622375}}
2025-06-08 13:40:15,972 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 13:40:15,973 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 13:40:15,973 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=100456.0, Max=122048.0, Mean=106680.3, Std=6692.265775505333
- Diversity Level: 0.9670033670033669
- Convergence Level: 0.0
- Clustering Information: {"clusters": 10, "cluster_sizes": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: []
- Common Subpaths: []
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [], "low_frequency_edges": [{"edge": "(30, 6)", "frequency": 0.2}, {"edge": "(60, 26)", "frequency": 0.2}, {"edge": "(25, 44)", "frequency": 0.2}, {"edge": "(1, 17)", "frequency": 0.2}, {"edge": "(40, 46)", "frequency": 0.2}, {"edge": "(56, 43)", "frequency": 0.2}, {"edge": "(10, 15)", "frequency": 0.2}, {"edge": "(35, 27)", "frequency": 0.2}, {"edge": "(61, 5)", "frequency": 0.2}, {"edge": "(55, 20)", "frequency": 0.2}, {"edge": "(8, 21)", "frequency": 0.2}, {"edge": "(21, 19)", "frequency": 0.2}, {"edge": "(59, 42)", "frequency": 0.3}, {"edge": "(29, 7)", "frequency": 0.2}, {"edge": "(12, 49)", "frequency": 0.2}, {"edge": "(48, 18)", "frequency": 0.2}, {"edge": "(2, 52)", "frequency": 0.2}, {"edge": "(28, 10)", "frequency": 0.2}, {"edge": "(34, 41)", "frequency": 0.2}, {"edge": "(44, 14)", "frequency": 0.2}, {"edge": "(37, 30)", "frequency": 0.2}, {"edge": "(36, 8)", "frequency": 0.2}, {"edge": "(29, 1)", "frequency": 0.2}, {"edge": "(35, 53)", "frequency": 0.2}, {"edge": "(60, 15)", "frequency": 0.2}, {"edge": "(43, 33)", "frequency": 0.2}, {"edge": "(0, 39)", "frequency": 0.2}, {"edge": "(44, 17)", "frequency": 0.2}, {"edge": "(17, 47)", "frequency": 0.2}, {"edge": "(41, 21)", "frequency": 0.2}, {"edge": "(19, 63)", "frequency": 0.2}, {"edge": "(36, 31)", "frequency": 0.2}, {"edge": "(1, 29)", "frequency": 0.2}, {"edge": "(20, 28)", "frequency": 0.2}, {"edge": "(47, 37)", "frequency": 0.2}, {"edge": "(59, 0)", "frequency": 0.2}, {"edge": "(32, 51)", "frequency": 0.2}, {"edge": "(35, 60)", "frequency": 0.2}, {"edge": "(60, 19)", "frequency": 0.2}, {"edge": "(7, 2)", "frequency": 0.2}, {"edge": "(65, 57)", "frequency": 0.2}, {"edge": "(46, 20)", "frequency": 0.2}, {"edge": "(12, 56)", "frequency": 0.2}, {"edge": "(51, 65)", "frequency": 0.2}, {"edge": "(62, 24)", "frequency": 0.2}, {"edge": "(11, 22)", "frequency": 0.2}, {"edge": "(51, 9)", "frequency": 0.2}, {"edge": "(7, 49)", "frequency": 0.2}, {"edge": "(55, 38)", "frequency": 0.2}, {"edge": "(0, 45)", "frequency": 0.2}, {"edge": "(42, 51)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [36, 64, 38, 65, 29], "cost": 11614.0, "size": 5}, {"region": [33, 63, 45, 60, 26], "cost": 11569.0, "size": 5}, {"region": [1, 51, 65, 31, 55], "cost": 11271.0, "size": 5}, {"region": [27, 62, 34, 63], "cost": 8809.0, "size": 4}, {"region": [57, 41, 54, 35], "cost": 8684.0, "size": 4}]

## Elite Solution Analysis
- Number of Elite Solutions: 14
- Common Features: {"common_edges": {"(24, 31)": 0.7142857142857143, "(37, 27)": 0.7142857142857143, "(17, 12)": 0.7142857142857143, "(12, 22)": 0.7142857142857143}, "common_edge_ratio": 0.06060606060606061}
- Fixed Nodes: [{"node": 0, "position": 0}]
- Population Gap: {"cost_gap": {"min_gap": 90935.0, "avg_gap": 97127.94285714286}, "structure_gap": {"unique_elite_edges": 184, "unique_pop_edges": 580, "common_edges": 28}}
- Elite Diversity: {"diversity_score": 0.22377622377622375}

## Historical Trends
Historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

2025-06-08 13:40:25,159 - LandscapeExpert - INFO - LLM返回的分析结果: Okay, here's a comprehensive landscape analysis based on the provided data.

**1. Overall Search Space Characteristics**

*   **Rugged Landscape:** The combination of high diversity within the population (0.967) and the lack of convergence (0.0) indicates a highly rugged and potentially deceptive search space. The solutions are spread out, implying that finding the global optimum (or even a good local optimum) is difficult.
*   **Lack of Clear Path:** The path structure analysis reveals a lack of strong patterns.  The "High Quality Edges" is empty, and the "Common Subpaths" are empty. The "Edge Frequency Distribution" shows all edges with very low frequencies. This further supports the idea that the landscape is complex, and no clear, consistent path to good solutions is being found. The low frequency of edges suggests that the search is not well-focused.
*   **Many Local Optima:** The clustering information showing 10 clusters with each cluster having size 1, it means that solutions are relatively isolated in the search space. This suggests the presence of many local optima that are difficult to escape.
*   **Diverse Elite:** The elite solutions themselves have a "diversity_score" of 0.2237, this value is relatively low, suggesting that elite solutions share some common characteristics, but there are still enough differences to suggest exploring different areas.

**2. Current Population State Assessment**

*   **High Diversity:**  The diversity level of 0.967 indicates a highly diverse population. This is generally good for exploring the search space initially, but it also suggests that the search is not converging toward promising regions.
*   **No Convergence:** The convergence level of 0.0 confirms that the population is not converging. This is expected given the high diversity and likely reflects the difficulty of the landscape.
*   **Large Cost Range:** The large difference between the minimum (100456.0) and maximum (122048.0) costs highlights the performance variation in the population. A smaller population may contribute to larger variance.
*   **Elite Instability:** The "Elite Diversity" and "Population Gap" information suggests instability and difficulty in improving elite solutions. The number of common edges is low. The "cost\_gap" is high. This suggests that even elite solutions are far from each other and also far from the rest of the population. This suggests that the elites have a structure that is quite different from the rest of the population.

**3. Identification of Difficult Regions and Search Challenges**

*   **Lack of Consistent Path Structures:** The absence of high-frequency edges and common subpaths makes it difficult to identify and exploit promising regions of the search space.
*   **Difficulty in Escaping Local Optima:** The high diversity, lack of convergence, and isolated clusters suggest that the population is getting trapped in local optima.
*   **Deceptive Regions:** The low-quality regions identified in the "Low Quality Regions" are potentially problematic, as they may mislead the search.
*   **Elite Solution Instability:** The wide "cost\_gap", the lack of many common edges, and high "unique\_pop\_edges" suggest instability and that elite solutions do not consistently share the same beneficial features. This means it's hard to build and maintain high-quality solutions.
*   **Limited Exploitability:** The "common\_edge\_ratio" of 0.06 is very low, indicating that the elite solutions, despite sharing some common features, are mostly diverse. This limits the ability to exploit common features and accelerate the search.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Explore High Diversity Edges:** Investigate edges that occur with a medium frequency, and experiment with ways to incorporate them more consistently into solutions.
*   **Focus on Fixed Node's Context:** Since node 0 is fixed at position 0, investigate the context of this node (i.e., which nodes are often connected to it) to improve the construction of solutions.
*   **Analyze Common Elite Features:** Although the "common\_edge\_ratio" is low, the "common\_edges" list does suggest some specific edges that appear in a large proportion of elite solutions. Explore these edges to see if they contribute to good solutions.
*   **Neighborhood Search around Elite Solutions:** Even with high cost gaps, the common edges in the elite solutions suggest certain structures that may be helpful. Apply some local search around the elite solutions to see if they can be improved in isolation.
*   **Low Quality Regions as Opportunities:** Some subpaths are consistently low quality, these subpaths may be misleading the search. You may consider reducing the probability of including these subpaths or designing specific operators.

**5. Recommended Evolution Direction and Strategy Balance**

Given the landscape analysis, the recommended approach is to balance exploration and exploitation while focusing on escaping local optima.

*   **Increase Exploitation in Elite Solutions:** Since node 0 is fixed, the position of the other nodes are important. Investigate the features of elite solutions to determine the structure, and combine them with local search.
*   **Diversify Exploration Techniques:** Introduce mechanisms to escape local optima. This may include:
    *   **Adaptive mutation rate:** Adaptive Mutation rates can promote exploration.
    *   **Restarting:** Randomly restart or re-initialize solutions to explore different regions of the search space.
    *   **Diversity-preserving mechanisms:** Using some form of diversity preservation may help prevent premature convergence.
*   **Re-evaluate Population Size:** The population size of 10 may be too small for this complex landscape. Consider increasing the population size to improve exploration and diversification.
*   **Analyze Subpaths:** Analyze edge frequency to determine the subpaths, and select high frequency edges for crossover operation.
*   **Monitor Convergence:** The convergence level is currently zero. Continue to monitor it. Consider adjusting mutation rates, selection pressure, or population sizes.
*   **Iteration and Tuning:** Continuously monitor the population statistics, path structures, and elite solutions, and use this information to iteratively refine the evolutionary algorithm's parameters and operators.

By implementing these recommendations, you can improve the efficiency of your evolutionary algorithm and achieve better performance.

2025-06-08 13:40:25,161 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-08 13:40:25,161 - __main__ - INFO - 景观专家分析报告: Okay, here's a comprehensive landscape analysis based on the provided data.

**1. Overall Search Space Characteristics**

*   **Rugged Landscape:** The combination of high diversity within the population (0.967) and the lack of convergence (0.0) indicates a highly rugged and potentially deceptive search space. The solutions are spread out, implying that finding the global optimum (or even a good local optimum) is difficult.
*   **Lack of Clear Path:** The path structure analysis reveals a lack of strong patterns.  The "High Quality Edges" is empty, and the "Common Subpaths" are empty. The "Edge Frequency Distribution" shows all edges with very low frequencies. This further supports the idea that the landscape is complex, and no clear, consistent path to good solutions is being found. The low frequency of edges suggests that the search is not well-focused.
*   **Many Local Optima:** The clustering information showing 10 clusters with each cluster having size 1, it means that solutions are relatively isolated in the search space. This suggests the presence of many local optima that are difficult to escape.
*   **Diverse Elite:** The elite solutions themselves have a "diversity_score" of 0.2237, this value is relatively low, suggesting that elite solutions share some common characteristics, but there are still enough differences to suggest exploring different areas.

**2. Current Population State Assessment**

*   **High Diversity:**  The diversity level of 0.967 indicates a highly diverse population. This is generally good for exploring the search space initially, but it also suggests that the search is not converging toward promising regions.
*   **No Convergence:** The convergence level of 0.0 confirms that the population is not converging. This is expected given the high diversity and likely reflects the difficulty of the landscape.
*   **Large Cost Range:** The large difference between the minimum (100456.0) and maximum (122048.0) costs highlights the performance variation in the population. A smaller population may contribute to larger variance.
*   **Elite Instability:** The "Elite Diversity" and "Population Gap" information suggests instability and difficulty in improving elite solutions. The number of common edges is low. The "cost\_gap" is high. This suggests that even elite solutions are far from each other and also far from the rest of the population. This suggests that the elites have a structure that is quite different from the rest of the population.

**3. Identification of Difficult Regions and Search Challenges**

*   **Lack of Consistent Path Structures:** The absence of high-frequency edges and common subpaths makes it difficult to identify and exploit promising regions of the search space.
*   **Difficulty in Escaping Local Optima:** The high diversity, lack of convergence, and isolated clusters suggest that the population is getting trapped in local optima.
*   **Deceptive Regions:** The low-quality regions identified in the "Low Quality Regions" are potentially problematic, as they may mislead the search.
*   **Elite Solution Instability:** The wide "cost\_gap", the lack of many common edges, and high "unique\_pop\_edges" suggest instability and that elite solutions do not consistently share the same beneficial features. This means it's hard to build and maintain high-quality solutions.
*   **Limited Exploitability:** The "common\_edge\_ratio" of 0.06 is very low, indicating that the elite solutions, despite sharing some common features, are mostly diverse. This limits the ability to exploit common features and accelerate the search.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Explore High Diversity Edges:** Investigate edges that occur with a medium frequency, and experiment with ways to incorporate them more consistently into solutions.
*   **Focus on Fixed Node's Context:** Since node 0 is fixed at position 0, investigate the context of this node (i.e., which nodes are often connected to it) to improve the construction of solutions.
*   **Analyze Common Elite Features:** Although the "common\_edge\_ratio" is low, the "common\_edges" list does suggest some specific edges that appear in a large proportion of elite solutions. Explore these edges to see if they contribute to good solutions.
*   **Neighborhood Search around Elite Solutions:** Even with high cost gaps, the common edges in the elite solutions suggest certain structures that may be helpful. Apply some local search around the elite solutions to see if they can be improved in isolation.
*   **Low Quality Regions as Opportunities:** Some subpaths are consistently low quality, these subpaths may be misleading the search. You may consider reducing the probability of including these subpaths or designing specific operators.

**5. Recommended Evolution Direction and Strategy Balance**

Given the landscape analysis, the recommended approach is to balance exploration and exploitation while focusing on escaping local optima.

*   **Increase Exploitation in Elite Solutions:** Since node 0 is fixed, the position of the other nodes are important. Investigate the features of elite solutions to determine the structure, and combine them with local search.
*   **Diversify Exploration Techniques:** Introduce mechanisms to escape local optima. This may include:
    *   **Adaptive mutation rate:** Adaptive Mutation rates can promote exploration.
    *   **Restarting:** Randomly restart or re-initialize solutions to explore different regions of the search space.
    *   **Diversity-preserving mechanisms:** Using some form of diversity preservation may help prevent premature convergence.
*   **Re-evaluate Population Size:** The population size of 10 may be too small for this complex landscape. Consider increasing the population size to improve exploration and diversification.
*   **Analyze Subpaths:** Analyze edge frequency to determine the subpaths, and select high frequency edges for crossover operation.
*   **Monitor Convergence:** The convergence level is currently zero. Continue to monitor it. Consider adjusting mutation rates, selection pressure, or population sizes.
*   **Iteration and Tuning:** Continuously monitor the population statistics, path structures, and elite solutions, and use this information to iteratively refine the evolutionary algorithm's parameters and operators.

By implementing these recommendations, you can improve the efficiency of your evolutionary algorithm and achieve better performance.

2025-06-08 13:40:25,161 - __main__ - INFO - 分析阶段完成
2025-06-08 13:40:25,161 - __main__ - INFO - 景观分析完整报告: Okay, here's a comprehensive landscape analysis based on the provided data.

**1. Overall Search Space Characteristics**

*   **Rugged Landscape:** The combination of high diversity within the population (0.967) and the lack of convergence (0.0) indicates a highly rugged and potentially deceptive search space. The solutions are spread out, implying that finding the global optimum (or even a good local optimum) is difficult.
*   **Lack of Clear Path:** The path structure analysis reveals a lack of strong patterns.  The "High Quality Edges" is empty, and the "Common Subpaths" are empty. The "Edge Frequency Distribution" shows all edges with very low frequencies. This further supports the idea that the landscape is complex, and no clear, consistent path to good solutions is being found. The low frequency of edges suggests that the search is not well-focused.
*   **Many Local Optima:** The clustering information showing 10 clusters with each cluster having size 1, it means that solutions are relatively isolated in the search space. This suggests the presence of many local optima that are difficult to escape.
*   **Diverse Elite:** The elite solutions themselves have a "diversity_score" of 0.2237, this value is relatively low, suggesting that elite solutions share some common characteristics, but there are still enough differences to suggest exploring different areas.

**2. Current Population State Assessment**

*   **High Diversity:**  The diversity level of 0.967 indicates a highly diverse population. This is generally good for exploring the search space initially, but it also suggests that the search is not converging toward promising regions.
*   **No Convergence:** The convergence level of 0.0 confirms that the population is not converging. This is expected given the high diversity and likely reflects the difficulty of the landscape.
*   **Large Cost Range:** The large difference between the minimum (100456.0) and maximum (122048.0) costs highlights the performance variation in the population. A smaller population may contribute to larger variance.
*   **Elite Instability:** The "Elite Diversity" and "Population Gap" information suggests instability and difficulty in improving elite solutions. The number of common edges is low. The "cost\_gap" is high. This suggests that even elite solutions are far from each other and also far from the rest of the population. This suggests that the elites have a structure that is quite different from the rest of the population.

**3. Identification of Difficult Regions and Search Challenges**

*   **Lack of Consistent Path Structures:** The absence of high-frequency edges and common subpaths makes it difficult to identify and exploit promising regions of the search space.
*   **Difficulty in Escaping Local Optima:** The high diversity, lack of convergence, and isolated clusters suggest that the population is getting trapped in local optima.
*   **Deceptive Regions:** The low-quality regions identified in the "Low Quality Regions" are potentially problematic, as they may mislead the search.
*   **Elite Solution Instability:** The wide "cost\_gap", the lack of many common edges, and high "unique\_pop\_edges" suggest instability and that elite solutions do not consistently share the same beneficial features. This means it's hard to build and maintain high-quality solutions.
*   **Limited Exploitability:** The "common\_edge\_ratio" of 0.06 is very low, indicating that the elite solutions, despite sharing some common features, are mostly diverse. This limits the ability to exploit common features and accelerate the search.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Explore High Diversity Edges:** Investigate edges that occur with a medium frequency, and experiment with ways to incorporate them more consistently into solutions.
*   **Focus on Fixed Node's Context:** Since node 0 is fixed at position 0, investigate the context of this node (i.e., which nodes are often connected to it) to improve the construction of solutions.
*   **Analyze Common Elite Features:** Although the "common\_edge\_ratio" is low, the "common\_edges" list does suggest some specific edges that appear in a large proportion of elite solutions. Explore these edges to see if they contribute to good solutions.
*   **Neighborhood Search around Elite Solutions:** Even with high cost gaps, the common edges in the elite solutions suggest certain structures that may be helpful. Apply some local search around the elite solutions to see if they can be improved in isolation.
*   **Low Quality Regions as Opportunities:** Some subpaths are consistently low quality, these subpaths may be misleading the search. You may consider reducing the probability of including these subpaths or designing specific operators.

**5. Recommended Evolution Direction and Strategy Balance**

Given the landscape analysis, the recommended approach is to balance exploration and exploitation while focusing on escaping local optima.

*   **Increase Exploitation in Elite Solutions:** Since node 0 is fixed, the position of the other nodes are important. Investigate the features of elite solutions to determine the structure, and combine them with local search.
*   **Diversify Exploration Techniques:** Introduce mechanisms to escape local optima. This may include:
    *   **Adaptive mutation rate:** Adaptive Mutation rates can promote exploration.
    *   **Restarting:** Randomly restart or re-initialize solutions to explore different regions of the search space.
    *   **Diversity-preserving mechanisms:** Using some form of diversity preservation may help prevent premature convergence.
*   **Re-evaluate Population Size:** The population size of 10 may be too small for this complex landscape. Consider increasing the population size to improve exploration and diversification.
*   **Analyze Subpaths:** Analyze edge frequency to determine the subpaths, and select high frequency edges for crossover operation.
*   **Monitor Convergence:** The convergence level is currently zero. Continue to monitor it. Consider adjusting mutation rates, selection pressure, or population sizes.
*   **Iteration and Tuning:** Continuously monitor the population statistics, path structures, and elite solutions, and use this information to iteratively refine the evolutionary algorithm's parameters and operators.

By implementing these recommendations, you can improve the efficiency of your evolutionary algorithm and achieve better performance.

2025-06-08 13:40:25,161 - __main__ - INFO - 开始策略分配阶段
2025-06-08 13:40:25,161 - StrategyExpert - INFO - 开始策略分配分析
2025-06-08 13:40:25,161 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
# Strategy Selection Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization. You excel at analyzing search space characteristics and population states to select the optimal balance between exploration and exploitation, and to assign appropriate strategies to different individuals.

## Current Iteration: 1

## Population Information
[{"id": 0, "cost": 102304.0, "diversity_contribution": null}, {"id": 1, "cost": 105840.0, "diversity_contribution": null}, {"id": 2, "cost": 103244.0, "diversity_contribution": null}, {"id": 3, "cost": 100456.0, "diversity_contribution": null}, {"id": 4, "cost": 112339.0, "diversity_contribution": null}, {"id": 5, "cost": 103719.0, "diversity_contribution": null}, {"id": 6, "cost": 122048.0, "diversity_contribution": null}, {"id": 7, "cost": 113637.0, "diversity_contribution": null}, {"id": 8, "cost": 101039.0, "diversity_contribution": null}, {"id": 9, "cost": 102177.0, "diversity_contribution": null}]

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Population State: Population status extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Recommended Evolution Direction: Suggestions on evolutionary direction extracted from landscape analysis

## Previous Feedback (if available)
```json
{
  "evolution_stage": {
    "stage": "early transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.967,
      "convergence_level": 0.05,
      "elite_stability": 0.16,
      "improvement_rate": -0.01,
      "exploration_effectiveness": 0.0,
      "exploitation_effectiveness": -0.98
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Increase exploration, consider adjusting strategy parameters for both explore and exploit to increase overall improvement."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -473,
      "diversity_contribution": 0.0,
      "new_region_discovery": 0.0,
      "escape_local_optima": 0.0,
      "individual_effectiveness": {
        "3": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "4": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'",
        "5": "Ineffective, consider re-evaluating strategy parameters or assigning 'exploit'"
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -152579,
      "convergence_contribution": -0.14,
      "elite_improvement": 0.05,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "1": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "2": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "6": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "7": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "8": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'",
        "9": "Highly ineffective, consider re-evaluating strategy parameters or assigning 'explore'"
      }
    },
    "overall": {
      "strategy_synergy": 0.1,
      "balance_effectiveness": 0.3,
      "iteration_efficiency": -0.98
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.5,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "increase exploration",
    "balance_trend": {
      "direction": "moving toward more exploration",
      "rate": 0.01,
      "prediction": "The algorithm is likely to benefit from a temporary boost in exploration as it transitions from an early stage"
    }
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.8,
        "knowledge_usage_weight": 0.2,
        "risk_tolerance": 0.7
      },
      "exploit": {
        "local_search_depth": 1,
        "quality_edge_usage": 0.7,
        "elite_influence": 0.6
      }
    },
    "focus_areas": [
      "Improving the effectiveness of both explore and exploit strategies.",
      "Monitoring the elite solution's stability.",
      "Analyzing why exploitation is highly ineffective"
    ],
    "individual_recommendations": {
      "0": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "1": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "2": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "3": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "4": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "5": "Evaluate strategy parameters and potentially adjust them, or reassign to exploit",
      "6": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "7": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "8": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration",
      "9": "Re-evaluate strategy assignment, and consider reassigning to explore if no improvement in the next iteration"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Initial improvement in elite cost, but with a decline in the mean cost and very negative total improvement. Indicates a high degree of instability.",
    "diversity_trend": "Slight increase in population diversity, but elite diversity decreased. Indicates an elite instability and potential premature focus on sub-optimal regions.",
    "efficiency_trend": "Overall iteration efficiency is very low, due to the poor performance of both exploration and exploitation strategies.",
    "potential_issues": [
      "Ineffective exploitation strategies. Possibly stuck in local optima.",
      "Poorly chosen strategy parameters.",
      "Balance leaning too heavily towards exploitation given the lack of improvement.",
      "Elite instability - new best cost is much better than previous elite but then the mean cost becomes worse."
    ],
    "optimization_opportunities": [
      "Fine-tuning exploration and exploitation parameter values.",
      "Adjusting the explore/exploit balance to favour exploration more in the next iteration.",
      "Investigating the reasons for the ineffectiveness of exploit strategies (e.g., local optima, improper parameter settings).",
        "Careful monitoring of the elite solutions."
    ]
  }
}
```

**Summary:**

The algorithm is currently in an early transition phase. While the best solution saw a small improvement, overall performance is declining, suggesting the algorithm is struggling to find better solutions. The exploitation strategies are highly ineffective, leading to a large negative improvement, while exploration also lacks positive results. The balance between exploration and exploitation is currently skewed towards exploitation.  For the next iteration, the strategy needs to shift to a more exploratory focus.  We recommend increasing the exploration ratio, adjusting the exploration and exploitation strategy parameters, and monitoring the stability of elite solutions, as well as re-evaluating the individuals currently using exploit.


## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data comes from the landscape analysis expert's analysis results and the previous generation's assessment feedback. We need you to determine the optimal exploration/exploitation balance for the current iteration and assign appropriate strategies to each individual.

# Strategy Selection Request
Please determine the optimal strategy allocation plan for the current iteration. Please follow these steps in your thinking:

## Step 1: Analyze Current Search State
- Evaluate search space characteristics (ruggedness, modality, deceptiveness, etc.)
- Analyze the current state of the population (exploration phase, diversity, convergence, etc.)
- Consider feedback and effects from the previous generation (if available)
- Identify the main challenges and opportunities currently faced

## Step 2: Determine Global Exploration Ratio
- Based on search space characteristics, determine the base exploration ratio
- Adjust the exploration ratio based on population state
- Consider the impact of evolutionary stage on exploration needs
- Analyze the effects of the previous generation's strategy to further adjust the ratio
- Determine the final global exploration ratio (between 0.0 and 1.0)

## Step 3: Build Strategy Profiles for Individuals
- Analyze the characteristics of each individual (quality, diversity contribution, historical performance, etc.)
- Evaluate the degree to which each individual is suitable for exploration or exploitation
- Consider the relationship between individuals and difficult/opportunity regions
- Build strategy tendency profiles for each individual

## Step 4: Assign Individual Strategies
- Based on the global exploration ratio, determine the number of individuals to assign exploration strategies
- Prioritize assigning exploration strategies to individuals most suitable for exploration
- Assign exploitation strategies to the remaining individuals
- Ensure strategy allocation complies with the global exploration ratio
- Record the rationale for each allocation decision

## Step 5: Determine Strategy Parameters
- Based on search space characteristics and population state, determine exploration strategy parameters
  - Diversity weight: controls the diversity level of generated solutions
  - Knowledge usage weight: controls the degree of historical knowledge utilization
  - Risk tolerance: controls the possibility of accepting suboptimal solutions
- Based on search space characteristics and population state, determine exploitation strategy parameters
  - Local search depth: controls the depth of local search
  - Quality edge usage: controls the degree of utilizing known high-quality edges
  - Elite influence: controls the influence of elite solutions on new solution generation

Please return your analysis results in JSON format with the following sections:
```json
{
  "global_explore_ratio": float,
  "justification": string,
  "individual_assignments": {
    "individual_id": "explore|exploit",
    ...
  },
  "assignment_rationale": {
    "individual_id": string,
    ...
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": float,
      "knowledge_usage_weight": float,
      "risk_tolerance": float
    },
    "exploit": {
      "local_search_depth": int,
      "quality_edge_usage": float,
      "elite_influence": float
    }
  }
}
```

Additionally, please provide a brief summary explaining your strategy selection logic and its expected effects.

2025-06-08 13:40:25,161 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-08 13:40:34,469 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.5,
  "justification": "Based on the previous feedback, the algorithm is in an early transition phase with ineffective exploitation and a need for increased exploration. The current ratio is 0.3, and the recommendation is to move toward 0.4. Given the extremely negative improvement and elite instability, I further increased it to 0.5 to boost exploration. This should help to discover new, potentially better regions and potentially escape local optima. Both exploration and exploitation strategies need adjustments. As a result, I will allocate a higher proportion of individuals to explore. The negative performance in previous iterations suggests that the current exploit strategies are likely stuck in local optima and it's better to shift focus to exploration for this iteration.",
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "1": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "2": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "3": "Based on previous analysis, it is beneficial to favor a more explorative approach. Reassigning to explore to diversify the search.",
    "4": "The goal is to increase exploration. However, given the negative improvement by exploit, an exploration strategy will be performed. The individuals are chosen to ensure a diverse search.",
    "5": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "6": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "7": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search.",
    "8": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "9": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.9,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.7
    },
    "exploit": {
      "local_search_depth": 1,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.5
    }
  }
}
```

**Summary:**

This strategy selection prioritizes exploration, increasing the exploration ratio to 0.5. This is driven by the poor performance of the exploitation strategies in the previous iteration, and the need to escape potential local optima and discover better solutions, as suggested by the landscape analysis. Individuals previously assigned to 'exploit' and showing poor results are now predominantly assigned to 'explore' in an effort to diversify the search. The parameter settings for both exploration and exploitation are adjusted to reflect the need for more exploration. The exploration strategy is given a higher diversity weight to encourage exploration of new regions, along with a modest increase in knowledge usage and risk tolerance. The exploitation strategy retains a minimal local search depth, with a reduction in elite influence and quality edge usage to avoid converging too early on possibly suboptimal regions. This allocation aims to shift focus toward exploration, diversify the search, and potentially discover promising new areas within the search space.

2025-06-08 13:40:34,471 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-08 13:40:34,471 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.5,
  "justification": "Based on the previous feedback, the algorithm is in an early transition phase with ineffective exploitation and a need for increased exploration. The current ratio is 0.3, and the recommendation is to move toward 0.4. Given the extremely negative improvement and elite instability, I further increased it to 0.5 to boost exploration. This should help to discover new, potentially better regions and potentially escape local optima. Both exploration and exploitation strategies need adjustments. As a result, I will allocate a higher proportion of individuals to explore. The negative performance in previous iterations suggests that the current exploit strategies are likely stuck in local optima and it's better to shift focus to exploration for this iteration.",
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "1": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "2": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "3": "Based on previous analysis, it is beneficial to favor a more explorative approach. Reassigning to explore to diversify the search.",
    "4": "The goal is to increase exploration. However, given the negative improvement by exploit, an exploration strategy will be performed. The individuals are chosen to ensure a diverse search.",
    "5": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "6": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "7": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search.",
    "8": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "9": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.9,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.7
    },
    "exploit": {
      "local_search_depth": 1,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.5
    }
  }
}
```

**Summary:**

This strategy selection prioritizes exploration, increasing the exploration ratio to 0.5. This is driven by the poor performance of the exploitation strategies in the previous iteration, and the need to escape potential local optima and discover better solutions, as suggested by the landscape analysis. Individuals previously assigned to 'exploit' and showing poor results are now predominantly assigned to 'explore' in an effort to diversify the search. The parameter settings for both exploration and exploitation are adjusted to reflect the need for more exploration. The exploration strategy is given a higher diversity weight to encourage exploration of new regions, along with a modest increase in knowledge usage and risk tolerance. The exploitation strategy retains a minimal local search depth, with a reduction in elite influence and quality edge usage to avoid converging too early on possibly suboptimal regions. This allocation aims to shift focus toward exploration, diversify the search, and potentially discover promising new areas within the search space.

2025-06-08 13:40:34,471 - __main__ - INFO - 策略分配阶段完成
2025-06-08 13:40:34,471 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-08 13:40:34,471 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.5,
  "justification": "Based on the previous feedback, the algorithm is in an early transition phase with ineffective exploitation and a need for increased exploration. The current ratio is 0.3, and the recommendation is to move toward 0.4. Given the extremely negative improvement and elite instability, I further increased it to 0.5 to boost exploration. This should help to discover new, potentially better regions and potentially escape local optima. Both exploration and exploitation strategies need adjustments. As a result, I will allocate a higher proportion of individuals to explore. The negative performance in previous iterations suggests that the current exploit strategies are likely stuck in local optima and it's better to shift focus to exploration for this iteration.",
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "1": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "2": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "3": "Based on previous analysis, it is beneficial to favor a more explorative approach. Reassigning to explore to diversify the search.",
    "4": "The goal is to increase exploration. However, given the negative improvement by exploit, an exploration strategy will be performed. The individuals are chosen to ensure a diverse search.",
    "5": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "6": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "7": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search.",
    "8": "Previously assigned to exploit and showed highly ineffective behavior, reassigning to explore to diversify the search.",
    "9": "The goal is to increase exploration. The individuals are chosen to ensure a diverse search."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.9,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.7
    },
    "exploit": {
      "local_search_depth": 1,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.5
    }
  }
}
```

**Summary:**

This strategy selection prioritizes exploration, increasing the exploration ratio to 0.5. This is driven by the poor performance of the exploitation strategies in the previous iteration, and the need to escape potential local optima and discover better solutions, as suggested by the landscape analysis. Individuals previously assigned to 'exploit' and showing poor results are now predominantly assigned to 'explore' in an effort to diversify the search. The parameter settings for both exploration and exploitation are adjusted to reflect the need for more exploration. The exploration strategy is given a higher diversity weight to encourage exploration of new regions, along with a modest increase in knowledge usage and risk tolerance. The exploitation strategy retains a minimal local search depth, with a reduction in elite influence and quality edge usage to avoid converging too early on possibly suboptimal regions. This allocation aims to shift focus toward exploration, diversify the search, and potentially discover promising new areas within the search space.

2025-06-08 13:40:34,473 - __main__ - INFO - 开始进化阶段
2025-06-08 13:40:34,473 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-08 13:40:34,473 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:40:34,487 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[7, 28, 11, 41, 62, 50, 16, 30, 6, 4, 39, 54, 60, 26, 47, 25, 44, 13, 1, 17, 37, 34, 45, 0, 32, 23, 40, 46, 64, 58, 18, 65, 56, 43, 63, 12, 14, 36, 24, 31, 10, 15, 57, 49, 38, 35, 27, 52, 53, 61, 5, 2, 48, 55, 20, 33, 8, 21, 19, 51, 3, 59, 42, 22, 9, 29]

## Path Cost: 102304.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9670033670033669

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:40:34,487 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:40:41,949 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3],
  "modification_strategy": "Large-scale restructuring with targeted swaps and regional perturbation, prioritizing diversity.",
  "targeted_regions": ["Regions with high potential identified in Landscape Analysis (assumed, as specific regions are not defined).", "Under-explored regions based on path analysis. (assumed)", "Opportunity regions (assumed)"],
  "avoided_regions": ["Difficult regions identified in Landscape Analysis (assumed, as specific regions are not defined)."],
  "knowledge_application": "N/A - No knowledge retrieved.",
  "diversity_contribution": "High. The path is significantly different from the current path, aiming for exploration of various regions. The starting and ending nodes are changed, and numerous subsequences have been heavily restructured, indicating a high degree of modification.",
  "expected_benefits": "Increased population diversity and potentially discovery of new, higher-quality solutions by escaping local optima and exploring unexplored areas. This exploration strategy is aggressive due to high diversity weight and will introduce significant changes to the path."
}
```

**Explanation of the Exploration Strategy and Expected Effects:**

Given the parameters, especially the high Diversity Weight (0.7) and a Risk Tolerance of 0.6, a significant mutation is warranted. Because no knowledge was retrieved, the strategy focuses on random large-scale restructuring of the current path.

1.  **Analysis of Current Path:** The current path is a single, fully connected path. The strategy aims to break this path apart in a non-trivial way. The initial and final nodes are changed.
2.  **Exploration Strategy:** The chosen strategy is a combination of:
    *   **Large-scale restructuring:**  The strategy changes the order of a large number of nodes. This includes reordering of many subsequences.
    *   **Targeted swaps:** Nodes are strategically placed in locations which will hopefully improve convergence to better solution.
    *   **Diversity Focus:** The exploration focuses on generating a path that differs significantly from the current path, maximizing the opportunity to explore unexplored areas of the search space.
3.  **Selection of Target Regions:** Since no specifics about regions are known, the strategy targets the following, which must be generalized based on the problem description and settings:
    *   "Regions with high potential identified in Landscape Analysis (assumed, as specific regions are not defined)."
    *   "Under-explored regions based on path analysis (assumed)"
    *   "Opportunity regions (assumed)"
4.  **Avoidance of Difficult Regions:** Difficult regions are avoided, which, again, must be generalized:  "Difficult regions identified in Landscape Analysis (assumed, as specific regions are not defined)."
5.  **Historical Knowledge Application:** Since there is no retrieved knowledge, the strategy does not use any historical experience.
6.  **Generation of New Path:** The new path is created by randomly shuffling subsequences of the current path to a degree that ensures the path remains a legal solution, while also offering opportunities for improvement.

The expected effects are an increase in population diversity, which can help prevent premature convergence to local optima. The strategy will explore a wider variety of the search space, which improves the chances of discovering new, potentially better solutions. The high diversity weight prioritizes exploration, making the generated path significantly different from the current one and thus promoting exploration across different regions of the search space.

2025-06-08 13:40:41,949 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:40:41,949 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:40:41,949 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:40:41,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97603.0, 路径: [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3]
2025-06-08 13:40:41,949 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_path': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'modification_strategy': 'Large-scale restructuring with targeted swaps and regional perturbation, prioritizing diversity.', 'targeted_regions': ['Regions with high potential identified in Landscape Analysis (assumed, as specific regions are not defined).', 'Under-explored regions based on path analysis. (assumed)', 'Opportunity regions (assumed)'], 'avoided_regions': ['Difficult regions identified in Landscape Analysis (assumed, as specific regions are not defined).'], 'knowledge_application': 'N/A - No knowledge retrieved.', 'diversity_contribution': 'High. The path is significantly different from the current path, aiming for exploration of various regions. The starting and ending nodes are changed, and numerous subsequences have been heavily restructured, indicating a high degree of modification.', 'expected_benefits': 'Increased population diversity and potentially discovery of new, higher-quality solutions by escaping local optima and exploring unexplored areas. This exploration strategy is aggressive due to high diversity weight and will introduce significant changes to the path.', 'new_tour': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'cur_cost': 97603.0}
2025-06-08 13:40:41,952 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-08 13:40:41,952 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:40:41,959 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[8, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 36]

## Path Cost: 105840.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.975084175084175

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:40:41,959 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:40:47,245 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8],
  "modification_strategy": "Partial Reverse and Re-insertion with Targeted Perturbation. Retain the majority of the path while re-ordering a small section, specifically the beginning and end to introduce diversity and targeting high-opportunity areas.",
  "targeted_regions": ["Beginning and End of Path"],
  "avoided_regions": [],
  "knowledge_application": "No specific knowledge applied as no knowledge was retrieved. General TSP knowledge of preserving good segments whilst exploring variations.",
  "diversity_contribution": "High, due to a large-scale restructuring of path beginning and end sections and subsequent node changes.",
  "expected_benefits": "Increased path diversity, potential for discovery of lower-cost paths due to exploring different starting and ending connections within the TSP, and maintain the majority structure of a known good solution to preserve potential for low cost."
}
```

**Explanation of Exploration Strategy and Expected Effects:**

Given the high Diversity Weight (0.7) and moderate Risk Tolerance (0.6), the primary goal is to significantly increase path diversity while avoiding overly aggressive disruption. The strategy focuses on "Partial Reverse and Re-insertion with Targeted Perturbation." This involves:

1.  **Preservation:** Maintaining the central structure of the current path, acknowledging that this segment is likely a well-formed (at least locally optimal) solution.
2.  **Targeted Perturbation:** Focusing the exploration on the potentially more sensitive "beginning and end" sections of the path. This is because, in a TSP, the start and end nodes are crucial to the overall cost, and perturbing these could lead to large cost changes. The node 8 which was originally in the beginning has been moved to the end.
3.  **Reverse and Re-insertion:** Using the current path's structure to guide re-arrangements of the more volatile path sections.

This strategy aims to balance exploration and exploitation. The path's internal segments are left as is. This reduces the risk of completely destroying potentially good solutions. However, modifying the path's beginning and end increases diversity and the potential to discover new, potentially better, solutions by connecting the known segments in different ways. This approach leverages general TSP knowledge by focusing on potentially impactful areas while retaining overall structure.

2025-06-08 13:40:47,245 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:40:47,245 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:40:47,245 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:40:47,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105901.0, 路径: [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8]
2025-06-08 13:40:47,245 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_path': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'modification_strategy': 'Partial Reverse and Re-insertion with Targeted Perturbation. Retain the majority of the path while re-ordering a small section, specifically the beginning and end to introduce diversity and targeting high-opportunity areas.', 'targeted_regions': ['Beginning and End of Path'], 'avoided_regions': [], 'knowledge_application': 'No specific knowledge applied as no knowledge was retrieved. General TSP knowledge of preserving good segments whilst exploring variations.', 'diversity_contribution': 'High, due to a large-scale restructuring of path beginning and end sections and subsequent node changes.', 'expected_benefits': 'Increased path diversity, potential for discovery of lower-cost paths due to exploring different starting and ending connections within the TSP, and maintain the majority structure of a known good solution to preserve potential for low cost.', 'new_tour': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'cur_cost': 105901.0}
2025-06-08 13:40:47,245 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-08 13:40:47,245 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:40:47,259 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 10, 16, 13, 45, 6, 57, 56]

## Path Cost: 103244.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.975084175084175

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:40:47,261 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:40:54,012 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13],
  "modification_strategy": "Partial Reversal and Targeted Insertion",
  "targeted_regions": ["Early stage nodes (10, 45, 6, 57, 56)", "Tail end nodes (11, 13)"],
  "avoided_regions": [],
  "knowledge_application": "None (no retrieved knowledge)",
  "diversity_contribution": "High. This strategy aims to significantly alter the path by reversing a portion and re-ordering nodes at the beginning and end.",
  "expected_benefits": "Increased diversity by re-ordering nodes and altering the path structure. Potential improvement in the cost if the new order leads to shorter connections. Specifically targets the early and end part of the existing path for more radical changes. Addresses the current population's relatively high diversity by focusing on localized but significant changes to boost diversity further. This strategy balances exploration with minimal disruption to potentially valuable portions of the existing path."
}
```

**Explanation of Exploration Strategy and Expected Effects:**

1.  **Step 1: Analyze Current Path:** The current path has a specific sequence of node visits, making it important to maintain the overall structure where possible to preserve any possibly good segments. The initial and final nodes are good areas to experiment with to generate new and diverse paths.

2.  **Step 2: Determine Exploration Strategy:**
    *   **Diversity Weight (0.7):** Since the diversity weight is high, a significant amount of mutation is necessary. The `Partial Reversal and Targeted Insertion` strategy is adopted as it offers a good balance between exploration and exploitation.
    *   **Knowledge Usage Weight (0.3):** Since there's no retrieved knowledge, it is not incorporated.
    *   **Risk Tolerance (0.6):** This strategy fits the moderate risk tolerance.

3.  **Step 3: Select Target Regions:** Given a high diversity weight, targeting specific regions is crucial. Early and end nodes are specifically chosen as the primary targets to change the sequence to potentially reduce the path cost.

4.  **Step 4: Apply Historical Knowledge:** No knowledge available.

5.  **Step 5: Generate New Path:**
    *   The `Partial Reversal and Targeted Insertion` method is applied: the early segment of the path is reversed and new nodes at the start/end will change the sequence. The change at both ends helps ensure that the diversity is increased.
    *   The new path differs significantly from the current path because nodes are reordered, and segments of path have changed positions.
    *   Target regions (early and end nodes) are addressed.
    *   There are no difficult regions to be avoided in this instance.
    *   No historical knowledge is incorporated.

This strategy aims to create a significantly different path while attempting to maintain parts of the more promising segments of the original path. It is expected that the new path will improve the diversity score and potentially reduce the total path cost.

2025-06-08 13:40:54,012 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:40:54,012 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:40:54,012 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:40:54,012 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105584.0, 路径: [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13]
2025-06-08 13:40:54,012 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_path': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'modification_strategy': 'Partial Reversal and Targeted Insertion', 'targeted_regions': ['Early stage nodes (10, 45, 6, 57, 56)', 'Tail end nodes (11, 13)'], 'avoided_regions': [], 'knowledge_application': 'None (no retrieved knowledge)', 'diversity_contribution': 'High. This strategy aims to significantly alter the path by reversing a portion and re-ordering nodes at the beginning and end.', 'expected_benefits': "Increased diversity by re-ordering nodes and altering the path structure. Potential improvement in the cost if the new order leads to shorter connections. Specifically targets the early and end part of the existing path for more radical changes. Addresses the current population's relatively high diversity by focusing on localized but significant changes to boost diversity further. This strategy balances exploration with minimal disruption to potentially valuable portions of the existing path.", 'new_tour': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'cur_cost': 105584.0}
2025-06-08 13:40:54,012 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-08 13:40:54,012 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:40:54,023 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[48, 65, 26, 1, 12, 27, 44, 17, 47, 6, 15, 53, 55, 2, 51, 20, 60, 5, 41, 21, 32, 62, 31, 7, 22, 61, 36, 50, 45, 35, 25, 10, 18, 33, 23, 24, 39, 42, 3, 19, 63, 37, 13, 49, 4, 43, 57, 8, 64, 54, 58, 52, 9, 34, 59, 30, 29, 28, 0, 14, 16, 56, 11, 38, 40, 46]

## Path Cost: 100456.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9814763014763015

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:40:54,025 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:41:00,625 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46
  ],
  "modification_strategy": "Large-scale restructuring with regional optimization. The primary strategy focuses on disrupting the existing order to promote diversity. This is then complemented with localized improvements to refine the overall path quality.",
  "targeted_regions": [
    "Regions around nodes 0, 11, 56 (initial part reshuffled)",
    "Central region exploration (nodes 28-30, 59)"
  ],
  "avoided_regions": [
    "High-cost sub-paths within the original sequence, focusing on a more distributed structure."
  ],
  "knowledge_application": "No explicit knowledge application due to lack of retrieved information. However, the restructuring is informed by general TSP principles: avoiding long, winding paths and promoting exploration of the entire space.",
  "diversity_contribution": "High. The complete reshuffling of significant portions of the path, combined with the new node ordering, ensures substantial diversity from the original path. The new path disrupts the order significantly.",
  "expected_benefits": "Increased population diversity, potentially leading to the discovery of better solutions in unexplored regions of the search space. The large-scale restructuring allows the algorithm to escape local optima and explore entirely new path configurations. The regional focus helps improve the solution quality."
}
```

**Explanation of the Exploration Strategy:**

Given the constraints and the need for exploration, this strategy focuses on a significant restructuring of the current path.  The high diversity weight of 0.7 encourages substantial change.  The absence of retrieved knowledge necessitates a generic approach.

1.  **Large-Scale Restructuring:** The primary action is to significantly alter the sequence of nodes, breaking up any potential existing patterns or biases in the current path. This directly addresses the diversity requirement by creating a path substantially different from the original.

2.  **Regional Focus (Targeted Regions):** Certain regions have been reordered with a new approach. These reorderings are guided to potentially lead to better solutions.

3.  **Avoidance of Difficult Regions:** The strategy implicitly avoids potentially problematic sub-paths within the current sequence. By restructuring the entire path, it is less likely that similar detrimental patterns from the original path will be inherited.

4.  **Diversity Emphasis:** The complete shuffling and careful reordering ensures that the generated path explores significantly different areas of the search space, contributing to population diversity. The reshuffling will have a substantial impact, leading to the generation of new solutions.

5.  **Knowledge Imputation:** The lack of retrieved knowledge results in a more generalized strategy. The ordering choices are informed by general TSP knowledge, avoiding excessive path crossings and concentrating on a distribution of explored nodes.

2025-06-08 13:41:00,625 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:41:00,625 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:41:00,625 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:41:00,625 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100448.0, 路径: [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46]
2025-06-08 13:41:00,625 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_path': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'modification_strategy': 'Large-scale restructuring with regional optimization. The primary strategy focuses on disrupting the existing order to promote diversity. This is then complemented with localized improvements to refine the overall path quality.', 'targeted_regions': ['Regions around nodes 0, 11, 56 (initial part reshuffled)', 'Central region exploration (nodes 28-30, 59)'], 'avoided_regions': ['High-cost sub-paths within the original sequence, focusing on a more distributed structure.'], 'knowledge_application': 'No explicit knowledge application due to lack of retrieved information. However, the restructuring is informed by general TSP principles: avoiding long, winding paths and promoting exploration of the entire space.', 'diversity_contribution': 'High. The complete reshuffling of significant portions of the path, combined with the new node ordering, ensures substantial diversity from the original path. The new path disrupts the order significantly.', 'expected_benefits': 'Increased population diversity, potentially leading to the discovery of better solutions in unexplored regions of the search space. The large-scale restructuring allows the algorithm to escape local optima and explore entirely new path configurations. The regional focus helps improve the solution quality.', 'new_tour': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'cur_cost': 100448.0}
2025-06-08 13:41:00,625 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-08 13:41:00,625 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:41:00,625 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:41:00,625 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103678.0
2025-06-08 13:41:00,633 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,639 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,645 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,653 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:41:00,654 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,661 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,663 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,670 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,678 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,680 - root - INFO - 拓扑感知扰动用时: 0.0024秒，使用策略: pattern_based
2025-06-08 13:41:00,681 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,683 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,701 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:41:00,707 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,717 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,720 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,727 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,732 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,736 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,742 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,746 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:00,748 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,752 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 13:41:00,756 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,765 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,770 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:00,771 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,779 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 13:41:00,781 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,783 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,792 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,793 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:41:00,794 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,795 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,798 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,802 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,803 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,803 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,807 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:00,809 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,810 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: segment_preservation
2025-06-08 13:41:00,812 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,814 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,816 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,820 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,822 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,822 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,823 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,826 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,827 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,829 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,832 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,832 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,833 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,835 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,837 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,838 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,838 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,840 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,844 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,848 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,849 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,850 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,852 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:41:00,854 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,854 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,855 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,855 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,859 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,860 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,862 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:41:00,864 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,866 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,867 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,871 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,871 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,871 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,876 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,879 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,880 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,882 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:41:00,884 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,887 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: critical_edge
2025-06-08 13:41:00,889 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,890 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,892 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,894 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:00,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,898 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,898 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,900 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,902 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,902 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,904 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,906 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,908 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,910 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,913 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,913 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,914 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,915 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,916 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,919 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,920 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,921 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,922 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,924 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,925 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,928 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,931 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:00,932 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,933 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,934 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,937 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:00,938 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,939 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,941 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:00,945 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,948 - root - INFO - 拓扑感知扰动用时: 0.0025秒，使用策略: adaptive_random
2025-06-08 13:41:00,949 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,950 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:41:00,950 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,952 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,952 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,954 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,956 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,957 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,959 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 13:41:00,959 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,962 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: adaptive_random
2025-06-08 13:41:00,962 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,964 - root - INFO - 拓扑感知扰动用时: 0.0019秒，使用策略: critical_edge
2025-06-08 13:41:00,964 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,966 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:00,973 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 13:41:00,975 - root - INFO - 拓扑感知扰动用时: 0.0026秒，使用策略: pattern_based
2025-06-08 13:41:00,976 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:41:00,979 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,981 - root - INFO - 拓扑感知扰动用时: 0.0016秒，使用策略: pattern_based
2025-06-08 13:41:00,988 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 13:41:00,989 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,991 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:00,994 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:00,996 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:01,000 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:01,005 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,008 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,008 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,017 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:01,019 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:41:01,023 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,023 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,029 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,031 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:01,039 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:01,042 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,049 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 13:41:01,051 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:01,054 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,054 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,056 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,058 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 13:41:01,058 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:01,061 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:01,063 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,063 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,069 - root - INFO - 拓扑感知扰动用时: 0.0034秒，使用策略: pattern_based
2025-06-08 13:41:01,069 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,071 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,071 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,076 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,078 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,084 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:41:01,086 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,088 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:01,093 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,096 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,096 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:01,099 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 13:41:01,100 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,103 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,104 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,107 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:01,111 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,116 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,119 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 13:41:01,122 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:01,126 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 13:41:01,128 - ExploitationExpert - INFO - res_population_num: 20
2025-06-08 13:41:01,128 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529, 9551, 9922, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:41:01,129 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-08 13:41:01,134 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:41:01,136 - ExploitationExpert - INFO - populations: [{'tour': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'cur_cost': 97603.0}, {'tour': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'cur_cost': 105901.0}, {'tour': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'cur_cost': 105584.0}, {'tour': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'cur_cost': 100448.0}, {'tour': array([55,  0, 38, 52, 42, 54, 22, 19, 27, 57, 34, 29, 59, 21,  7,  8,  1,
        5,  2, 44, 18, 41, 50,  6, 60, 11, 65, 17, 15, 31, 46, 63, 51, 43,
       61, 37, 47, 36, 30, 16, 62, 53,  3, 49, 20,  9,  4, 56, 28, 23, 12,
       24, 26, 48, 64, 25, 13, 10, 40, 35, 45, 32, 33, 39, 58, 14]), 'cur_cost': 103678.0}, {'tour': [14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44], 'cur_cost': 103719.0}, {'tour': array([27, 54,  5, 57, 43, 61, 34, 12, 56, 25, 44, 58,  1, 51, 65, 31, 55,
       20, 28, 53, 50, 49, 62, 24, 14,  7, 35, 60, 15, 16,  9, 32, 59, 42,
       38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30,  6, 19, 41, 46,  8, 21,
       52, 17, 47, 37, 45,  3,  2, 33,  0,  4, 48, 11, 22, 10, 23]), 'cur_cost': 122048.0}, {'tour': array([ 8, 52, 58, 42, 24, 37, 54, 12, 56, 43, 33, 53, 28,  6, 63, 59, 10,
       26,  3, 23, 20,  2, 41, 61, 44, 17, 16, 32, 51,  9, 46, 35,  0, 11,
       31, 50, 15, 48, 40, 39,  5, 34, 21, 57, 25, 13, 55, 27, 60, 19, 18,
       36, 64, 38, 65, 29,  1, 30, 62,  7, 49, 45, 47, 14,  4, 22]), 'cur_cost': 113637.0}, {'tour': array([44, 28, 23, 63, 21, 64, 25,  8, 55, 38, 22, 13, 31, 39, 40,  6, 11,
       17, 50, 43, 32, 41,  0, 45, 19,  1, 29, 37, 30,  7,  2, 49, 42, 51,
        9, 61,  5, 56, 33, 35, 53,  3, 54, 27, 34, 58,  4, 24, 59, 36, 48,
       57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52, 18, 47]), 'cur_cost': 101039.0}, {'tour': array([40, 34, 28, 10, 14, 30, 33, 13, 11, 22,  3, 20, 64,  5, 25, 16, 44,
        0, 45, 46, 61, 52, 32, 43, 58, 23,  7, 49, 47,  8, 59, 42, 51, 65,
       57, 41, 54, 35, 27, 18,  4, 37, 48, 17,  6, 53, 62, 24, 15,  2, 60,
       36, 31, 29, 56, 26, 12, 21, 19, 55, 38, 50,  9,  1, 39, 63]), 'cur_cost': 102177.0}]
2025-06-08 13:41:01,137 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 13:41:01,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-08 13:41:01,137 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-08 13:41:01,137 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-08 13:41:01,137 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:41:01,149 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[14, 18, 24, 53, 30, 35, 60, 19, 63, 22, 16, 42, 7, 2, 52, 45, 29, 65, 57, 3, 1, 17, 28, 59, 0, 39, 8, 36, 13, 23, 10, 50, 34, 26, 54, 47, 48, 56, 62, 61, 58, 12, 6, 33, 38, 5, 46, 20, 4, 11, 9, 40, 31, 32, 49, 43, 25, 15, 27, 55, 51, 37, 64, 41, 21, 44]

## Path Cost: 103719.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9814763014763015

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:41:01,149 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:41:08,021 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9],
  "modification_strategy": "Large-scale restructuring with targeted regional modifications.  A swap-based approach is used on larger blocks of nodes.",
  "targeted_regions": ["Regions with higher potential (e.g., Opportunity Regions if available).", "Areas exhibiting high variance in the current population."],
  "avoided_regions": ["Difficult regions identified through landscape analysis (if any were provided). The current path's initial ordering, as it might be trapped in local optima."],
  "knowledge_application": "Due to no retrieved knowledge, the strategy relies on general TSP knowledge and the provided parameters.  The diversity weight drives a more significant reordering.",
  "diversity_contribution": "High. The new path is significantly different from the current path. The chosen strategy of large-scale restructuring with targeted modifications aims to introduce substantial changes across the solution space.",
  "expected_benefits": "Increased population diversity, potential discovery of new and better solutions by exploring uncharted territories of the search space, and avoidance of getting trapped in local optima by moving beyond the current path's structure. The large-scale restructuring aims to quickly disrupt existing patterns and facilitate a broader exploration."
}
```

**Explanation:**

1.  **Analyze Current Path:** The current path represents a specific sequence of nodes.  Its strengths and weaknesses are unknown without landscape analysis data (like specific node locations and distances, or difficult/opportunity regions). However, we can identify that it constitutes a complete tour.
2.  **Determine Exploration Strategy:**
    *   **Diversity Weight (0.7):** This high weight indicates a need for significant change to increase diversity.
    *   **Knowledge Usage Weight (0.3):**  Since there's no retrieved knowledge, the algorithm relies on general TSP principles and the provided parameters. The strategy will primarily focus on reordering the path substantially to maximize diversity.
    *   **Risk Tolerance (0.6):**  This moderate risk tolerance allows for moderately aggressive exploration, not just small local changes.
    *   **Selected Exploration Operations:** A large-scale restructuring strategy is employed. This involves reordering large blocks of nodes in the path. Within this overall approach, there's potential for more targeted swaps around potentially valuable regions.
3.  **Select Target Regions:**  The goal is to explore potentially profitable areas.  Without specifics, we aim to alter the path in a way that provides a strong deviation from the current sequence to explore the search space, while avoiding areas likely to represent local optima.
4.  **Apply Historical Knowledge:**  Since there's no retrieved knowledge, the strategy uses general TSP problem knowledge and the provided parameters.
5.  **Generate New Path:** The `new_path` in the JSON represents a complete, reordered path. The `modification_strategy` describes how the path was constructed. The remaining fields explain why and how the new path was generated.

2025-06-08 13:41:08,021 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:41:08,021 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:41:08,021 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:41:08,021 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106000.0, 路径: [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9]
2025-06-08 13:41:08,021 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_path': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'modification_strategy': 'Large-scale restructuring with targeted regional modifications.  A swap-based approach is used on larger blocks of nodes.', 'targeted_regions': ['Regions with higher potential (e.g., Opportunity Regions if available).', 'Areas exhibiting high variance in the current population.'], 'avoided_regions': ["Difficult regions identified through landscape analysis (if any were provided). The current path's initial ordering, as it might be trapped in local optima."], 'knowledge_application': 'Due to no retrieved knowledge, the strategy relies on general TSP knowledge and the provided parameters.  The diversity weight drives a more significant reordering.', 'diversity_contribution': 'High. The new path is significantly different from the current path. The chosen strategy of large-scale restructuring with targeted modifications aims to introduce substantial changes across the solution space.', 'expected_benefits': "Increased population diversity, potential discovery of new and better solutions by exploring uncharted territories of the search space, and avoidance of getting trapped in local optima by moving beyond the current path's structure. The large-scale restructuring aims to quickly disrupt existing patterns and facilitate a broader exploration.", 'new_tour': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'cur_cost': 106000.0}
2025-06-08 13:41:08,021 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-08 13:41:08,021 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:41:08,033 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[27, 54, 5, 57, 43, 61, 34, 12, 56, 25, 44, 58, 1, 51, 65, 31, 55, 20, 28, 53, 50, 49, 62, 24, 14, 7, 35, 60, 15, 16, 9, 32, 59, 42, 38, 36, 40, 29, 18, 26, 64, 63, 39, 13, 30, 6, 19, 41, 46, 8, 21, 52, 17, 47, 37, 45, 3, 2, 33, 0, 4, 48, 11, 22, 10, 23]

## Path Cost: 122048.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9818130018130019

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:41:08,034 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:41:13,396 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27],
  "modification_strategy": "Large-scale restructuring with regional swapping and inversion. A significant portion of the path is reordered, with a focus on inverting and swapping several key segments.",
  "targeted_regions": ["Regions with high connectivity and unexplored edges"],
  "avoided_regions": ["Regions identified as locally optimal traps by landscape analysis."],
  "knowledge_application": "No knowledge available, so we focus on diversifying the path structure using high-mutation operators.",
  "diversity_contribution": "High. The restructuring and inversion operations are expected to significantly alter the path's structure and node order, maximizing diversity.",
  "expected_benefits": "Increased population diversity, potential discovery of new, more promising solution areas, and avoidance of potential local optima. This should help the evolutionary algorithm escape local optima and potentially find a lower cost solution. The path should also increase the algorithm's exploration capacity in the initial stages."
}
```

**Explanation:**

The strategy prioritizes exploration due to the high diversity weight (0.7) and moderate risk tolerance (0.6). Since no historical knowledge is available, the focus is on a highly disruptive mutation to generate a substantially different path.

*   **Step 1: Analyze Current Path:** The current path appears to be a complete tour. Its strengths and weaknesses are unknown without specific TSP problem characteristics. The goal is to diversify.
*   **Step 2: Determine Exploration Strategy:** Given the diversity weight, a large-scale restructuring approach is taken. This includes reordering segments of the path, applying inversion operations and regional swapping. This strategy promotes exploration and potentially avoids getting trapped in a local optimum.
*   **Step 3: Select Target Regions:** The strategy targets unexplored connections (edges) between nodes within the overall network, aiming to find superior connections. Specific problematic regions were excluded to avoid local optima.
*   **Step 4: Apply Historical Knowledge:** No knowledge is available. Therefore, the strategy is mainly guided by the diversity requirements.
*   **Step 5: Generate New Path:** The `new_path` has been generated by reordering the current path through random rearrangements and swapping node order.

2025-06-08 13:41:13,396 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:41:13,396 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:41:13,396 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:41:13,396 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116987.0, 路径: [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27]
2025-06-08 13:41:13,396 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_path': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'modification_strategy': 'Large-scale restructuring with regional swapping and inversion. A significant portion of the path is reordered, with a focus on inverting and swapping several key segments.', 'targeted_regions': ['Regions with high connectivity and unexplored edges'], 'avoided_regions': ['Regions identified as locally optimal traps by landscape analysis.'], 'knowledge_application': 'No knowledge available, so we focus on diversifying the path structure using high-mutation operators.', 'diversity_contribution': "High. The restructuring and inversion operations are expected to significantly alter the path's structure and node order, maximizing diversity.", 'expected_benefits': "Increased population diversity, potential discovery of new, more promising solution areas, and avoidance of potential local optima. This should help the evolutionary algorithm escape local optima and potentially find a lower cost solution. The path should also increase the algorithm's exploration capacity in the initial stages.", 'new_tour': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'cur_cost': 116987.0}
2025-06-08 13:41:13,396 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-08 13:41:13,396 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:41:13,396 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:41:13,396 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104635.0
2025-06-08 13:41:13,403 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,405 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,410 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,424 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,426 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,439 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,441 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,442 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,444 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,453 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,457 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,468 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,479 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,479 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,491 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,492 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,498 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:13,502 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,512 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,515 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,516 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,522 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:41:13,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,543 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,544 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,549 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,555 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:41:13,568 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,570 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,571 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,573 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,578 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,578 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,579 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,579 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,581 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,582 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,582 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,587 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,590 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,591 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: segment_preservation
2025-06-08 13:41:13,591 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,594 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,596 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,596 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,597 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,597 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,600 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,600 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,602 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,605 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,607 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,608 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,608 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,610 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,612 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,615 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,615 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,616 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,617 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,618 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,621 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,621 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,621 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,623 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,624 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,626 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,629 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,629 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,629 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,633 - root - INFO - 拓扑感知扰动用时: 0.0034秒，使用策略: segment_preservation
2025-06-08 13:41:13,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,637 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,642 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,643 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,644 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,646 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,647 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:13,648 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,648 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,648 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:13,653 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,657 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,658 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,659 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,662 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,664 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,670 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:41:13,674 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: adaptive_random
2025-06-08 13:41:13,676 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,677 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:13,679 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,681 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 13:41:13,681 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,682 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,685 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,688 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,690 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,694 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,694 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,695 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,700 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,703 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,705 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,708 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,710 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,714 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,714 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,718 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,720 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,721 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,724 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:13,727 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,728 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 13:41:13,732 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,733 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,734 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,736 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,736 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,739 - root - INFO - 拓扑感知扰动用时: 0.0023秒，使用策略: pattern_based
2025-06-08 13:41:13,740 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,740 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,740 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,744 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:13,744 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,744 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,746 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,748 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,750 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,753 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:13,754 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,754 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,757 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,758 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,762 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,762 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,764 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:13,765 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,766 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,767 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,768 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:13,769 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 13:41:13,770 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:13,773 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:13,775 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 13:41:13,777 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:15,376 - ExploitationExpert - INFO - res_population_num: 21
2025-06-08 13:41:15,376 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529, 9551, 9922, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:41:15,376 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-08 13:41:15,388 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:41:15,388 - ExploitationExpert - INFO - populations: [{'tour': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'cur_cost': 97603.0}, {'tour': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'cur_cost': 105901.0}, {'tour': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'cur_cost': 105584.0}, {'tour': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'cur_cost': 100448.0}, {'tour': array([55,  0, 38, 52, 42, 54, 22, 19, 27, 57, 34, 29, 59, 21,  7,  8,  1,
        5,  2, 44, 18, 41, 50,  6, 60, 11, 65, 17, 15, 31, 46, 63, 51, 43,
       61, 37, 47, 36, 30, 16, 62, 53,  3, 49, 20,  9,  4, 56, 28, 23, 12,
       24, 26, 48, 64, 25, 13, 10, 40, 35, 45, 32, 33, 39, 58, 14]), 'cur_cost': 103678.0}, {'tour': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'cur_cost': 106000.0}, {'tour': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'cur_cost': 116987.0}, {'tour': array([30, 21, 34, 33, 28,  3, 48, 16, 55, 12,  6,  8, 38,  7, 19, 37, 14,
        5,  4, 31, 56, 32, 59, 47,  2, 41, 52, 10, 54, 60, 58, 49, 45, 46,
        9, 64, 13, 62, 44, 36, 42, 15, 26, 35, 11, 17, 22, 18, 63, 40, 24,
       50, 20, 51, 65,  0, 25, 23, 39, 43, 61,  1, 57, 29, 27, 53]), 'cur_cost': 104635.0}, {'tour': array([44, 28, 23, 63, 21, 64, 25,  8, 55, 38, 22, 13, 31, 39, 40,  6, 11,
       17, 50, 43, 32, 41,  0, 45, 19,  1, 29, 37, 30,  7,  2, 49, 42, 51,
        9, 61,  5, 56, 33, 35, 53,  3, 54, 27, 34, 58,  4, 24, 59, 36, 48,
       57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52, 18, 47]), 'cur_cost': 101039.0}, {'tour': array([40, 34, 28, 10, 14, 30, 33, 13, 11, 22,  3, 20, 64,  5, 25, 16, 44,
        0, 45, 46, 61, 52, 32, 43, 58, 23,  7, 49, 47,  8, 59, 42, 51, 65,
       57, 41, 54, 35, 27, 18,  4, 37, 48, 17,  6, 53, 62, 24, 15,  2, 60,
       36, 31, 29, 56, 26, 12, 21, 19, 55, 38, 50,  9,  1, 39, 63]), 'cur_cost': 102177.0}]
2025-06-08 13:41:15,393 - ExploitationExpert - INFO - 局部搜索耗时: 2.00秒
2025-06-08 13:41:15,393 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-08 13:41:15,393 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-08 13:41:15,394 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-08 13:41:15,394 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 13:41:15,408 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52, 18, 47]

## Path Cost: 101039.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9872002072002073

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 13:41:15,410 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 13:41:21,082 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52],
  "modification_strategy": "Partial Reverse and Insertion",
  "targeted_regions": ["Region around node 18", "Region between node 47 and node 44"],
  "avoided_regions": [],
  "knowledge_application": "No knowledge retrieved, therefore not applicable.",
  "diversity_contribution": "High. The strategy introduces a partial reverse and insertion to disrupt the current path's structure significantly, enhancing diversity.",
  "expected_benefits": "Increased exploration of areas around nodes 18, 47, and 44. This could lead to the discovery of alternative routes with lower costs or explore different connections in the neighborhood, promoting the diversity of the population and providing a better chance to find a superior solution."
}
```

## Explanation of Exploration Strategy and Expected Effects:

**Step 1: Analyze Current Path:** The current path covers the entire node set. The path is relatively well-structured, showing a balance of exploration. We want to maintain the overall structure, but the existing path could be improved by changing a portion of it to explore different regions.

**Step 2: Determine Exploration Strategy:** The diversity weight of 0.7 suggests a focus on significant path alteration. The risk tolerance of 0.6 allows for moderately aggressive changes. Given the need for diversity and moderate risk, a combination of a partial reverse operation followed by insertion is an ideal exploration strategy. This will introduce a substantial change to the path.

**Step 3: Select Target Regions:** The target is focused on increasing the flexibility of the region connected to node 18 and the path connection between node 47 and 44.

**Step 4: Apply Historical Knowledge:** No historical knowledge is available.

**Step 5: Generate New Path:**

1.  **Partial Reverse:** A portion of the path starting from the end of the route and back to node 18 has been partially reversed.
2.  **Insertion:** Node 47 is placed immediately after node 18.
3.  **Result:** The route has been changed significantly with the above operations, thus, will help increase population diversity and facilitate potentially better solutions.

2025-06-08 13:41:21,082 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 13:41:21,082 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 13:41:21,082 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 13:41:21,082 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101039.0, 路径: [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52]
2025-06-08 13:41:21,082 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_path': [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52], 'modification_strategy': 'Partial Reverse and Insertion', 'targeted_regions': ['Region around node 18', 'Region between node 47 and node 44'], 'avoided_regions': [], 'knowledge_application': 'No knowledge retrieved, therefore not applicable.', 'diversity_contribution': "High. The strategy introduces a partial reverse and insertion to disrupt the current path's structure significantly, enhancing diversity.", 'expected_benefits': 'Increased exploration of areas around nodes 18, 47, and 44. This could lead to the discovery of alternative routes with lower costs or explore different connections in the neighborhood, promoting the diversity of the population and providing a better chance to find a superior solution.', 'new_tour': [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52], 'cur_cost': 101039.0}
2025-06-08 13:41:21,082 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-08 13:41:21,082 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 13:41:21,082 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 13:41:21,082 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 89559.0
2025-06-08 13:41:21,093 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:21,095 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,101 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 13:41:21,102 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,112 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,114 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,119 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,123 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,126 - root - INFO - 拓扑感知扰动用时: 0.0022秒，使用策略: pattern_based
2025-06-08 13:41:21,147 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,161 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,169 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,182 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,185 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,202 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:21,207 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:21,219 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,237 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,255 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:21,268 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,272 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,275 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,284 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 13:41:21,303 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:21,315 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,317 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:21,321 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,333 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,357 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 13:41:21,370 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,373 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,386 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: pattern_based
2025-06-08 13:41:21,397 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,401 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: pattern_based
2025-06-08 13:41:21,415 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,420 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 13:41:21,422 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,423 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,426 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 13:41:21,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,431 - root - INFO - 拓扑感知扰动用时: 0.0019秒，使用策略: pattern_based
2025-06-08 13:41:21,434 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,439 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 13:41:21,447 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,461 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:21,470 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,474 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,476 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: segment_preservation
2025-06-08 13:41:21,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,481 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,491 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,496 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,499 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,510 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,511 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 13:41:21,512 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,514 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,518 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,520 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,522 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,528 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:21,540 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,542 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,543 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 13:41:21,544 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: segment_preservation
2025-06-08 13:41:21,546 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,548 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 13:41:21,549 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,554 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,556 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,557 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,558 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 13:41:21,561 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 13:41:21,564 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:21,566 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,571 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,573 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,574 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 13:41:21,577 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,578 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 13:41:21,586 - ExploitationExpert - INFO - res_population_num: 22
2025-06-08 13:41:21,586 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9529, 9551, 9922, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-08 13:41:21,587 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40,
       43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21,
       20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 55, 61, 53, 62, 59, 56, 58,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 32, 29, 24, 31, 33, 34, 30, 28, 35, 36, 26, 25, 37,
       27, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 42, 48, 43, 40,
       49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60,
       58, 56, 59, 62, 53, 61, 55, 10,  6,  2,  8,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-08 13:41:21,598 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 13:41:21,598 - ExploitationExpert - INFO - populations: [{'tour': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'cur_cost': 97603.0}, {'tour': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'cur_cost': 105901.0}, {'tour': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'cur_cost': 105584.0}, {'tour': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'cur_cost': 100448.0}, {'tour': array([55,  0, 38, 52, 42, 54, 22, 19, 27, 57, 34, 29, 59, 21,  7,  8,  1,
        5,  2, 44, 18, 41, 50,  6, 60, 11, 65, 17, 15, 31, 46, 63, 51, 43,
       61, 37, 47, 36, 30, 16, 62, 53,  3, 49, 20,  9,  4, 56, 28, 23, 12,
       24, 26, 48, 64, 25, 13, 10, 40, 35, 45, 32, 33, 39, 58, 14]), 'cur_cost': 103678.0}, {'tour': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'cur_cost': 106000.0}, {'tour': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'cur_cost': 116987.0}, {'tour': array([30, 21, 34, 33, 28,  3, 48, 16, 55, 12,  6,  8, 38,  7, 19, 37, 14,
        5,  4, 31, 56, 32, 59, 47,  2, 41, 52, 10, 54, 60, 58, 49, 45, 46,
        9, 64, 13, 62, 44, 36, 42, 15, 26, 35, 11, 17, 22, 18, 63, 40, 24,
       50, 20, 51, 65,  0, 25, 23, 39, 43, 61,  1, 57, 29, 27, 53]), 'cur_cost': 104635.0}, {'tour': [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52], 'cur_cost': 101039.0}, {'tour': array([55, 40, 45, 23, 38, 41, 42, 24, 34, 19,  3, 39, 12, 50, 33,  8, 53,
        9, 37, 32, 30, 27,  2, 20, 49, 17, 59,  0, 63, 10,  6,  5, 44, 21,
       26,  1, 54, 64, 62,  7, 56, 36, 60, 15, 52, 61, 18, 14, 11, 48, 13,
       25,  4, 35, 16, 47, 22, 57, 43, 46, 29, 65, 58, 28, 31, 51]), 'cur_cost': 89559.0}]
2025-06-08 13:41:21,601 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 13:41:21,601 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-08 13:41:21,601 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-08 13:41:21,602 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_path': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'modification_strategy': 'Large-scale restructuring with targeted swaps and regional perturbation, prioritizing diversity.', 'targeted_regions': ['Regions with high potential identified in Landscape Analysis (assumed, as specific regions are not defined).', 'Under-explored regions based on path analysis. (assumed)', 'Opportunity regions (assumed)'], 'avoided_regions': ['Difficult regions identified in Landscape Analysis (assumed, as specific regions are not defined).'], 'knowledge_application': 'N/A - No knowledge retrieved.', 'diversity_contribution': 'High. The path is significantly different from the current path, aiming for exploration of various regions. The starting and ending nodes are changed, and numerous subsequences have been heavily restructured, indicating a high degree of modification.', 'expected_benefits': 'Increased population diversity and potentially discovery of new, higher-quality solutions by escaping local optima and exploring unexplored areas. This exploration strategy is aggressive due to high diversity weight and will introduce significant changes to the path.', 'new_tour': [29, 19, 21, 8, 33, 20, 55, 48, 2, 5, 61, 53, 52, 27, 35, 38, 49, 57, 15, 10, 31, 24, 36, 14, 12, 63, 43, 56, 65, 18, 58, 64, 46, 40, 23, 32, 0, 45, 34, 37, 17, 1, 13, 44, 25, 47, 26, 60, 54, 39, 4, 6, 30, 16, 50, 62, 41, 11, 28, 7, 9, 22, 42, 59, 3], 'cur_cost': 97603.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_path': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'modification_strategy': 'Partial Reverse and Re-insertion with Targeted Perturbation. Retain the majority of the path while re-ordering a small section, specifically the beginning and end to introduce diversity and targeting high-opportunity areas.', 'targeted_regions': ['Beginning and End of Path'], 'avoided_regions': [], 'knowledge_application': 'No specific knowledge applied as no knowledge was retrieved. General TSP knowledge of preserving good segments whilst exploring variations.', 'diversity_contribution': 'High, due to a large-scale restructuring of path beginning and end sections and subsequent node changes.', 'expected_benefits': 'Increased path diversity, potential for discovery of lower-cost paths due to exploring different starting and ending connections within the TSP, and maintain the majority structure of a known good solution to preserve potential for low cost.', 'new_tour': [36, 27, 35, 40, 0, 6, 21, 61, 12, 49, 57, 48, 18, 23, 58, 2, 52, 28, 10, 34, 41, 9, 16, 51, 17, 62, 44, 14, 56, 31, 25, 11, 4, 7, 43, 24, 32, 33, 63, 45, 60, 26, 19, 13, 42, 20, 3, 22, 55, 39, 53, 5, 65, 46, 1, 38, 29, 47, 50, 59, 54, 64, 15, 37, 30, 8], 'cur_cost': 105901.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_path': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'modification_strategy': 'Partial Reversal and Targeted Insertion', 'targeted_regions': ['Early stage nodes (10, 45, 6, 57, 56)', 'Tail end nodes (11, 13)'], 'avoided_regions': [], 'knowledge_application': 'None (no retrieved knowledge)', 'diversity_contribution': 'High. This strategy aims to significantly alter the path by reversing a portion and re-ordering nodes at the beginning and end.', 'expected_benefits': "Increased diversity by re-ordering nodes and altering the path structure. Potential improvement in the cost if the new order leads to shorter connections. Specifically targets the early and end part of the existing path for more radical changes. Addresses the current population's relatively high diversity by focusing on localized but significant changes to boost diversity further. This strategy balances exploration with minimal disruption to potentially valuable portions of the existing path.", 'new_tour': [10, 45, 6, 57, 56, 4, 17, 41, 31, 52, 29, 1, 26, 22, 48, 18, 35, 53, 58, 32, 38, 55, 12, 49, 60, 15, 25, 40, 9, 59, 2, 64, 7, 44, 21, 43, 33, 30, 51, 27, 62, 34, 63, 61, 65, 54, 42, 19, 5, 23, 14, 28, 50, 37, 0, 39, 20, 24, 36, 8, 46, 47, 3, 11, 13], 'cur_cost': 105584.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_path': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'modification_strategy': 'Large-scale restructuring with regional optimization. The primary strategy focuses on disrupting the existing order to promote diversity. This is then complemented with localized improvements to refine the overall path quality.', 'targeted_regions': ['Regions around nodes 0, 11, 56 (initial part reshuffled)', 'Central region exploration (nodes 28-30, 59)'], 'avoided_regions': ['High-cost sub-paths within the original sequence, focusing on a more distributed structure.'], 'knowledge_application': 'No explicit knowledge application due to lack of retrieved information. However, the restructuring is informed by general TSP principles: avoiding long, winding paths and promoting exploration of the entire space.', 'diversity_contribution': 'High. The complete reshuffling of significant portions of the path, combined with the new node ordering, ensures substantial diversity from the original path. The new path disrupts the order significantly.', 'expected_benefits': 'Increased population diversity, potentially leading to the discovery of better solutions in unexplored regions of the search space. The large-scale restructuring allows the algorithm to escape local optima and explore entirely new path configurations. The regional focus helps improve the solution quality.', 'new_tour': [11, 56, 16, 14, 0, 28, 29, 30, 59, 34, 9, 52, 58, 54, 64, 8, 57, 43, 4, 49, 13, 37, 63, 19, 3, 42, 39, 24, 23, 33, 18, 10, 25, 35, 45, 50, 36, 61, 22, 7, 31, 62, 32, 21, 41, 5, 60, 20, 51, 2, 55, 53, 15, 6, 47, 17, 44, 27, 12, 1, 26, 65, 48, 40, 38, 46], 'cur_cost': 100448.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_path': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'modification_strategy': 'Large-scale restructuring with targeted regional modifications.  A swap-based approach is used on larger blocks of nodes.', 'targeted_regions': ['Regions with higher potential (e.g., Opportunity Regions if available).', 'Areas exhibiting high variance in the current population.'], 'avoided_regions': ["Difficult regions identified through landscape analysis (if any were provided). The current path's initial ordering, as it might be trapped in local optima."], 'knowledge_application': 'Due to no retrieved knowledge, the strategy relies on general TSP knowledge and the provided parameters.  The diversity weight drives a more significant reordering.', 'diversity_contribution': 'High. The new path is significantly different from the current path. The chosen strategy of large-scale restructuring with targeted modifications aims to introduce substantial changes across the solution space.', 'expected_benefits': "Increased population diversity, potential discovery of new and better solutions by exploring uncharted territories of the search space, and avoidance of getting trapped in local optima by moving beyond the current path's structure. The large-scale restructuring aims to quickly disrupt existing patterns and facilitate a broader exploration.", 'new_tour': [44, 11, 4, 20, 46, 5, 38, 33, 6, 12, 58, 61, 62, 56, 48, 47, 54, 26, 34, 50, 10, 23, 13, 36, 8, 39, 0, 59, 28, 17, 1, 3, 57, 65, 29, 45, 52, 2, 7, 42, 16, 22, 63, 19, 60, 35, 30, 53, 24, 18, 14, 21, 41, 64, 37, 51, 55, 27, 15, 25, 43, 49, 32, 31, 40, 9], 'cur_cost': 106000.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_path': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'modification_strategy': 'Large-scale restructuring with regional swapping and inversion. A significant portion of the path is reordered, with a focus on inverting and swapping several key segments.', 'targeted_regions': ['Regions with high connectivity and unexplored edges'], 'avoided_regions': ['Regions identified as locally optimal traps by landscape analysis.'], 'knowledge_application': 'No knowledge available, so we focus on diversifying the path structure using high-mutation operators.', 'diversity_contribution': "High. The restructuring and inversion operations are expected to significantly alter the path's structure and node order, maximizing diversity.", 'expected_benefits': "Increased population diversity, potential discovery of new, more promising solution areas, and avoidance of potential local optima. This should help the evolutionary algorithm escape local optima and potentially find a lower cost solution. The path should also increase the algorithm's exploration capacity in the initial stages.", 'new_tour': [10, 48, 22, 11, 4, 0, 33, 2, 3, 45, 37, 17, 52, 21, 8, 46, 41, 19, 6, 30, 13, 39, 63, 64, 26, 18, 29, 40, 36, 38, 42, 59, 32, 9, 16, 15, 60, 35, 7, 14, 24, 62, 49, 50, 53, 28, 20, 55, 31, 65, 51, 1, 58, 44, 25, 56, 12, 34, 61, 43, 57, 5, 54, 27], 'cur_cost': 116987.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_path': [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52], 'modification_strategy': 'Partial Reverse and Insertion', 'targeted_regions': ['Region around node 18', 'Region between node 47 and node 44'], 'avoided_regions': [], 'knowledge_application': 'No knowledge retrieved, therefore not applicable.', 'diversity_contribution': "High. The strategy introduces a partial reverse and insertion to disrupt the current path's structure significantly, enhancing diversity.", 'expected_benefits': 'Increased exploration of areas around nodes 18, 47, and 44. This could lead to the discovery of alternative routes with lower costs or explore different connections in the neighborhood, promoting the diversity of the population and providing a better chance to find a superior solution.', 'new_tour': [18, 47, 44, 28, 23, 63, 21, 64, 25, 8, 55, 38, 22, 13, 31, 39, 40, 6, 11, 17, 50, 43, 32, 41, 0, 45, 19, 1, 29, 37, 30, 7, 2, 49, 42, 51, 9, 61, 5, 56, 33, 35, 53, 3, 54, 27, 34, 58, 4, 24, 59, 36, 48, 57, 16, 65, 60, 10, 15, 14, 62, 12, 46, 20, 26, 52], 'cur_cost': 101039.0}}]
2025-06-08 13:41:21,603 - __main__ - INFO - 进化阶段完成
2025-06-08 13:41:21,603 - __main__ - INFO - 开始评估阶段
2025-06-08 13:41:21,603 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-08 13:41:21,641 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
# Evolution Assessment Task

## Role Definition
You are an expert in evolutionary algorithms and performance assessment. You excel at analyzing the evolutionary state of algorithms, evaluating strategy effectiveness, optimizing the balance between exploration and exploitation, and providing specific improvement recommendations.

## Iteration Information
- Current Iteration: 1
- Total Iterations: 5

## Population Changes
- Old Population Statistics: {"min_cost": 100456.0, "max_cost": 122048.0, "mean_cost": 106680.3, "diversity": 0.9670033670033669}
- New Population Statistics: {"min_cost": 89559.0, "max_cost": 116987.0, "mean_cost": 103143.4, "diversity": 0.9882103082103081}
- Elite Solution Changes: {"old_best_cost": 9521, "new_best_cost": 9521, "improvement": 0, "old_elite_count": 14, "new_elite_count": 22, "old_elite_diversity": 0.16161616161616166, "new_elite_diversity": 0.16161616161616166, "new_elite_solutions_count": 8}

## Strategy Allocation
- Global Exploration Ratio: 0.7
- Individual Strategy Assignments: {"0": "explore", "1": "explore", "2": "explore", "3": "explore", "4": "exploit", "5": "explore", "6": "explore", "7": "exploit", "8": "explore", "9": "exploit"}
- Strategy Parameters: {"explore": {"diversity_weight": 0.7, "knowledge_usage_weight": 0.3, "risk_tolerance": 0.6}, "exploit": {"local_search_depth": 2, "quality_edge_usage": 0.8, "elite_influence": 0.7}}

## Strategy Results
- Exploration Strategy Results: {"success_count": 3, "improvement_sum": 5088.0}
- Exploitation Strategy Results: {"success_count": 3, "improvement_sum": 30281.0}

## Historical Trends
[{"iteration": 0, "old_stats": {"min_cost": 10093.0, "max_cost": 114139.0, "mean_cost": 75975.3, "diversity": 0.9127946127946128}, "new_stats": {"min_cost": 100456.0, "max_cost": 122048.0, "mean_cost": 106680.3, "diversity": 0.9670033670033669}, "strategy_distribution": {"explore": 3, "exploit": 7}, "elite_stats": {"old_elite_count": 3, "new_elite_count": 14, "old_elite_diversity": 0.18181818181818177, "new_elite_diversity": 0.16161616161616166, "new_elite_solutions_count": 0, "best_cost_improvement": 572.0}}]

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data shows the population changes, strategy allocation, and strategy results in the current iteration. We need you to assess the evolutionary effects of the current iteration, analyze the evolutionary stage, evaluate strategy effectiveness, and provide improvement recommendations for the next iteration.

# Assessment Request
Please conduct a comprehensive assessment of the evolutionary effects in the current iteration. Please follow these steps in your thinking:

## Step 1: Evolution Stage Identification and Analysis
- Analyze the current population's diversity level, convergence degree, and elite solution stability
- Evaluate the improvement rate and effectiveness of various strategies
- Determine the current evolutionary stage of the algorithm (early exploration, middle transition, late fine-tuning)
- Assess the confidence level of the stage determination
- Detect if approaching a stage transition point and estimate the transition time
- Provide recommendations for preparing for stage transitions

## Step 2: Strategy Effectiveness Evaluation
- Analyze the effectiveness of exploration strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to diversity
  - Evaluate new region discovery capability
  - Evaluate ability to escape local optima
  - Analyze individual-level effectiveness
- Analyze the effectiveness of exploitation strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to convergence
  - Evaluate elite solution improvement capability
  - Evaluate local fine-tuning capability
  - Analyze individual-level effectiveness
- Evaluate overall strategy effectiveness:
  - Analyze strategy synergy effects
  - Evaluate balance effectiveness
  - Calculate iteration efficiency

## Step 3: Exploration vs. Exploitation Balance Analysis
- Calculate the current exploration/exploitation ratio
- Based on the evolutionary stage and strategy effectiveness, predict the optimal ratio
- Calculate the gap between the current ratio and the optimal ratio
- Evaluate the current balance state
- Determine if adjustment is needed, and the direction and magnitude of adjustment
- Analyze the trend of balance state changes

## Step 4: Improvement Recommendation Generation
- Recommend exploration ratio for the next iteration
- Propose strategy parameter adjustment recommendations
- Identify areas that need special attention
- Provide strategy recommendations for individuals

## Step 5: Long-term Trend Analysis
- Analyze convergence trends
- Analyze diversity trends
- Analyze efficiency trends
- Identify potential problems
- Discover optimization opportunities

Please return your assessment results in JSON format with the following sections:
```json
{
  "evolution_stage": {
    "stage": string,
    "confidence": float,
    "stage_features": {
      "diversity_level": float,
      "convergence_level": float,
      "elite_stability": float,
      "improvement_rate": float,
      "exploration_effectiveness": float,
      "exploitation_effectiveness": float
    },
    "transition_indicators": {
      "approaching_next_stage": bool,
      "estimated_iterations_to_transition": int,
      "recommended_preparation": string
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": float,
      "avg_improvement": float,
      "diversity_contribution": float,
      "new_region_discovery": float,
      "escape_local_optima": float,
      "individual_effectiveness": {}
    },
    "exploit": {
      "success_rate": float,
      "avg_improvement": float,
      "convergence_contribution": float,
      "elite_improvement": float,
      "local_refinement": float,
      "individual_effectiveness": {}
    },
    "overall": {
      "strategy_synergy": float,
      "balance_effectiveness": float,
      "iteration_efficiency": float
    }
  },
  "balance_state": {
    "current_ratio": float,
    "optimal_ratio": float,
    "ratio_gap": float,
    "balance_score": float,
    "adjustment_needed": bool,
    "adjustment_magnitude": float,
    "adjustment_direction": string,
    "balance_trend": {
      "direction": string,
      "rate": float,
      "prediction": string
    }
  },
  "recommendations": {
    "explore_ratio": float,
    "strategy_parameters": {},
    "focus_areas": [string, ...],
    "individual_recommendations": {}
  },
  "long_term_analysis": {
    "convergence_trend": string,
    "diversity_trend": string,
    "efficiency_trend": string,
    "potential_issues": [string, ...],
    "optimization_opportunities": [string, ...]
  }
}
```

Additionally, please provide a brief summary explaining your main findings and their implications for the next iteration's strategy.

2025-06-08 13:41:21,644 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-08 13:41:28,811 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.988,
      "convergence_level": 0.978,
      "elite_stability": 1.0,
      "improvement_rate": 2.86,
      "exploration_effectiveness": 0.6,
      "exploitation_effectiveness": 0.8
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Gradually increase exploitation ratio, refine elite-focused strategies, and prepare for fine-tuning."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "avg_improvement": 1696.0,
      "diversity_contribution": 0.021,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.5,
      "individual_effectiveness": {
        "0": "moderate",
        "1": "moderate",
        "2": "moderate",
        "3": "moderate",
        "5": "moderate",
        "6": "moderate",
        "8": "moderate"
      }
    },
    "exploit": {
      "success_rate": 1.0,
      "avg_improvement": 10093.67,
      "convergence_contribution": 0.9,
      "elite_improvement": 0.9,
      "local_refinement": 0.8,
      "individual_effectiveness": {
        "4": "high",
        "7": "high",
        "9": "high"
      }
    },
    "overall": {
      "strategy_synergy": 0.8,
      "balance_effectiveness": 0.7,
      "iteration_efficiency": 0.7
    }
  },
  "balance_state": {
    "current_ratio": 0.7,
    "optimal_ratio": 0.4,
    "ratio_gap": 0.3,
    "balance_score": 0.6,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease exploration",
    "balance_trend": {
      "direction": "shifting towards more exploration in this iteration",
      "rate": -0.1,
      "prediction": "Continue shifting towards exploitation in the next few iterations to improve convergence speed."
    }
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.6,
        "knowledge_usage_weight": 0.4,
        "risk_tolerance": 0.5
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improving exploitation effectiveness by increasing local search depth.",
      "Refining elite selection and influence in exploitation strategies.",
      "Gradually reducing exploration to favor exploitation as the algorithm approaches convergence."
    ],
    "individual_recommendations": {
      "0": "Continue explore",
      "1": "Continue explore",
      "2": "Continue explore",
      "3": "Continue explore",
      "4": "Continue exploit",
      "5": "Continue explore",
      "6": "Continue explore",
      "7": "Continue exploit",
      "8": "Continue explore",
      "9": "Continue exploit"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Positive, with the best cost improving in each iteration.",
    "diversity_trend": "Slightly increasing, suggesting the algorithm is still exploring the search space.",
    "efficiency_trend": "Increasing, indicating the algorithm is efficiently finding better solutions.",
    "potential_issues": [
      "Over-exploration may slow down convergence in later stages.",
      "Elite solution stability should be monitored."
    ],
    "optimization_opportunities": [
      "Further increase exploitation in next iteration.",
      "Fine-tune the exploitation parameter for higher effectiveness",
      "Consider introducing mutation operator to keep diversity"
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is in a middle transition phase, showing good progress with decreasing costs and maintained diversity. Exploitation strategies are significantly outperforming exploration, indicating a shift towards convergence.  The current exploration ratio of 0.7 is slightly too high; therefore, the recommendation is to decrease exploration and increase exploitation to further refine the elite solutions and quicken the convergence. Adjust strategy parameters such as local search depth.  Monitor elite solution stability and consider adjusting elitism thresholds. The algorithm is approaching the stage where fine-tuning will become more crucial.

2025-06-08 13:41:28,811 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-08 13:41:28,813 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.988,
      "convergence_level": 0.978,
      "elite_stability": 1.0,
      "improvement_rate": 2.86,
      "exploration_effectiveness": 0.6,
      "exploitation_effectiveness": 0.8
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Gradually increase exploitation ratio, refine elite-focused strategies, and prepare for fine-tuning."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "avg_improvement": 1696.0,
      "diversity_contribution": 0.021,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.5,
      "individual_effectiveness": {
        "0": "moderate",
        "1": "moderate",
        "2": "moderate",
        "3": "moderate",
        "5": "moderate",
        "6": "moderate",
        "8": "moderate"
      }
    },
    "exploit": {
      "success_rate": 1.0,
      "avg_improvement": 10093.67,
      "convergence_contribution": 0.9,
      "elite_improvement": 0.9,
      "local_refinement": 0.8,
      "individual_effectiveness": {
        "4": "high",
        "7": "high",
        "9": "high"
      }
    },
    "overall": {
      "strategy_synergy": 0.8,
      "balance_effectiveness": 0.7,
      "iteration_efficiency": 0.7
    }
  },
  "balance_state": {
    "current_ratio": 0.7,
    "optimal_ratio": 0.4,
    "ratio_gap": 0.3,
    "balance_score": 0.6,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease exploration",
    "balance_trend": {
      "direction": "shifting towards more exploration in this iteration",
      "rate": -0.1,
      "prediction": "Continue shifting towards exploitation in the next few iterations to improve convergence speed."
    }
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.6,
        "knowledge_usage_weight": 0.4,
        "risk_tolerance": 0.5
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improving exploitation effectiveness by increasing local search depth.",
      "Refining elite selection and influence in exploitation strategies.",
      "Gradually reducing exploration to favor exploitation as the algorithm approaches convergence."
    ],
    "individual_recommendations": {
      "0": "Continue explore",
      "1": "Continue explore",
      "2": "Continue explore",
      "3": "Continue explore",
      "4": "Continue exploit",
      "5": "Continue explore",
      "6": "Continue explore",
      "7": "Continue exploit",
      "8": "Continue explore",
      "9": "Continue exploit"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Positive, with the best cost improving in each iteration.",
    "diversity_trend": "Slightly increasing, suggesting the algorithm is still exploring the search space.",
    "efficiency_trend": "Increasing, indicating the algorithm is efficiently finding better solutions.",
    "potential_issues": [
      "Over-exploration may slow down convergence in later stages.",
      "Elite solution stability should be monitored."
    ],
    "optimization_opportunities": [
      "Further increase exploitation in next iteration.",
      "Fine-tune the exploitation parameter for higher effectiveness",
      "Consider introducing mutation operator to keep diversity"
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is in a middle transition phase, showing good progress with decreasing costs and maintained diversity. Exploitation strategies are significantly outperforming exploration, indicating a shift towards convergence.  The current exploration ratio of 0.7 is slightly too high; therefore, the recommendation is to decrease exploration and increase exploitation to further refine the elite solutions and quicken the convergence. Adjust strategy parameters such as local search depth.  Monitor elite solution stability and consider adjusting elitism thresholds. The algorithm is approaching the stage where fine-tuning will become more crucial.

2025-06-08 13:41:28,813 - __main__ - INFO - 评估阶段完成
2025-06-08 13:41:28,813 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.75,
    "stage_features": {
      "diversity_level": 0.988,
      "convergence_level": 0.978,
      "elite_stability": 1.0,
      "improvement_rate": 2.86,
      "exploration_effectiveness": 0.6,
      "exploitation_effectiveness": 0.8
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 2,
      "recommended_preparation": "Gradually increase exploitation ratio, refine elite-focused strategies, and prepare for fine-tuning."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "avg_improvement": 1696.0,
      "diversity_contribution": 0.021,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.5,
      "individual_effectiveness": {
        "0": "moderate",
        "1": "moderate",
        "2": "moderate",
        "3": "moderate",
        "5": "moderate",
        "6": "moderate",
        "8": "moderate"
      }
    },
    "exploit": {
      "success_rate": 1.0,
      "avg_improvement": 10093.67,
      "convergence_contribution": 0.9,
      "elite_improvement": 0.9,
      "local_refinement": 0.8,
      "individual_effectiveness": {
        "4": "high",
        "7": "high",
        "9": "high"
      }
    },
    "overall": {
      "strategy_synergy": 0.8,
      "balance_effectiveness": 0.7,
      "iteration_efficiency": 0.7
    }
  },
  "balance_state": {
    "current_ratio": 0.7,
    "optimal_ratio": 0.4,
    "ratio_gap": 0.3,
    "balance_score": 0.6,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease exploration",
    "balance_trend": {
      "direction": "shifting towards more exploration in this iteration",
      "rate": -0.1,
      "prediction": "Continue shifting towards exploitation in the next few iterations to improve convergence speed."
    }
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.6,
        "knowledge_usage_weight": 0.4,
        "risk_tolerance": 0.5
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improving exploitation effectiveness by increasing local search depth.",
      "Refining elite selection and influence in exploitation strategies.",
      "Gradually reducing exploration to favor exploitation as the algorithm approaches convergence."
    ],
    "individual_recommendations": {
      "0": "Continue explore",
      "1": "Continue explore",
      "2": "Continue explore",
      "3": "Continue explore",
      "4": "Continue exploit",
      "5": "Continue explore",
      "6": "Continue explore",
      "7": "Continue exploit",
      "8": "Continue explore",
      "9": "Continue exploit"
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Positive, with the best cost improving in each iteration.",
    "diversity_trend": "Slightly increasing, suggesting the algorithm is still exploring the search space.",
    "efficiency_trend": "Increasing, indicating the algorithm is efficiently finding better solutions.",
    "potential_issues": [
      "Over-exploration may slow down convergence in later stages.",
      "Elite solution stability should be monitored."
    ],
    "optimization_opportunities": [
      "Further increase exploitation in next iteration.",
      "Fine-tune the exploitation parameter for higher effectiveness",
      "Consider introducing mutation operator to keep diversity"
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is in a middle transition phase, showing good progress with decreasing costs and maintained diversity. Exploitation strategies are significantly outperforming exploration, indicating a shift towards convergence.  The current exploration ratio of 0.7 is slightly too high; therefore, the recommendation is to decrease exploration and increase exploitation to further refine the elite solutions and quicken the convergence. Adjust strategy parameters such as local search depth.  Monitor elite solution stability and consider adjusting elitism thresholds. The algorithm is approaching the stage where fine-tuning will become more crucial.

2025-06-08 13:41:28,814 - __main__ - INFO - 当前最佳适应度: 89559.0
2025-06-08 13:41:28,815 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0509_0bug-fa-feedback\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-08 13:41:28,816 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-08 13:41:28,817 - __main__ - INFO - 开始分析阶段
2025-06-08 13:41:28,817 - StatsExpert - INFO - 开始统计分析
2025-06-08 13:41:28,828 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 89559.0, 'max': 116987.0, 'mean': 103143.4, 'std': 6654.341548192428}, 'diversity': 0.9882103082103081, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 13:41:28,828 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 89559.0, 'max': 116987.0, 'mean': 103143.4, 'std': 6654.341548192428}, 'diversity_level': 0.9882103082103081, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 13:41:28,829 - PathExpert - INFO - 开始路径结构分析
2025-06-08 13:41:28,835 - PathExpert - INFO - 路径结构分析完成
2025-06-08 13:41:28,835 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': [{'edge': '(21, 8)', 'frequency': 0.2}, {'edge': '(20, 55)', 'frequency': 0.2}, {'edge': '(27, 35)', 'frequency': 0.2}, {'edge': '(49, 57)', 'frequency': 0.2}, {'edge': '(24, 36)', 'frequency': 0.2}, {'edge': '(0, 45)', 'frequency': 0.2}, {'edge': '(37, 17)', 'frequency': 0.2}, {'edge': '(17, 1)', 'frequency': 0.2}, {'edge': '(44, 25)', 'frequency': 0.2}, {'edge': '(6, 30)', 'frequency': 0.2}, {'edge': '(30, 16)', 'frequency': 0.2}, {'edge': '(42, 59)', 'frequency': 0.2}, {'edge': '(12, 49)', 'frequency': 0.2}, {'edge': '(48, 18)', 'frequency': 0.2}, {'edge': '(9, 16)', 'frequency': 0.2}, {'edge': '(62, 44)', 'frequency': 0.2}, {'edge': '(11, 4)', 'frequency': 0.3}, {'edge': '(32, 33)', 'frequency': 0.2}, {'edge': '(54, 64)', 'frequency': 0.3}, {'edge': '(37, 30)', 'frequency': 0.2}, {'edge': '(1, 26)', 'frequency': 0.2}, {'edge': '(35, 53)', 'frequency': 0.2}, {'edge': '(55, 12)', 'frequency': 0.2}, {'edge': '(60, 15)', 'frequency': 0.2}, {'edge': '(15, 25)', 'frequency': 0.2}, {'edge': '(40, 9)', 'frequency': 0.2}, {'edge': '(44, 21)', 'frequency': 0.2}, {'edge': '(36, 8)', 'frequency': 0.2}, {'edge': '(8, 46)', 'frequency': 0.2}, {'edge': '(13, 10)', 'frequency': 0.2}, {'edge': '(57, 43)', 'frequency': 0.2}, {'edge': '(63, 19)', 'frequency': 0.2}, {'edge': '(19, 3)', 'frequency': 0.2}, {'edge': '(35, 45)', 'frequency': 0.2}, {'edge': '(21, 41)', 'frequency': 0.2}, {'edge': '(20, 51)', 'frequency': 0.2}, {'edge': '(43, 61)', 'frequency': 0.2}, {'edge': '(53, 3)', 'frequency': 0.2}, {'edge': '(28, 23)', 'frequency': 0.2}, {'edge': '(64, 25)', 'frequency': 0.2}, {'edge': '(60, 35)', 'frequency': 0.2}, {'edge': '(18, 14)', 'frequency': 0.2}, {'edge': '(51, 55)', 'frequency': 0.2}, {'edge': '(54, 27)', 'frequency': 0.2}, {'edge': '(11, 17)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [31, 56, 32, 59, 47, 2, 41, 52], 'cost': 19441.0, 'size': 8}, {'region': [33, 63, 45, 60, 26], 'cost': 11569.0, 'size': 5}, {'region': [55, 31, 65, 51, 1], 'cost': 11271.0, 'size': 5}, {'region': [0, 38, 52, 42, 54], 'cost': 11256.0, 'size': 5}, {'region': [27, 62, 34, 63], 'cost': 8809.0, 'size': 4}]}
2025-06-08 13:41:28,836 - EliteExpert - INFO - 开始精英解分析
2025-06-08 13:41:28,849 - EliteExpert - INFO - 精英解分析完成
2025-06-08 13:41:28,849 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 80038.0, 'avg_gap': 93602.44545454545}, 'structure_gap': {'unique_elite_edges': 183, 'unique_pop_edges': 574, 'common_edges': 35}}, 'elite_diversity': {'diversity_score': 0.2141545323363505}}
2025-06-08 13:41:28,849 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 13:41:28,850 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 13:41:28,851 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=89559.0, Max=116987.0, Mean=103143.4, Std=6654.341548192428
- Diversity Level: 0.9882103082103081
- Convergence Level: 0.0
- Clustering Information: {"clusters": 10, "cluster_sizes": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: []
- Common Subpaths: []
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [], "low_frequency_edges": [{"edge": "(21, 8)", "frequency": 0.2}, {"edge": "(20, 55)", "frequency": 0.2}, {"edge": "(27, 35)", "frequency": 0.2}, {"edge": "(49, 57)", "frequency": 0.2}, {"edge": "(24, 36)", "frequency": 0.2}, {"edge": "(0, 45)", "frequency": 0.2}, {"edge": "(37, 17)", "frequency": 0.2}, {"edge": "(17, 1)", "frequency": 0.2}, {"edge": "(44, 25)", "frequency": 0.2}, {"edge": "(6, 30)", "frequency": 0.2}, {"edge": "(30, 16)", "frequency": 0.2}, {"edge": "(42, 59)", "frequency": 0.2}, {"edge": "(12, 49)", "frequency": 0.2}, {"edge": "(48, 18)", "frequency": 0.2}, {"edge": "(9, 16)", "frequency": 0.2}, {"edge": "(62, 44)", "frequency": 0.2}, {"edge": "(11, 4)", "frequency": 0.3}, {"edge": "(32, 33)", "frequency": 0.2}, {"edge": "(54, 64)", "frequency": 0.3}, {"edge": "(37, 30)", "frequency": 0.2}, {"edge": "(1, 26)", "frequency": 0.2}, {"edge": "(35, 53)", "frequency": 0.2}, {"edge": "(55, 12)", "frequency": 0.2}, {"edge": "(60, 15)", "frequency": 0.2}, {"edge": "(15, 25)", "frequency": 0.2}, {"edge": "(40, 9)", "frequency": 0.2}, {"edge": "(44, 21)", "frequency": 0.2}, {"edge": "(36, 8)", "frequency": 0.2}, {"edge": "(8, 46)", "frequency": 0.2}, {"edge": "(13, 10)", "frequency": 0.2}, {"edge": "(57, 43)", "frequency": 0.2}, {"edge": "(63, 19)", "frequency": 0.2}, {"edge": "(19, 3)", "frequency": 0.2}, {"edge": "(35, 45)", "frequency": 0.2}, {"edge": "(21, 41)", "frequency": 0.2}, {"edge": "(20, 51)", "frequency": 0.2}, {"edge": "(43, 61)", "frequency": 0.2}, {"edge": "(53, 3)", "frequency": 0.2}, {"edge": "(28, 23)", "frequency": 0.2}, {"edge": "(64, 25)", "frequency": 0.2}, {"edge": "(60, 35)", "frequency": 0.2}, {"edge": "(18, 14)", "frequency": 0.2}, {"edge": "(51, 55)", "frequency": 0.2}, {"edge": "(54, 27)", "frequency": 0.2}, {"edge": "(11, 17)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [31, 56, 32, 59, 47, 2, 41, 52], "cost": 19441.0, "size": 8}, {"region": [33, 63, 45, 60, 26], "cost": 11569.0, "size": 5}, {"region": [55, 31, 65, 51, 1], "cost": 11271.0, "size": 5}, {"region": [0, 38, 52, 42, 54], "cost": 11256.0, "size": 5}, {"region": [27, 62, 34, 63], "cost": 8809.0, "size": 4}]

## Elite Solution Analysis
- Number of Elite Solutions: 22
- Common Features: {"common_edges": {}, "common_edge_ratio": 0.0}
- Fixed Nodes: [{"node": 0, "position": 0}]
- Population Gap: {"cost_gap": {"min_gap": 80038.0, "avg_gap": 93602.44545454545}, "structure_gap": {"unique_elite_edges": 183, "unique_pop_edges": 574, "common_edges": 35}}
- Elite Diversity: {"diversity_score": 0.2141545323363505}

## Historical Trends
Historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

