2025-07-04 10:23:44,511 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 10:23:44,511 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-04 10:23:44,511 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:23:44,531 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9945.0, 'max': 111302.0, 'mean': 77448.0, 'std': 44317.39713927252}, 'diversity': 0.9104377104377104, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:23:44,531 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:23:44,547 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:23:44,547 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:23:44,547 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 10:23:45,015 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:23:45,015 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:23:45,015 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9945.0, mean 77448.0, max 111302.0, std 44317.39713927252
- diversity: 0.9104377104377104
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: [{'subpath': (23, 16, 18), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [27, 54, 44, 63, 45, 56], 'cost': 14281.0, 'size': 6}, {'region': [9, 38, 26, 51, 56, 28], 'cost': 12548.0, 'size': 6}, {'region': [31, 44, 61, 34, 52], 'cost': 10892.0, 'size': 5}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:23:45,015 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:23:48,958 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.91,
    "convergence": 0.0,
    "clustering": "no_clustering_info"
  },
  "difficult_regions": [
    {
      "region": [
        27,
        54,
        44,
        63,
        45,
        56
      ],
      "cost": 14281.0,
      "size": 6,
      "details": "Identified via 'difficult_regions_sample'."
    },
    {
      "region": [
        9,
        38,
        26,
        51,
        56,
        28
      ],
      "cost": 12548.0,
      "size": 6,
      "details": "Identified via 'difficult_regions_sample'."
    },
    {
      "region": [
        31,
        44,
        61,
        34,
        52
      ],
      "cost": 10892.0,
      "size": 5,
      "details": "Identified via 'difficult_regions_sample'."
    }
  ],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply diversification operators, such as Random-Restart Hill Climbing or large-scale perturbation. The high diversity and lack of convergence indicate a need to explore the search space.",
      "Prioritize the identified 'difficult_regions' by applying specialized operators like 2-opt or 3-opt within these regions. These may contain local optima or areas of increased complexity.",
      "Consider guided exploration using the 'common_subpaths_sample' (e.g., creating partial solutions based on frequent subsequences)"
    ]
  }
}
```
2025-07-04 10:23:48,958 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:23:48,960 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.91, 'convergence': 0.0, 'clustering': 'no_clustering_info'}, 'difficult_regions': [{'region': [27, 54, 44, 63, 45, 56], 'cost': 14281.0, 'size': 6, 'details': "Identified via 'difficult_regions_sample'."}, {'region': [9, 38, 26, 51, 56, 28], 'cost': 12548.0, 'size': 6, 'details': "Identified via 'difficult_regions_sample'."}, {'region': [31, 44, 61, 34, 52], 'cost': 10892.0, 'size': 5, 'details': "Identified via 'difficult_regions_sample'."}], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply diversification operators, such as Random-Restart Hill Climbing or large-scale perturbation. The high diversity and lack of convergence indicate a need to explore the search space.', "Prioritize the identified 'difficult_regions' by applying specialized operators like 2-opt or 3-opt within these regions. These may contain local optima or areas of increased complexity.", "Consider guided exploration using the 'common_subpaths_sample' (e.g., creating partial solutions based on frequent subsequences)"]}}
2025-07-04 10:23:48,960 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:23:48,960 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:23:48,960 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.91
  • best_cost: 9945.0
  • mean_cost: 77448.0
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [27, 54, , {'region': [9, 38, 2...
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:23:48,960 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:23:48,960 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:23:51,294 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High population diversity (0.91) and an exploration focus warrants a high explore ratio. Individuals ranked lower explore."
}
```
2025-07-04 10:23:51,294 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-07-04 10:23:51,294 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-07-04 10:23:51,294 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High population diversity (0.91) and an exploration focus warrants a high explore ratio. Individuals ranked lower explore."
}
```
2025-07-04 10:23:51,294 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:23:51,294 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore']
2025-07-04 10:23:51,294 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High population diversity (0.91) and an exploration focus warrants a high explore ratio. Individuals ranked lower explore."
}
```
2025-07-04 10:23:51,294 - __main__ - INFO - 开始进化阶段
2025-07-04 10:23:51,294 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:23:51,294 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:23:51,294 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10030.0 (rank 20.0%)
- Population diversity: 0.7246153846153847
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:23:51,294 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:23:51,294 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:23:54,645 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61],
  "targeted_regions": "Avoids difficult regions. Prioritizes sparse cells near region [27].",
  "strategy_comment": "Exploration focused on avoiding problematic areas while incorporating sparse cell exploration near initial nodes."
}
```
2025-07-04 10:23:54,645 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:23:54,647 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29813.0, 路径: [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61]
2025-07-04 10:23:54,647 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61], 'cur_cost': 29813.0}
2025-07-04 10:23:54,647 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:23:54,647 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:23:54,647 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9945.0 (rank 0.0%)
- Population diversity: 0.7923076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:23:54,647 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:23:54,647 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:23:57,982 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 6, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26],
  "targeted_regions": "Avoided difficult regions: [27, 9]. Explored sparse cells.",
  "strategy_comment": "Prioritized avoiding difficult regions and attempted to cover sparse cells to boost diversity."
}
```
2025-07-04 10:23:57,982 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:23:57,982 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99649.0, 路径: [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26]
2025-07-04 10:23:57,982 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26], 'cur_cost': 99649.0}
2025-07-04 10:23:57,982 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:23:57,982 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:23:57,982 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9958.0 (rank 0.0%)
- Population diversity: 0.8569230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:23:57,982 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:23:57,982 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:01,134 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0],
  "targeted_regions": "Avoids difficult regions [27, 9] and targets sparse cells. ",
  "strategy_comment": "Prioritizes difficult region avoidance while seeking unexplored space, maintains path length and node uniqueness."
}
```
2025-07-04 10:24:01,134 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:01,134 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35937.0, 路径: [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0, 63]
2025-07-04 10:24:01,134 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0, 63], 'cur_cost': 35937.0}
2025-07-04 10:24:01,134 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:24:01,134 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:01,134 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107101.0 (rank 60.0%)
- Population diversity: 0.8338461538461538
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:01,134 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:01,134 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:04,119 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65],
  "targeted_regions": "Avoided difficult regions.",
  "strategy_comment": "Prioritized avoidance of difficult regions and generated a diverse path structure."
}
```
2025-07-04 10:24:04,119 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:04,119 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23370.0, 路径: [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65]
2025-07-04 10:24:04,119 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65], 'cur_cost': 23370.0}
2025-07-04 10:24:04,119 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 10:24:04,119 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:24:04,119 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:24:04,119 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 110210.0
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - res_population_costs: [99529]
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - res_populations: [array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64)]
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - populations: [{'tour': [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61], 'cur_cost': 29813.0}, {'tour': [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26], 'cur_cost': 99649.0}, {'tour': [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0, 63], 'cur_cost': 35937.0}, {'tour': [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65], 'cur_cost': 23370.0}, {'tour': array([27, 56, 10, 30, 60, 34,  5, 61, 31,  3, 44, 42,  2, 12, 22, 23, 17,
       64, 45, 58,  9, 65, 16, 48, 38, 39, 54, 47, 18,  8, 21, 26,  0, 49,
       43, 50, 20, 11, 55,  1, 40, 36, 14, 32, 41, 15, 62, 24, 13, 59, 25,
        7, 46, 35, 51, 29, 63, 28, 19, 33, 52, 53, 57, 37,  6,  4]), 'cur_cost': 110210.0}, {'tour': [7, 59, 50, 64, 26, 22, 30, 12, 40, 23, 36, 55, 15, 52, 13, 33, 45, 31, 1, 54, 46, 34, 21, 38, 27, 51, 57, 32, 11, 5, 0, 42, 39, 41, 20, 25, 49, 44, 4, 10, 2, 63, 53, 48, 17, 58, 43, 37, 35, 28, 8, 61, 60, 9, 29, 18, 6, 65, 16, 24, 56, 3, 14, 47, 19, 62], 'cur_cost': 100125.0}, {'tour': [5, 53, 30, 16, 36, 13, 7, 39, 43, 52, 3, 61, 18, 40, 38, 51, 17, 28, 2, 62, 42, 37, 32, 14, 22, 8, 19, 41, 15, 47, 9, 58, 12, 6, 33, 64, 46, 48, 24, 59, 0, 65, 4, 10, 27, 54, 44, 63, 45, 56, 20, 49, 55, 60, 25, 50, 21, 57, 23, 29, 1, 11, 35, 34, 31, 26], 'cur_cost': 101508.0}, {'tour': [20, 15, 26, 63, 23, 6, 33, 30, 5, 46, 48, 45, 29, 36, 60, 38, 22, 53, 51, 9, 39, 11, 56, 64, 58, 43, 41, 47, 55, 2, 19, 49, 50, 4, 16, 31, 44, 61, 34, 52, 57, 12, 8, 59, 3, 17, 37, 14, 28, 62, 35, 1, 18, 7, 27, 42, 0, 10, 32, 40, 21, 54, 65, 25, 13, 24], 'cur_cost': 103477.0}, {'tour': [23, 3, 24, 17, 48, 32, 39, 53, 11, 31, 33, 51, 29, 5, 30, 56, 64, 27, 54, 60, 37, 19, 22, 21, 57, 46, 28, 36, 65, 18, 0, 41, 45, 42, 2, 15, 10, 14, 38, 8, 7, 44, 4, 20, 59, 35, 58, 62, 47, 25, 34, 1, 55, 16, 13, 26, 49, 61, 50, 52, 63, 12, 43, 9, 40, 6], 'cur_cost': 111302.0}, {'tour': [42, 26, 5, 29, 51, 24, 36, 25, 40, 33, 53, 30, 28, 19, 0, 32, 55, 21, 3, 54, 43, 7, 38, 45, 17, 10, 18, 1, 65, 31, 13, 49, 22, 6, 52, 16, 56, 61, 34, 37, 62, 60, 39, 2, 47, 58, 63, 14, 8, 15, 9, 64, 41, 23, 57, 46, 11, 50, 48, 35, 27, 44, 12, 4, 20, 59], 'cur_cost': 110056.0}]
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - 局部搜索耗时: 2.39秒
2025-07-04 10:24:06,508 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 10:24:06,508 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-04 10:24:06,508 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:24:06,508 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:06,513 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100125.0 (rank 40.0%)
- Population diversity: 0.816923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:06,513 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:06,513 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:09,579 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 27, 9, 15, 42, 33, 58, 61, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65],
  "targeted_regions": "Avoids regions 9 and 27, prioritizes node exploration.",
  "strategy_comment": "Prioritized avoidance of difficult regions while exploring and building a novel, diverse path."
}
```
2025-07-04 10:24:09,579 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:09,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23722.0, 路径: [0, 27, 9, 15, 42, 33, 58, 61, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65]
2025-07-04 10:24:09,579 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 27, 9, 15, 42, 33, 58, 61, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65], 'cur_cost': 23722.0}
2025-07-04 10:24:09,581 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:24:09,581 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:09,582 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101508.0 (rank 50.0%)
- Population diversity: 0.7461538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:09,582 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:09,582 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:12,590 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [27, 9, 15, 33, 42, 51, 60, 65, 56, 47, 38, 29, 20, 11, 2, 8, 14, 23, 32, 41, 50, 59, 64, 55, 46, 37, 28, 19, 10, 1, 7, 13, 22, 31, 40, 49, 58, 63, 54, 45, 36, 27, 18, 9, 0, 6, 12, 21, 30, 39, 48, 57, 62, 53, 44, 35, 26, 17, 34, 25, 16, 24, 3, 4, 5, 16, 61],
  "targeted_regions": "avoiding difficult regions and exploring sparse cells",
  "strategy_comment": "Prioritizes cell exploration with avoidance of difficult regions while maintaining reasonable cost."
}
```
2025-07-04 10:24:12,590 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:12,590 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72656.0, 路径: [27, 9, 15, 33, 42, 51, 60, 65, 56, 47, 38, 29, 20, 11, 2, 8, 14, 23, 32, 41, 50, 59, 64, 55, 46, 37, 28, 19, 10, 1, 7, 13, 22, 31, 40, 49, 58, 63, 54, 45, 36, 18, 0, 6, 12, 21, 30, 39, 48, 57, 62, 53, 44, 35, 26, 17, 34, 25, 16, 24, 3, 4, 5, 61, 52, 43]
2025-07-04 10:24:12,590 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [27, 9, 15, 33, 42, 51, 60, 65, 56, 47, 38, 29, 20, 11, 2, 8, 14, 23, 32, 41, 50, 59, 64, 55, 46, 37, 28, 19, 10, 1, 7, 13, 22, 31, 40, 49, 58, 63, 54, 45, 36, 18, 0, 6, 12, 21, 30, 39, 48, 57, 62, 53, 44, 35, 26, 17, 34, 25, 16, 24, 3, 4, 5, 61, 52, 43], 'cur_cost': 72656.0}
2025-07-04 10:24:12,590 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:24:12,590 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:24:12,590 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:24:12,590 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 116288.0
2025-07-04 10:24:13,482 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 10:24:13,482 - ExploitationExpert - INFO - res_population_costs: [99529, 9544]
2025-07-04 10:24:13,482 - ExploitationExpert - INFO - res_populations: [array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-07-04 10:24:13,482 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:24:13,482 - ExploitationExpert - INFO - populations: [{'tour': [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61], 'cur_cost': 29813.0}, {'tour': [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26], 'cur_cost': 99649.0}, {'tour': [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0, 63], 'cur_cost': 35937.0}, {'tour': [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65], 'cur_cost': 23370.0}, {'tour': array([27, 56, 10, 30, 60, 34,  5, 61, 31,  3, 44, 42,  2, 12, 22, 23, 17,
       64, 45, 58,  9, 65, 16, 48, 38, 39, 54, 47, 18,  8, 21, 26,  0, 49,
       43, 50, 20, 11, 55,  1, 40, 36, 14, 32, 41, 15, 62, 24, 13, 59, 25,
        7, 46, 35, 51, 29, 63, 28, 19, 33, 52, 53, 57, 37,  6,  4]), 'cur_cost': 110210.0}, {'tour': [0, 27, 9, 15, 42, 33, 58, 61, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65], 'cur_cost': 23722.0}, {'tour': [27, 9, 15, 33, 42, 51, 60, 65, 56, 47, 38, 29, 20, 11, 2, 8, 14, 23, 32, 41, 50, 59, 64, 55, 46, 37, 28, 19, 10, 1, 7, 13, 22, 31, 40, 49, 58, 63, 54, 45, 36, 18, 0, 6, 12, 21, 30, 39, 48, 57, 62, 53, 44, 35, 26, 17, 34, 25, 16, 24, 3, 4, 5, 61, 52, 43], 'cur_cost': 72656.0}, {'tour': array([15, 53, 60,  0, 37,  9, 43, 38, 47, 24, 61, 26, 48, 44, 10, 63, 45,
        3, 51,  5, 21, 27, 22,  6, 55, 46, 62, 25, 52, 39,  7, 16, 56, 23,
       64, 34, 19, 59, 12, 20, 31,  4, 65,  1, 49, 50, 17,  2, 30, 29, 41,
       58, 13,  8, 18, 36, 11, 33, 57, 35, 32, 14, 42, 28, 40, 54]), 'cur_cost': 116288.0}, {'tour': [23, 3, 24, 17, 48, 32, 39, 53, 11, 31, 33, 51, 29, 5, 30, 56, 64, 27, 54, 60, 37, 19, 22, 21, 57, 46, 28, 36, 65, 18, 0, 41, 45, 42, 2, 15, 10, 14, 38, 8, 7, 44, 4, 20, 59, 35, 58, 62, 47, 25, 34, 1, 55, 16, 13, 26, 49, 61, 50, 52, 63, 12, 43, 9, 40, 6], 'cur_cost': 111302.0}, {'tour': [42, 26, 5, 29, 51, 24, 36, 25, 40, 33, 53, 30, 28, 19, 0, 32, 55, 21, 3, 54, 43, 7, 38, 45, 17, 10, 18, 1, 65, 31, 13, 49, 22, 6, 52, 16, 56, 61, 34, 37, 62, 60, 39, 2, 47, 58, 63, 14, 8, 15, 9, 64, 41, 23, 57, 46, 11, 50, 48, 35, 27, 44, 12, 4, 20, 59], 'cur_cost': 110056.0}]
2025-07-04 10:24:13,486 - ExploitationExpert - INFO - 局部搜索耗时: 0.90秒
2025-07-04 10:24:13,486 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 10:24:13,486 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:24:13,486 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-04 10:24:13,486 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:13,488 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111302.0 (rank 80.0%)
- Population diversity: 0.7369230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:13,488 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:13,488 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:16,632 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [27, 9, 15, 3, 60, 61, 45, 10, 38, 5, 41, 22, 56, 32, 20, 4, 18, 52, 1, 25, 12, 48, 63, 43, 19, 55, 28, 30, 2, 64, 46, 33, 58, 11, 44, 39, 21, 37, 59, 6, 54, 17, 35, 50, 36, 65, 51, 7, 42, 53, 29, 34, 40, 49, 16, 14, 24, 23, 8, 26, 13, 47, 31, 57],
  "targeted_regions": "Avoids difficult regions 27 & 9 and explores sparse areas with an initial segment.",
  "strategy_comment": "Prioritizes avoiding problematic regions and exploring under-visited areas early on to reduce overall cost."
}
```
2025-07-04 10:24:16,632 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:16,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114183.0, 路径: [27, 9, 15, 3, 60, 61, 45, 10, 38, 5, 41, 22, 56, 32, 20, 4, 18, 52, 1, 25, 12, 48, 63, 43, 19, 55, 28, 30, 2, 64, 46, 33, 58, 11, 44, 39, 21, 37, 59, 6, 54, 17, 35, 50, 36, 65, 51, 7, 42, 53, 29, 34, 40, 49, 16, 14, 24, 23, 8, 26, 13, 47, 31, 57, 62, 0]
2025-07-04 10:24:16,632 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [27, 9, 15, 3, 60, 61, 45, 10, 38, 5, 41, 22, 56, 32, 20, 4, 18, 52, 1, 25, 12, 48, 63, 43, 19, 55, 28, 30, 2, 64, 46, 33, 58, 11, 44, 39, 21, 37, 59, 6, 54, 17, 35, 50, 36, 65, 51, 7, 42, 53, 29, 34, 40, 49, 16, 14, 24, 23, 8, 26, 13, 47, 31, 57, 62, 0], 'cur_cost': 114183.0}
2025-07-04 10:24:16,632 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-04 10:24:16,632 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:16,632 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 110056.0 (rank 60.0%)
- Population diversity: 0.7430769230769231
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [27,, {'region': [9, ...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:16,632 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:16,632 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:19,787 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Avoided difficult regions: 27, 9. Focus on node coverage.",
  "strategy_comment": "Prioritized avoidance of difficult regions while maintaining a basic node traversal for path generation."
}
```
2025-07-04 10:24:19,787 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:19,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15170.0, 路径: [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:19,787 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15170.0}
2025-07-04 10:24:19,787 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 27, 9, 15, 32, 45, 58, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 3, 5, 6, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 26, 29, 30, 33, 39, 40, 42, 43, 46, 48, 49, 51, 52, 54, 55, 57, 60, 61], 'cur_cost': 29813.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [27, 9, 12, 35, 48, 5, 18, 42, 55, 62, 1, 23, 39, 50, 60, 8, 29, 45, 3, 15, 32, 58, 64, 10, 25, 41, 52, 6, 20, 37, 53, 65, 4, 16, 33, 59, 61, 7, 21, 38, 54, 63, 2, 13, 30, 46, 57, 0, 11, 28, 44, 56, 17, 34, 49, 51, 19, 36, 47, 14, 31, 40, 22, 24, 43, 26], 'cur_cost': 99649.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [27, 9, 15, 32, 48, 60, 55, 40, 23, 12, 1, 5, 8, 11, 17, 20, 22, 25, 28, 30, 33, 36, 39, 42, 45, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 57, 54, 52, 51, 49, 46, 44, 43, 41, 38, 37, 35, 34, 31, 29, 26, 24, 21, 19, 18, 16, 14, 13, 10, 7, 6, 4, 3, 2, 0, 63], 'cur_cost': 35937.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [27, 9, 15, 33, 48, 52, 61, 60, 59, 58, 57, 56, 55, 54, 53, 51, 50, 49, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 32, 31, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 14, 13, 12, 11, 10, 8, 7, 6, 5, 4, 3, 2, 1, 0, 62, 63, 64, 65], 'cur_cost': 23370.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 27, 9, 15, 42, 33, 58, 61, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65], 'cur_cost': 23722.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [27, 9, 15, 33, 42, 51, 60, 65, 56, 47, 38, 29, 20, 11, 2, 8, 14, 23, 32, 41, 50, 59, 64, 55, 46, 37, 28, 19, 10, 1, 7, 13, 22, 31, 40, 49, 58, 63, 54, 45, 36, 18, 0, 6, 12, 21, 30, 39, 48, 57, 62, 53, 44, 35, 26, 17, 34, 25, 16, 24, 3, 4, 5, 61, 52, 43], 'cur_cost': 72656.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [27, 9, 15, 3, 60, 61, 45, 10, 38, 5, 41, 22, 56, 32, 20, 4, 18, 52, 1, 25, 12, 48, 63, 43, 19, 55, 28, 30, 2, 64, 46, 33, 58, 11, 44, 39, 21, 37, 59, 6, 54, 17, 35, 50, 36, 65, 51, 7, 42, 53, 29, 34, 40, 49, 16, 14, 24, 23, 8, 26, 13, 47, 31, 57, 62, 0], 'cur_cost': 114183.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15170.0}}]
2025-07-04 10:24:19,787 - __main__ - INFO - 进化阶段完成
2025-07-04 10:24:19,787 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:24:19,808 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15170.0, 'max': 116288.0, 'mean': 64099.8, 'std': 40431.64168272172}, 'diversity': 0.8737373737373736, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 3, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:24:19,808 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-04 10:24:19,808 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-04 10:24:19,809 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:24:19,810 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 2 → 2
- Best elite cost: 9544 → 9544 (Δ 0)
- Elite diversity: 0.985 → 0.985 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:24:19,810 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:21,713 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_explore_individuals", "increase_diversity_weight"]
}
```
2025-07-04 10:24:21,714 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:24:21,715 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-04 10:24:21,715 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-04 10:24:21,715 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-04 10:24:21,715 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:24:21,731 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15170.0, 'max': 116288.0, 'mean': 64099.8, 'std': 40431.64168272172}, 'diversity': 0.8737373737373736, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 3, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:24:21,731 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:24:21,731 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:24:21,731 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:24:21,731 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:24:21,731 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:24:21,740 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:24:21,740 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:24:21,740 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 15170.0, mean 64099.8, max 116288.0, std 40431.64168272172
- diversity: 0.8737373737373736
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}, {'node': 53, 'position': 54}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.9696969696969697}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:24:21,741 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:24,449 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "N/A",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.87,
    "convergence": 0.0,
    "clustering": "no information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply mutation operators with high diversity (e.g., 2-opt, swap).",
      "Increase population size to further explore the search space.",
      "If possible, implement a mechanism to prevent premature convergence, such as fitness sharing or a diversity-preserving selection strategy."
    ]
  }
}
```
2025-07-04 10:24:24,450 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:24:24,450 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'N/A', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.87, 'convergence': 0.0, 'clustering': 'no information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply mutation operators with high diversity (e.g., 2-opt, swap).', 'Increase population size to further explore the search space.', 'If possible, implement a mechanism to prevent premature convergence, such as fitness sharing or a diversity-preserving selection strategy.']}}
2025-07-04 10:24:24,450 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:24:24,450 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:24:24,450 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.87
  • best_cost: 15170.0
  • mean_cost: 64099.8
- Search-space: ruggedness N/A, modality unknown, deceptiveness unknown
- Evolution phase: early exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:24:24,451 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:24:24,451 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:26,914 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity; focusing heavily on exploration to map the search space."
}
```
2025-07-04 10:24:26,914 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:26,914 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:26,914 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity; focusing heavily on exploration to map the search space."
}
```
2025-07-04 10:24:26,914 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:24:26,914 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:26,914 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity; focusing heavily on exploration to map the search space."
}
```
2025-07-04 10:24:26,917 - __main__ - INFO - 开始进化阶段
2025-07-04 10:24:26,918 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:24:26,918 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:26,918 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29813.0 (rank 30.0%)
- Population diversity: 0.6753846153846154
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:26,918 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:26,918 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:30,690 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells - all nodes visited once to avoid unexplored areas.",
  "strategy_comment": "Creates a path traversing all nodes, maximizing exploration and increasing population diversity."
}
```
2025-07-04 10:24:30,690 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:30,691 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 10:24:30,691 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 10:24:30,691 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:24:30,692 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:30,692 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99649.0 (rank 60.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:30,693 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:30,693 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:34,661 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all).",
  "strategy_comment": "Initial path starting from 0, covering all nodes for exploration and diversification."
}
```
2025-07-04 10:24:34,661 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:34,661 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:34,661 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:24:34,661 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:24:34,661 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:34,661 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 35937.0 (rank 50.0%)
- Population diversity: 0.5615384615384615
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:34,661 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:34,661 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:37,666 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells: Starting at node 0, then high index nodes.",
  "strategy_comment": "Prioritizing exploration of the entire graph, starting with a focus on nodes that may have been less visited."
}
```
2025-07-04 10:24:37,667 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:37,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 10:24:37,668 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 10:24:37,668 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:24:37,668 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:37,669 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 23370.0 (rank 40.0%)
- Population diversity: 0.48923076923076925
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:37,669 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:37,669 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:40,617 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all)",
  "strategy_comment": "Simple path covering all nodes to maximize exploration of unexplored areas given the iteration context."
}
```
2025-07-04 10:24:40,617 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:40,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:40,618 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:24:40,618 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:24:40,618 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:40,619 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 110210.0 (rank 70.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:40,619 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:40,619 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:44,274 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells (all nodes)",
  "strategy_comment": "Simple path covering all nodes to explore the entire space, optimizing for full coverage."
}
```
2025-07-04 10:24:44,275 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:44,275 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:44,275 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:24:44,275 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:24:44,275 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:44,275 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 23722.0 (rank 60.0%)
- Population diversity: 0.39692307692307693
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:44,275 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:44,275 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:47,232 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells exploration",
  "strategy_comment": "Basic path, exploring all nodes to build base and increase diversity."
}
```
2025-07-04 10:24:47,232 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:47,232 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:47,232 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:24:47,232 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:24:47,232 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:47,232 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 72656.0 (rank 70.0%)
- Population diversity: 0.3861538461538462
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:47,237 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:47,237 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:50,300 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells - all unexplored",
  "strategy_comment": "Simple path visiting all nodes in ascending order to explore the entire space."
}
```
2025-07-04 10:24:50,300 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:24:50,300 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:24:50,300 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:24:50,300 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:24:50,300 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:24:50,300 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:24:50,300 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101302.0
2025-07-04 10:24:50,807 - ExploitationExpert - INFO - res_population_num: 7
2025-07-04 10:24:50,807 - ExploitationExpert - INFO - res_population_costs: [9544, 99529, 9536, 9527, 9522, 9521, 9521]
2025-07-04 10:24:50,810 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:24:50,813 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:24:50,813 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([48, 36, 12, 23, 30, 24, 42, 33, 55,  5, 56, 46,  3, 62, 52, 60, 27,
       58, 26, 47, 38,  7, 40,  4, 59, 22, 29, 45, 39, 20,  1, 65, 31, 17,
       25,  8, 49, 18, 14, 43, 16, 63, 21, 57, 61,  6, 50, 34, 35, 53, 32,
       10,  2, 11,  0, 54, 15, 28,  9, 37, 41, 51, 13, 19, 44, 64]), 'cur_cost': 101302.0}, {'tour': [27, 9, 15, 3, 60, 61, 45, 10, 38, 5, 41, 22, 56, 32, 20, 4, 18, 52, 1, 25, 12, 48, 63, 43, 19, 55, 28, 30, 2, 64, 46, 33, 58, 11, 44, 39, 21, 37, 59, 6, 54, 17, 35, 50, 36, 65, 51, 7, 42, 53, 29, 34, 40, 49, 16, 14, 24, 23, 8, 26, 13, 47, 31, 57, 62, 0], 'cur_cost': 114183.0}, {'tour': [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15170.0}]
2025-07-04 10:24:50,814 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:24:50,814 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 10:24:50,814 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:24:50,814 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:24:50,814 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:24:50,815 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:24:50,815 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100104.0
2025-07-04 10:24:51,323 - ExploitationExpert - INFO - res_population_num: 15
2025-07-04 10:24:51,323 - ExploitationExpert - INFO - res_population_costs: [9544, 99529, 9536, 9527, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:24:51,323 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:24:51,332 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:24:51,332 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([48, 36, 12, 23, 30, 24, 42, 33, 55,  5, 56, 46,  3, 62, 52, 60, 27,
       58, 26, 47, 38,  7, 40,  4, 59, 22, 29, 45, 39, 20,  1, 65, 31, 17,
       25,  8, 49, 18, 14, 43, 16, 63, 21, 57, 61,  6, 50, 34, 35, 53, 32,
       10,  2, 11,  0, 54, 15, 28,  9, 37, 41, 51, 13, 19, 44, 64]), 'cur_cost': 101302.0}, {'tour': array([53,  2, 55, 49, 31,  6, 38, 25, 63, 58,  1, 46, 22, 19, 61, 40, 39,
       21, 59, 43, 29, 11, 26,  4, 13, 16, 27, 60, 28,  0,  5, 54, 41, 47,
       24, 30, 17, 10, 34, 51,  8, 57, 52, 14, 20, 48, 50, 37, 35, 64,  3,
       65, 32, 44, 42, 23, 18, 36, 56, 62,  9, 45, 15, 12,  7, 33]), 'cur_cost': 100104.0}, {'tour': [0, 27, 9, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15170.0}]
2025-07-04 10:24:51,333 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:24:51,334 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 10:24:51,334 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:24:51,334 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:24:51,334 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:24:51,334 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:24:51,334 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99453.0
2025-07-04 10:24:51,848 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:24:51,851 - ExploitationExpert - INFO - res_population_costs: [9544, 99529, 9536, 9527, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:24:51,851 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 10:24:51,857 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:24:51,858 - ExploitationExpert - INFO - populations: [{'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([48, 36, 12, 23, 30, 24, 42, 33, 55,  5, 56, 46,  3, 62, 52, 60, 27,
       58, 26, 47, 38,  7, 40,  4, 59, 22, 29, 45, 39, 20,  1, 65, 31, 17,
       25,  8, 49, 18, 14, 43, 16, 63, 21, 57, 61,  6, 50, 34, 35, 53, 32,
       10,  2, 11,  0, 54, 15, 28,  9, 37, 41, 51, 13, 19, 44, 64]), 'cur_cost': 101302.0}, {'tour': array([53,  2, 55, 49, 31,  6, 38, 25, 63, 58,  1, 46, 22, 19, 61, 40, 39,
       21, 59, 43, 29, 11, 26,  4, 13, 16, 27, 60, 28,  0,  5, 54, 41, 47,
       24, 30, 17, 10, 34, 51,  8, 57, 52, 14, 20, 48, 50, 37, 35, 64,  3,
       65, 32, 44, 42, 23, 18, 36, 56, 62,  9, 45, 15, 12,  7, 33]), 'cur_cost': 100104.0}, {'tour': array([17, 38, 12, 14, 36, 22, 33, 56, 39, 64, 49, 27, 30, 23,  2,  7,  8,
       37, 21, 20, 62,  3, 60,  9, 18, 31,  1, 15, 43, 32, 48, 24, 42, 53,
       45, 54, 65, 29,  4, 35, 44, 19, 52, 51, 40,  0, 26, 25, 50, 47, 41,
       13, 11, 10, 61, 46, 57, 28,  5, 59, 63, 58, 55, 16,  6, 34]), 'cur_cost': 99453.0}]
2025-07-04 10:24:51,859 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:24:51,859 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 10:24:51,860 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:24:51,860 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 10:24:51,860 - __main__ - INFO - 进化阶段完成
2025-07-04 10:24:51,860 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:24:51,876 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 101302.0, 'mean': 38538.0, 'std': 40433.622063822084}, 'diversity': 0.5202020202020201, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:24:51,876 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-04 10:24:51,876 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-04 10:24:51,878 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:24:51,878 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 17 → 17
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.151 → 0.151 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:24:51,878 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:53,724 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight"]
}
```

2025-07-04 10:24:53,724 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:24:53,724 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-04 10:24:53,724 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-04 10:24:53,724 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-04 10:24:53,724 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:24:53,741 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 101302.0, 'mean': 38538.0, 'std': 40433.622063822084}, 'diversity': 0.5202020202020201, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:24:53,741 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:24:53,744 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:24:53,745 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:24:53,745 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:24:53,749 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:24:53,751 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:24:53,753 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:24:53,753 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 38538.0, max 101302.0, std 40433.622063822084
- diversity: 0.5202020202020201
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {'(51, 38)': 0.7647058823529411}, 'common_edge_ratio': 0.015151515151515152}
- elite_diversity: {'diversity_score': 0.28721033868092694}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:24:53,753 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:56,499 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.52,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore and exploit",
    "operators": [
      "Increase population diversity to explore the search space. Consider operators that introduce significant changes in the tour, such as large-scale rearrangements.",
      "Given the high diversity and zero convergence, prioritize exploration of new regions.  Consider crossover operators that disrupt existing tours more aggressively.",
      "Since elite_common_edges are present, consider operators that can intelligently insert edges, or disrupt those edges. "
    ]
  }
}
```
2025-07-04 10:24:56,499 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:24:56,500 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.52, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Increase population diversity to explore the search space. Consider operators that introduce significant changes in the tour, such as large-scale rearrangements.', 'Given the high diversity and zero convergence, prioritize exploration of new regions.  Consider crossover operators that disrupt existing tours more aggressively.', 'Since elite_common_edges are present, consider operators that can intelligently insert edges, or disrupt those edges. ']}}
2025-07-04 10:24:56,500 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:24:56,500 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:24:56,500 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.52
  • best_cost: 11479.0
  • mean_cost: 38538.0
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:24:56,500 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:24:56,500 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:24:58,686 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration bias due to the initial evolution stage and relatively low population diversity. Most individuals will explore."
}
```
2025-07-04 10:24:58,686 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:58,686 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:58,686 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration bias due to the initial evolution stage and relatively low population diversity. Most individuals will explore."
}
```
2025-07-04 10:24:58,686 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:24:58,686 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:24:58,690 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High exploration bias due to the initial evolution stage and relatively low population diversity. Most individuals will explore."
}
```
2025-07-04 10:24:58,690 - __main__ - INFO - 开始进化阶段
2025-07-04 10:24:58,690 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:24:58,691 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:24:58,691 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 50.0%)
- Population diversity: 0.37846153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:24:58,691 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:24:58,691 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:01,662 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells with zero visits",
  "strategy_comment": "Basic path covering all nodes to explore unexplored areas and boost diversity."
}
```
2025-07-04 10:25:01,662 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:01,662 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:01,662 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:01,662 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:25:01,662 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:01,662 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.37846153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:01,662 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:01,662 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:04,430 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells - all nodes visited.",
  "strategy_comment": "A basic path to cover all nodes in a sequential order to increase diversity and exploit all areas."
}
```
2025-07-04 10:25:04,430 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:04,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:04,430 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:04,430 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:25:04,430 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:04,430 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 60.0%)
- Population diversity: 0.37846153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:04,430 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:04,430 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:07,483 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cell exploration.",
  "strategy_comment": "Simple path exploring all nodes, aiming for high diversity with basic structure."
}
```
2025-07-04 10:25:07,483 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:07,483 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:07,483 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:07,483 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:25:07,483 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:07,483 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3769230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:07,483 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:07,483 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:10,790 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore sparse cells (all).",
  "strategy_comment": "Simple path that visits every node once, aiming for complete exploration and increased diversity."
}
```
2025-07-04 10:25:10,790 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:10,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:10,790 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:10,790 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:25:10,790 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:10,790 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3769230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:10,790 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:10,790 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:13,951 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes, basic exploration.",
  "strategy_comment": "Simple linear path to start exploration, covering all nodes and boosting diversity."
}
```
2025-07-04 10:25:13,952 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:13,952 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:13,953 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:13,953 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:25:13,953 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:13,954 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.3769230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:13,954 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:13,955 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:16,965 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited in a linear fashion to maximize exploration.",
  "strategy_comment": "Creates a very diverse path to sample the entire space, aiming for maximum node coverage."
}
```
2025-07-04 10:25:16,965 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:16,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:16,965 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:16,965 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 10:25:16,965 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:16,965 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:16,965 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 96196.0
2025-07-04 10:25:17,467 - ExploitationExpert - INFO - res_population_num: 18
2025-07-04 10:25:17,468 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521]
2025-07-04 10:25:17,468 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:25:17,474 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:17,474 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([31, 13, 50, 49, 38,  0, 11,  3, 20,  6, 53, 27, 55,  1, 10, 60, 32,
       18, 22,  5, 43, 45, 46, 62, 36,  8,  2,  9, 41, 64, 63, 25, 16, 59,
       24, 44, 39, 17,  7, 21, 28, 58, 12, 48, 37, 15, 29, 33,  4, 14, 30,
       65, 34, 52, 61, 56, 51, 57, 19, 54, 35, 26, 47, 40, 42, 23]), 'cur_cost': 96196.0}, {'tour': array([48, 36, 12, 23, 30, 24, 42, 33, 55,  5, 56, 46,  3, 62, 52, 60, 27,
       58, 26, 47, 38,  7, 40,  4, 59, 22, 29, 45, 39, 20,  1, 65, 31, 17,
       25,  8, 49, 18, 14, 43, 16, 63, 21, 57, 61,  6, 50, 34, 35, 53, 32,
       10,  2, 11,  0, 54, 15, 28,  9, 37, 41, 51, 13, 19, 44, 64]), 'cur_cost': 101302.0}, {'tour': array([53,  2, 55, 49, 31,  6, 38, 25, 63, 58,  1, 46, 22, 19, 61, 40, 39,
       21, 59, 43, 29, 11, 26,  4, 13, 16, 27, 60, 28,  0,  5, 54, 41, 47,
       24, 30, 17, 10, 34, 51,  8, 57, 52, 14, 20, 48, 50, 37, 35, 64,  3,
       65, 32, 44, 42, 23, 18, 36, 56, 62,  9, 45, 15, 12,  7, 33]), 'cur_cost': 100104.0}, {'tour': array([17, 38, 12, 14, 36, 22, 33, 56, 39, 64, 49, 27, 30, 23,  2,  7,  8,
       37, 21, 20, 62,  3, 60,  9, 18, 31,  1, 15, 43, 32, 48, 24, 42, 53,
       45, 54, 65, 29,  4, 35, 44, 19, 52, 51, 40,  0, 26, 25, 50, 47, 41,
       13, 11, 10, 61, 46, 57, 28,  5, 59, 63, 58, 55, 16,  6, 34]), 'cur_cost': 99453.0}]
2025-07-04 10:25:17,476 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:25:17,476 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-04 10:25:17,476 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 10:25:17,478 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:25:17,478 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:17,478 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:17,478 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 90864.0
2025-07-04 10:25:17,980 - ExploitationExpert - INFO - res_population_num: 18
2025-07-04 10:25:17,981 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521]
2025-07-04 10:25:17,981 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:25:17,983 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:17,983 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([31, 13, 50, 49, 38,  0, 11,  3, 20,  6, 53, 27, 55,  1, 10, 60, 32,
       18, 22,  5, 43, 45, 46, 62, 36,  8,  2,  9, 41, 64, 63, 25, 16, 59,
       24, 44, 39, 17,  7, 21, 28, 58, 12, 48, 37, 15, 29, 33,  4, 14, 30,
       65, 34, 52, 61, 56, 51, 57, 19, 54, 35, 26, 47, 40, 42, 23]), 'cur_cost': 96196.0}, {'tour': array([45, 36, 29, 14, 23, 20, 42, 18, 64, 32, 61,  1, 58, 33, 26, 41,  8,
        3, 52, 31, 27, 16, 21, 40, 57, 19, 15, 24, 56, 55,  0, 43, 46, 30,
       62, 37, 34, 49,  5, 44, 13, 39, 35, 12, 63, 10,  4, 11, 65,  2, 53,
        7, 60, 28, 22, 48, 38, 51, 47, 50, 54,  9, 59, 17,  6, 25]), 'cur_cost': 90864.0}, {'tour': array([53,  2, 55, 49, 31,  6, 38, 25, 63, 58,  1, 46, 22, 19, 61, 40, 39,
       21, 59, 43, 29, 11, 26,  4, 13, 16, 27, 60, 28,  0,  5, 54, 41, 47,
       24, 30, 17, 10, 34, 51,  8, 57, 52, 14, 20, 48, 50, 37, 35, 64,  3,
       65, 32, 44, 42, 23, 18, 36, 56, 62,  9, 45, 15, 12,  7, 33]), 'cur_cost': 100104.0}, {'tour': array([17, 38, 12, 14, 36, 22, 33, 56, 39, 64, 49, 27, 30, 23,  2,  7,  8,
       37, 21, 20, 62,  3, 60,  9, 18, 31,  1, 15, 43, 32, 48, 24, 42, 53,
       45, 54, 65, 29,  4, 35, 44, 19, 52, 51, 40,  0, 26, 25, 50, 47, 41,
       13, 11, 10, 61, 46, 57, 28,  5, 59, 63, 58, 55, 16,  6, 34]), 'cur_cost': 99453.0}]
2025-07-04 10:25:17,989 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:25:17,989 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-04 10:25:17,989 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:25:17,989 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:25:17,991 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:17,991 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:17,991 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 110293.0
2025-07-04 10:25:18,502 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 10:25:18,503 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521]
2025-07-04 10:25:18,503 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:25:18,510 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:18,510 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([31, 13, 50, 49, 38,  0, 11,  3, 20,  6, 53, 27, 55,  1, 10, 60, 32,
       18, 22,  5, 43, 45, 46, 62, 36,  8,  2,  9, 41, 64, 63, 25, 16, 59,
       24, 44, 39, 17,  7, 21, 28, 58, 12, 48, 37, 15, 29, 33,  4, 14, 30,
       65, 34, 52, 61, 56, 51, 57, 19, 54, 35, 26, 47, 40, 42, 23]), 'cur_cost': 96196.0}, {'tour': array([45, 36, 29, 14, 23, 20, 42, 18, 64, 32, 61,  1, 58, 33, 26, 41,  8,
        3, 52, 31, 27, 16, 21, 40, 57, 19, 15, 24, 56, 55,  0, 43, 46, 30,
       62, 37, 34, 49,  5, 44, 13, 39, 35, 12, 63, 10,  4, 11, 65,  2, 53,
        7, 60, 28, 22, 48, 38, 51, 47, 50, 54,  9, 59, 17,  6, 25]), 'cur_cost': 90864.0}, {'tour': array([21, 18, 58,  8, 46, 10, 62, 48, 28, 36, 32, 14, 51, 13, 50, 23, 19,
       37, 57, 56, 16, 20, 65, 54, 49, 24, 44, 27, 53, 41,  5, 63, 31,  0,
       22, 26, 43, 29,  4, 17, 25, 15,  9, 52, 34,  1, 39, 40, 30, 38, 61,
       12, 59, 60, 47, 33, 11,  6, 45,  3, 35,  7,  2, 55, 42, 64]), 'cur_cost': 110293.0}, {'tour': array([17, 38, 12, 14, 36, 22, 33, 56, 39, 64, 49, 27, 30, 23,  2,  7,  8,
       37, 21, 20, 62,  3, 60,  9, 18, 31,  1, 15, 43, 32, 48, 24, 42, 53,
       45, 54, 65, 29,  4, 35, 44, 19, 52, 51, 40,  0, 26, 25, 50, 47, 41,
       13, 11, 10, 61, 46, 57, 28,  5, 59, 63, 58, 55, 16,  6, 34]), 'cur_cost': 99453.0}]
2025-07-04 10:25:18,512 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:25:18,513 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-04 10:25:18,513 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:25:18,513 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:25:18,513 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:18,514 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:18,514 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104903.0
2025-07-04 10:25:20,127 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:25:20,127 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521, 9521, 9521]
2025-07-04 10:25:20,127 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 10:25:20,136 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:20,136 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([31, 13, 50, 49, 38,  0, 11,  3, 20,  6, 53, 27, 55,  1, 10, 60, 32,
       18, 22,  5, 43, 45, 46, 62, 36,  8,  2,  9, 41, 64, 63, 25, 16, 59,
       24, 44, 39, 17,  7, 21, 28, 58, 12, 48, 37, 15, 29, 33,  4, 14, 30,
       65, 34, 52, 61, 56, 51, 57, 19, 54, 35, 26, 47, 40, 42, 23]), 'cur_cost': 96196.0}, {'tour': array([45, 36, 29, 14, 23, 20, 42, 18, 64, 32, 61,  1, 58, 33, 26, 41,  8,
        3, 52, 31, 27, 16, 21, 40, 57, 19, 15, 24, 56, 55,  0, 43, 46, 30,
       62, 37, 34, 49,  5, 44, 13, 39, 35, 12, 63, 10,  4, 11, 65,  2, 53,
        7, 60, 28, 22, 48, 38, 51, 47, 50, 54,  9, 59, 17,  6, 25]), 'cur_cost': 90864.0}, {'tour': array([21, 18, 58,  8, 46, 10, 62, 48, 28, 36, 32, 14, 51, 13, 50, 23, 19,
       37, 57, 56, 16, 20, 65, 54, 49, 24, 44, 27, 53, 41,  5, 63, 31,  0,
       22, 26, 43, 29,  4, 17, 25, 15,  9, 52, 34,  1, 39, 40, 30, 38, 61,
       12, 59, 60, 47, 33, 11,  6, 45,  3, 35,  7,  2, 55, 42, 64]), 'cur_cost': 110293.0}, {'tour': array([19, 56, 65, 42, 49, 26, 43, 17, 46, 32, 12, 13, 51,  8, 31, 36, 62,
       64, 44, 50, 30,  7, 52, 14,  9, 22, 63, 57, 28, 23, 20, 15, 40, 37,
       24,  6, 55, 10, 39,  3, 16, 41, 34,  1,  5, 48, 60, 59,  4, 18, 38,
       45, 58, 33, 61, 25, 21, 11,  2, 29,  0, 47, 53, 35, 54, 27]), 'cur_cost': 104903.0}]
2025-07-04 10:25:20,139 - ExploitationExpert - INFO - 局部搜索耗时: 1.62秒
2025-07-04 10:25:20,140 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-04 10:25:20,140 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:25:20,140 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 10:25:20,140 - __main__ - INFO - 进化阶段完成
2025-07-04 10:25:20,140 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:25:20,156 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110293.0, 'mean': 47113.0, 'std': 43901.456839152845}, 'diversity': 0.6441077441077441, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:25:20,156 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-04 10:25:20,156 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-04 10:25:20,158 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:25:20,158 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.124 → 0.124 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:25:20,158 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:22,134 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate"]
}
```
2025-07-04 10:25:22,134 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:25:22,134 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-04 10:25:22,134 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-04 10:25:22,134 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-04 10:25:22,134 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:25:22,152 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110293.0, 'mean': 47113.0, 'std': 43901.456839152845}, 'diversity': 0.6441077441077441, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:25:22,153 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:25:22,155 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:25:22,155 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:25:22,156 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:25:22,164 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:25:22,166 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:25:22,166 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:25:22,166 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 47113.0, max 110293.0, std 43901.456839152845
- diversity: 0.6441077441077441
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {'(51, 38)': 0.7142857142857143}, 'common_edge_ratio': 0.015151515151515152}
- elite_diversity: {'diversity_score': 0.27316017316017316}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:25:22,167 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:24,947 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.644,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Increase population size to broaden exploration.",
      "Employ a diversification operator such as a large-scale mutation or crossover to introduce new genetic material.",
      "Consider a restart strategy with a different initial population if stagnation continues."
    ]
  }
}
```
2025-07-04 10:25:24,947 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:25:24,947 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.644, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Increase population size to broaden exploration.', 'Employ a diversification operator such as a large-scale mutation or crossover to introduce new genetic material.', 'Consider a restart strategy with a different initial population if stagnation continues.']}}
2025-07-04 10:25:24,948 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:25:24,948 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:25:24,949 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.644
  • best_cost: 11479.0
  • mean_cost: 47113.0
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:25:24,949 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:25:24,949 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:28,370 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early evolution stage, low diversity, and explore recommended. Biased toward exploration across most individuals."
}
```
2025-07-04 10:25:28,370 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:25:28,371 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:25:28,371 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early evolution stage, low diversity, and explore recommended. Biased toward exploration across most individuals."
}
```
2025-07-04 10:25:28,371 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:25:28,371 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:25:28,371 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early evolution stage, low diversity, and explore recommended. Biased toward exploration across most individuals."
}
```
2025-07-04 10:25:28,371 - __main__ - INFO - 开始进化阶段
2025-07-04 10:25:28,371 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:25:28,371 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:28,371 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:28,371 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:28,371 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:32,097 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes are visited.",
  "strategy_comment": "Basic linear path covering all nodes to establish a baseline for exploration."
}
```
2025-07-04 10:25:32,098 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:32,098 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:32,099 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:32,099 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:25:32,099 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:32,100 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:32,100 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:32,100 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:35,219 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore sparse cells. Simple linear path to increase diversity.",
  "strategy_comment": "Create a basic linear path to improve population diversity and cover sparse cells."
}
```
2025-07-04 10:25:35,220 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:35,220 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:35,221 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:35,221 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:25:35,222 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:35,222 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:35,222 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:35,222 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:38,193 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells, all nodes visited.",
  "strategy_comment": "A simple path that visits all nodes to maximize diversity during exploration."
}
```
2025-07-04 10:25:38,193 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:38,193 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:38,193 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:38,193 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:25:38,193 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:38,193 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:38,193 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:38,193 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:41,151 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes are visited, effectively exploring the entire space.",
  "strategy_comment": "Simple sequential path to ensure complete exploration, targeting all nodes given exploration focus."
}
```
2025-07-04 10:25:41,151 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:41,151 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:41,151 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:41,151 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:25:41,151 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:41,151 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:41,151 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:41,151 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:44,114 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Start at node 0 and explore all other nodes.",
  "strategy_comment": "A simple but diverse path, prioritizing exploration by visiting all nodes in a linear fashion."
}
```
2025-07-04 10:25:44,114 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:44,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 10:25:44,114 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 10:25:44,114 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:25:44,114 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:44,118 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4707692307692308
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:44,118 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:44,118 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:46,933 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited, no specific regions.",
  "strategy_comment": "Simple path covering all nodes to increase diversity. Baseline for further iterations."
}
```
2025-07-04 10:25:46,934 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:46,934 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:46,935 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:46,935 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:25:46,935 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:46,935 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96196.0 (rank 70.0%)
- Population diversity: 0.4707692307692308
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:46,936 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:46,936 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:49,706 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes explored.",
  "strategy_comment": "Simple linear traversal. Prioritizes exploration across all nodes."
}
```
2025-07-04 10:25:49,706 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:49,706 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:49,706 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:49,706 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 10:25:49,706 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:25:49,706 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90864.0 (rank 70.0%)
- Population diversity: 0.3861538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:25:49,710 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:25:49,710 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:52,648 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cell exploration.",
  "strategy_comment": "Initial path visiting all nodes, ensuring full exploration and path diversity."
}
```
2025-07-04 10:25:52,648 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:25:52,650 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:25:52,650 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:25:52,650 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:25:52,650 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:52,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:52,650 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 101679.0
2025-07-04 10:25:53,153 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 10:25:53,153 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521]
2025-07-04 10:25:53,153 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:25:53,162 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:53,162 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 56, 52, 53, 57,  3, 17,  1, 36, 31, 41, 39, 51, 10, 30, 22, 40,
       25, 63, 19, 47, 29, 13,  8, 45, 50, 62, 46, 64, 54, 24, 55, 26,  9,
       38, 16, 23,  6, 21, 14, 65, 37, 32, 27, 60,  5, 42, 48, 35,  4,  7,
       34, 15,  2, 49,  0, 28, 44, 20, 61, 33, 12, 11, 58, 59, 43]), 'cur_cost': 101679.0}, {'tour': array([19, 56, 65, 42, 49, 26, 43, 17, 46, 32, 12, 13, 51,  8, 31, 36, 62,
       64, 44, 50, 30,  7, 52, 14,  9, 22, 63, 57, 28, 23, 20, 15, 40, 37,
       24,  6, 55, 10, 39,  3, 16, 41, 34,  1,  5, 48, 60, 59,  4, 18, 38,
       45, 58, 33, 61, 25, 21, 11,  2, 29,  0, 47, 53, 35, 54, 27]), 'cur_cost': 104903.0}]
2025-07-04 10:25:53,164 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:25:53,164 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-04 10:25:53,164 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:25:53,164 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:25:53,164 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:25:53,165 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:25:53,165 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 106449.0
2025-07-04 10:25:53,667 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-07-04 10:25:53,667 - ExploitationExpert - INFO - res_population_num: 24
2025-07-04 10:25:53,667 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521, 9521]
2025-07-04 10:25:53,667 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 10:25:53,676 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:25:53,676 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 56, 52, 53, 57,  3, 17,  1, 36, 31, 41, 39, 51, 10, 30, 22, 40,
       25, 63, 19, 47, 29, 13,  8, 45, 50, 62, 46, 64, 54, 24, 55, 26,  9,
       38, 16, 23,  6, 21, 14, 65, 37, 32, 27, 60,  5, 42, 48, 35,  4,  7,
       34, 15,  2, 49,  0, 28, 44, 20, 61, 33, 12, 11, 58, 59, 43]), 'cur_cost': 101679.0}, {'tour': array([ 2, 37, 26,  1,  4, 27,  3, 62, 12, 35,  6, 11, 39, 53, 25,  7, 15,
       63,  8, 43, 17, 16, 65, 54, 31, 23, 50,  9, 13, 30, 56, 46, 57, 18,
       59, 32, 33, 24, 14, 58, 60, 45, 29, 36, 51, 49, 34, 47, 38, 61, 55,
       44, 52, 41,  5, 40, 22, 42, 21, 28, 48, 20,  0, 19, 10, 64]), 'cur_cost': 106449.0}]
2025-07-04 10:25:53,678 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:25:53,678 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-04 10:25:53,678 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:25:53,678 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 10:25:53,678 - __main__ - INFO - 进化阶段完成
2025-07-04 10:25:53,678 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:25:53,693 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 106449.0, 'mean': 30204.4, 'std': 36950.342223584346}, 'diversity': 0.3686868686868687, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:25:53,693 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-04 10:25:53,693 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-04 10:25:53,693 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:25:53,693 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 24 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.108 → 0.108 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:25:53,693 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:55,716 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase explore_ratio to 0.8",
    "increase diversity_weight to 0.5"
  ]
}
```
2025-07-04 10:25:55,718 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:25:55,718 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-04 10:25:55,718 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-04 10:25:55,718 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-04 10:25:55,718 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:25:55,718 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 106449.0, 'mean': 30204.4, 'std': 36950.342223584346}, 'diversity': 0.3686868686868687, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:25:55,733 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:25:55,736 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:25:55,737 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:25:55,737 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:25:55,746 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:25:55,746 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:25:55,746 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:25:55,746 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 30204.4, max 106449.0, std 36950.342223584346
- diversity: 0.3686868686868687
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.26778656126482214}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:25:55,746 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:25:58,944 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "unknown",
    "modality": "unknown",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.3686868686868687,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Explore through mutation: increase mutation rate to diversify solutions further.",
      "Introduce new solutions: Reseed the population with entirely new and diverse solutions.",
      "Use a local search operator in conjunction with the existing evolutionary strategy."
    ]
  }
}
```
2025-07-04 10:25:58,944 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:25:58,945 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'unknown', 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.3686868686868687, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Explore through mutation: increase mutation rate to diversify solutions further.', 'Introduce new solutions: Reseed the population with entirely new and diverse solutions.', 'Use a local search operator in conjunction with the existing evolutionary strategy.']}}
2025-07-04 10:25:58,945 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:25:58,945 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:25:58,945 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.369
  • best_cost: 11479.0
  • mean_cost: 30204.4
- Search-space: ruggedness unknown, modality unknown, deceptiveness unknown
- Evolution phase: early exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:25:58,946 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:25:58,946 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:01,733 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity warrants high exploration. Best individuals get a chance to refine."
}
```
2025-07-04 10:26:01,733 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:26:01,733 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:26:01,733 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity warrants high exploration. Best individuals get a chance to refine."
}
```
2025-07-04 10:26:01,733 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:26:01,733 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-04 10:26:01,733 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity warrants high exploration. Best individuals get a chance to refine."
}
```
2025-07-04 10:26:01,733 - __main__ - INFO - 开始进化阶段
2025-07-04 10:26:01,733 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:26:01,733 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:01,733 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:01,733 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:01,733 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:04,667 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells by visiting nodes sequentially.",
  "strategy_comment": "Creates a simple, diverse path to explore the search space, prioritizing node order variations."
}
```
2025-07-04 10:26:04,667 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:04,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:04,667 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:04,667 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:26:04,667 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:04,667 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:04,667 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:04,667 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:07,578 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells exploration",
  "strategy_comment": "Basic path to start exploring all nodes and increase diversity from the existing population."
}
```
2025-07-04 10:26:07,578 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:07,578 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:07,578 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:07,578 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:26:07,578 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:07,578 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:07,582 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:07,582 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:10,702 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64],
  "targeted_regions": "Sparse cells (all).",
  "strategy_comment": "Prioritizes exploring all nodes in a single pass, focusing on overall coverage during early exploration."
}
```
2025-07-04 10:26:10,703 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:10,703 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]
2025-07-04 10:26:10,703 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}
2025-07-04 10:26:10,703 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:26:10,703 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:10,703 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:10,705 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:10,705 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:13,698 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse Cells (all)",
  "strategy_comment": "Initial path exploring all nodes in a sequential order, covering all sparse regions."
}
```
2025-07-04 10:26:13,698 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:13,698 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:13,698 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:13,698 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:26:13,698 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:13,698 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 60.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:13,702 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:13,702 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:16,769 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse cells (all)",
  "strategy_comment": "A simple path visiting all nodes, exploring unexplored areas. Prioritizing breadth of coverage."
}
```
2025-07-04 10:26:16,769 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:16,769 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:16,769 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:16,769 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:26:16,769 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:16,769 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:16,769 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:16,769 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:20,767 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Simple linear path to ensure complete exploration and maximum diversity in early exploration phase."
}
```
2025-07-04 10:26:20,767 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:20,767 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:20,767 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:20,769 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:26:20,769 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:20,769 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:20,769 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:20,769 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:23,630 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells explored in a linear fashion.",
  "strategy_comment": "Creates a simple, diverse path visiting all nodes to explore the unexplored regions."
}
```
2025-07-04 10:26:23,630 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:23,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:23,630 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:23,630 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 10:26:23,630 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:26:23,630 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.29384615384615387
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:26:23,630 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:26:23,630 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:27,886 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "Simple linear path to ensure coverage of all nodes and initial exploration."
}
```
2025-07-04 10:26:27,886 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:26:27,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:26:27,886 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:26:27,886 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:26:27,886 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:26:27,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:26:27,886 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100881.0
2025-07-04 10:26:28,387 - ExploitationExpert - INFO - res_population_num: 26
2025-07-04 10:26:28,387 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521]
2025-07-04 10:26:28,387 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:26:28,397 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:26:28,398 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 26, 23, 51, 39, 20, 52, 35, 56, 17, 57, 45, 54, 18, 62, 10, 58,
       11, 24, 33, 46, 47, 41, 28,  4, 31, 25,  1, 19, 60, 43,  8, 61, 32,
       40, 49,  5, 64,  9, 53, 34, 30, 42, 44,  0,  2, 16, 63, 38,  3, 27,
       48, 29, 36,  7, 59, 22,  6, 13, 21, 15, 65, 55, 37, 12, 14]), 'cur_cost': 100881.0}, {'tour': array([ 2, 37, 26,  1,  4, 27,  3, 62, 12, 35,  6, 11, 39, 53, 25,  7, 15,
       63,  8, 43, 17, 16, 65, 54, 31, 23, 50,  9, 13, 30, 56, 46, 57, 18,
       59, 32, 33, 24, 14, 58, 60, 45, 29, 36, 51, 49, 34, 47, 38, 61, 55,
       44, 52, 41,  5, 40, 22, 42, 21, 28, 48, 20,  0, 19, 10, 64]), 'cur_cost': 106449.0}]
2025-07-04 10:26:28,399 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:26:28,399 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-04 10:26:28,399 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:26:28,399 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:26:28,399 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:26:28,400 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:26:28,400 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99840.0
2025-07-04 10:26:28,903 - ExploitationExpert - INFO - res_population_num: 27
2025-07-04 10:26:28,903 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9527, 9536, 9544, 99529, 9521, 9521, 9521]
2025-07-04 10:26:28,903 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 50,
       51, 38, 41, 45, 44, 39, 47, 46, 48, 43, 40, 49, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 57, 17,  4, 55, 35, 44, 41,  2, 14, 58, 29, 24, 12, 59, 64, 34,
       30, 16, 10, 37,  5, 15, 26, 51, 43, 49, 52, 18, 11, 19, 28, 60,  7,
        1, 54, 21, 27, 20, 46, 56,  3, 61, 33, 42, 32, 31, 50, 39, 63, 65,
       13, 45, 22, 53, 38, 48, 25, 47, 62, 36,  9,  8,  6, 40, 23],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 10:26:28,914 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:26:28,915 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 26, 23, 51, 39, 20, 52, 35, 56, 17, 57, 45, 54, 18, 62, 10, 58,
       11, 24, 33, 46, 47, 41, 28,  4, 31, 25,  1, 19, 60, 43,  8, 61, 32,
       40, 49,  5, 64,  9, 53, 34, 30, 42, 44,  0,  2, 16, 63, 38,  3, 27,
       48, 29, 36,  7, 59, 22,  6, 13, 21, 15, 65, 55, 37, 12, 14]), 'cur_cost': 100881.0}, {'tour': array([48, 39,  1, 54, 31, 20, 22, 38, 51, 61, 64,  5, 42, 28, 11, 58, 35,
       17,  8, 24, 15, 37,  4, 55, 45, 12, 60, 59,  2, 57, 30, 10, 47, 65,
       63, 25, 46, 53, 16, 50, 49, 52,  7, 40, 13,  9, 56,  6, 19, 23, 62,
        3, 14, 27, 36, 44, 29, 33, 32,  0, 43, 18, 34, 41, 26, 21]), 'cur_cost': 99840.0}]
2025-07-04 10:26:28,916 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:26:28,916 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-04 10:26:28,916 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:26:28,916 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64], 'cur_cost': 13563.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 10:26:28,918 - __main__ - INFO - 进化阶段完成
2025-07-04 10:26:28,918 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:26:28,932 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 100881.0, 'mean': 29463.7, 'std': 35454.523886381554}, 'diversity': 0.371043771043771, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:26:28,932 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-04 10:26:28,933 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-04 10:26:28,934 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:26:28,935 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 27 → 27
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.096 → 0.096 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:26:28,935 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:26:31,006 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_mutation_rate_increase"]
}
```

2025-07-04 10:26:31,006 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:26:31,006 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-04 10:26:31,022 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-04 10:26:31,022 - __main__ - INFO - 实例 composite13_66 处理完成
