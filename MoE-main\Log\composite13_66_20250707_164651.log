2025-07-07 16:46:51,565 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-07 16:46:51,565 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-07 16:46:51,565 - <PERSON>atsExpert - INFO - 开始统计分析
2025-07-07 16:46:51,585 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10030.0, 'max': 116278.0, 'mean': 74648.1, 'std': 42567.47445280258}, 'diversity': 0.9202020202020204, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:46:51,586 - PathExpert - INFO - 开始路径结构分析
2025-07-07 16:46:51,600 - PathExpert - INFO - 路径结构分析完成
2025-07-07 16:46:51,601 - <PERSON>Expert - INFO - 开始精英解分析
2025-07-07 16:46:51,601 - <PERSON>Expert - WARNING - 没有精英解可供分析
2025-07-07 16:46:52,205 - LandscapeExpert - INFO - 开始景观分析
2025-07-07 16:46:52,205 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-07 16:46:52,205 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 10030.0, mean 74648.1, max 116278.0, std 42567.47445280258
- diversity: 0.9202020202020204
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [46, 61, 37, 52, 33, 41], 'cost': 13804.0, 'size': 6}, {'region': [52, 40, 31, 55, 49, 37], 'cost': 12678.0, 'size': 6}, {'region': [27, 47, 25, 43, 57, 29], 'cost': 12069.0, 'size': 6}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-07 16:46:52,205 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:46:54,693 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.920,
    "convergence": 0.000,
    "clustering": "no"
  },
  "difficult_regions": [
    [
      10,
      20,
      30,
      40,
      50
    ],
    [
      7,
      14,
      21,
      28,
      35,
      42
    ]
  ],
  "opportunity_regions": [
    [
      6,
      11,
      16,
      21,
      26
    ],
    [
      50,
      51,
      52
    ]
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "mutation",
      "large_neighborhood_search"
    ]
  }
}
```
2025-07-07 16:46:54,693 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-07 16:46:54,693 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'no'}, 'difficult_regions': [[10, 20, 30, 40, 50], [7, 14, 21, 28, 35, 42]], 'opportunity_regions': [[6, 11, 16, 21, 26], [50, 51, 52]], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'large_neighborhood_search']}}
2025-07-07 16:46:54,693 - __main__ - INFO - 开始策略分配阶段
2025-07-07 16:46:54,693 - StrategyExpert - INFO - 开始策略分配分析
2025-07-07 16:46:54,693 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 10030.0
  • mean_cost: 74648.1
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [[10, 20, 30, 40, 50], [7, 14, 21, 28, 35, 42]]
- Opportunity regions (# 2): [[6, 11, 16, 21, 26], [50, 51, 52]]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-07 16:46:54,693 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-07 16:46:54,698 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:46:57,566 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase require focus on discovering new solutions. 75% explore to maintain population diversity."
}
```
2025-07-07 16:46:57,566 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:46:57,566 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:46:57,566 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase require focus on discovering new solutions. 75% explore to maintain population diversity."
}
```
2025-07-07 16:46:57,566 - __main__ - INFO - 策略分配阶段完成
2025-07-07 16:46:57,566 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:46:57,566 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase require focus on discovering new solutions. 75% explore to maintain population diversity."
}
```
2025-07-07 16:46:57,566 - __main__ - INFO - 开始进化阶段
2025-07-07 16:46:57,566 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-07 16:46:57,566 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:46:57,566 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10267.0 (rank 20.0%)
- Population diversity: 0.74
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [6, 11, 16, 21,, [50, 51, 52]
- Difficult regions to avoid (sample): [10, 20, 30, 40, [7, 14, 21, 28,
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:46:57,571 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:46:57,571 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:01,219 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7],
  "targeted_regions": "opportunity regions [6, 11, 16, 21, 50, 51, 52]",
  "strategy_comment": "Prioritized opportunity regions & connected them while avoiding difficult regions for diversity."
}
```
2025-07-07 16:47:01,219 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:01,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24235.0, 路径: [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10]
2025-07-07 16:47:01,219 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}
2025-07-07 16:47:01,219 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-07 16:47:01,219 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:01,223 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10030.0 (rank 0.0%)
- Population diversity: 0.7938461538461539
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [6, 11, 16, 21,, [50, 51, 52]
- Difficult regions to avoid (sample): [10, 20, 30, 40, [7, 14, 21, 28,
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:01,223 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:01,223 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:04,919 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28],
  "targeted_regions": "Opportunity regions [6, 11, 16, 21, [50, 51, 52]], sparse cells and avoiding difficult regions.",
  "strategy_comment": "Prioritizes opportunity regions and sparse cells while carefully avoiding difficult areas to maintain cost efficiency."
}
```
2025-07-07 16:47:04,919 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:04,919 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24611.0, 路径: [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28]
2025-07-07 16:47:04,919 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}
2025-07-07 16:47:04,919 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-07 16:47:04,919 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:04,919 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10030.0 (rank 0.0%)
- Population diversity: 0.7815384615384615
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [6, 11, 16, 21,, [50, 51, 52]
- Difficult regions to avoid (sample): [10, 20, 30, 40, [7, 14, 21, 28,
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:04,919 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:04,919 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:07,726 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28, 6],
  "targeted_regions": "Opportunity regions 6, 11, 16, 21, [50,51,52]. Avoids difficult regions.",
  "strategy_comment": "Prioritizes opportunity regions, attempts to stay away from difficult ones and maintains full path coverage for diversity."
}
```
2025-07-07 16:47:07,726 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:07,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24053.0, 路径: [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28]
2025-07-07 16:47:07,726 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}
2025-07-07 16:47:07,726 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-07 16:47:07,726 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:07,726 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102948.0 (rank 80.0%)
- Population diversity: 0.7169230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [6, 11, 16, 21,, [50, 51, 52]
- Difficult regions to avoid (sample): [10, 20, 30, 40, [7, 14, 21, 28,
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:07,726 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:07,726 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:11,651 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 40],
  "targeted_regions": "Opportunity regions: 6, 11, 16, 21, 50-52. Avoided: 10, 20, 30, 40. ",
  "strategy_comment": "Prioritized opportunity regions and explored adjacent areas while avoiding difficult regions, aiming for path diversification."
}
```
2025-07-07 16:47:11,651 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:11,651 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30228.0, 路径: [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7]
2025-07-07 16:47:11,651 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}
2025-07-07 16:47:11,651 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-07 16:47:11,651 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:11,654 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116278.0 (rank 90.0%)
- Population diversity: 0.6446153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [6, 11, 16, 21,, [50, 51, 52]
- Difficult regions to avoid (sample): [10, 20, 30, 40, [7, 14, 21, 28,
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:11,655 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:11,655 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:14,404 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40],
  "targeted_regions": "Opportunity regions: 6, 11, 16, 21, 50-52. Avoided: 7, 10, 14, 20, 28, 30, 40.",
  "strategy_comment": "Prioritized opportunity regions and avoided difficult regions, aiming for exploration and cost balance."
}
```
2025-07-07 16:47:14,404 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:14,404 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24093.0, 路径: [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7]
2025-07-07 16:47:14,404 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}
2025-07-07 16:47:14,404 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-07 16:47:14,404 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:14,404 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:14,404 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 105106.0
2025-07-07 16:47:16,761 - ExploitationExpert - INFO - res_population_num: 1
2025-07-07 16:47:16,763 - ExploitationExpert - INFO - res_population_costs: [84858]
2025-07-07 16:47:16,763 - ExploitationExpert - INFO - res_populations: [array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:47:16,763 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:16,763 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}, {'tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}, {'tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}, {'tour': array([49, 60, 56, 58, 62, 39, 52,  0,  8, 20, 21, 38, 55, 22, 15, 17, 48,
       13,  3, 42, 23, 14, 46, 29, 40, 41, 34, 11, 31, 64, 35, 50, 59, 27,
       53,  2,  1, 24,  4, 57, 33, 10, 63, 43, 36, 12,  6, 44, 37, 16, 51,
       65, 19, 54, 30, 18, 28, 26, 25, 45, 47,  7,  5, 32,  9, 61]), 'cur_cost': 105106.0}, {'tour': [25, 15, 10, 17, 40, 36, 30, 49, 62, 28, 33, 7, 9, 3, 16, 19, 59, 2, 13, 50, 51, 18, 64, 11, 31, 14, 47, 1, 58, 61, 65, 56, 48, 44, 26, 22, 46, 6, 39, 55, 5, 43, 63, 60, 41, 53, 21, 42, 57, 34, 35, 54, 27, 24, 12, 29, 52, 45, 32, 37, 8, 38, 0, 20, 4, 23], 'cur_cost': 101987.0}, {'tour': [26, 52, 10, 5, 56, 30, 64, 11, 33, 54, 19, 45, 18, 4, 16, 44, 0, 35, 60, 62, 65, 12, 50, 25, 31, 24, 28, 40, 39, 51, 55, 29, 47, 1, 36, 59, 3, 9, 41, 48, 57, 2, 37, 49, 63, 7, 14, 17, 53, 8, 27, 38, 46, 42, 58, 6, 13, 32, 20, 22, 43, 34, 21, 23, 61, 15], 'cur_cost': 97842.0}, {'tour': [27, 5, 28, 60, 48, 49, 7, 29, 24, 62, 44, 45, 59, 64, 65, 30, 21, 14, 9, 1, 26, 15, 51, 63, 6, 39, 25, 17, 41, 55, 46, 0, 32, 8, 57, 43, 61, 52, 16, 35, 20, 34, 50, 11, 40, 47, 42, 3, 56, 22, 23, 13, 19, 54, 18, 58, 38, 53, 31, 33, 10, 4, 12, 2, 37, 36], 'cur_cost': 102653.0}, {'tour': [60, 34, 35, 53, 24, 28, 62, 6, 0, 36, 32, 20, 4, 3, 2, 39, 49, 45, 57, 50, 48, 9, 30, 19, 51, 5, 61, 40, 27, 22, 15, 13, 11, 14, 38, 44, 26, 64, 21, 33, 59, 47, 41, 1, 10, 23, 54, 43, 8, 12, 58, 46, 65, 7, 18, 52, 42, 25, 17, 37, 16, 29, 31, 63, 56, 55], 'cur_cost': 97979.0}]
2025-07-07 16:47:16,764 - ExploitationExpert - INFO - 局部搜索耗时: 2.36秒
2025-07-07 16:47:16,764 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-07 16:47:16,764 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-07 16:47:16,764 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-07 16:47:16,764 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:16,764 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:16,766 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 121267.0
2025-07-07 16:47:17,655 - ExploitationExpert - INFO - res_population_num: 2
2025-07-07 16:47:17,655 - ExploitationExpert - INFO - res_population_costs: [84858, 9592]
2025-07-07 16:47:17,656 - ExploitationExpert - INFO - res_populations: [array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-07 16:47:17,656 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:17,656 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}, {'tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}, {'tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}, {'tour': array([49, 60, 56, 58, 62, 39, 52,  0,  8, 20, 21, 38, 55, 22, 15, 17, 48,
       13,  3, 42, 23, 14, 46, 29, 40, 41, 34, 11, 31, 64, 35, 50, 59, 27,
       53,  2,  1, 24,  4, 57, 33, 10, 63, 43, 36, 12,  6, 44, 37, 16, 51,
       65, 19, 54, 30, 18, 28, 26, 25, 45, 47,  7,  5, 32,  9, 61]), 'cur_cost': 105106.0}, {'tour': array([58, 31, 18, 49, 13,  8, 37, 59, 33, 17, 50, 65, 36,  0, 20, 60, 61,
       41, 30, 15, 32, 21, 19, 34, 44, 43, 64, 28, 48, 51, 29, 12, 46, 16,
       38, 52, 25, 56, 27, 40, 54, 11, 26,  4, 47, 55, 45, 63,  6, 23, 62,
       35,  1, 24,  2,  7, 42, 57, 39, 10,  3,  5, 14, 53,  9, 22]), 'cur_cost': 121267.0}, {'tour': [26, 52, 10, 5, 56, 30, 64, 11, 33, 54, 19, 45, 18, 4, 16, 44, 0, 35, 60, 62, 65, 12, 50, 25, 31, 24, 28, 40, 39, 51, 55, 29, 47, 1, 36, 59, 3, 9, 41, 48, 57, 2, 37, 49, 63, 7, 14, 17, 53, 8, 27, 38, 46, 42, 58, 6, 13, 32, 20, 22, 43, 34, 21, 23, 61, 15], 'cur_cost': 97842.0}, {'tour': [27, 5, 28, 60, 48, 49, 7, 29, 24, 62, 44, 45, 59, 64, 65, 30, 21, 14, 9, 1, 26, 15, 51, 63, 6, 39, 25, 17, 41, 55, 46, 0, 32, 8, 57, 43, 61, 52, 16, 35, 20, 34, 50, 11, 40, 47, 42, 3, 56, 22, 23, 13, 19, 54, 18, 58, 38, 53, 31, 33, 10, 4, 12, 2, 37, 36], 'cur_cost': 102653.0}, {'tour': [60, 34, 35, 53, 24, 28, 62, 6, 0, 36, 32, 20, 4, 3, 2, 39, 49, 45, 57, 50, 48, 9, 30, 19, 51, 5, 61, 40, 27, 22, 15, 13, 11, 14, 38, 44, 26, 64, 21, 33, 59, 47, 41, 1, 10, 23, 54, 43, 8, 12, 58, 46, 65, 7, 18, 52, 42, 25, 17, 37, 16, 29, 31, 63, 56, 55], 'cur_cost': 97979.0}]
2025-07-07 16:47:17,658 - ExploitationExpert - INFO - 局部搜索耗时: 0.89秒
2025-07-07 16:47:17,658 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-07 16:47:17,659 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-07 16:47:17,659 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-07 16:47:17,659 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:17,660 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:17,660 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 114283.0
2025-07-07 16:47:18,163 - ExploitationExpert - INFO - res_population_num: 15
2025-07-07 16:47:18,163 - ExploitationExpert - INFO - res_population_costs: [84858, 9592, 9578, 9572, 9560, 9560, 9545, 9544, 9541, 9534, 9527, 9527, 9526, 9525, 9521]
2025-07-07 16:47:18,164 - ExploitationExpert - INFO - res_populations: [array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:47:18,169 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:18,169 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}, {'tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}, {'tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}, {'tour': array([49, 60, 56, 58, 62, 39, 52,  0,  8, 20, 21, 38, 55, 22, 15, 17, 48,
       13,  3, 42, 23, 14, 46, 29, 40, 41, 34, 11, 31, 64, 35, 50, 59, 27,
       53,  2,  1, 24,  4, 57, 33, 10, 63, 43, 36, 12,  6, 44, 37, 16, 51,
       65, 19, 54, 30, 18, 28, 26, 25, 45, 47,  7,  5, 32,  9, 61]), 'cur_cost': 105106.0}, {'tour': array([58, 31, 18, 49, 13,  8, 37, 59, 33, 17, 50, 65, 36,  0, 20, 60, 61,
       41, 30, 15, 32, 21, 19, 34, 44, 43, 64, 28, 48, 51, 29, 12, 46, 16,
       38, 52, 25, 56, 27, 40, 54, 11, 26,  4, 47, 55, 45, 63,  6, 23, 62,
       35,  1, 24,  2,  7, 42, 57, 39, 10,  3,  5, 14, 53,  9, 22]), 'cur_cost': 121267.0}, {'tour': array([21, 48,  8, 17, 54, 41, 53, 61, 42, 33,  1,  4, 34, 49, 10, 52, 43,
       16, 60, 64, 44, 19,  0, 50, 31, 30, 36, 63, 27, 23, 32, 56, 37, 62,
       26, 65, 59, 28, 45, 11,  5, 51, 55, 22, 40,  9, 35, 15,  2, 18, 29,
       20, 47,  7, 38, 39, 13, 46, 24, 25, 58,  3,  6, 12, 14, 57]), 'cur_cost': 114283.0}, {'tour': [27, 5, 28, 60, 48, 49, 7, 29, 24, 62, 44, 45, 59, 64, 65, 30, 21, 14, 9, 1, 26, 15, 51, 63, 6, 39, 25, 17, 41, 55, 46, 0, 32, 8, 57, 43, 61, 52, 16, 35, 20, 34, 50, 11, 40, 47, 42, 3, 56, 22, 23, 13, 19, 54, 18, 58, 38, 53, 31, 33, 10, 4, 12, 2, 37, 36], 'cur_cost': 102653.0}, {'tour': [60, 34, 35, 53, 24, 28, 62, 6, 0, 36, 32, 20, 4, 3, 2, 39, 49, 45, 57, 50, 48, 9, 30, 19, 51, 5, 61, 40, 27, 22, 15, 13, 11, 14, 38, 44, 26, 64, 21, 33, 59, 47, 41, 1, 10, 23, 54, 43, 8, 12, 58, 46, 65, 7, 18, 52, 42, 25, 17, 37, 16, 29, 31, 63, 56, 55], 'cur_cost': 97979.0}]
2025-07-07 16:47:18,171 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-07 16:47:18,171 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-07 16:47:18,171 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-07 16:47:18,171 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-07 16:47:18,171 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:18,172 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:18,172 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 111977.0
2025-07-07 16:47:18,675 - ExploitationExpert - INFO - res_population_num: 19
2025-07-07 16:47:18,675 - ExploitationExpert - INFO - res_population_costs: [84858, 9592, 9578, 9572, 9560, 9560, 9545, 9544, 9541, 9534, 9527, 9527, 9526, 9525, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:18,675 - ExploitationExpert - INFO - res_populations: [array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:47:18,684 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:18,684 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}, {'tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}, {'tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}, {'tour': array([49, 60, 56, 58, 62, 39, 52,  0,  8, 20, 21, 38, 55, 22, 15, 17, 48,
       13,  3, 42, 23, 14, 46, 29, 40, 41, 34, 11, 31, 64, 35, 50, 59, 27,
       53,  2,  1, 24,  4, 57, 33, 10, 63, 43, 36, 12,  6, 44, 37, 16, 51,
       65, 19, 54, 30, 18, 28, 26, 25, 45, 47,  7,  5, 32,  9, 61]), 'cur_cost': 105106.0}, {'tour': array([58, 31, 18, 49, 13,  8, 37, 59, 33, 17, 50, 65, 36,  0, 20, 60, 61,
       41, 30, 15, 32, 21, 19, 34, 44, 43, 64, 28, 48, 51, 29, 12, 46, 16,
       38, 52, 25, 56, 27, 40, 54, 11, 26,  4, 47, 55, 45, 63,  6, 23, 62,
       35,  1, 24,  2,  7, 42, 57, 39, 10,  3,  5, 14, 53,  9, 22]), 'cur_cost': 121267.0}, {'tour': array([21, 48,  8, 17, 54, 41, 53, 61, 42, 33,  1,  4, 34, 49, 10, 52, 43,
       16, 60, 64, 44, 19,  0, 50, 31, 30, 36, 63, 27, 23, 32, 56, 37, 62,
       26, 65, 59, 28, 45, 11,  5, 51, 55, 22, 40,  9, 35, 15,  2, 18, 29,
       20, 47,  7, 38, 39, 13, 46, 24, 25, 58,  3,  6, 12, 14, 57]), 'cur_cost': 114283.0}, {'tour': array([41, 50,  3, 52, 15, 48, 60, 47,  6, 37, 58, 16, 28, 32, 12, 36, 31,
        2,  5, 46,  1, 43, 34, 42,  9, 62, 49, 53, 24, 18, 29, 14, 20, 13,
       10, 65, 27, 63, 25, 56, 44, 35, 21,  0, 39, 33, 64, 59,  8, 45, 40,
       11, 55, 51, 22, 17, 38, 19, 54, 23, 57, 61, 30, 26,  7,  4]), 'cur_cost': 111977.0}, {'tour': [60, 34, 35, 53, 24, 28, 62, 6, 0, 36, 32, 20, 4, 3, 2, 39, 49, 45, 57, 50, 48, 9, 30, 19, 51, 5, 61, 40, 27, 22, 15, 13, 11, 14, 38, 44, 26, 64, 21, 33, 59, 47, 41, 1, 10, 23, 54, 43, 8, 12, 58, 46, 65, 7, 18, 52, 42, 25, 17, 37, 16, 29, 31, 63, 56, 55], 'cur_cost': 97979.0}]
2025-07-07 16:47:18,686 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-07 16:47:18,686 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-07 16:47:18,687 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-07 16:47:18,687 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-07 16:47:18,688 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:18,688 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:18,688 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113611.0
2025-07-07 16:47:20,096 - ExploitationExpert - INFO - res_population_num: 21
2025-07-07 16:47:20,099 - ExploitationExpert - INFO - res_population_costs: [84858, 9592, 9578, 9572, 9560, 9560, 9545, 9544, 9541, 9534, 9527, 9527, 9526, 9525, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:20,099 - ExploitationExpert - INFO - res_populations: [array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:47:20,106 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:20,106 - ExploitationExpert - INFO - populations: [{'tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}, {'tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}, {'tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}, {'tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}, {'tour': array([49, 60, 56, 58, 62, 39, 52,  0,  8, 20, 21, 38, 55, 22, 15, 17, 48,
       13,  3, 42, 23, 14, 46, 29, 40, 41, 34, 11, 31, 64, 35, 50, 59, 27,
       53,  2,  1, 24,  4, 57, 33, 10, 63, 43, 36, 12,  6, 44, 37, 16, 51,
       65, 19, 54, 30, 18, 28, 26, 25, 45, 47,  7,  5, 32,  9, 61]), 'cur_cost': 105106.0}, {'tour': array([58, 31, 18, 49, 13,  8, 37, 59, 33, 17, 50, 65, 36,  0, 20, 60, 61,
       41, 30, 15, 32, 21, 19, 34, 44, 43, 64, 28, 48, 51, 29, 12, 46, 16,
       38, 52, 25, 56, 27, 40, 54, 11, 26,  4, 47, 55, 45, 63,  6, 23, 62,
       35,  1, 24,  2,  7, 42, 57, 39, 10,  3,  5, 14, 53,  9, 22]), 'cur_cost': 121267.0}, {'tour': array([21, 48,  8, 17, 54, 41, 53, 61, 42, 33,  1,  4, 34, 49, 10, 52, 43,
       16, 60, 64, 44, 19,  0, 50, 31, 30, 36, 63, 27, 23, 32, 56, 37, 62,
       26, 65, 59, 28, 45, 11,  5, 51, 55, 22, 40,  9, 35, 15,  2, 18, 29,
       20, 47,  7, 38, 39, 13, 46, 24, 25, 58,  3,  6, 12, 14, 57]), 'cur_cost': 114283.0}, {'tour': array([41, 50,  3, 52, 15, 48, 60, 47,  6, 37, 58, 16, 28, 32, 12, 36, 31,
        2,  5, 46,  1, 43, 34, 42,  9, 62, 49, 53, 24, 18, 29, 14, 20, 13,
       10, 65, 27, 63, 25, 56, 44, 35, 21,  0, 39, 33, 64, 59,  8, 45, 40,
       11, 55, 51, 22, 17, 38, 19, 54, 23, 57, 61, 30, 26,  7,  4]), 'cur_cost': 111977.0}, {'tour': array([58, 25, 55, 64, 63, 51, 37,  7, 16, 10, 42, 24, 53, 31,  3, 15, 30,
       34, 14, 44, 20, 28, 61, 27,  6, 13, 29,  2,  4, 65, 45,  5, 36, 12,
       26, 46, 17, 50, 54, 40, 56,  8, 35, 38, 62, 21, 43, 18, 48, 49, 23,
       41, 57, 33, 19, 39, 59, 52,  1, 60, 11, 47, 32,  9, 22,  0]), 'cur_cost': 113611.0}]
2025-07-07 16:47:20,108 - ExploitationExpert - INFO - 局部搜索耗时: 1.42秒
2025-07-07 16:47:20,109 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-07 16:47:20,109 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-07 16:47:20,109 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 11, 16, 21, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 28, 30, 40, 7, 10], 'cur_cost': 24235.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 10, 20, 30, 40, 7, 28], 'cur_cost': 24611.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 40, 30, 7, 10, 20, 28], 'cur_cost': 24053.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 51, 50, 52, 11, 16, 21, 1, 2, 3, 4, 5, 8, 9, 12, 13, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 40, 30, 10, 14, 20, 28, 7], 'cur_cost': 30228.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 6, 50, 51, 52, 1, 2, 3, 4, 5, 8, 9, 11, 12, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 14, 20, 21, 28, 30, 40, 10, 7], 'cur_cost': 24093.0}}]
2025-07-07 16:47:20,109 - __main__ - INFO - 进化阶段完成
2025-07-07 16:47:20,110 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:47:20,126 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 24053.0, 'max': 121267.0, 'mean': 69346.4, 'std': 44086.76050970404}, 'diversity': 0.7888888888888889, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:47:20,126 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-07 16:47:20,126 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-07 16:47:20,129 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-07 16:47:20,129 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.136 → 0.136 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-07 16:47:20,129 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:22,733 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase_explore_ratio",
    "increase_mutation_rate",
    "monitor_diversity_closely"
  ]
}
```
2025-07-07 16:47:22,733 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-07 16:47:22,733 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-07 16:47:22,733 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-07 16:47:22,733 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-07 16:47:22,733 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:47:22,749 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 24053.0, 'max': 121267.0, 'mean': 69346.4, 'std': 44086.76050970404}, 'diversity': 0.7888888888888889, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:47:22,749 - PathExpert - INFO - 开始路径结构分析
2025-07-07 16:47:22,756 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-07 16:47:22,756 - PathExpert - INFO - 路径结构分析完成
2025-07-07 16:47:22,756 - EliteExpert - INFO - 开始精英解分析
2025-07-07 16:47:22,759 - EliteExpert - INFO - 精英解分析完成
2025-07-07 16:47:22,768 - LandscapeExpert - INFO - 开始景观分析
2025-07-07 16:47:22,769 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-07 16:47:22,769 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 24053.0, mean 69346.4, max 121267.0, std 44086.76050970404
- diversity: 0.7888888888888889
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {'(53, 64)': 0.7142857142857143, '(64, 57)': 0.7142857142857143, '(57, 54)': 0.7142857142857143, '(62, 59)': 0.7142857142857143}, 'common_edge_ratio': 0.06060606060606061}
- elite_diversity: {'diversity_score': 0.3265512265512265}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-07 16:47:22,769 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:25,252 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.789,
    "convergence": 0.000,
    "clustering": "no clustering information"
  },
  "difficult_regions": [
    [
      1,
      2
    ],
    [
      3,
      4
    ],
    [
      5,
      6
    ],
    [
      7,
      8
    ]
  ],
  "opportunity_regions": [
    [
      60,
      61
    ],
    [
      62,
      63
    ],
    [
      64,
      65
    ],
    [
      66,
      67
    ]
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "mutation",
      "crossover"
    ]
  }
}
```
2025-07-07 16:47:25,252 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-07 16:47:25,252 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.789, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [[1, 2], [3, 4], [5, 6], [7, 8]], 'opportunity_regions': [[60, 61], [62, 63], [64, 65], [66, 67]], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-07 16:47:25,252 - __main__ - INFO - 开始策略分配阶段
2025-07-07 16:47:25,252 - StrategyExpert - INFO - 开始策略分配分析
2025-07-07 16:47:25,252 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.789
  • best_cost: 24053.0
  • mean_cost: 69346.4
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 4): 4 items, first 2: [[1, 2], [3, 4]]
- Opportunity regions (# 4): 4 items, first 2: [[60, 61], [62, 63]]
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-07 16:47:25,255 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-07 16:47:25,255 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:27,012 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity, exploration phase, focus on explore is suggested. Assign majority individuals to explore."
}
```
2025-07-07 16:47:27,012 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-07 16:47:27,012 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-07 16:47:27,012 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity, exploration phase, focus on explore is suggested. Assign majority individuals to explore."
}
```
2025-07-07 16:47:27,012 - __main__ - INFO - 策略分配阶段完成
2025-07-07 16:47:27,012 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-07 16:47:27,012 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity, exploration phase, focus on explore is suggested. Assign majority individuals to explore."
}
```
2025-07-07 16:47:27,012 - __main__ - INFO - 开始进化阶段
2025-07-07 16:47:27,012 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-07 16:47:27,012 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:27,012 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 24235.0 (rank 20.0%)
- Population diversity: 0.5876923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:27,012 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:27,012 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:31,419 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4],
  "targeted_regions": "Opportunity regions [60, 61], [62, 63], avoid difficult regions [1, 2], [3, 4].",
  "strategy_comment": "Prioritizes opportunity regions, then avoids difficult regions by placing them last, maximizing path length and diversity."
}
```
2025-07-07 16:47:31,420 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:31,420 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4]
2025-07-07 16:47:31,420 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}
2025-07-07 16:47:31,420 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-07 16:47:31,421 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:31,421 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 24611.0 (rank 30.0%)
- Population diversity: 0.6076923076923076
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:31,422 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:31,422 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:34,182 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1],
  "targeted_regions": "Opportunity regions [60, 61], [62, 63], avoiding [1, 2], [3, 4].",
  "strategy_comment": "Prioritizes exploration of opportunity regions, links them and then returns, while avoiding the known difficult areas."
}
```
2025-07-07 16:47:34,183 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:34,183 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13477.0, 路径: [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1]
2025-07-07 16:47:34,183 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}
2025-07-07 16:47:34,184 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-07 16:47:34,184 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:34,185 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 24053.0 (rank 20.0%)
- Population diversity: 0.6076923076923076
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:34,185 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:34,185 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:36,788 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4],
  "targeted_regions": "opportunity regions [60,63], avoided difficult regions [1,4]",
  "strategy_comment": "Prioritized opportunity regions first, then filled in the remaining nodes with a focus on diversity."
}
```
2025-07-07 16:47:36,788 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:36,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13571.0, 路径: [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4]
2025-07-07 16:47:36,788 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}
2025-07-07 16:47:36,788 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-07 16:47:36,788 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:36,788 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30228.0 (rank 40.0%)
- Population diversity: 0.6030769230769231
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:36,788 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:36,788 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:39,732 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65],
  "targeted_regions": "opportunity regions (60, 61, 62, 63), avoids (1,2,3,4).",
  "strategy_comment": "Prioritized opportunity regions, bypassed difficult regions, and aimed for a diverse path."
}
```
2025-07-07 16:47:39,733 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:39,733 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11540.0, 路径: [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65]
2025-07-07 16:47:39,733 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}
2025-07-07 16:47:39,733 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-07 16:47:39,733 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:39,735 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 24093.0 (rank 40.0%)
- Population diversity: 0.5892307692307692
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:39,735 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:39,735 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:43,328 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4],
  "targeted_regions": "Opportunity regions [60, 61], [62, 63], Sparse cells around [60, 61], [62, 63]. Avoids [1, 2], [3, 4]",
  "strategy_comment": "Prioritizes opportunity regions, avoids difficult ones. Uses a sweeping path to cover the grid."
}
```
2025-07-07 16:47:43,328 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:43,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4]
2025-07-07 16:47:43,328 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}
2025-07-07 16:47:43,330 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-07 16:47:43,330 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:43,330 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:43,331 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 101609.0
2025-07-07 16:47:43,834 - ExploitationExpert - INFO - res_population_num: 29
2025-07-07 16:47:43,834 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:43,834 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:47:43,844 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:43,844 - ExploitationExpert - INFO - populations: [{'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}, {'tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': array([23, 24,  2, 53, 60, 37, 22, 13, 65, 29, 48, 15, 63, 16, 19, 50, 27,
       56, 46, 33,  7, 17, 30, 35, 41,  1, 42, 39, 10, 36, 40, 14, 20, 52,
        4, 21,  8,  9, 58,  6,  0, 38, 44, 43, 55, 54,  5, 57, 47, 11, 31,
       61, 51, 49, 28, 18, 62, 45, 64, 25, 34, 32, 59,  3, 26, 12]), 'cur_cost': 101609.0}, {'tour': array([58, 31, 18, 49, 13,  8, 37, 59, 33, 17, 50, 65, 36,  0, 20, 60, 61,
       41, 30, 15, 32, 21, 19, 34, 44, 43, 64, 28, 48, 51, 29, 12, 46, 16,
       38, 52, 25, 56, 27, 40, 54, 11, 26,  4, 47, 55, 45, 63,  6, 23, 62,
       35,  1, 24,  2,  7, 42, 57, 39, 10,  3,  5, 14, 53,  9, 22]), 'cur_cost': 121267.0}, {'tour': array([21, 48,  8, 17, 54, 41, 53, 61, 42, 33,  1,  4, 34, 49, 10, 52, 43,
       16, 60, 64, 44, 19,  0, 50, 31, 30, 36, 63, 27, 23, 32, 56, 37, 62,
       26, 65, 59, 28, 45, 11,  5, 51, 55, 22, 40,  9, 35, 15,  2, 18, 29,
       20, 47,  7, 38, 39, 13, 46, 24, 25, 58,  3,  6, 12, 14, 57]), 'cur_cost': 114283.0}, {'tour': array([41, 50,  3, 52, 15, 48, 60, 47,  6, 37, 58, 16, 28, 32, 12, 36, 31,
        2,  5, 46,  1, 43, 34, 42,  9, 62, 49, 53, 24, 18, 29, 14, 20, 13,
       10, 65, 27, 63, 25, 56, 44, 35, 21,  0, 39, 33, 64, 59,  8, 45, 40,
       11, 55, 51, 22, 17, 38, 19, 54, 23, 57, 61, 30, 26,  7,  4]), 'cur_cost': 111977.0}, {'tour': array([58, 25, 55, 64, 63, 51, 37,  7, 16, 10, 42, 24, 53, 31,  3, 15, 30,
       34, 14, 44, 20, 28, 61, 27,  6, 13, 29,  2,  4, 65, 45,  5, 36, 12,
       26, 46, 17, 50, 54, 40, 56,  8, 35, 38, 62, 21, 43, 18, 48, 49, 23,
       41, 57, 33, 19, 39, 59, 52,  1, 60, 11, 47, 32,  9, 22,  0]), 'cur_cost': 113611.0}]
2025-07-07 16:47:43,847 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:47:43,847 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-07 16:47:43,848 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-07 16:47:43,848 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-07 16:47:43,848 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:43,848 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:43,848 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 114280.0
2025-07-07 16:47:44,349 - ExploitationExpert - INFO - res_population_num: 33
2025-07-07 16:47:44,351 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:44,351 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - populations: [{'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}, {'tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': array([23, 24,  2, 53, 60, 37, 22, 13, 65, 29, 48, 15, 63, 16, 19, 50, 27,
       56, 46, 33,  7, 17, 30, 35, 41,  1, 42, 39, 10, 36, 40, 14, 20, 52,
        4, 21,  8,  9, 58,  6,  0, 38, 44, 43, 55, 54,  5, 57, 47, 11, 31,
       61, 51, 49, 28, 18, 62, 45, 64, 25, 34, 32, 59,  3, 26, 12]), 'cur_cost': 101609.0}, {'tour': array([57, 43, 53, 49, 64, 27, 35, 62, 63, 14, 52,  3, 55, 59, 65, 42, 16,
       47, 33, 48, 25, 34, 15, 40,  1, 23,  9, 54, 29, 61, 36,  8, 28, 50,
       11, 38, 46, 21, 26, 58, 17, 12, 10, 20,  2, 22, 51, 56, 44, 13, 37,
        4, 45, 19, 30,  7, 31, 24, 18, 60,  5,  0, 39,  6, 41, 32]), 'cur_cost': 114280.0}, {'tour': array([21, 48,  8, 17, 54, 41, 53, 61, 42, 33,  1,  4, 34, 49, 10, 52, 43,
       16, 60, 64, 44, 19,  0, 50, 31, 30, 36, 63, 27, 23, 32, 56, 37, 62,
       26, 65, 59, 28, 45, 11,  5, 51, 55, 22, 40,  9, 35, 15,  2, 18, 29,
       20, 47,  7, 38, 39, 13, 46, 24, 25, 58,  3,  6, 12, 14, 57]), 'cur_cost': 114283.0}, {'tour': array([41, 50,  3, 52, 15, 48, 60, 47,  6, 37, 58, 16, 28, 32, 12, 36, 31,
        2,  5, 46,  1, 43, 34, 42,  9, 62, 49, 53, 24, 18, 29, 14, 20, 13,
       10, 65, 27, 63, 25, 56, 44, 35, 21,  0, 39, 33, 64, 59,  8, 45, 40,
       11, 55, 51, 22, 17, 38, 19, 54, 23, 57, 61, 30, 26,  7,  4]), 'cur_cost': 111977.0}, {'tour': array([58, 25, 55, 64, 63, 51, 37,  7, 16, 10, 42, 24, 53, 31,  3, 15, 30,
       34, 14, 44, 20, 28, 61, 27,  6, 13, 29,  2,  4, 65, 45,  5, 36, 12,
       26, 46, 17, 50, 54, 40, 56,  8, 35, 38, 62, 21, 43, 18, 48, 49, 23,
       41, 57, 33, 19, 39, 59, 52,  1, 60, 11, 47, 32,  9, 22,  0]), 'cur_cost': 113611.0}]
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-07 16:47:44,362 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-07 16:47:44,362 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:44,362 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 115277.0
2025-07-07 16:47:44,863 - ExploitationExpert - INFO - res_population_num: 35
2025-07-07 16:47:44,863 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:44,863 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-07 16:47:44,877 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:44,877 - ExploitationExpert - INFO - populations: [{'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}, {'tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': array([23, 24,  2, 53, 60, 37, 22, 13, 65, 29, 48, 15, 63, 16, 19, 50, 27,
       56, 46, 33,  7, 17, 30, 35, 41,  1, 42, 39, 10, 36, 40, 14, 20, 52,
        4, 21,  8,  9, 58,  6,  0, 38, 44, 43, 55, 54,  5, 57, 47, 11, 31,
       61, 51, 49, 28, 18, 62, 45, 64, 25, 34, 32, 59,  3, 26, 12]), 'cur_cost': 101609.0}, {'tour': array([57, 43, 53, 49, 64, 27, 35, 62, 63, 14, 52,  3, 55, 59, 65, 42, 16,
       47, 33, 48, 25, 34, 15, 40,  1, 23,  9, 54, 29, 61, 36,  8, 28, 50,
       11, 38, 46, 21, 26, 58, 17, 12, 10, 20,  2, 22, 51, 56, 44, 13, 37,
        4, 45, 19, 30,  7, 31, 24, 18, 60,  5,  0, 39,  6, 41, 32]), 'cur_cost': 114280.0}, {'tour': array([54, 53, 52, 26, 14, 32, 24, 42, 20,  7, 45, 46, 62, 25, 47,  8, 40,
        6, 22, 59,  2, 43, 11, 50, 18,  0, 17,  4, 31, 34,  9, 49, 65, 30,
       60, 63,  1, 44, 16, 56, 33, 64, 28,  3, 58, 48, 23, 12, 38, 15, 27,
        5, 21, 36, 35, 55, 39, 51, 61, 41, 29, 19, 10, 57, 37, 13]), 'cur_cost': 115277.0}, {'tour': array([41, 50,  3, 52, 15, 48, 60, 47,  6, 37, 58, 16, 28, 32, 12, 36, 31,
        2,  5, 46,  1, 43, 34, 42,  9, 62, 49, 53, 24, 18, 29, 14, 20, 13,
       10, 65, 27, 63, 25, 56, 44, 35, 21,  0, 39, 33, 64, 59,  8, 45, 40,
       11, 55, 51, 22, 17, 38, 19, 54, 23, 57, 61, 30, 26,  7,  4]), 'cur_cost': 111977.0}, {'tour': array([58, 25, 55, 64, 63, 51, 37,  7, 16, 10, 42, 24, 53, 31,  3, 15, 30,
       34, 14, 44, 20, 28, 61, 27,  6, 13, 29,  2,  4, 65, 45,  5, 36, 12,
       26, 46, 17, 50, 54, 40, 56,  8, 35, 38, 62, 21, 43, 18, 48, 49, 23,
       41, 57, 33, 19, 39, 59, 52,  1, 60, 11, 47, 32,  9, 22,  0]), 'cur_cost': 113611.0}]
2025-07-07 16:47:44,879 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:47:44,879 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-07 16:47:44,879 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-07 16:47:44,879 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-07 16:47:44,879 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:47:44,879 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:47:44,879 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102807.0
2025-07-07 16:47:45,380 - ExploitationExpert - INFO - res_population_num: 36
2025-07-07 16:47:45,380 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-07 16:47:45,380 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-07 16:47:45,395 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:47:45,395 - ExploitationExpert - INFO - populations: [{'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}, {'tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}, {'tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': array([23, 24,  2, 53, 60, 37, 22, 13, 65, 29, 48, 15, 63, 16, 19, 50, 27,
       56, 46, 33,  7, 17, 30, 35, 41,  1, 42, 39, 10, 36, 40, 14, 20, 52,
        4, 21,  8,  9, 58,  6,  0, 38, 44, 43, 55, 54,  5, 57, 47, 11, 31,
       61, 51, 49, 28, 18, 62, 45, 64, 25, 34, 32, 59,  3, 26, 12]), 'cur_cost': 101609.0}, {'tour': array([57, 43, 53, 49, 64, 27, 35, 62, 63, 14, 52,  3, 55, 59, 65, 42, 16,
       47, 33, 48, 25, 34, 15, 40,  1, 23,  9, 54, 29, 61, 36,  8, 28, 50,
       11, 38, 46, 21, 26, 58, 17, 12, 10, 20,  2, 22, 51, 56, 44, 13, 37,
        4, 45, 19, 30,  7, 31, 24, 18, 60,  5,  0, 39,  6, 41, 32]), 'cur_cost': 114280.0}, {'tour': array([54, 53, 52, 26, 14, 32, 24, 42, 20,  7, 45, 46, 62, 25, 47,  8, 40,
        6, 22, 59,  2, 43, 11, 50, 18,  0, 17,  4, 31, 34,  9, 49, 65, 30,
       60, 63,  1, 44, 16, 56, 33, 64, 28,  3, 58, 48, 23, 12, 38, 15, 27,
        5, 21, 36, 35, 55, 39, 51, 61, 41, 29, 19, 10, 57, 37, 13]), 'cur_cost': 115277.0}, {'tour': array([10, 13,  6,  8, 20, 45, 30, 36, 17, 22, 12, 58, 25, 38, 50, 47, 42,
       56,  5, 32, 53, 31,  2, 34, 15, 11, 35, 51, 41, 55, 24, 52, 61, 16,
       40, 48, 19, 39, 27, 46,  1, 37, 65, 49, 21,  7, 62, 14, 57, 23, 18,
       29, 44, 60,  4, 64, 63, 26,  9, 33, 54, 59,  0,  3, 43, 28]), 'cur_cost': 102807.0}, {'tour': array([58, 25, 55, 64, 63, 51, 37,  7, 16, 10, 42, 24, 53, 31,  3, 15, 30,
       34, 14, 44, 20, 28, 61, 27,  6, 13, 29,  2,  4, 65, 45,  5, 36, 12,
       26, 46, 17, 50, 54, 40, 56,  8, 35, 38, 62, 21, 43, 18, 48, 49, 23,
       41, 57, 33, 19, 39, 59, 52,  1, 60, 11, 47, 32,  9, 22,  0]), 'cur_cost': 113611.0}]
2025-07-07 16:47:45,397 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:47:45,397 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-07 16:47:45,397 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-07 16:47:45,397 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-07 16:47:45,398 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:45,398 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113611.0 (rank 70.0%)
- Population diversity: 0.5723076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [60, 61], [62, 63]...
- Difficult regions to avoid (sample): [1, 2], [3, 4]...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:45,398 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:45,399 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:49,519 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65],
  "targeted_regions": "opportunity regions [60, 61], [62, 63] and avoid [1,2], [3,4].",
  "strategy_comment": "Prioritizes opportunity regions first, followed by avoiding difficulties and connecting remaining nodes."
}
```
2025-07-07 16:47:49,520 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:47:49,520 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15459.0, 路径: [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65]
2025-07-07 16:47:49,520 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65], 'cur_cost': 15459.0}
2025-07-07 16:47:49,521 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 4, 3, 2, 1], 'cur_cost': 13477.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 60, 61, 62, 63, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65, 1, 2, 3, 4], 'cur_cost': 13571.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [60, 61, 62, 63, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 64, 65], 'cur_cost': 11540.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65], 'cur_cost': 15459.0}}]
2025-07-07 16:47:49,521 - __main__ - INFO - 进化阶段完成
2025-07-07 16:47:49,521 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:47:49,538 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11540.0, 'max': 115277.0, 'mean': 51514.6, 'std': 46701.92385373434}, 'diversity': 0.6673400673400673, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:47:49,538 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-07 16:47:49,538 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-07 16:47:49,540 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-07 16:47:49,540 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 36 → 36
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.081 → 0.081 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-07 16:47:49,541 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:52,732 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight"]
}
```
2025-07-07 16:47:52,733 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-07 16:47:52,733 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-07 16:47:52,733 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-07 16:47:52,733 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-07 16:47:52,733 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:47:52,748 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11540.0, 'max': 115277.0, 'mean': 51514.6, 'std': 46701.92385373434}, 'diversity': 0.6673400673400673, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:47:52,748 - PathExpert - INFO - 开始路径结构分析
2025-07-07 16:47:52,748 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-07 16:47:52,748 - PathExpert - INFO - 路径结构分析完成
2025-07-07 16:47:52,748 - EliteExpert - INFO - 开始精英解分析
2025-07-07 16:47:52,776 - EliteExpert - INFO - 精英解分析完成
2025-07-07 16:47:52,777 - LandscapeExpert - INFO - 开始景观分析
2025-07-07 16:47:52,777 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-07 16:47:52,777 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11540.0, mean 51514.6, max 115277.0, std 46701.92385373434
- diversity: 0.6673400673400673
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2747234247234247}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-07 16:47:52,777 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:56,023 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.667,
    "convergence": 0.000,
    "clustering": "unknown"
  },
  "difficult_regions": [
    [
      321.0,
      436.0,
      2463.0,
      1565.212
    ],
    [
      1795.652,
      2463.0,
      3345.0,
      1565.212
    ]
  ],
  "opportunity_regions": [
    [
      436.0,
      321.0,
      1795.652,
      1565.212
    ],
    [
      1795.652,
      321.0,
      3345.0,
      1565.212
    ]
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "mutation with large step size",
      "random restart"
    ]
  }
}
```
2025-07-07 16:47:56,023 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-07 16:47:56,023 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.667, 'convergence': 0.0, 'clustering': 'unknown'}, 'difficult_regions': [[321.0, 436.0, 2463.0, 1565.212], [1795.652, 2463.0, 3345.0, 1565.212]], 'opportunity_regions': [[436.0, 321.0, 1795.652, 1565.212], [1795.652, 321.0, 3345.0, 1565.212]], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation with large step size', 'random restart']}}
2025-07-07 16:47:56,023 - __main__ - INFO - 开始策略分配阶段
2025-07-07 16:47:56,023 - StrategyExpert - INFO - 开始策略分配分析
2025-07-07 16:47:56,023 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.667
  • best_cost: 11540.0
  • mean_cost: 51514.6
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [[321.0, 436.0, 2463.0, 1565.212], [1795.652, 2463.0, 3345.0, 1565.212]]
- Opportunity regions (# 2): [[436.0, 321.0, 1795.652, 1565.212], [1795.652, 321.0, 3345.0, 1565.212]]
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-07 16:47:56,023 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-07 16:47:56,027 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:47:57,776 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploration phase. Low diversity suggests more exploration. Assigned exploration to top performers."
}
```
2025-07-07 16:47:57,776 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-07 16:47:57,776 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-07 16:47:57,776 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploration phase. Low diversity suggests more exploration. Assigned exploration to top performers."
}
```
2025-07-07 16:47:57,776 - __main__ - INFO - 策略分配阶段完成
2025-07-07 16:47:57,776 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-07 16:47:57,776 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 2, exploration phase. Low diversity suggests more exploration. Assigned exploration to top performers."
}
```
2025-07-07 16:47:57,776 - __main__ - INFO - 开始进化阶段
2025-07-07 16:47:57,776 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-07 16:47:57,776 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:47:57,776 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 20.0%)
- Population diversity: 0.49230769230769234
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:47:57,776 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:47:57,776 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:00,676 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42],
  "targeted_regions": "Explores around sparse and opportunity regions (43-65,0-2).",
  "strategy_comment": "Prioritizes opportunity regions and extends path through unexplored adjacent low-density cells."
}
```
2025-07-07 16:48:00,676 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:00,676 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]
2025-07-07 16:48:00,676 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}
2025-07-07 16:48:00,676 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-07 16:48:00,676 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:00,679 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13477.0 (rank 20.0%)
- Population diversity: 0.4938461538461538
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:00,679 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:00,679 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:04,032 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59],
  "targeted_regions": "opportunity regions and adjacent sparse cells",
  "strategy_comment": "Prioritize exploration of opportunity areas while attempting to avoid difficult regions and maintain a reasonable cost."
}
```
2025-07-07 16:48:04,032 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:04,033 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95743.0, 路径: [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59]
2025-07-07 16:48:04,033 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}
2025-07-07 16:48:04,033 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-07 16:48:04,033 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:04,034 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13571.0 (rank 30.0%)
- Population diversity: 0.5876923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:04,034 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:04,034 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:07,047 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44],
  "targeted_regions": "Opportunity regions (e.g., around nodes close to the beginning, middle, and end of the path)",
  "strategy_comment": "Prioritizes opportunity nodes and spreads out the path to increase diversity."
}
```
2025-07-07 16:48:07,048 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:07,048 - ExplorationExpert - INFO - 探索路径生成完成，成本: 82826.0, 路径: [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44]
2025-07-07 16:48:07,048 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}
2025-07-07 16:48:07,048 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-07 16:48:07,048 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:07,048 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:07,050 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 104133.0
2025-07-07 16:48:07,552 - ExploitationExpert - INFO - res_population_num: 38
2025-07-07 16:48:07,552 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521]
2025-07-07 16:48:07,553 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-07 16:48:07,565 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:07,565 - ExploitationExpert - INFO - populations: [{'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}, {'tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}, {'tour': array([36, 39, 44, 57, 27, 26,  7, 53, 47, 17, 45, 63, 10,  2, 49, 18, 24,
       31, 61, 55, 21, 32,  3, 15, 35, 30, 43, 38,  4, 64,  5, 19, 14, 11,
       34,  9, 42,  6, 41, 22, 56,  0, 48, 52, 50, 51, 37, 62, 60,  1, 29,
       33, 46, 58, 40, 28, 13,  8, 54, 20, 12, 65, 25, 16, 23, 59]), 'cur_cost': 104133.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 1, 2, 3, 4], 'cur_cost': 13563.0}, {'tour': array([23, 24,  2, 53, 60, 37, 22, 13, 65, 29, 48, 15, 63, 16, 19, 50, 27,
       56, 46, 33,  7, 17, 30, 35, 41,  1, 42, 39, 10, 36, 40, 14, 20, 52,
        4, 21,  8,  9, 58,  6,  0, 38, 44, 43, 55, 54,  5, 57, 47, 11, 31,
       61, 51, 49, 28, 18, 62, 45, 64, 25, 34, 32, 59,  3, 26, 12]), 'cur_cost': 101609.0}, {'tour': array([57, 43, 53, 49, 64, 27, 35, 62, 63, 14, 52,  3, 55, 59, 65, 42, 16,
       47, 33, 48, 25, 34, 15, 40,  1, 23,  9, 54, 29, 61, 36,  8, 28, 50,
       11, 38, 46, 21, 26, 58, 17, 12, 10, 20,  2, 22, 51, 56, 44, 13, 37,
        4, 45, 19, 30,  7, 31, 24, 18, 60,  5,  0, 39,  6, 41, 32]), 'cur_cost': 114280.0}, {'tour': array([54, 53, 52, 26, 14, 32, 24, 42, 20,  7, 45, 46, 62, 25, 47,  8, 40,
        6, 22, 59,  2, 43, 11, 50, 18,  0, 17,  4, 31, 34,  9, 49, 65, 30,
       60, 63,  1, 44, 16, 56, 33, 64, 28,  3, 58, 48, 23, 12, 38, 15, 27,
        5, 21, 36, 35, 55, 39, 51, 61, 41, 29, 19, 10, 57, 37, 13]), 'cur_cost': 115277.0}, {'tour': array([10, 13,  6,  8, 20, 45, 30, 36, 17, 22, 12, 58, 25, 38, 50, 47, 42,
       56,  5, 32, 53, 31,  2, 34, 15, 11, 35, 51, 41, 55, 24, 52, 61, 16,
       40, 48, 19, 39, 27, 46,  1, 37, 65, 49, 21,  7, 62, 14, 57, 23, 18,
       29, 44, 60,  4, 64, 63, 26,  9, 33, 54, 59,  0,  3, 43, 28]), 'cur_cost': 102807.0}, {'tour': [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65], 'cur_cost': 15459.0}]
2025-07-07 16:48:07,569 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:07,569 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-07 16:48:07,569 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-07 16:48:07,569 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-07 16:48:07,569 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:07,570 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 10.0%)
- Population diversity: 0.7461538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:07,571 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:07,571 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:11,341 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3],
  "targeted_regions": "Exploiting opportunity and avoiding difficult areas. Focus on exploration.",
  "strategy_comment": "Prioritizes opportunity regions while maintaining path validity and maximizing diversity."
}
```
2025-07-07 16:48:11,341 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:11,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32108.0, 路径: [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56]
2025-07-07 16:48:11,341 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56], 'cur_cost': 32108.0}
2025-07-07 16:48:11,341 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-07 16:48:11,341 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:11,341 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101609.0 (rank 50.0%)
- Population diversity: 0.8138461538461539
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:11,341 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:11,341 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:14,136 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42],
  "targeted_regions": "Opportunity region around nodes 43-65 and 0-10.",
  "strategy_comment": "Prioritizes opportunity region with a high-density initial segment, ensuring diverse path construction."
}
```
2025-07-07 16:48:14,137 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:14,137 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]
2025-07-07 16:48:14,137 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}
2025-07-07 16:48:14,137 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-07 16:48:14,139 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:14,139 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:14,139 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 97116.0
2025-07-07 16:48:14,642 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:14,643 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521]
2025-07-07 16:48:14,643 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:48:14,656 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:14,656 - ExploitationExpert - INFO - populations: [{'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}, {'tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}, {'tour': array([36, 39, 44, 57, 27, 26,  7, 53, 47, 17, 45, 63, 10,  2, 49, 18, 24,
       31, 61, 55, 21, 32,  3, 15, 35, 30, 43, 38,  4, 64,  5, 19, 14, 11,
       34,  9, 42,  6, 41, 22, 56,  0, 48, 52, 50, 51, 37, 62, 60,  1, 29,
       33, 46, 58, 40, 28, 13,  8, 54, 20, 12, 65, 25, 16, 23, 59]), 'cur_cost': 104133.0}, {'tour': [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56], 'cur_cost': 32108.0}, {'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': array([59, 64, 34, 26, 30, 18, 44, 31, 20,  3, 61,  4, 33, 22, 12, 43, 58,
       24, 32, 56, 54, 35, 60, 52, 13,  8, 41, 45, 38, 46,  2, 21, 57, 23,
        5, 19, 62, 42, 39,  0, 36, 15, 55, 50,  9, 65,  7, 27, 14, 63, 37,
       47, 48, 28, 29, 17,  1, 11, 53, 10, 16, 25, 51,  6, 49, 40]), 'cur_cost': 97116.0}, {'tour': array([54, 53, 52, 26, 14, 32, 24, 42, 20,  7, 45, 46, 62, 25, 47,  8, 40,
        6, 22, 59,  2, 43, 11, 50, 18,  0, 17,  4, 31, 34,  9, 49, 65, 30,
       60, 63,  1, 44, 16, 56, 33, 64, 28,  3, 58, 48, 23, 12, 38, 15, 27,
        5, 21, 36, 35, 55, 39, 51, 61, 41, 29, 19, 10, 57, 37, 13]), 'cur_cost': 115277.0}, {'tour': array([10, 13,  6,  8, 20, 45, 30, 36, 17, 22, 12, 58, 25, 38, 50, 47, 42,
       56,  5, 32, 53, 31,  2, 34, 15, 11, 35, 51, 41, 55, 24, 52, 61, 16,
       40, 48, 19, 39, 27, 46,  1, 37, 65, 49, 21,  7, 62, 14, 57, 23, 18,
       29, 44, 60,  4, 64, 63, 26,  9, 33, 54, 59,  0,  3, 43, 28]), 'cur_cost': 102807.0}, {'tour': [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65], 'cur_cost': 15459.0}]
2025-07-07 16:48:14,658 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:14,658 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-07 16:48:14,658 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-07 16:48:14,659 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-07 16:48:14,659 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:14,659 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 115277.0 (rank 90.0%)
- Population diversity: 0.7230769230769231
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 321.0, , [1795.652, 321.
- Difficult regions to avoid (sample): [321.0, 436.0, , [1795.652, 2463
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:14,660 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:14,660 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:18,466 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55],
  "targeted_regions": "Opportunity regions around node 43 and node 56. Includes low-density cells.",
  "strategy_comment": "Explores suggested regions using a modified route, increasing diversity and addressing sparsity."
}
```
2025-07-07 16:48:18,466 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:18,467 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116941.0, 路径: [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55]
2025-07-07 16:48:18,467 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55], 'cur_cost': 116941.0}
2025-07-07 16:48:18,467 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-07 16:48:18,467 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:18,467 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:18,470 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 97098.0
2025-07-07 16:48:18,971 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:18,971 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521]
2025-07-07 16:48:18,971 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:48:18,986 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:18,986 - ExploitationExpert - INFO - populations: [{'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}, {'tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}, {'tour': array([36, 39, 44, 57, 27, 26,  7, 53, 47, 17, 45, 63, 10,  2, 49, 18, 24,
       31, 61, 55, 21, 32,  3, 15, 35, 30, 43, 38,  4, 64,  5, 19, 14, 11,
       34,  9, 42,  6, 41, 22, 56,  0, 48, 52, 50, 51, 37, 62, 60,  1, 29,
       33, 46, 58, 40, 28, 13,  8, 54, 20, 12, 65, 25, 16, 23, 59]), 'cur_cost': 104133.0}, {'tour': [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56], 'cur_cost': 32108.0}, {'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': array([59, 64, 34, 26, 30, 18, 44, 31, 20,  3, 61,  4, 33, 22, 12, 43, 58,
       24, 32, 56, 54, 35, 60, 52, 13,  8, 41, 45, 38, 46,  2, 21, 57, 23,
        5, 19, 62, 42, 39,  0, 36, 15, 55, 50,  9, 65,  7, 27, 14, 63, 37,
       47, 48, 28, 29, 17,  1, 11, 53, 10, 16, 25, 51,  6, 49, 40]), 'cur_cost': 97116.0}, {'tour': [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55], 'cur_cost': 116941.0}, {'tour': array([59,  0, 40,  7, 39,  1, 64, 13, 56, 60, 51, 35, 34, 29, 26, 20, 38,
       23,  6, 47,  9, 53, 14, 31, 15, 44, 18, 58, 36,  5, 19, 24, 22, 21,
       17,  3,  4, 55, 65,  8, 61, 52, 49, 10, 50, 54, 28, 25, 46, 37, 30,
       43, 45, 12, 41, 48, 62, 16, 27, 32, 57,  2, 33, 42, 63, 11]), 'cur_cost': 97098.0}, {'tour': [0, 60, 61, 1, 2, 62, 63, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 64, 65], 'cur_cost': 15459.0}]
2025-07-07 16:48:18,988 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:18,988 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-07 16:48:18,988 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-07 16:48:18,988 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-07 16:48:18,988 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:18,988 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:18,990 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108956.0
2025-07-07 16:48:19,492 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:19,493 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858, 9521, 9521, 9521, 9521]
2025-07-07 16:48:19,493 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-07 16:48:19,506 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:19,506 - ExploitationExpert - INFO - populations: [{'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}, {'tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}, {'tour': array([36, 39, 44, 57, 27, 26,  7, 53, 47, 17, 45, 63, 10,  2, 49, 18, 24,
       31, 61, 55, 21, 32,  3, 15, 35, 30, 43, 38,  4, 64,  5, 19, 14, 11,
       34,  9, 42,  6, 41, 22, 56,  0, 48, 52, 50, 51, 37, 62, 60,  1, 29,
       33, 46, 58, 40, 28, 13,  8, 54, 20, 12, 65, 25, 16, 23, 59]), 'cur_cost': 104133.0}, {'tour': [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56], 'cur_cost': 32108.0}, {'tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}, {'tour': array([59, 64, 34, 26, 30, 18, 44, 31, 20,  3, 61,  4, 33, 22, 12, 43, 58,
       24, 32, 56, 54, 35, 60, 52, 13,  8, 41, 45, 38, 46,  2, 21, 57, 23,
        5, 19, 62, 42, 39,  0, 36, 15, 55, 50,  9, 65,  7, 27, 14, 63, 37,
       47, 48, 28, 29, 17,  1, 11, 53, 10, 16, 25, 51,  6, 49, 40]), 'cur_cost': 97116.0}, {'tour': [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55], 'cur_cost': 116941.0}, {'tour': array([59,  0, 40,  7, 39,  1, 64, 13, 56, 60, 51, 35, 34, 29, 26, 20, 38,
       23,  6, 47,  9, 53, 14, 31, 15, 44, 18, 58, 36,  5, 19, 24, 22, 21,
       17,  3,  4, 55, 65,  8, 61, 52, 49, 10, 50, 54, 28, 25, 46, 37, 30,
       43, 45, 12, 41, 48, 62, 16, 27, 32, 57,  2, 33, 42, 63, 11]), 'cur_cost': 97098.0}, {'tour': array([48, 53, 46, 59, 19, 17, 51, 37, 31, 32, 35, 41,  2, 28, 54, 62, 27,
       56, 12, 25, 13,  8,  7, 50, 11, 20, 14,  6,  1, 29, 16,  4, 21, 24,
       18,  0, 55, 22, 52, 65, 33, 63,  9, 45,  3, 36, 60, 34, 64, 40, 43,
       39, 44, 30, 47, 15, 23, 61, 42, 38,  5, 49, 57, 58, 26, 10]), 'cur_cost': 108956.0}]
2025-07-07 16:48:19,508 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:19,508 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-07 16:48:19,509 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-07 16:48:19,509 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 43, 12, 32, 54, 18, 6, 21, 48, 5, 2, 15, 28, 60, 39, 1, 4, 19, 25, 51, 63, 58, 10, 31, 37, 45, 27, 56, 41, 11, 3, 16, 22, 29, 50, 65, 61, 9, 17, 24, 30, 34, 53, 64, 57, 7, 20, 26, 33, 38, 46, 55, 62, 8, 23, 36, 44, 52, 40, 49, 13, 35, 42, 14, 47, 59], 'cur_cost': 95743.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 43, 12, 58, 21, 3, 1, 6, 9, 15, 23, 34, 45, 56, 62, 4, 17, 28, 39, 50, 60, 65, 64, 5, 18, 29, 40, 51, 61, 2, 7, 10, 16, 24, 35, 46, 57, 63, 8, 11, 19, 30, 41, 52, 59, 20, 25, 36, 47, 53, 13, 14, 22, 26, 37, 48, 54, 27, 38, 49, 55, 31, 32, 33, 42, 44], 'cur_cost': 82826.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 43, 12, 55, 32, 60, 1, 2, 4, 6, 8, 10, 15, 20, 22, 25, 28, 30, 35, 38, 40, 42, 45, 48, 50, 52, 58, 62, 65, 64, 63, 61, 59, 57, 54, 53, 51, 49, 47, 46, 44, 41, 39, 37, 36, 34, 33, 31, 29, 27, 26, 24, 23, 21, 19, 18, 17, 16, 14, 13, 11, 9, 7, 5, 3, 56], 'cur_cost': 32108.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55], 'cur_cost': 116941.0}}]
2025-07-07 16:48:19,509 - __main__ - INFO - 进化阶段完成
2025-07-07 16:48:19,509 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:48:19,530 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 116941.0, 'mean': 75787.9, 'std': 38901.281692124234}, 'diversity': 0.9356902356902356, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:48:19,530 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-07 16:48:19,530 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-07 16:48:19,533 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-07 16:48:19,534 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 40 → 40
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.073 → 0.073 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-07 16:48:19,534 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:21,464 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "increase_mutation_rate_by_10%",
    "consider_decreasing_diversity_weight_to_0.05"
  ]
}
```
2025-07-07 16:48:21,464 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-07 16:48:21,464 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-07 16:48:21,464 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-07 16:48:21,464 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-07 16:48:21,464 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:48:21,482 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 116941.0, 'mean': 75787.9, 'std': 38901.281692124234}, 'diversity': 0.9356902356902356, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:48:21,482 - PathExpert - INFO - 开始路径结构分析
2025-07-07 16:48:21,488 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-07 16:48:21,488 - PathExpert - INFO - 路径结构分析完成
2025-07-07 16:48:21,489 - EliteExpert - INFO - 开始精英解分析
2025-07-07 16:48:21,516 - EliteExpert - INFO - 精英解分析完成
2025-07-07 16:48:21,518 - LandscapeExpert - INFO - 开始景观分析
2025-07-07 16:48:21,519 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-07 16:48:21,521 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 75787.9, max 116941.0, std 38901.281692124234
- diversity: 0.9356902356902356
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2636946386946386}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-07 16:48:21,521 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:25,608 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.936,
    "convergence": 0.000,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "description": "Long edge corridor potentially connecting regions",
      "edges": [
        [
          10,
          25
        ],
        [
          30,
          45
        ]
      ]
    },
    {
      "description": "Low-density regions with sparse connections",
      "nodes": [
        2,
        5
      ]
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density regions with potential for local improvements",
      "cells": [
        "NW",
        "C",
        "SE"
      ],
      "nodes": [
        10,
        12,
        13
      ]
    }
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation (e.g., 2-opt, swap) to diversify solutions and explore new regions.",
      "Random restart to escape local optima."
    ]
  }
}
```
2025-07-07 16:48:25,608 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-07 16:48:25,608 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.936, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Long edge corridor potentially connecting regions', 'edges': [[10, 25], [30, 45]]}, {'description': 'Low-density regions with sparse connections', 'nodes': [2, 5]}], 'opportunity_regions': [{'description': 'High-density regions with potential for local improvements', 'cells': ['NW', 'C', 'SE'], 'nodes': [10, 12, 13]}], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation (e.g., 2-opt, swap) to diversify solutions and explore new regions.', 'Random restart to escape local optima.']}}
2025-07-07 16:48:25,608 - __main__ - INFO - 开始策略分配阶段
2025-07-07 16:48:25,608 - StrategyExpert - INFO - 开始策略分配分析
2025-07-07 16:48:25,608 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.936
  • best_cost: 11479.0
  • mean_cost: 75787.9
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: early exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [{"description": "Long edge corridor potentially connecting regions", "edges": [[10, 25], [30, 45]]}, {"description": "Low-density regions with sparse connections", "nodes": [2, 5]}]
- Opportunity regions (# 1): [{"description": "High-density regions with potential for local improvements", "cells": ["NW", "C", "SE"], "nodes": [10, 12, 13]}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-07 16:48:25,608 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-07 16:48:25,612 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:28,387 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, a good balance is preferred. Prioritize exploration with the elite."
}
```
2025-07-07 16:48:28,389 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:28,389 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:28,389 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, a good balance is preferred. Prioritize exploration with the elite."
}
```
2025-07-07 16:48:28,389 - __main__ - INFO - 策略分配阶段完成
2025-07-07 16:48:28,389 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:28,389 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, a good balance is preferred. Prioritize exploration with the elite."
}
```
2025-07-07 16:48:28,391 - __main__ - INFO - 开始进化阶段
2025-07-07 16:48:28,391 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-07 16:48:28,391 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:28,392 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.7769230769230769
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:28,392 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:28,392 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:31,101 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2],
  "targeted_regions": "Sparse cells near nodes 2, 3, and 65.",
  "strategy_comment": "Prioritizes exploration of low-density areas while maintaining path diversity."
}
```
2025-07-07 16:48:31,101 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:31,102 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17796.0, 路径: [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4]
2025-07-07 16:48:31,102 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}
2025-07-07 16:48:31,102 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-07 16:48:31,102 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:31,102 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95743.0 (rank 40.0%)
- Population diversity: 0.8138461538461539
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:31,102 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:31,102 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:34,031 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Explored low-density regions around nodes 10, 20, and 60.",
  "strategy_comment": "Prioritizes low-density areas by strategically connecting nodes across the map."
}
```
2025-07-07 16:48:34,031 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:34,032 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25838.0, 路径: [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-07 16:48:34,032 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}
2025-07-07 16:48:34,032 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-07 16:48:34,032 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:34,033 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 82826.0 (rank 40.0%)
- Population diversity: 0.7738461538461539
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:34,033 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:34,033 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:36,617 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Exploits all nodes in a straightforward manner.",
  "strategy_comment": "Initial path focuses on visiting all nodes to understand the landscape."
}
```
2025-07-07 16:48:36,617 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:36,617 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-07 16:48:36,617 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-07 16:48:36,617 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-07 16:48:36,617 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:36,617 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104133.0 (rank 70.0%)
- Population diversity: 0.703076923076923
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:36,617 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:36,617 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:39,608 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Exploits opportunity regions in the center and periphery, avoiding known difficult areas.",
  "strategy_comment": "Prioritizes opportunity regions, balancing exploration with cost to diversify the path."
}
```
2025-07-07 16:48:39,608 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:39,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25860.0, 路径: [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-07 16:48:39,608 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}
2025-07-07 16:48:39,608 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-07 16:48:39,608 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:39,608 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 32108.0 (rank 50.0%)
- Population diversity: 0.6338461538461538
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:39,608 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:39,608 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:42,540 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63],
  "targeted_regions": "sparse cells near opportunity regions.",
  "strategy_comment": "Prioritized sparse areas adjacent to opportunity regions to increase exploration and path diversity."
}
```
2025-07-07 16:48:42,540 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:42,540 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36996.0, 路径: [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60]
2025-07-07 16:48:42,540 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}
2025-07-07 16:48:42,540 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-07 16:48:42,540 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:42,543 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6892307692307692
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:42,543 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:42,543 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:45,406 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3],
  "targeted_regions": "Opportunity regions and unexplored cells.",
  "strategy_comment": "Prioritized opportunity regions & sparse cells, avoiding difficult ones, focusing on early exploration to diversify."
}
```
2025-07-07 16:48:45,406 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:45,408 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3]
2025-07-07 16:48:45,408 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}
2025-07-07 16:48:45,408 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-07 16:48:45,408 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:45,409 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:45,409 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 109704.0
2025-07-07 16:48:45,912 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:45,913 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:48:45,913 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:48:45,926 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:45,926 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}, {'tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}, {'tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}, {'tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}, {'tour': array([32, 61, 16, 26, 11, 27, 50, 43, 54, 28, 63, 18, 25, 49, 38, 62, 40,
       64, 48, 21,  1, 20, 22, 12,  4,  9, 36, 24,  3, 19, 47, 17, 53, 44,
       23, 34, 55, 46, 14, 10, 15,  2, 13, 59, 39, 45, 56,  6, 65, 42, 58,
       60, 52,  8, 51, 35, 57, 41,  5, 31, 37, 30, 29,  7, 33,  0]), 'cur_cost': 109704.0}, {'tour': [0, 43, 12, 56, 32, 1, 5, 18, 22, 45, 61, 3, 11, 25, 38, 50, 15, 28, 41, 64, 7, 19, 35, 52, 2, 9, 23, 39, 59, 4, 14, 27, 40, 63, 8, 17, 34, 51, 6, 13, 26, 42, 65, 10, 20, 36, 53, 24, 31, 47, 60, 33, 48, 62, 16, 30, 46, 58, 21, 37, 54, 44, 57, 29, 49, 55], 'cur_cost': 116941.0}, {'tour': array([59,  0, 40,  7, 39,  1, 64, 13, 56, 60, 51, 35, 34, 29, 26, 20, 38,
       23,  6, 47,  9, 53, 14, 31, 15, 44, 18, 58, 36,  5, 19, 24, 22, 21,
       17,  3,  4, 55, 65,  8, 61, 52, 49, 10, 50, 54, 28, 25, 46, 37, 30,
       43, 45, 12, 41, 48, 62, 16, 27, 32, 57,  2, 33, 42, 63, 11]), 'cur_cost': 97098.0}, {'tour': array([48, 53, 46, 59, 19, 17, 51, 37, 31, 32, 35, 41,  2, 28, 54, 62, 27,
       56, 12, 25, 13,  8,  7, 50, 11, 20, 14,  6,  1, 29, 16,  4, 21, 24,
       18,  0, 55, 22, 52, 65, 33, 63,  9, 45,  3, 36, 60, 34, 64, 40, 43,
       39, 44, 30, 47, 15, 23, 61, 42, 38,  5, 49, 57, 58, 26, 10]), 'cur_cost': 108956.0}]
2025-07-07 16:48:45,927 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:45,928 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-07 16:48:45,928 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-07 16:48:45,929 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-07 16:48:45,929 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:45,929 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:45,930 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 89413.0
2025-07-07 16:48:46,433 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:46,434 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:48:46,434 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:48:46,446 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:46,447 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}, {'tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}, {'tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}, {'tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}, {'tour': array([32, 61, 16, 26, 11, 27, 50, 43, 54, 28, 63, 18, 25, 49, 38, 62, 40,
       64, 48, 21,  1, 20, 22, 12,  4,  9, 36, 24,  3, 19, 47, 17, 53, 44,
       23, 34, 55, 46, 14, 10, 15,  2, 13, 59, 39, 45, 56,  6, 65, 42, 58,
       60, 52,  8, 51, 35, 57, 41,  5, 31, 37, 30, 29,  7, 33,  0]), 'cur_cost': 109704.0}, {'tour': array([ 7,  3, 14, 58, 48,  0, 38, 34, 57, 61, 62, 51, 47, 55, 21,  9, 54,
       64, 13, 28, 24, 35, 23, 53,  1, 40, 41, 15, 22, 16, 52, 11, 12,  5,
       25, 63, 49, 30, 26, 29, 44, 32, 33, 45, 60, 18, 36, 31, 19, 20, 39,
       42, 43, 37,  2, 27, 50, 10, 59, 56, 46, 65,  4,  6,  8, 17]), 'cur_cost': 89413.0}, {'tour': array([59,  0, 40,  7, 39,  1, 64, 13, 56, 60, 51, 35, 34, 29, 26, 20, 38,
       23,  6, 47,  9, 53, 14, 31, 15, 44, 18, 58, 36,  5, 19, 24, 22, 21,
       17,  3,  4, 55, 65,  8, 61, 52, 49, 10, 50, 54, 28, 25, 46, 37, 30,
       43, 45, 12, 41, 48, 62, 16, 27, 32, 57,  2, 33, 42, 63, 11]), 'cur_cost': 97098.0}, {'tour': array([48, 53, 46, 59, 19, 17, 51, 37, 31, 32, 35, 41,  2, 28, 54, 62, 27,
       56, 12, 25, 13,  8,  7, 50, 11, 20, 14,  6,  1, 29, 16,  4, 21, 24,
       18,  0, 55, 22, 52, 65, 33, 63,  9, 45,  3, 36, 60, 34, 64, 40, 43,
       39, 44, 30, 47, 15, 23, 61, 42, 38,  5, 49, 57, 58, 26, 10]), 'cur_cost': 108956.0}]
2025-07-07 16:48:46,450 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:46,450 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-07 16:48:46,450 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-07 16:48:46,450 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-07 16:48:46,450 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:46,451 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:46,451 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 104358.0
2025-07-07 16:48:46,952 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:46,954 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:48:46,954 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:48:46,967 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:46,967 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}, {'tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}, {'tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}, {'tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}, {'tour': array([32, 61, 16, 26, 11, 27, 50, 43, 54, 28, 63, 18, 25, 49, 38, 62, 40,
       64, 48, 21,  1, 20, 22, 12,  4,  9, 36, 24,  3, 19, 47, 17, 53, 44,
       23, 34, 55, 46, 14, 10, 15,  2, 13, 59, 39, 45, 56,  6, 65, 42, 58,
       60, 52,  8, 51, 35, 57, 41,  5, 31, 37, 30, 29,  7, 33,  0]), 'cur_cost': 109704.0}, {'tour': array([ 7,  3, 14, 58, 48,  0, 38, 34, 57, 61, 62, 51, 47, 55, 21,  9, 54,
       64, 13, 28, 24, 35, 23, 53,  1, 40, 41, 15, 22, 16, 52, 11, 12,  5,
       25, 63, 49, 30, 26, 29, 44, 32, 33, 45, 60, 18, 36, 31, 19, 20, 39,
       42, 43, 37,  2, 27, 50, 10, 59, 56, 46, 65,  4,  6,  8, 17]), 'cur_cost': 89413.0}, {'tour': array([38, 11, 10, 28, 17, 12, 52, 57, 39, 24, 43, 50, 49, 15,  6, 46,  7,
       30, 51, 55, 63, 29, 18, 23, 27, 33, 47, 60, 59, 62, 42, 16, 65, 64,
       21, 13,  5, 34,  2, 54, 19, 53, 48,  9, 36, 56, 58, 41, 22, 14, 26,
       45, 31, 61, 25,  0, 32,  4,  8,  1, 40, 35, 44, 20, 37,  3]), 'cur_cost': 104358.0}, {'tour': array([48, 53, 46, 59, 19, 17, 51, 37, 31, 32, 35, 41,  2, 28, 54, 62, 27,
       56, 12, 25, 13,  8,  7, 50, 11, 20, 14,  6,  1, 29, 16,  4, 21, 24,
       18,  0, 55, 22, 52, 65, 33, 63,  9, 45,  3, 36, 60, 34, 64, 40, 43,
       39, 44, 30, 47, 15, 23, 61, 42, 38,  5, 49, 57, 58, 26, 10]), 'cur_cost': 108956.0}]
2025-07-07 16:48:46,970 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:46,970 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-07 16:48:46,970 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-07 16:48:46,970 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-07 16:48:46,971 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:48:46,971 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:48:46,972 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 94104.0
2025-07-07 16:48:47,475 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:48:47,476 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:48:47,476 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:48:47,489 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:48:47,489 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}, {'tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}, {'tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}, {'tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}, {'tour': array([32, 61, 16, 26, 11, 27, 50, 43, 54, 28, 63, 18, 25, 49, 38, 62, 40,
       64, 48, 21,  1, 20, 22, 12,  4,  9, 36, 24,  3, 19, 47, 17, 53, 44,
       23, 34, 55, 46, 14, 10, 15,  2, 13, 59, 39, 45, 56,  6, 65, 42, 58,
       60, 52,  8, 51, 35, 57, 41,  5, 31, 37, 30, 29,  7, 33,  0]), 'cur_cost': 109704.0}, {'tour': array([ 7,  3, 14, 58, 48,  0, 38, 34, 57, 61, 62, 51, 47, 55, 21,  9, 54,
       64, 13, 28, 24, 35, 23, 53,  1, 40, 41, 15, 22, 16, 52, 11, 12,  5,
       25, 63, 49, 30, 26, 29, 44, 32, 33, 45, 60, 18, 36, 31, 19, 20, 39,
       42, 43, 37,  2, 27, 50, 10, 59, 56, 46, 65,  4,  6,  8, 17]), 'cur_cost': 89413.0}, {'tour': array([38, 11, 10, 28, 17, 12, 52, 57, 39, 24, 43, 50, 49, 15,  6, 46,  7,
       30, 51, 55, 63, 29, 18, 23, 27, 33, 47, 60, 59, 62, 42, 16, 65, 64,
       21, 13,  5, 34,  2, 54, 19, 53, 48,  9, 36, 56, 58, 41, 22, 14, 26,
       45, 31, 61, 25,  0, 32,  4,  8,  1, 40, 35, 44, 20, 37,  3]), 'cur_cost': 104358.0}, {'tour': array([53, 11,  6, 38, 44, 54, 56,  3, 13, 17, 22, 18, 58,  7, 10, 61, 48,
       65, 36, 47, 28, 25, 31, 24, 16,  4, 46, 57, 40, 33, 37, 12, 27, 41,
       23, 51, 15,  5, 32,  8, 39, 50, 19, 62, 64,  2, 59,  0, 55, 29, 60,
       35, 30, 34, 14, 43, 63,  9, 26, 21, 20, 52, 49, 45,  1, 42]), 'cur_cost': 94104.0}]
2025-07-07 16:48:47,491 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:48:47,492 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-07 16:48:47,492 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-07 16:48:47,492 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 2, 4], 'cur_cost': 17796.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [10, 5, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 0, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25838.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 15, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 5, 1, 2, 3, 4, 6, 7, 8, 9, 12, 13, 14, 17, 18, 19, 20, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25860.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 23, 45, 12, 34, 56, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 2, 5, 8, 11, 14, 17, 20, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 59, 62, 65, 3, 6, 9, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 48, 51, 54, 63, 57, 60], 'cur_cost': 36996.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3], 'cur_cost': 11479.0}}]
2025-07-07 16:48:47,492 - __main__ - INFO - 进化阶段完成
2025-07-07 16:48:47,492 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:48:47,511 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109704.0, 'mean': 52702.7, 'std': 39096.************}, 'diversity': 0.8855218855218854, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 2, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:48:47,511 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-07 16:48:47,511 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-07 16:48:47,513 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-07 16:48:47,514 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 40 → 40
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.073 → 0.073 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-07 16:48:47,514 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:49,281 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": [
    "Increase explore_ratio. The explore strategy is currently not being used or not succeeding.  This suggests the search is stuck in a local optimum.",
    "Increase diversity weight to encourage exploration of new areas of the search space. The diversity metric is not changing and elite diversity is low.",
    "Consider re-evaluating the fitness function to ensure it correctly guides the search and provides sufficient gradients."
  ]
}
```
2025-07-07 16:48:49,282 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-07 16:48:49,282 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-07 16:48:49,282 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-07 16:48:49,282 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-07 16:48:49,282 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:48:49,300 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109704.0, 'mean': 52702.7, 'std': 39096.************}, 'diversity': 0.8855218855218854, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 2, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:48:49,300 - PathExpert - INFO - 开始路径结构分析
2025-07-07 16:48:49,307 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-07 16:48:49,307 - PathExpert - INFO - 路径结构分析完成
2025-07-07 16:48:49,307 - EliteExpert - INFO - 开始精英解分析
2025-07-07 16:48:49,333 - EliteExpert - INFO - 精英解分析完成
2025-07-07 16:48:49,335 - LandscapeExpert - INFO - 开始景观分析
2025-07-07 16:48:49,338 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-07 16:48:49,338 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 52702.7, max 109704.0, std 39096.************
- diversity: 0.8855218855218854
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2636946386946386}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-07 16:48:49,338 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:52,352 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.886,
    "convergence": 0.000,
    "clustering": "unknown"
  },
  "difficult_regions": [
    [
      "bounding_box",
      [
        3000.0,
        2300.0,
        3300.0,
        2400.0
      ]
    ],
    [
      "long_edge_corridor",
      [
        2000.0,
        1000.0,
        3000.0,
        1200.0
      ]
    ]
  ],
  "opportunity_regions": [
    [
      "density_grid_cell",
      [
        2,
        2
      ]
    ],
    [
      "density_grid_cell",
      [
        0,
        0
      ]
    ]
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Mutation operators (e.g., 2-opt, 3-opt) to explore different solution structures.",
      "Diversity-preserving mechanisms (e.g., niching, crowding) to prevent premature convergence.",
      "Edge recombination operators to combine promising sub-paths."
    ]
  }
}
```
2025-07-07 16:48:52,352 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-07 16:48:52,352 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.886, 'convergence': 0.0, 'clustering': 'unknown'}, 'difficult_regions': [['bounding_box', [3000.0, 2300.0, 3300.0, 2400.0]], ['long_edge_corridor', [2000.0, 1000.0, 3000.0, 1200.0]]], 'opportunity_regions': [['density_grid_cell', [2, 2]], ['density_grid_cell', [0, 0]]], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Mutation operators (e.g., 2-opt, 3-opt) to explore different solution structures.', 'Diversity-preserving mechanisms (e.g., niching, crowding) to prevent premature convergence.', 'Edge recombination operators to combine promising sub-paths.']}}
2025-07-07 16:48:52,352 - __main__ - INFO - 开始策略分配阶段
2025-07-07 16:48:52,352 - StrategyExpert - INFO - 开始策略分配分析
2025-07-07 16:48:52,352 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.886
  • best_cost: 11479.0
  • mean_cost: 52702.7
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: early exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 2): [["bounding_box", [3000.0, 2300.0, 3300.0, 2400.0]], ["long_edge_corridor", [2000.0, 1000.0, 3000.0, 1200.0]]]
- Opportunity regions (# 2): [["density_grid_cell", [2, 2]], ["density_grid_cell", [0, 0]]]
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-07 16:48:52,352 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-07 16:48:52,352 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:54,230 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with medium ruggedness. Density grids suggest some exploitation within specific areas, but overall bias toward exploration."
}
```
2025-07-07 16:48:54,230 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:54,230 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:54,230 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with medium ruggedness. Density grids suggest some exploitation within specific areas, but overall bias toward exploration."
}
```
2025-07-07 16:48:54,231 - __main__ - INFO - 策略分配阶段完成
2025-07-07 16:48:54,231 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-07 16:48:54,231 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with medium ruggedness. Density grids suggest some exploitation within specific areas, but overall bias toward exploration."
}
```
2025-07-07 16:48:54,231 - __main__ - INFO - 开始进化阶段
2025-07-07 16:48:54,231 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-07 16:48:54,231 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:54,231 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17796.0 (rank 20.0%)
- Population diversity: 0.6892307692307692
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:54,231 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:54,231 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:48:57,693 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Sparse cells adjacent to opportunity regions.",
  "strategy_comment": "Explores low-density areas connected to identified opportunities for diversification and cost optimization."
}
```
2025-07-07 16:48:57,693 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:48:57,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25989.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-07 16:48:57,693 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}
2025-07-07 16:48:57,693 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-07 16:48:57,693 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:48:57,696 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25838.0 (rank 20.0%)
- Population diversity: 0.6061538461538462
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:48:57,696 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:48:57,696 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:01,346 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Exploits opportunity regions using a serpentine path.",
  "strategy_comment": "Explored low-density areas to increase diversity, avoiding difficult regions."
}
```
2025-07-07 16:49:01,346 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:49:01,346 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25827.0, 路径: [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-07 16:49:01,346 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}
2025-07-07 16:49:01,346 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-07 16:49:01,346 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:49:01,346 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6092307692307692
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:49:01,346 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:49:01,346 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:03,978 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
    20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37,
    38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55,
    56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "targeted_regions": "Explores the density grid and avoids bounding box regions.",
  "strategy_comment": "Prioritizes exploration of opportunity regions in a zig-zag pattern."
}
```
2025-07-07 16:49:03,979 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:49:03,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-07 16:49:03,980 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-07 16:49:03,980 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-07 16:49:03,980 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:49:03,981 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25860.0 (rank 30.0%)
- Population diversity: 0.6092307692307692
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:49:03,981 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:49:03,981 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:06,410 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "density_grid_",
  "strategy_comment": "Initial path focusing on visiting all nodes to assess unexplored regions and maximize diversity."
}
```
2025-07-07 16:49:06,411 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:49:06,411 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-07 16:49:06,411 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-07 16:49:06,411 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-07 16:49:06,411 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:49:06,411 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 36996.0 (rank 50.0%)
- Population diversity: 0.6030769230769231
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:49:06,411 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:49:06,411 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:10,430 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64],
  "targeted_regions": "Opportunity regions and sparse cells exploration via zig-zag pattern.",
  "strategy_comment": "Zig-zag path designed to explore potential regions while avoiding difficult areas and increasing diversity."
}
```
2025-07-07 16:49:10,430 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:49:10,431 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40314.0, 路径: [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64]
2025-07-07 16:49:10,431 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}
2025-07-07 16:49:10,431 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-07 16:49:10,431 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:49:10,432 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:49:10,432 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 103873.0
2025-07-07 16:49:10,937 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:49:10,937 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:49:10,937 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:49:10,953 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:49:10,953 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}, {'tour': array([40, 46, 39, 25, 11, 52, 34, 47, 48,  5, 18, 59, 26, 56,  2, 60, 37,
        6, 35, 22, 42, 55, 24, 49, 21, 13,  1, 33,  3, 38,  0, 36, 17, 16,
       29, 28, 43, 58, 51, 53, 62, 54, 64, 14, 27, 32, 45, 41, 10, 57,  9,
        4, 50, 31, 61, 19, 12, 44, 63, 30, 65, 15, 20,  7,  8, 23]), 'cur_cost': 103873.0}, {'tour': array([32, 61, 16, 26, 11, 27, 50, 43, 54, 28, 63, 18, 25, 49, 38, 62, 40,
       64, 48, 21,  1, 20, 22, 12,  4,  9, 36, 24,  3, 19, 47, 17, 53, 44,
       23, 34, 55, 46, 14, 10, 15,  2, 13, 59, 39, 45, 56,  6, 65, 42, 58,
       60, 52,  8, 51, 35, 57, 41,  5, 31, 37, 30, 29,  7, 33,  0]), 'cur_cost': 109704.0}, {'tour': array([ 7,  3, 14, 58, 48,  0, 38, 34, 57, 61, 62, 51, 47, 55, 21,  9, 54,
       64, 13, 28, 24, 35, 23, 53,  1, 40, 41, 15, 22, 16, 52, 11, 12,  5,
       25, 63, 49, 30, 26, 29, 44, 32, 33, 45, 60, 18, 36, 31, 19, 20, 39,
       42, 43, 37,  2, 27, 50, 10, 59, 56, 46, 65,  4,  6,  8, 17]), 'cur_cost': 89413.0}, {'tour': array([38, 11, 10, 28, 17, 12, 52, 57, 39, 24, 43, 50, 49, 15,  6, 46,  7,
       30, 51, 55, 63, 29, 18, 23, 27, 33, 47, 60, 59, 62, 42, 16, 65, 64,
       21, 13,  5, 34,  2, 54, 19, 53, 48,  9, 36, 56, 58, 41, 22, 14, 26,
       45, 31, 61, 25,  0, 32,  4,  8,  1, 40, 35, 44, 20, 37,  3]), 'cur_cost': 104358.0}, {'tour': array([53, 11,  6, 38, 44, 54, 56,  3, 13, 17, 22, 18, 58,  7, 10, 61, 48,
       65, 36, 47, 28, 25, 31, 24, 16,  4, 46, 57, 40, 33, 37, 12, 27, 41,
       23, 51, 15,  5, 32,  8, 39, 50, 19, 62, 64,  2, 59,  0, 55, 29, 60,
       35, 30, 34, 14, 43, 63,  9, 26, 21, 20, 52, 49, 45,  1, 42]), 'cur_cost': 94104.0}]
2025-07-07 16:49:10,955 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:49:10,956 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-07 16:49:10,956 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-07 16:49:10,956 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-07 16:49:10,956 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-07 16:49:10,956 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109704.0 (rank 90.0%)
- Population diversity: 0.6523076923076923
- Evolution phase: early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): ['density_grid_, ['density_grid_
- Difficult regions to avoid (sample): ['bounding_box', ['long_edge_cor
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-07 16:49:10,957 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-07 16:49:10,957 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:16,120 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells adjacent to opportunity regions, ensuring full coverage.",
  "strategy_comment": "Prioritizing complete graph traversal and maximizing exploration, focusing on untouched areas."
}
```
2025-07-07 16:49:16,120 - ExplorationExpert - INFO - 计算路径成本
2025-07-07 16:49:16,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11395.0, 路径: [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-07 16:49:16,120 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11395.0}
2025-07-07 16:49:16,120 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-07 16:49:16,120 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:49:16,120 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:49:16,124 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107981.0
2025-07-07 16:49:16,626 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:49:16,626 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:49:16,626 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:49:16,640 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:49:16,641 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}, {'tour': array([40, 46, 39, 25, 11, 52, 34, 47, 48,  5, 18, 59, 26, 56,  2, 60, 37,
        6, 35, 22, 42, 55, 24, 49, 21, 13,  1, 33,  3, 38,  0, 36, 17, 16,
       29, 28, 43, 58, 51, 53, 62, 54, 64, 14, 27, 32, 45, 41, 10, 57,  9,
        4, 50, 31, 61, 19, 12, 44, 63, 30, 65, 15, 20,  7,  8, 23]), 'cur_cost': 103873.0}, {'tour': [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11395.0}, {'tour': array([ 0, 52,  3, 14,  1, 35,  8, 33,  7, 58, 49, 20, 57, 61, 15, 45, 16,
       34,  6, 30, 26,  9, 63, 64, 13, 56, 43, 21, 19, 17, 54, 53,  2, 60,
       31, 10, 62, 23, 22, 65, 37, 47, 28, 44, 27,  5, 50, 36, 18, 38, 11,
       55, 51, 24, 40, 32, 42, 46, 29, 59, 39,  4, 48, 41, 25, 12]), 'cur_cost': 107981.0}, {'tour': array([38, 11, 10, 28, 17, 12, 52, 57, 39, 24, 43, 50, 49, 15,  6, 46,  7,
       30, 51, 55, 63, 29, 18, 23, 27, 33, 47, 60, 59, 62, 42, 16, 65, 64,
       21, 13,  5, 34,  2, 54, 19, 53, 48,  9, 36, 56, 58, 41, 22, 14, 26,
       45, 31, 61, 25,  0, 32,  4,  8,  1, 40, 35, 44, 20, 37,  3]), 'cur_cost': 104358.0}, {'tour': array([53, 11,  6, 38, 44, 54, 56,  3, 13, 17, 22, 18, 58,  7, 10, 61, 48,
       65, 36, 47, 28, 25, 31, 24, 16,  4, 46, 57, 40, 33, 37, 12, 27, 41,
       23, 51, 15,  5, 32,  8, 39, 50, 19, 62, 64,  2, 59,  0, 55, 29, 60,
       35, 30, 34, 14, 43, 63,  9, 26, 21, 20, 52, 49, 45,  1, 42]), 'cur_cost': 94104.0}]
2025-07-07 16:49:16,643 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:49:16,644 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-07 16:49:16,644 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-07 16:49:16,644 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-07 16:49:16,644 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:49:16,645 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:49:16,645 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102452.0
2025-07-07 16:49:17,148 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:49:17,148 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:49:17,148 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:49:17,163 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:49:17,163 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}, {'tour': array([40, 46, 39, 25, 11, 52, 34, 47, 48,  5, 18, 59, 26, 56,  2, 60, 37,
        6, 35, 22, 42, 55, 24, 49, 21, 13,  1, 33,  3, 38,  0, 36, 17, 16,
       29, 28, 43, 58, 51, 53, 62, 54, 64, 14, 27, 32, 45, 41, 10, 57,  9,
        4, 50, 31, 61, 19, 12, 44, 63, 30, 65, 15, 20,  7,  8, 23]), 'cur_cost': 103873.0}, {'tour': [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11395.0}, {'tour': array([ 0, 52,  3, 14,  1, 35,  8, 33,  7, 58, 49, 20, 57, 61, 15, 45, 16,
       34,  6, 30, 26,  9, 63, 64, 13, 56, 43, 21, 19, 17, 54, 53,  2, 60,
       31, 10, 62, 23, 22, 65, 37, 47, 28, 44, 27,  5, 50, 36, 18, 38, 11,
       55, 51, 24, 40, 32, 42, 46, 29, 59, 39,  4, 48, 41, 25, 12]), 'cur_cost': 107981.0}, {'tour': array([33, 28,  4, 36, 59, 32,  2, 57, 20, 30, 62, 50, 42, 26, 14, 53, 23,
        0, 54, 58, 52, 34, 65, 60,  5, 49, 19, 47, 29, 18, 51, 12, 40, 15,
       61, 39, 21, 48, 13, 43, 35, 27, 37, 44, 45, 46,  3, 63, 31, 11, 24,
        8, 56,  9, 64,  1, 41, 25, 22, 10, 17,  7, 38, 55,  6, 16]), 'cur_cost': 102452.0}, {'tour': array([53, 11,  6, 38, 44, 54, 56,  3, 13, 17, 22, 18, 58,  7, 10, 61, 48,
       65, 36, 47, 28, 25, 31, 24, 16,  4, 46, 57, 40, 33, 37, 12, 27, 41,
       23, 51, 15,  5, 32,  8, 39, 50, 19, 62, 64,  2, 59,  0, 55, 29, 60,
       35, 30, 34, 14, 43, 63,  9, 26, 21, 20, 52, 49, 45,  1, 42]), 'cur_cost': 94104.0}]
2025-07-07 16:49:17,163 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:49:17,166 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-07-07 16:49:17,166 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-07 16:49:17,166 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-07 16:49:17,167 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-07 16:49:17,167 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-07 16:49:17,167 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 116261.0
2025-07-07 16:49:17,670 - ExploitationExpert - INFO - res_population_num: 40
2025-07-07 16:49:17,670 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9525, 9526, 9527, 9527, 9534, 9541, 9544, 9545, 9560, 9560, 9572, 9578, 9592, 84858]
2025-07-07 16:49:17,670 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 16, 18, 23, 13, 21, 20,
       14, 15, 22, 12, 17,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43,
       21, 20, 13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 15, 14, 20, 21, 13, 19,
       16, 23, 22, 12, 17, 18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 17, 12, 22, 23,
       13, 20, 21, 19, 16, 18, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4, 14, 15, 22, 12, 17, 18, 16, 23, 13,
       20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34,
       43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 56,
       59, 62, 53, 64, 57, 54, 52, 63, 65, 61, 55,  6,  2,  8, 10],
      dtype=int64), array([ 0, 10,  8,  2,  6, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59,
       56, 58, 41, 38, 51, 50, 45, 44, 39, 47, 46, 48, 49, 40, 43, 42, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13,
       23, 16, 18, 17, 12, 22, 15, 14,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 36, 37, 31, 24, 29, 32, 33, 25, 26, 35, 28, 30,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 56, 58, 60, 62, 59, 53, 64,
       57, 54, 52, 63, 65, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 55, 61, 53, 62, 59,
       60, 54, 57, 64, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 63, 50, 46,  6,  5, 32, 30,  7, 35, 33, 28, 47, 18, 42, 23, 58,
       38, 44, 60, 52, 64, 39, 12, 22, 65, 57, 51, 40, 15, 10, 37, 36, 20,
       11, 55, 25, 27, 21, 61, 26, 41, 31, 29, 45, 16, 24, 54, 53, 59, 62,
       56, 14,  4, 43, 34,  2, 17, 19,  8, 48, 49,  3, 13,  9,  1],
      dtype=int64)]
2025-07-07 16:49:17,686 - ExploitationExpert - INFO - populations_num: 10
2025-07-07 16:49:17,686 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}, {'tour': array([40, 46, 39, 25, 11, 52, 34, 47, 48,  5, 18, 59, 26, 56,  2, 60, 37,
        6, 35, 22, 42, 55, 24, 49, 21, 13,  1, 33,  3, 38,  0, 36, 17, 16,
       29, 28, 43, 58, 51, 53, 62, 54, 64, 14, 27, 32, 45, 41, 10, 57,  9,
        4, 50, 31, 61, 19, 12, 44, 63, 30, 65, 15, 20,  7,  8, 23]), 'cur_cost': 103873.0}, {'tour': [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11395.0}, {'tour': array([ 0, 52,  3, 14,  1, 35,  8, 33,  7, 58, 49, 20, 57, 61, 15, 45, 16,
       34,  6, 30, 26,  9, 63, 64, 13, 56, 43, 21, 19, 17, 54, 53,  2, 60,
       31, 10, 62, 23, 22, 65, 37, 47, 28, 44, 27,  5, 50, 36, 18, 38, 11,
       55, 51, 24, 40, 32, 42, 46, 29, 59, 39,  4, 48, 41, 25, 12]), 'cur_cost': 107981.0}, {'tour': array([33, 28,  4, 36, 59, 32,  2, 57, 20, 30, 62, 50, 42, 26, 14, 53, 23,
        0, 54, 58, 52, 34, 65, 60,  5, 49, 19, 47, 29, 18, 51, 12, 40, 15,
       61, 39, 21, 48, 13, 43, 35, 27, 37, 44, 45, 46,  3, 63, 31, 11, 24,
        8, 56,  9, 64,  1, 41, 25, 22, 10, 17,  7, 38, 55,  6, 16]), 'cur_cost': 102452.0}, {'tour': array([25, 58, 30, 50,  7, 44, 15, 33, 48, 36, 54, 17,  3, 32, 39, 31, 51,
       35, 43, 28,  6, 38, 29, 46,  2, 11, 45, 26, 22, 52, 57, 12, 41, 34,
       18, 65,  0, 13, 23,  1, 24, 55, 37, 14,  9, 27,  8, 40,  4, 19, 49,
       10, 47, 20, 64, 62, 56, 63, 16, 59,  5, 42, 21, 53, 60, 61]), 'cur_cost': 116261.0}]
2025-07-07 16:49:17,688 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-07 16:49:17,688 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-07 16:49:17,688 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-07 16:49:17,688 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25827.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 5, 0, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64], 'cur_cost': 40314.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11395.0}}]
2025-07-07 16:49:17,690 - __main__ - INFO - 进化阶段完成
2025-07-07 16:49:17,690 - StatsExpert - INFO - 开始统计分析
2025-07-07 16:49:17,707 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11395.0, 'max': 116261.0, 'mean': 55705.0, 'std': 43351.03253441606}, 'diversity': 0.8107744107744106, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-07 16:49:17,708 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-07 16:49:17,708 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-07 16:49:17,710 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-07 16:49:17,710 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 40 → 40
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.073 → 0.073 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-07 16:49:17,712 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-07 16:49:19,269 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_mutation_rate_increase"]
}
```
2025-07-07 16:49:19,270 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-07 16:49:19,270 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-07 16:49:19,286 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-07 16:49:19,286 - __main__ - INFO - 实例 composite13_66 处理完成
