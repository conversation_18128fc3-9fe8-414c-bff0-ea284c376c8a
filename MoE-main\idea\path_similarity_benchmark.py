# 路径相似度优化效率测试脚本
# 比较优化前后的路径相似度计算效率

import numpy as np
import time
import random
import matplotlib.pyplot as plt
import os

# 导入原始相似度计算函数
from gls_evol_enhanced import share_distance, calculate_path_similarity

# 导入优化后的路径相似度优化器
from path_similarity_optimizer import PathSimilarityOptimizer


def generate_random_path(size):
    """生成随机路径
    
    参数:
        size: 路径长度
        
    返回:
        list: 随机路径
    """
    path = list(range(size))
    random.shuffle(path)
    return path


def generate_similar_path(path, change_rate=0.2):
    """生成与给定路径相似的路径
    
    参数:
        path: 原始路径
        change_rate: 变化率，表示要交换的节点对数量占路径长度的比例
        
    返回:
        list: 相似路径
    """
    similar_path = path.copy()
    size = len(path)
    changes = max(1, int(size * change_rate))
    
    for _ in range(changes):
        i, j = random.sample(range(size), 2)
        similar_path[i], similar_path[j] = similar_path[j], similar_path[i]
    
    return similar_path


def benchmark_original_method(paths, num_comparisons):
    """测试原始方法的性能
    
    参数:
        paths: 路径列表
        num_comparisons: 比较次数
        
    返回:
        float: 平均执行时间（毫秒）
    """
    start_time = time.time()
    
    for _ in range(num_comparisons):
        i, j = random.sample(range(len(paths)), 2)
        similarity = calculate_path_similarity(paths[i], paths[j])
    
    end_time = time.time()
    avg_time = (end_time - start_time) * 1000 / num_comparisons  # 转换为毫秒
    
    return avg_time


def benchmark_optimized_method(paths, num_comparisons):
    """测试优化方法的性能
    
    参数:
        paths: 路径列表
        num_comparisons: 比较次数
        
    返回:
        float: 平均执行时间（毫秒）
    """
    # 创建路径相似度优化器
    optimizer = PathSimilarityOptimizer()
    
    # 添加所有路径
    for path in paths:
        optimizer.add_path(path)
    
    start_time = time.time()
    
    for _ in range(num_comparisons):
        i, j = random.sample(range(len(paths)), 2)
        similarity = optimizer.calculate_similarity(paths[i], paths[j])
    
    end_time = time.time()
    avg_time = (end_time - start_time) * 1000 / num_comparisons  # 转换为毫秒
    
    return avg_time


def benchmark_path_lookup(paths, similar_paths, original_threshold=0.7):
    """测试路径查找性能
    
    参数:
        paths: 存储的路径列表
        similar_paths: 待查找的相似路径列表
        original_threshold: 原始相似度阈值
        
    返回:
        tuple: (原始方法时间, 优化方法时间)，单位为毫秒
    """
    # 测试原始方法
    start_time = time.time()
    
    for test_path in similar_paths:
        for stored_path in paths:
            similarity = calculate_path_similarity(test_path, stored_path)
            if similarity >= original_threshold:
                break
    
    original_time = (time.time() - start_time) * 1000  # 转换为毫秒
    
    # 测试优化方法
    optimizer = PathSimilarityOptimizer(similarity_threshold=original_threshold)
    
    # 添加所有路径
    for path in paths:
        optimizer.add_path(path)
    
    start_time = time.time()
    
    for test_path in similar_paths:
        is_similar, _, _ = optimizer.check_similarity(test_path)
    
    optimized_time = (time.time() - start_time) * 1000  # 转换为毫秒
    
    return original_time, optimized_time


def run_benchmarks():
    """运行所有基准测试"""
    print("开始路径相似度计算效率测试...")
    
    # 测试参数
    path_sizes = [50, 100, 200, 500, 1000]  # 路径长度
    num_paths = 100  # 每个大小的路径数量
    num_comparisons = 1000  # 比较次数
    
    # 存储结果
    original_times = []
    optimized_times = []
    
    # 路径查找测试结果
    lookup_sizes = [10, 50, 100, 200, 500]  # 存储的路径数量
    original_lookup_times = []
    optimized_lookup_times = []
    
    # 测试不同路径长度的计算效率
    for size in path_sizes:
        print(f"\n测试路径长度: {size}")
        
        # 生成随机路径
        paths = [generate_random_path(size) for _ in range(num_paths)]
        
        # 测试原始方法
        original_time = benchmark_original_method(paths, num_comparisons)
        original_times.append(original_time)
        print(f"原始方法平均执行时间: {original_time:.4f} 毫秒")
        
        # 测试优化方法
        optimized_time = benchmark_optimized_method(paths, num_comparisons)
        optimized_times.append(optimized_time)
        print(f"优化方法平均执行时间: {optimized_time:.4f} 毫秒")
        
        # 计算加速比
        speedup = original_time / optimized_time
        print(f"加速比: {speedup:.2f}x")
    
    # 测试路径查找效率
    test_size = 100  # 固定路径长度为100
    num_similar_paths = 50  # 待查找的相似路径数量
    
    print("\n\n测试路径查找效率...")
    for num_stored_paths in lookup_sizes:
        print(f"\n存储路径数量: {num_stored_paths}")
        
        # 生成存储路径
        stored_paths = [generate_random_path(test_size) for _ in range(num_stored_paths)]
        
        # 生成相似路径（确保有一些路径是相似的）
        similar_paths = []
        for _ in range(num_similar_paths):
            if random.random() < 0.3:  # 30%的概率生成相似路径
                original = random.choice(stored_paths)
                similar_paths.append(generate_similar_path(original, 0.1))  # 低变化率，确保相似
            else:
                similar_paths.append(generate_random_path(test_size))
        
        # 测试路径查找性能
        original_time, optimized_time = benchmark_path_lookup(stored_paths, similar_paths)
        original_lookup_times.append(original_time)
        optimized_lookup_times.append(optimized_time)
        
        print(f"原始方法查找时间: {original_time:.4f} 毫秒")
        print(f"优化方法查找时间: {optimized_time:.4f} 毫秒")
        print(f"加速比: {original_time / optimized_time:.2f}x")
    
    # 绘制结果图表
    plot_results(path_sizes, original_times, optimized_times, 
                lookup_sizes, original_lookup_times, optimized_lookup_times)


def plot_results(path_sizes, original_times, optimized_times, 
                lookup_sizes, original_lookup_times, optimized_lookup_times):
    """绘制测试结果图表"""
    # 创建图表目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "benchmark_results")
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 绘制计算效率图表
    plt.figure(figsize=(10, 6))
    plt.plot(path_sizes, original_times, 'o-', label='原始方法')
    plt.plot(path_sizes, optimized_times, 's-', label='优化方法')
    plt.xlabel('路径长度')
    plt.ylabel('平均执行时间 (毫秒)')
    plt.title('路径相似度计算效率比较')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'calculation_efficiency.png'))
    
    # 绘制路径查找效率图表
    plt.figure(figsize=(10, 6))
    plt.plot(lookup_sizes, original_lookup_times, 'o-', label='原始方法')
    plt.plot(lookup_sizes, optimized_lookup_times, 's-', label='优化方法')
    plt.xlabel('存储路径数量')
    plt.ylabel('查找时间 (毫秒)')
    plt.title('路径查找效率比较')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'lookup_efficiency.png'))
    
    # 绘制加速比图表
    plt.figure(figsize=(10, 6))
    calculation_speedup = [o/n for o, n in zip(original_times, optimized_times)]
    lookup_speedup = [o/n for o, n in zip(original_lookup_times, optimized_lookup_times)]
    
    plt.plot(path_sizes, calculation_speedup, 'o-', label='计算加速比')
    plt.plot(lookup_sizes, lookup_speedup, 's-', label='查找加速比')
    plt.xlabel('路径长度/存储路径数量')
    plt.ylabel('加速比')
    plt.title('优化方法加速比')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'speedup_ratio.png'))
    
    print(f"\n结果图表已保存到: {output_dir}")


if __name__ == "__main__":
    run_benchmarks()