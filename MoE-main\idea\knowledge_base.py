import os
import json
import time
import uuid
import copy
import logging
import shutil
import glob
import numpy as np

class KnowledgeBase:
    """知识库基类，提供知识条目的存储、检索和管理功能"""
    
    def __init__(self, config=None):
        """初始化知识库
        
        Args:
            config (dict): 知识库配置
        """
        self.config = config or {}
        self.knowledge_entries = []
        self.metadata = {"created_at": time.time(), "version": "1.0"}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置存储路径
        self.storage_path = self.config.get("storage_path", "knowledge_base")
        os.makedirs(self.storage_path, exist_ok=True)
        
        # 设置相似度阈值
        self.similarity_threshold = self.config.get("similarity_threshold", 0.7)
        
        # 设置最大条目数
        self.max_entries = self.config.get("max_entries", 100)
        
        # 设置备份文件数量
        self.backup_count = self.config.get("backup_count", 5)
        
        # 加载知识库
        self._load_knowledge()
    
    def add_entry(self, entry):
        """添加知识条目到知识库
        
        Args:
            entry (dict): 知识条目
            
        Returns:
            str: 条目ID
        """
        # 生成唯一ID
        if "id" not in entry:
            entry["id"] = str(uuid.uuid4())
        
        # 设置初始统计数据
        if "usage_count" not in entry:
            entry["usage_count"] = 0
        if "success_count" not in entry:
            entry["success_count"] = 0
        if "timestamp" not in entry:
            entry["timestamp"] = time.time()
        
        # 检查是否有相似条目，有则合并或更新
        for i, existing_entry in enumerate(self.knowledge_entries):
            similarity = self._calculate_similarity(entry, existing_entry)
            if similarity > self.similarity_threshold:
                # 合并或更新条目
                self.knowledge_entries[i] = self._merge_entries(existing_entry, entry)
                self._save_knowledge()
                return self.knowledge_entries[i]["id"]
        
        # 如果没有相似条目，添加新条目
        self.knowledge_entries.append(entry)
        
        # 如果超过最大条目数，删除最低质量的条目
        if len(self.knowledge_entries) > self.max_entries:
            self._remove_lowest_quality_entry()
        
        self._save_knowledge()
        return entry["id"]
    
    def update_entry(self, entry_id, update_data, success=None):
        """更新知识条目
        
        Args:
            entry_id (str): 条目ID
            update_data (dict): 更新数据
            success (bool): 是否成功应用
            
        Returns:
            bool: 是否成功更新
        """
        for i, entry in enumerate(self.knowledge_entries):
            if entry.get("id") == entry_id:
                # 更新使用统计
                self.knowledge_entries[i]["usage_count"] += 1
                if success is not None:
                    if success:
                        self.knowledge_entries[i]["success_count"] += 1
                    
                    # 更新改进统计
                    if "improvement" in update_data and success:
                        improvement = update_data.pop("improvement")
                        self._update_improvement_stats(i, improvement)
                
                # 更新其他数据
                for key, value in update_data.items():
                    if key in self.knowledge_entries[i]:
                        self.knowledge_entries[i][key] = value
                
                # 更新时间戳
                self.knowledge_entries[i]["timestamp"] = time.time()
                
                self._save_knowledge()
                return True
        
        return False
    
    def retrieve(self, query, count=5):
        """检索与查询相关的知识条目
        
        Args:
            query (dict): 查询条件
            count (int): 返回条目数量
            
        Returns:
            list: 相关知识条目列表
        """
        if not self.knowledge_entries:
            return []
        
        # 计算查询与所有条目的相似度
        similarities = [(entry, self._calculate_similarity(query, entry)) 
                        for entry in self.knowledge_entries]
        
        # 按相似度排序
        sorted_entries = sorted(similarities, key=lambda x: x[1], reverse=True)
        
        # 返回前count个条目
        return [entry for entry, _ in sorted_entries[:count]]
    
    def advanced_retrieve(self, query, filters=None, sort_by="similarity", count=5):
        """高级检索，支持过滤和自定义排序
        
        Args:
            query (dict): 查询条件
            filters (dict): 过滤条件
            sort_by (str): 排序方式
            count (int): 返回条目数量
            
        Returns:
            list: 相关知识条目列表
        """
        if not self.knowledge_entries:
            return []
        
        # 应用过滤条件
        filtered_entries = self.knowledge_entries
        if filters:
            filtered_entries = self._apply_filters(filtered_entries, filters)
        
        # 计算相似度
        entry_scores = []
        for entry in filtered_entries:
            similarity = self._calculate_similarity(query, entry)
            success_rate = entry.get("success_count", 0) / entry.get("usage_count", 1)
            avg_improvement = entry.get("improvement_stats", {}).get("avg_improvement", 0)
            recency = 1.0 / (1.0 + (time.time() - entry.get("timestamp", 0)) / 86400)  # 时间衰减因子
            
            entry_scores.append({
                "entry": entry,
                "similarity": similarity,
                "success_rate": success_rate,
                "avg_improvement": avg_improvement,
                "recency": recency,
                "combined_score": 0.4 * similarity + 0.3 * success_rate + 0.2 * avg_improvement + 0.1 * recency
            })
        
        # 根据指定方式排序
        if sort_by == "similarity":
            sorted_entries = sorted(entry_scores, key=lambda x: x["similarity"], reverse=True)
        elif sort_by == "success_rate":
            sorted_entries = sorted(entry_scores, key=lambda x: x["success_rate"], reverse=True)
        elif sort_by == "improvement":
            sorted_entries = sorted(entry_scores, key=lambda x: x["avg_improvement"], reverse=True)
        elif sort_by == "recency":
            sorted_entries = sorted(entry_scores, key=lambda x: x["recency"], reverse=True)
        else:  # 默认使用组合得分
            sorted_entries = sorted(entry_scores, key=lambda x: x["combined_score"], reverse=True)
        
        # 返回前count个条目
        return [item["entry"] for item in sorted_entries[:count]]
    
    def _calculate_similarity(self, entry1, entry2):
        """计算两个知识条目的相似度
        
        Args:
            entry1 (dict): 知识条目1
            entry2 (dict): 知识条目2
            
        Returns:
            float: 相似度 (0-1)
        """
        # 基于多个特征的加权相似度计算
        similarity_scores = []
        weights = []
        
        # 1. 关键词相似度 (0.4)
        keywords1 = set(entry1.get("keywords", []))
        keywords2 = set(entry2.get("keywords", []))
        
        if keywords1 and keywords2:
            intersection = len(keywords1.intersection(keywords2))
            union = len(keywords1.union(keywords2))
            keyword_similarity = intersection / union if union > 0 else 0.0
            similarity_scores.append(keyword_similarity)
            weights.append(0.4)
        
        # 2. 问题特征相似度 (0.4)
        features1 = entry1.get("problem_features", {})
        features2 = entry2.get("problem_features", {})
        
        if features1 and features2:
            feature_similarity = self._calculate_feature_similarity(features1, features2)
            similarity_scores.append(feature_similarity)
            weights.append(0.4)
        
        # 3. 标签相似度 (0.2)
        tags1 = set(entry1.get("tags", []))
        tags2 = set(entry2.get("tags", []))
        
        if tags1 and tags2:
            intersection = len(tags1.intersection(tags2))
            union = len(tags1.union(tags2))
            tag_similarity = intersection / union if union > 0 else 0.0
            similarity_scores.append(tag_similarity)
            weights.append(0.2)
        
        # 计算加权平均相似度
        if similarity_scores:
            total_weight = sum(weights)
            weighted_similarity = sum(s * w for s, w in zip(similarity_scores, weights)) / total_weight
            return weighted_similarity
        
        return 0.0
    
    def _calculate_feature_similarity(self, features1, features2):
        """计算问题特征的相似度
        
        Args:
            features1 (dict): 特征1
            features2 (dict): 特征2
            
        Returns:
            float: 相似度 (0-1)
        """
        similarity_scores = []
        
        # 1. 问题规模相似度
        if "instance_size" in features1 and "instance_size" in features2:
            size1 = features1["instance_size"]
            size2 = features2["instance_size"]
            # 规模相似度：1 - |size1-size2|/max(size1,size2)
            size_similarity = 1 - abs(size1 - size2) / max(size1, size2)
            similarity_scores.append(size_similarity)
        
        # 2. 多样性水平相似度
        if "diversity_level" in features1 and "diversity_level" in features2:
            div1 = features1["diversity_level"]
            div2 = features2["diversity_level"]
            # 多样性相似度：1 - |div1-div2|
            div_similarity = 1 - abs(div1 - div2)
            similarity_scores.append(div_similarity)
        
        # 3. 收敛阶段相似度
        if "convergence_stage" in features1 and "convergence_stage" in features2:
            stage1 = features1["convergence_stage"]
            stage2 = features2["convergence_stage"]
            # 收敛阶段相似度：相同为1，不同为0
            stage_similarity = 1.0 if stage1 == stage2 else 0.0
            similarity_scores.append(stage_similarity)
        
        # 计算平均相似度
        if similarity_scores:
            return sum(similarity_scores) / len(similarity_scores)
        
        return 0.0
    
    def _merge_entries(self, entry1, entry2):
        """合并两个知识条目
        
        Args:
            entry1 (dict): 知识条目1
            entry2 (dict): 知识条目2
            
        Returns:
            dict: 合并后的知识条目
        """
        # 合并关键词和标签
        keywords = list(set(entry1.get("keywords", []) + entry2.get("keywords", [])))
        tags = list(set(entry1.get("tags", []) + entry2.get("tags", [])))
        
        # 更新使用次数和成功率
        usage_count1 = entry1.get("usage_count", 0)
        usage_count2 = entry2.get("usage_count", 0)
        success_count1 = entry1.get("success_count", 0)
        success_count2 = entry2.get("success_count", 0)
        
        total_usage = usage_count1 + usage_count2
        total_success = success_count1 + success_count2
        
        # 合并改进统计
        improvement_stats = self._merge_improvement_stats(
            entry1.get("improvement_stats", {}),
            entry2.get("improvement_stats", {}),
            usage_count1, usage_count2
        )
        
        # 选择保留哪个策略内容
        # 1. 优先保留更新的条目
        # 2. 如果时间接近，保留成功率更高的条目
        # 3. 如果成功率接近，保留平均改进更大的条目
        strategy_content = entry1.get("strategy_content", "")
        timestamp1 = entry1.get("timestamp", 0)
        timestamp2 = entry2.get("timestamp", 0)
        
        success_rate1 = success_count1 / usage_count1 if usage_count1 > 0 else 0
        success_rate2 = success_count2 / usage_count2 if usage_count2 > 0 else 0
        
        avg_improvement1 = entry1.get("improvement_stats", {}).get("avg_improvement", 0)
        avg_improvement2 = entry2.get("improvement_stats", {}).get("avg_improvement", 0)
        
        # 时间差超过一天，选择更新的
        if abs(timestamp2 - timestamp1) > 86400:  # 86400秒 = 1天
            if timestamp2 > timestamp1:
                strategy_content = entry2.get("strategy_content", "")
        # 时间接近，比较成功率
        elif abs(success_rate2 - success_rate1) > 0.1:  # 成功率差异超过10%
            if success_rate2 > success_rate1:
                strategy_content = entry2.get("strategy_content", "")
        # 成功率接近，比较平均改进
        elif avg_improvement2 > avg_improvement1:
            strategy_content = entry2.get("strategy_content", "")
        
        # 合并问题特征
        problem_features = self._merge_problem_features(
            entry1.get("problem_features", {}),
            entry2.get("problem_features", {})
        )
        
        # 保留ID和来源
        merged_entry = {
            "id": entry1.get("id"),  # 保留第一个条目的ID
            "keywords": keywords,
            "strategy_content": strategy_content,
            "problem_features": problem_features,
            "usage_count": total_usage,
            "success_count": total_success,
            "improvement_stats": improvement_stats,
            "timestamp": max(timestamp1, timestamp2),
            "source": entry1.get("source", ""),  # 保留第一个条目的来源
            "tags": tags
        }
        
        return merged_entry
    
    def _merge_improvement_stats(self, stats1, stats2, count1, count2):
        """合并两个改进统计
        
        Args:
            stats1 (dict): 统计1
            stats2 (dict): 统计2
            count1 (int): 条目1的使用次数
            count2 (int): 条目2的使用次数
            
        Returns:
            dict: 合并后的统计
        """
        if not stats1:
            return stats2
        if not stats2:
            return stats1
        
        # 计算加权平均值
        total_count = count1 + count2
        if total_count == 0:
            return stats1
        
        weight1 = count1 / total_count
        weight2 = count2 / total_count
        
        avg_improvement1 = stats1.get("avg_improvement", 0)
        avg_improvement2 = stats2.get("avg_improvement", 0)
        
        # 合并统计
        merged_stats = {
            "avg_improvement": avg_improvement1 * weight1 + avg_improvement2 * weight2,
            "max_improvement": max(stats1.get("max_improvement", 0), stats2.get("max_improvement", 0)),
            "min_improvement": min(stats1.get("min_improvement", float("inf")), stats2.get("min_improvement", float("inf"))),
            "success_rate": (stats1.get("success_count", 0) + stats2.get("success_count", 0)) / total_count
        }
        
        return merged_stats
    
    def _merge_problem_features(self, features1, features2):
        """合并两个问题特征
        
        Args:
            features1 (dict): 特征1
            features2 (dict): 特征2
            
        Returns:
            dict: 合并后的特征
        """
        merged_features = {}
        
        # 合并数值型特征：取平均值
        numeric_features = ["population_size", "instance_size", "diversity_level"]
        for feature in numeric_features:
            if feature in features1 and feature in features2:
                merged_features[feature] = (features1[feature] + features2[feature]) / 2
            elif feature in features1:
                merged_features[feature] = features1[feature]
            elif feature in features2:
                merged_features[feature] = features2[feature]
        
        # 合并分类型特征：保留更新的或更常见的
        categorical_features = ["convergence_stage"]
        for feature in categorical_features:
            if feature in features1 and feature in features2:
                # 如果两个值相同，直接使用；否则，保留更新的条目中的值
                if features1[feature] == features2[feature]:
                    merged_features[feature] = features1[feature]
                elif features2.get("timestamp", 0) > features1.get("timestamp", 0):
                    merged_features[feature] = features2[feature]
                else:
                    merged_features[feature] = features1[feature]
            elif feature in features1:
                merged_features[feature] = features1[feature]
            elif feature in features2:
                merged_features[feature] = features2[feature]
        
        # 合并复杂结构：如成本分布、路径模式等
        complex_features = ["cost_distribution", "path_patterns", "landscape_features"]
        for feature in complex_features:
            if feature in features1 and feature in features2:
                # 简单策略：保留更新的条目中的复杂特征
                if features2.get("timestamp", 0) > features1.get("timestamp", 0):
                    merged_features[feature] = features2[feature]
                else:
                    merged_features[feature] = features1[feature]
            elif feature in features1:
                merged_features[feature] = features1[feature]
            elif feature in features2:
                merged_features[feature] = features2[feature]
        
        return merged_features
    
    def _update_improvement_stats(self, index, improvement):
        """更新知识条目的改进统计
        
        Args:
            index (int): 条目索引
            improvement (float): 改进幅度
        """
        entry = self.knowledge_entries[index]
        stats = entry.get("improvement_stats", {})
        
        # 获取当前统计数据
        avg_improvement = stats.get("avg_improvement", 0)
        max_improvement = stats.get("max_improvement", 0)
        min_improvement = stats.get("min_improvement", float("inf"))
        variance_sum = stats.get("variance_sum", 0)  # 用于计算方差的平方和
        count = entry.get("success_count", 0)
        
        # 更新统计数据
        if count == 0:
            # 第一次成功，直接设置
            new_avg = improvement
            new_max = improvement
            new_min = improvement
            new_variance_sum = 0
        else:
            # 更新平均值、最大值、最小值
            new_avg = (avg_improvement * (count - 1) + improvement) / count
            new_max = max(max_improvement, improvement)
            new_min = min(min_improvement, improvement)
            
            # 更新方差计算所需的平方和
            old_avg = avg_improvement
            variance_sum += (improvement - old_avg) * (improvement - new_avg)
            new_variance_sum = variance_sum
        
        # 计算方差
        new_variance = new_variance_sum / count if count > 0 else 0
        
        # 更新改进统计
        self.knowledge_entries[index]["improvement_stats"] = {
            "avg_improvement": new_avg,
            "max_improvement": new_max,
            "min_improvement": new_min,
            "improvement_variance": new_variance,
            "variance_sum": new_variance_sum,  # 内部使用，不对外暴露
            "success_rate": entry.get("success_count", 0) / entry.get("usage_count", 1)
        }
    
    def _apply_filters(self, entries, filters):
        """应用过滤条件
        
        Args:
            entries (list): 条目列表
            filters (dict): 过滤条件
            
        Returns:
            list: 过滤后的条目列表
        """
        filtered = entries
        
        # 按标签过滤
        if "tags" in filters:
            required_tags = set(filters["tags"])
            filtered = [e for e in filtered if required_tags.issubset(set(e.get("tags", [])))]
        
        # 按成功率过滤
        if "min_success_rate" in filters:
            min_rate = filters["min_success_rate"]
            filtered = [e for e in filtered if 
                       e.get("success_count", 0) / e.get("usage_count", 1) >= min_rate]
        
        # 按使用次数过滤
        if "min_usage_count" in filters:
            min_count = filters["min_usage_count"]
            filtered = [e for e in filtered if e.get("usage_count", 0) >= min_count]
        
        # 按时间过滤
        if "max_age_days" in filters:
            max_age = filters["max_age_days"] * 86400  # 转换为秒
            current_time = time.time()
            filtered = [e for e in filtered if 
                       (current_time - e.get("timestamp", 0)) <= max_age]
        
        # 按问题规模过滤
        if "instance_size_range" in filters:
            min_size, max_size = filters["instance_size_range"]
            filtered = [e for e in filtered if 
                       min_size <= e.get("problem_features", {}).get("instance_size", 0) <= max_size]
        
        # 按收敛阶段过滤
        if "convergence_stage" in filters:
            stage = filters["convergence_stage"]
            filtered = [e for e in filtered if 
                       e.get("problem_features", {}).get("convergence_stage") == stage]
        
        return filtered
    
    def _evaluate_entry_quality(self, entry):
        """评估知识条目的质量
        
        Args:
            entry (dict): 知识条目
            
        Returns:
            float: 质量分数 (0-1)
        """
        # 基于多个因素的加权评分
        quality_score = 0.0
        total_weight = 0.0
        
        # 1. 成功率 (权重0.4)
        usage_count = entry.get("usage_count", 0)
        if usage_count > 0:
            success_rate = entry.get("success_count", 0) / usage_count
            quality_score += 0.4 * success_rate
            total_weight += 0.4
        
        # 2. 平均改进幅度 (权重0.3)
        avg_improvement = entry.get("improvement_stats", {}).get("avg_improvement", 0)
        if avg_improvement > 0:
            # 归一化改进幅度，假设最大改进为30%
            normalized_improvement = min(avg_improvement / 30.0, 1.0)
            quality_score += 0.3 * normalized_improvement
            total_weight += 0.3
        
        # 3. 使用频率 (权重0.1)
        # 使用频率越高，说明该知识越通用
        if usage_count > 0:
            # 归一化使用次数，假设10次以上为高频使用
            usage_score = min(usage_count / 10.0, 1.0)
            quality_score += 0.1 * usage_score
            total_weight += 0.1
        
        # 4. 时间衰减因子 (权重0.2)
        # 较新的知识条目质量分数更高
        timestamp = entry.get("timestamp", 0)
        if timestamp > 0:
            current_time = time.time()
            age_days = (current_time - timestamp) / 86400  # 转换为天数
            # 时间衰减因子：1.0 / (1.0 + age_days/30)，30天后权重减半
            time_decay = 1.0 / (1.0 + age_days/30)
            quality_score += 0.2 * time_decay
            total_weight += 0.2
        
        # 计算最终质量分数
        if total_weight > 0:
            return quality_score / total_weight
        else:
            return 0.0
    
    def _remove_lowest_quality_entry(self):
        """移除质量最低的知识条目"""
        if not self.knowledge_entries:
            return
        
        # 计算所有条目的质量分数
        quality_scores = [(i, self._evaluate_entry_quality(entry)) 
                         for i, entry in enumerate(self.knowledge_entries)]
        
        # 按质量分数排序
        sorted_indices = sorted(quality_scores, key=lambda x: x[1])
        
        # 移除质量最低的条目
        lowest_quality_index = sorted_indices[0][0]
        removed_entry = self.knowledge_entries.pop(lowest_quality_index)
        
        self.logger.info(f"已移除质量最低的知识条目，ID: {removed_entry.get('id', 'unknown')}")
    
    def _load_knowledge(self):
        """从存储中加载知识库"""
        try:
            knowledge_file = os.path.join(self.storage_path, "knowledge.json")
            if os.path.exists(knowledge_file):
                with open(knowledge_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, dict) and "entries" in data:
                        self.knowledge_entries = data["entries"]
                        self.metadata = data.get("metadata", self.metadata)
                    else:
                        self.knowledge_entries = data
                self.logger.info(f"已加载{len(self.knowledge_entries)}条知识条目")
                
                # 版本兼容性检查和升级
                self._check_and_upgrade_entries()
        except Exception as e:
            self.logger.error(f"加载知识库失败: {e}")
            # 创建备份并初始化空知识库
            self._create_backup_and_initialize()
    
    def _save_knowledge(self):
        """将知识库保存到存储"""
        try:
            # 创建临时文件
            temp_file = os.path.join(self.storage_path, "knowledge_temp.json")
            with open(temp_file, "w", encoding="utf-8") as f:
                json.dump({
                    "entries": self.knowledge_entries,
                    "metadata": self.metadata
                }, f, ensure_ascii=False, indent=2)
            
            # 安全替换原文件
            knowledge_file = os.path.join(self.storage_path, "knowledge.json")
            if os.path.exists(knowledge_file):
                backup_file = os.path.join(self.storage_path, f"knowledge_backup_{int(time.time())}.json")
                shutil.copy2(knowledge_file, backup_file)
            
            shutil.move(temp_file, knowledge_file)
            self.logger.info(f"已保存{len(self.knowledge_entries)}条知识条目")
            
            # 清理过多的备份文件
            self._cleanup_backup_files()
        except Exception as e:
            self.logger.error(f"保存知识库失败: {e}")
    
    def _cleanup_backup_files(self):
        """清理过多的备份文件，只保留最新的几个"""
        try:
            backup_files = glob.glob(os.path.join(self.storage_path, "knowledge_backup_*.json"))
            
            # 按文件修改时间排序
            backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # 删除多余的备份文件
            for file_path in backup_files[self.backup_count:]:
                os.remove(file_path)
                self.logger.debug(f"已删除旧备份文件: {file_path}")
        except Exception as e:
            self.logger.error(f"清理备份文件失败: {e}")
    
    def _create_backup_and_initialize(self):
        """创建备份并初始化空知识库"""
        try:
            knowledge_file = os.path.join(self.storage_path, "knowledge.json")
            if os.path.exists(knowledge_file):
                # 创建备份
                backup_file = os.path.join(
                    self.storage_path, 
                    f"knowledge_backup_error_{int(time.time())}.json"
                )
                shutil.copy2(knowledge_file, backup_file)
                self.logger.warning(f"已创建错误备份: {backup_file}")
            
            # 初始化空知识库
            self.knowledge_entries = []
            self.metadata = {"created_at": time.time(), "version": "1.0"}
            self._save_knowledge()
        except Exception as e:
            self.logger.error(f"创建备份并初始化知识库失败: {e}")
    
    def _check_and_upgrade_entries(self):
        """检查并升级知识条目的版本兼容性"""
        # 当前版本为1.0，未来可能需要升级
        current_version = self.metadata.get("version", "1.0")
        
        # 如果有版本升级需求，在这里实现
        # 例如：从0.9升级到1.0
        if current_version == "0.9":
            self._upgrade_from_0_9_to_1_0()
            self.metadata["version"] = "1.0"
            self._save_knowledge()
    
    def _upgrade_from_0_9_to_1_0(self):
        """将知识条目从0.9版本升级到1.0版本"""
        # 示例升级逻辑
        for entry in self.knowledge_entries:
            # 添加新字段
            if "tags" not in entry:
                entry["tags"] = []
            
            # 更新字段结构
            if "improvement" in entry and "improvement_stats" not in entry:
                entry["improvement_stats"] = {
                    "avg_improvement": entry["improvement"],
                    "max_improvement": entry["improvement"],
                    "min_improvement": entry["improvement"],
                    "improvement_variance": 0
                }
                entry.pop("improvement")
        
        self.logger.info("已将知识条目从0.9版本升级到1.0版本")
    
    def create_version(self, description="Regular version"):
        """创建知识库的版本快照
        
        Args:
            description (str): 版本描述
            
        Returns:
            str: 版本ID
        """
        # 生成版本ID
        version_id = f"v{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        # 创建版本信息
        version_info = {
            "id": version_id,
            "timestamp": time.time(),
            "description": description,
            "entry_count": len(self.knowledge_entries),
            "metadata": copy.deepcopy(self.metadata)
        }
        
        # 保存版本快照
        try:
            # 确保版本目录存在
            version_dir = os.path.join(self.storage_path, "versions")
            os.makedirs(version_dir, exist_ok=True)
            
            # 保存知识条目快照
            version_file = os.path.join(version_dir, f"{version_id}.json")
            with open(version_file, "w", encoding="utf-8") as f:
                json.dump({
                    "version_info": version_info,
                    "knowledge_entries": self.knowledge_entries
                }, f, ensure_ascii=False, indent=2)
            
            # 更新版本历史
            if not hasattr(self, "version_history"):
                self.version_history = []
            self.version_history.append(version_info)
            
            # 保存版本历史
            history_file = os.path.join(self.storage_path, "version_history.json")
            with open(history_file, "w", encoding="utf-8") as f:
                json.dump(self.version_history, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"已创建知识库版本: {version_id}")
            return version_id
            
        except Exception as e:
            self.logger.error(f"创建知识库版本失败: {e}")
            return None
    
    def rollback_to_version(self, version_id):
        """回滚到指定版本的知识库
        
        Args:
            version_id (str): 版本ID
            
        Returns:
            bool: 是否成功回滚
        """
        try:
            version_file = os.path.join(self.storage_path, "versions", f"{version_id}.json")
            if not os.path.exists(version_file):
                self.logger.error(f"版本文件不存在: {version_file}")
                return False
            
            # 加载版本数据
            with open(version_file, "r", encoding="utf-8") as f:
                version_data = json.load(f)
            
            # 创建当前状态的备份
            self.create_version("Auto backup before rollback")
            
            # 更新知识条目和元数据
            self.knowledge_entries = version_data["knowledge_entries"]
            self.metadata = version_data["version_info"]["metadata"]
            
            # 保存回滚后的状态
            self._save_knowledge()
            
            self.logger.info(f"已回滚到版本: {version_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚到版本失败: {e}")
            return False