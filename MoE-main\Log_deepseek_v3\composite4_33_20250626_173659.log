2025-06-26 17:36:59,954 - __main__ - INFO - composite4_33 开始进化第 1 代
2025-06-26 17:36:59,954 - __main__ - INFO - 开始分析阶段
2025-06-26 17:36:59,954 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:36:59,960 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 8885.0, 'max': 51640.0, 'mean': 35326.2, 'std': 17594.57679968461}, 'diversity': 0.901010101010101, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:36:59,961 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 8885.0, 'max': 51640.0, 'mean': 35326.2, 'std': 17594.57679968461}, 'diversity_level': 0.901010101010101, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[5248, 4841], [5246, 4845], [5240, 4845], [5238, 4841], [5240, 4837], [5246, 4837], [5251, 4849], [5235, 4849], [5235, 4833], [5251, 4833], [6102, 3756], [6097, 3765], [6087, 3765], [6082, 3756], [6087, 3747], [6097, 3747], [6095, 3760], [6089, 3760], [6089, 3752], [6095, 3752], [2386, 3819], [2386, 3860], [2425, 3832], [2410, 3786], [2362, 3786], [2347, 3832], [5121, 4369], [5161, 4369], [5141, 4404], [5101, 4404], [5081, 4369], [5101, 4334], [5141, 4334]], 'distance_matrix': array([[  0.,   4.,   9., ..., 501., 528., 518.],
       [  4.,   0.,   6., ..., 504., 531., 522.],
       [  9.,   6.,   0., ..., 502., 530., 521.],
       ...,
       [501., 504., 502., ...,   0.,  40.,  69.],
       [528., 531., 530., ...,  40.,   0.,  40.],
       [518., 522., 521., ...,  69.,  40.,   0.]])}
2025-06-26 17:36:59,963 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:36:59,963 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:36:59,963 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:36:59,965 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:36:59,965 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (20, 22), 'frequency': 0.5, 'avg_cost': 41.0}], 'common_subpaths': [{'subpath': (22, 20, 21), 'frequency': 0.3}, {'subpath': (20, 21, 25), 'frequency': 0.3}, {'subpath': (21, 25, 24), 'frequency': 0.3}, {'subpath': (25, 24, 23), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(12, 17)', 'frequency': 0.4}, {'edge': '(29, 30)', 'frequency': 0.4}, {'edge': '(8, 31)', 'frequency': 0.4}, {'edge': '(3, 4)', 'frequency': 0.4}, {'edge': '(5, 9)', 'frequency': 0.4}, {'edge': '(20, 22)', 'frequency': 0.5}, {'edge': '(21, 25)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(11, 16)', 'frequency': 0.3}, {'edge': '(16, 17)', 'frequency': 0.3}, {'edge': '(12, 13)', 'frequency': 0.3}, {'edge': '(14, 18)', 'frequency': 0.3}, {'edge': '(14, 19)', 'frequency': 0.3}, {'edge': '(15, 19)', 'frequency': 0.3}, {'edge': '(10, 15)', 'frequency': 0.3}, {'edge': '(26, 32)', 'frequency': 0.2}, {'edge': '(26, 27)', 'frequency': 0.3}, {'edge': '(27, 28)', 'frequency': 0.3}, {'edge': '(28, 29)', 'frequency': 0.2}, {'edge': '(30, 31)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(2, 3)', 'frequency': 0.3}, {'edge': '(1, 2)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(0, 5)', 'frequency': 0.3}, {'edge': '(6, 9)', 'frequency': 0.3}, {'edge': '(6, 7)', 'frequency': 0.2}, {'edge': '(7, 22)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(24, 25)', 'frequency': 0.3}, {'edge': '(23, 24)', 'frequency': 0.3}, {'edge': '(11, 23)', 'frequency': 0.3}, {'edge': '(26, 28)', 'frequency': 0.2}, {'edge': '(13, 22)', 'frequency': 0.2}, {'edge': '(7, 23)', 'frequency': 0.2}, {'edge': '(6, 15)', 'frequency': 0.2}, {'edge': '(12, 21)', 'frequency': 0.2}, {'edge': '(12, 30)', 'frequency': 0.2}, {'edge': '(23, 25)', 'frequency': 0.2}, {'edge': '(0, 8)', 'frequency': 0.2}, {'edge': '(10, 26)', 'frequency': 0.2}, {'edge': '(19, 20)', 'frequency': 0.2}, {'edge': '(1, 27)', 'frequency': 0.3}, {'edge': '(7, 27)', 'frequency': 0.2}, {'edge': '(3, 22)', 'frequency': 0.3}, {'edge': '(13, 17)', 'frequency': 0.2}, {'edge': '(7, 13)', 'frequency': 0.3}, {'edge': '(10, 28)', 'frequency': 0.2}, {'edge': '(1, 22)', 'frequency': 0.2}, {'edge': '(14, 24)', 'frequency': 0.2}, {'edge': '(0, 9)', 'frequency': 0.2}, {'edge': '(29, 32)', 'frequency': 0.2}, {'edge': '(8, 25)', 'frequency': 0.2}, {'edge': '(0, 3)', 'frequency': 0.2}, {'edge': '(1, 19)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(24, 32)', 'frequency': 0.2}, {'edge': '(4, 10)', 'frequency': 0.2}, {'edge': '(3, 5)', 'frequency': 0.2}, {'edge': '(2, 20)', 'frequency': 0.2}, {'edge': '(19, 30)', 'frequency': 0.2}, {'edge': '(11, 21)', 'frequency': 0.2}, {'edge': '(0, 2)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [11, 23, 14, 24, 30], 'cost': 13870.0, 'size': 5}, {'region': [17, 23, 6, 21, 11], 'cost': 13455.0, 'size': 5}, {'region': [5, 24, 17, 22, 3], 'cost': 13450.0, 'size': 5}, {'region': [15, 23, 27, 25, 11], 'cost': 13115.0, 'size': 5}, {'region': [23, 11, 21, 3], 'cost': 10415.0, 'size': 4}]}
2025-06-26 17:36:59,966 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:36:59,966 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:36:59,966 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:36:59,966 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 17:36:59,966 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 17:36:59,967 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:36:59,967 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:36:59,969 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:36:59,969 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 8885.0, mean 35326.2, max 51640.0, std 17594.57679968461
- diversity: 0.901010101010101
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [16252.0, 16252.0, 16252.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (20, 22), 'frequency': 0.5, 'avg_cost': 41.0}]
- common_subpaths_sample: [{'subpath': (22, 20, 21), 'frequency': 0.3}, {'subpath': (20, 21, 25), 'frequency': 0.3}, {'subpath': (21, 25, 24), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [11, 23, 14, 24, 30], 'cost': 13870.0, 'size': 5}, {'region': [17, 23, 6, 21, 11], 'cost': 13455.0, 'size': 5}, {'region': [5, 24, 17, 22, 3], 'cost': 13450.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [2347.0, 3747.0, 6102.0, 4849.0]
- centroid/spread: [4954.939, 4226.303] | rms 1356.193
- density_grid(3x3): [[6, 0, 10], [0, 0, 7], [0, 0, 10]]
- edge_len_stats(min,q1,med,q3,max): [4.0, 449.75, 1191.5, 2812.25, 3756.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 4, 6, 1, 7, 1, 6, 1]
- nn_median_dist: 6.403

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:36:59,969 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:37:28,601 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High (wide range of edge lengths from 4.0 to 3756.0, with significant standard deviation)",
    "modality": "Potentially multimodal (presence of multiple common subpaths and high-quality edges)",
    "deceptiveness": "Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Sparse (density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (diversity high at 0.90, convergence low at 0.0)",
    "stagnation": "Recent stagnation detected in best costs"
  },
  "difficult_regions": [
    {"region": [11, 23, 14, 24, 30], "reason": "High cost (13870.0) and located in low-density grid areas"},
    {"region": [17, 23, 6, 21, 11], "reason": "High cost (13455.0) and directional imbalance (sector histogram)"},
    {"region": [5, 24, 17, 22, 3], "reason": "High cost (13450.0) and long-edge corridor"}
  ],
  "opportunity_regions": [
    {"region": [20, 22, 21, 25, 24], "reason": "Frequent high-quality edges and common subpaths in medium-density grid"},
    {"region": "Centroid area [4954.9, 4226.3]", "reason": "High node density in surrounding grid cells"}
  ],
  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",
  "evolution_direction": [
    "Operator suggestions:",
    "- Focused mutation around high-quality edges (20-22, 21-25)",
    "- Path relinking between common subpaths",
    "- Density-based crossover favoring centroid region",
    "- Penalize long edges in difficult regions"
  ]
}
```
2025-06-26 17:37:28,604 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:37:28,604 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High (wide range of edge lengths from 4.0 to 3756.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of multiple common subpaths and high-quality edges)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges)'}, 'population_state': {'coverage': 'Sparse (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity high at 0.90, convergence low at 0.0)', 'stagnation': 'Recent stagnation detected in best costs'}, 'difficult_regions': [{'region': [11, 23, 14, 24, 30], 'reason': 'High cost (13870.0) and located in low-density grid areas'}, {'region': [17, 23, 6, 21, 11], 'reason': 'High cost (13455.0) and directional imbalance (sector histogram)'}, {'region': [5, 24, 17, 22, 3], 'reason': 'High cost (13450.0) and long-edge corridor'}], 'opportunity_regions': [{'region': [20, 22, 21, 25, 24], 'reason': 'Frequent high-quality edges and common subpaths in medium-density grid'}, {'region': 'Centroid area [4954.9, 4226.3]', 'reason': 'High node density in surrounding grid cells'}], 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Operator suggestions:', '- Focused mutation around high-quality edges (20-22, 21-25)', '- Path relinking between common subpaths', '- Density-based crossover favoring centroid region', '- Penalize long edges in difficult regions']}
2025-06-26 17:37:28,604 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:37:28,604 - __main__ - INFO - 分析阶段完成
2025-06-26 17:37:28,604 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High (wide range of edge lengths from 4.0 to 3756.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of multiple common subpaths and high-quality edges)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges)'}, 'population_state': {'coverage': 'Sparse (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity high at 0.90, convergence low at 0.0)', 'stagnation': 'Recent stagnation detected in best costs'}, 'difficult_regions': [{'region': [11, 23, 14, 24, 30], 'reason': 'High cost (13870.0) and located in low-density grid areas'}, {'region': [17, 23, 6, 21, 11], 'reason': 'High cost (13455.0) and directional imbalance (sector histogram)'}, {'region': [5, 24, 17, 22, 3], 'reason': 'High cost (13450.0) and long-edge corridor'}], 'opportunity_regions': [{'region': [20, 22, 21, 25, 24], 'reason': 'Frequent high-quality edges and common subpaths in medium-density grid'}, {'region': 'Centroid area [4954.9, 4226.3]', 'reason': 'High node density in surrounding grid cells'}], 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Operator suggestions:', '- Focused mutation around high-quality edges (20-22, 21-25)', '- Path relinking between common subpaths', '- Density-based crossover favoring centroid region', '- Penalize long edges in difficult regions']}
2025-06-26 17:37:28,604 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:37:28,604 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:37:28,604 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 8885.0
  • mean_cost: 35326.2
- Search-space: ruggedness High (wide range of edge lengths from 4.0 to 3756.0, with significant standard deviation), modality Potentially multimodal (presence of multiple common subpaths and high-quality edges), deceptiveness Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges)
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [11, 23, , {'region': [17, 23, ...
- Opportunity regions (# 2): {'region': [20, 22, , {'region': 'Centroid
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:37:28,604 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:37:28,604 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:37:39,207 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early iteration favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 17:37:39,207 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:37:39,207 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:37:39,207 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early iteration favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 17:37:39,216 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:37:39,216 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:37:39,216 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early iteration favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 17:37:39,216 - __main__ - INFO - 开始进化阶段
2025-06-26 17:37:39,216 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:37:39,216 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:37:39,216 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 8908.0 (rank 20.0%)
- Population diversity: 0.70625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:37:39,216 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:37:39,216 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:37:50,081 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17],
  "targeted_regions": "Cen, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid difficult nodes, maintain diversity"
}
```
2025-06-26 17:37:50,081 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:37:50,081 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33883.0, 路径: [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17]
2025-06-26 17:37:50,081 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17], 'cur_cost': 33883.0}
2025-06-26 17:37:50,081 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:37:50,081 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:37:50,081 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 8885.0 (rank 0.0%)
- Population diversity: 0.7625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:37:50,081 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:37:50,081 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:01,126 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26],
  "targeted_regions": "Cen, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters"
}
```
2025-06-26 17:38:01,126 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:01,126 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31880.0, 路径: [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26]
2025-06-26 17:38:01,126 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26], 'cur_cost': 31880.0}
2025-06-26 17:38:01,126 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:38:01,126 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:01,126 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 8886.0 (rank 0.0%)
- Population diversity: 0.740625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:01,126 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:01,126 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:11,934 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31],
  "targeted_regions": "Central opportunity regions, adjacent sparse cells",
  "strategy_comment": "Prioritize central nodes early, spiral outward to balance exploration"
}
```
2025-06-26 17:38:11,934 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:11,934 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34575.0, 路径: [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31]
2025-06-26 17:38:11,934 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31], 'cur_cost': 34575.0}
2025-06-26 17:38:11,934 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:38:11,934 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:11,934 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 49940.0 (rank 70.0%)
- Population diversity: 0.7125
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:11,934 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:11,939 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:22,626 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32],
  "targeted_regions": "Cen, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding dense clusters"
}
```
2025-06-26 17:38:22,630 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:22,630 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57895.0, 路径: [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32]
2025-06-26 17:38:22,630 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32], 'cur_cost': 57895.0}
2025-06-26 17:38:22,630 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:38:22,630 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:22,630 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42272.0 (rank 40.0%)
- Population diversity: 0.7
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:22,630 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:22,630 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:33,773 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17],
  "targeted_regions": "Cen and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult nodes"
}
```
2025-06-26 17:38:33,780 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:33,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29096.0, 路径: [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17]
2025-06-26 17:38:33,780 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17], 'cur_cost': 29096.0}
2025-06-26 17:38:33,780 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:38:33,780 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:33,780 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 45099.0 (rank 50.0%)
- Population diversity: 0.659375
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:33,780 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:33,780 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:44,379 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11],
  "targeted_regions": "Cen, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters"
}
```
2025-06-26 17:38:44,387 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:44,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32874.0, 路径: [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11]
2025-06-26 17:38:44,387 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11], 'cur_cost': 32874.0}
2025-06-26 17:38:44,387 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:38:44,387 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:44,387 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 50721.0 (rank 70.0%)
- Population diversity: 0.59375
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:44,390 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:44,390 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:38:55,607 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17],
  "targeted_regions": "Central and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters, maintain diversity"
}
```
2025-06-26 17:38:55,607 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:38:55,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29091.0, 路径: [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17]
2025-06-26 17:38:55,607 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17], 'cur_cost': 29091.0}
2025-06-26 17:38:55,607 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 17:38:55,607 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:38:55,607 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 51640.0 (rank 80.0%)
- Population diversity: 0.5375
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [20,, {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [11,, {'region': [17,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:38:55,607 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:38:55,610 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:39:06,587 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32],
  "targeted_regions": "Cen, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters"
}
```
2025-06-26 17:39:06,587 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:39:06,587 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57918.0, 路径: [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32]
2025-06-26 17:39:06,587 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32], 'cur_cost': 57918.0}
2025-06-26 17:39:06,587 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:39:06,587 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:39:06,591 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:39:06,591 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 43202.0
2025-06-26 17:39:07,092 - ExploitationExpert - INFO - res_population_num: 51
2025-06-26 17:39:07,092 - ExploitationExpert - INFO - res_population_costs: [8778, 8776, 8776, 8776, 8776, 8771, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761]
2025-06-26 17:39:07,092 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-06-26 17:39:07,107 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:39:07,107 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17], 'cur_cost': 33883.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26], 'cur_cost': 31880.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31], 'cur_cost': 34575.0}, {'tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32], 'cur_cost': 57895.0}, {'tour': [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17], 'cur_cost': 29096.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11], 'cur_cost': 32874.0}, {'tour': [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17], 'cur_cost': 29091.0}, {'tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32], 'cur_cost': 57918.0}, {'tour': array([10,  7, 15,  1, 11, 29,  0, 25, 20, 13, 12,  9, 18, 19, 30, 32,  5,
       28,  8, 26, 23, 22, 27, 21, 16,  4,  2, 17, 14, 31, 24,  3,  6]), 'cur_cost': 43202.0}, {'tour': [13, 17, 15, 6, 11, 20, 22, 3, 9, 27, 1, 26, 4, 10, 24, 29, 32, 18, 25, 31, 8, 14, 28, 5, 19, 30, 12, 0, 2, 16, 21, 23, 7], 'cur_cost': 45316.0}]
2025-06-26 17:39:07,108 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:39:07,108 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 165, 'skip_rate': 0.05454545454545454, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 156, 'cache_hits': 172, 'similarity_calculations': 3185, 'cache_hit_rate': 0.05400313971742543, 'cache_size': 3013}}
2025-06-26 17:39:07,108 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:39:07,108 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:39:07,109 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:39:07,109 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:39:07,109 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 50542.0
2025-06-26 17:39:07,612 - ExploitationExpert - INFO - res_population_num: 61
2025-06-26 17:39:07,612 - ExploitationExpert - INFO - res_population_costs: [8778, 8776, 8776, 8776, 8776, 8771, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761]
2025-06-26 17:39:07,613 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-06-26 17:39:07,630 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:39:07,632 - ExploitationExpert - INFO - populations: [{'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17], 'cur_cost': 33883.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26], 'cur_cost': 31880.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31], 'cur_cost': 34575.0}, {'tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32], 'cur_cost': 57895.0}, {'tour': [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17], 'cur_cost': 29096.0}, {'tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11], 'cur_cost': 32874.0}, {'tour': [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17], 'cur_cost': 29091.0}, {'tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32], 'cur_cost': 57918.0}, {'tour': array([10,  7, 15,  1, 11, 29,  0, 25, 20, 13, 12,  9, 18, 19, 30, 32,  5,
       28,  8, 26, 23, 22, 27, 21, 16,  4,  2, 17, 14, 31, 24,  3,  6]), 'cur_cost': 43202.0}, {'tour': array([13,  9,  4, 12, 21, 30, 32,  7,  3, 27,  8, 22, 23, 14, 24, 26,  5,
       11, 28, 15, 16, 31, 10,  2, 17, 29,  6, 25,  1, 20, 19,  0, 18]), 'cur_cost': 50542.0}]
2025-06-26 17:39:07,632 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:39:07,632 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 166, 'skip_rate': 0.05421686746987952, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 157, 'cache_hits': 172, 'similarity_calculations': 3186, 'cache_hit_rate': 0.05398618957940992, 'cache_size': 3014}}
2025-06-26 17:39:07,632 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:39:07,634 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 29, 31, 2, 4, 7, 10, 14, 16, 19, 23, 26, 11, 17], 'cur_cost': 33883.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 2, 4, 7, 10, 12, 15, 18, 21, 24, 27, 29, 31, 1, 3, 6, 9, 11, 14, 16, 17, 19, 23, 26], 'cur_cost': 31880.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 22, 25, 3, 7, 10, 15, 18, 21, 24, 27, 30, 1, 4, 9, 12, 16, 19, 23, 26, 29, 32, 2, 6, 11, 14, 17, 28, 31], 'cur_cost': 34575.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 27, 6, 21, 10, 28, 4, 16, 23, 2, 19, 29, 11, 17, 26, 13, 31, 32], 'cur_cost': 57895.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 15, 2, 7, 10, 12, 18, 22, 25, 28, 30, 32, 1, 3, 6, 9, 14, 16, 19, 21, 23, 24, 26, 27, 29, 31, 4, 11, 17], 'cur_cost': 29096.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 22, 25, 28, 30, 32, 31, 29, 27, 24, 21, 18, 15, 12, 9, 6, 3, 1, 2, 4, 7, 10, 14, 16, 19, 23, 26, 17, 11], 'cur_cost': 32874.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 13, 15, 2, 7, 19, 22, 25, 28, 30, 32, 1, 3, 6, 9, 12, 14, 16, 18, 21, 23, 24, 26, 27, 29, 31, 4, 10, 11, 17], 'cur_cost': 29091.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 20, 5, 8, 15, 22, 3, 25, 30, 12, 7, 18, 24, 1, 9, 14, 21, 4, 27, 10, 16, 23, 6, 19, 26, 2, 13, 28, 17, 31, 11, 29, 32], 'cur_cost': 57918.0}}]
2025-06-26 17:39:07,634 - __main__ - INFO - 进化阶段完成
2025-06-26 17:39:07,634 - __main__ - INFO - 开始评估阶段
2025-06-26 17:39:07,634 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:39:07,635 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:39:07,635 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 8885.0 → 29091.0 (Δ -20206.0)
- Mean cost: 35326.2 → 40095.6
- Diversity: 0.706 → 0.506 (Δ -0.2)

#### Elite metrics
- Elite count: 0 → 61
- Best elite cost: 8885.0 → 8761 (Δ 124.0)

#### Strategy performance
- Explore: 8 inds, success 37.5% , avg Δ -5107.62
- Exploit: 2 inds, success 0.0% , avg Δ -3416.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [16252.0, 16252.0, 16252.0], diversity [0.8868686868686867, 0.8636363636363634, 0.8727272727272726]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:39:07,636 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:39:22,646 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.375,
      "average_delta": -5107.62,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -3416.5,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore is effective but exploit is not contributing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and exploration opportunities."
  }
}
```
2025-06-26 17:39:22,656 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:39:22,656 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.375,
      "average_delta": -5107.62,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -3416.5,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore is effective but exploit is not contributing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and exploration opportunities."
  }
}
```
2025-06-26 17:39:22,659 - __main__ - INFO - 评估阶段完成
2025-06-26 17:39:22,659 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.375,
      "average_delta": -5107.62,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -3416.5,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore is effective but exploit is not contributing",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and exploration opportunities."
  }
}
```
2025-06-26 17:39:22,660 - __main__ - INFO - 当前最佳适应度: 29091.0
2025-06-26 17:39:22,662 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_route_0.pkl
2025-06-26 17:39:22,662 - __main__ - INFO - composite4_33 开始进化第 2 代
2025-06-26 17:39:22,663 - __main__ - INFO - 开始分析阶段
2025-06-26 17:39:22,663 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:39:22,667 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 29091.0, 'max': 57918.0, 'mean': 40095.6, 'std': 10881.392421928364}, 'diversity': 0.7481481481481481, 'clusters': {'clusters': 7, 'cluster_sizes': [3, 1, 1, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:39:22,669 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 29091.0, 'max': 57918.0, 'mean': 40095.6, 'std': 10881.392421928364}, 'diversity_level': 0.7481481481481481, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [3, 1, 1, 2, 1, 1, 1]}, 'coordinates': [[5248, 4841], [5246, 4845], [5240, 4845], [5238, 4841], [5240, 4837], [5246, 4837], [5251, 4849], [5235, 4849], [5235, 4833], [5251, 4833], [6102, 3756], [6097, 3765], [6087, 3765], [6082, 3756], [6087, 3747], [6097, 3747], [6095, 3760], [6089, 3760], [6089, 3752], [6095, 3752], [2386, 3819], [2386, 3860], [2425, 3832], [2410, 3786], [2362, 3786], [2347, 3832], [5121, 4369], [5161, 4369], [5141, 4404], [5101, 4404], [5081, 4369], [5101, 4334], [5141, 4334]], 'distance_matrix': array([[  0.,   4.,   9., ..., 501., 528., 518.],
       [  4.,   0.,   6., ..., 504., 531., 522.],
       [  9.,   6.,   0., ..., 502., 530., 521.],
       ...,
       [501., 504., 502., ...,   0.,  40.,  69.],
       [528., 531., 530., ...,  40.,   0.,  40.],
       [518., 522., 521., ...,  69.,  40.,   0.]])}
2025-06-26 17:39:22,669 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:39:22,669 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:39:22,669 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:39:22,671 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:39:22,671 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:39:22,671 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:39:22,671 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:39:22,671 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:39:22,672 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:39:22,698 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:39:22,698 - __main__ - INFO - 精英专家分析报告: {'elite_count': 61, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 20330.0, 'avg_gap': 31333.1737704918}, 'structure_gap': {'unique_elite_edges': 91, 'unique_pop_edges': 175, 'common_edges': 19}}, 'elite_diversity': {'diversity_score': 0.2712369597615498}}
2025-06-26 17:39:22,698 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:39:22,698 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:39:22,702 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:39:22,702 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 29091.0, mean 40095.6, max 57918.0, std 10881.392421928364
- diversity: 0.7481481481481481
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [16252.0, 16252.0, 29091.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [2347.0, 3747.0, 6102.0, 4849.0]
- centroid/spread: [4954.939, 4226.303] | rms 1356.193
- density_grid(3x3): [[6, 0, 10], [0, 0, 7], [0, 0, 10]]
- edge_len_stats(min,q1,med,q3,max): [4.0, 449.75, 1191.5, 2812.25, 3756.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 4, 6, 1, 7, 1, 6, 1]
- nn_median_dist: 6.403

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:39:22,702 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:39:45,623 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 4.0 to 3756.0, std dev ~10881)",
    "modality": "Potentially multimodal (direction histogram shows uneven sector distribution)",
    "deceptiveness": "Possible due to long_edge_ratio (25%) and uneven spatial distribution"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows clusters in NW/NE/SW corners, empty central regions)",
    "convergence_status": "Early stage (diversity=0.75, convergence=0.0)",
    "elite_influence": "Minimal (0 elites, only 1 fixed node)"
  },
  "difficult_regions": [
    "Central grid cells (empty in density_grid)",
    "Edges >2812.25 length (q3 threshold)",
    "Direction sectors with low counts (sectors 3,5,7)"
  ],
  "opportunity_regions": [
    "NW/SW grid cells (high density=10)",
    "Short edges (<449.75 length, q1 threshold)",
    "Frequently occurring direction sectors (0,2,4,6)"
  ],
  "evolution_phase": "Exploration (high diversity, no convergence)",
  "evolution_direction": [
    "Prioritize crossover that preserves short edges (<q1)",
    "Apply directed mutation toward high-density cells",
    "Introduce path refinement in difficult central regions",
    "Enhance diversity in underrepresented direction sectors"
  ]
}
```
2025-06-26 17:39:45,623 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:39:45,623 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0, std dev ~10881)', 'modality': 'Potentially multimodal (direction histogram shows uneven sector distribution)', 'deceptiveness': 'Possible due to long_edge_ratio (25%) and uneven spatial distribution'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in NW/NE/SW corners, empty central regions)', 'convergence_status': 'Early stage (diversity=0.75, convergence=0.0)', 'elite_influence': 'Minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges >2812.25 length (q3 threshold)', 'Direction sectors with low counts (sectors 3,5,7)'], 'opportunity_regions': ['NW/SW grid cells (high density=10)', 'Short edges (<449.75 length, q1 threshold)', 'Frequently occurring direction sectors (0,2,4,6)'], 'evolution_phase': 'Exploration (high diversity, no convergence)', 'evolution_direction': ['Prioritize crossover that preserves short edges (<q1)', 'Apply directed mutation toward high-density cells', 'Introduce path refinement in difficult central regions', 'Enhance diversity in underrepresented direction sectors']}
2025-06-26 17:39:45,623 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:39:45,625 - __main__ - INFO - 分析阶段完成
2025-06-26 17:39:45,625 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0, std dev ~10881)', 'modality': 'Potentially multimodal (direction histogram shows uneven sector distribution)', 'deceptiveness': 'Possible due to long_edge_ratio (25%) and uneven spatial distribution'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in NW/NE/SW corners, empty central regions)', 'convergence_status': 'Early stage (diversity=0.75, convergence=0.0)', 'elite_influence': 'Minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges >2812.25 length (q3 threshold)', 'Direction sectors with low counts (sectors 3,5,7)'], 'opportunity_regions': ['NW/SW grid cells (high density=10)', 'Short edges (<449.75 length, q1 threshold)', 'Frequently occurring direction sectors (0,2,4,6)'], 'evolution_phase': 'Exploration (high diversity, no convergence)', 'evolution_direction': ['Prioritize crossover that preserves short edges (<q1)', 'Apply directed mutation toward high-density cells', 'Introduce path refinement in difficult central regions', 'Enhance diversity in underrepresented direction sectors']}
2025-06-26 17:39:45,625 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:39:45,625 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:39:45,625 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 29091.0
  • mean_cost: 40095.6
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 4.0 to 3756.0, std dev ~10881), modality Potentially multimodal (direction histogram shows uneven sector distribution), deceptiveness Possible due to long_edge_ratio (25%) and uneven spatial distribution
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Central grid cells (, Edges >2812.25 lengt...
- Opportunity regions (# 3): NW/SW grid cells (hi, Short edges (<449.75...
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.375,
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:39:45,625 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:39:45,625 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:39:56,269 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests exploration. Top 30% individuals exploit opportunity regions, others explore rugged areas."
}
```
2025-06-26 17:39:56,269 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:39:56,277 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:39:56,277 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests exploration. Top 30% individuals exploit opportunity regions, others explore rugged areas."
}
```
2025-06-26 17:39:56,277 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:39:56,277 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:39:56,277 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests exploration. Top 30% individuals exploit opportunity regions, others explore rugged areas."
}
```
2025-06-26 17:39:56,277 - __main__ - INFO - 开始进化阶段
2025-06-26 17:39:56,277 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:39:56,277 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:39:56,277 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 33883.0 (rank 40.0%)
- Population diversity: 0.50625
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:39:56,280 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:39:56,280 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:40:07,212 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32],
  "targeted_regions": "NW/SW grid cells, short edges",
  "strategy_comment": "Leverage grid symmetry for diversity while avoiding central regions"
}
```
2025-06-26 17:40:07,212 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:40:07,212 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60240.0, 路径: [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32]
2025-06-26 17:40:07,212 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 60240.0}
2025-06-26 17:40:07,212 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:40:07,212 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:40:07,212 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 31880.0 (rank 20.0%)
- Population diversity: 0.58125
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:40:07,212 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:40:07,212 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:40:18,410 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32],
  "targeted_regions": "NW/SW grid cell, Short edges (<4)",
  "strategy_comment": "Prioritize NW/SW regions with short edges"
}
```
2025-06-26 17:40:18,410 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:40:18,410 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9067.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]
2025-06-26 17:40:18,410 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 9067.0}
2025-06-26 17:40:18,410 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:40:18,410 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:40:18,410 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34575.0 (rank 40.0%)
- Population diversity: 0.621875
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:40:18,410 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:40:18,410 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:40:30,002 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32],
  "targeted_regions": "NW/SW grid cells, short edges",
  "strategy_comment": "Prioritize sparse NW/SW nodes with low-cost connections while avoiding central traps"
}
```
2025-06-26 17:40:30,002 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:40:30,002 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60630.0, 路径: [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32]
2025-06-26 17:40:30,002 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32], 'cur_cost': 60630.0}
2025-06-26 17:40:30,002 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:40:30,002 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:40:30,002 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 57895.0 (rank 60.0%)
- Population diversity: 0.625
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:40:30,002 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:40:30,002 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:40:41,211 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17],
  "targeted_regions": "NW/SW grid cells, short edges",
  "strategy_comment": "Focus on sparse NW/SW cells with short hops, avoiding central dense areas"
}
```
2025-06-26 17:40:41,211 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:40:41,217 - ExplorationExpert - INFO - 探索路径生成完成，成本: 50317.0, 路径: [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17]
2025-06-26 17:40:41,217 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17], 'cur_cost': 50317.0}
2025-06-26 17:40:41,217 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:40:41,217 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:40:41,218 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29096.0 (rank 20.0%)
- Population diversity: 0.6625
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:40:41,218 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:40:41,218 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:40:52,602 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29],
  "targeted_regions": "NW/SW grid cells, short edges",
  "strategy_comment": "Prioritize sparse NW/SW cells with short edges, avoid central dense regions"
}
```
2025-06-26 17:40:52,602 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:40:52,602 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58640.0, 路径: [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29]
2025-06-26 17:40:52,602 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29], 'cur_cost': 58640.0}
2025-06-26 17:40:52,602 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:40:52,602 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:40:52,602 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 32874.0 (rank 20.0%)
- Population diversity: 0.6875
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:40:52,602 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:40:52,602 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:41:03,418 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29],
  "targeted_regions": "NW/SW grid cells, short edges",
  "strategy_comment": "Leverage grid patterns in low-density regions"
}
```
2025-06-26 17:41:03,418 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:41:03,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41987.0, 路径: [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29]
2025-06-26 17:41:03,418 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}
2025-06-26 17:41:03,421 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:41:03,421 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:41:03,421 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29091.0 (rank 10.0%)
- Population diversity: 0.71875
- Evolution phase: Exploration (high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): NW/SW grid cell, Short edges (<4...
- Difficult regions to avoid (sample): Central grid ce, Edges >2812.25 ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:41:03,421 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:41:03,422 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:41:15,244 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29],
  "targeted_regions": "NW/SW grid cell, Short edges",
  "strategy_comment": "Leverage grid pattern for sparse regions, minimize long edges"
}
```
2025-06-26 17:41:15,244 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:41:15,252 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41987.0, 路径: [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29]
2025-06-26 17:41:15,253 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}
2025-06-26 17:41:15,253 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:41:15,253 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:41:15,253 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:41:15,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 50156.0
2025-06-26 17:41:15,754 - ExploitationExpert - INFO - res_population_num: 64
2025-06-26 17:41:15,755 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778, 8761, 8761, 8761]
2025-06-26 17:41:15,755 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-06-26 17:41:15,773 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:41:15,773 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 60240.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 9067.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32], 'cur_cost': 60630.0}, {'tour': [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17], 'cur_cost': 50317.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29], 'cur_cost': 58640.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': array([10, 21, 15,  4,  3,  0, 11, 12, 29,  7, 22, 26, 20,  9, 28, 31, 18,
        8, 19, 16, 25,  5,  2,  1, 27, 30, 24, 17, 14, 23, 32,  6, 13]), 'cur_cost': 50156.0}, {'tour': array([10,  7, 15,  1, 11, 29,  0, 25, 20, 13, 12,  9, 18, 19, 30, 32,  5,
       28,  8, 26, 23, 22, 27, 21, 16,  4,  2, 17, 14, 31, 24,  3,  6]), 'cur_cost': 43202.0}, {'tour': array([13,  9,  4, 12, 21, 30, 32,  7,  3, 27,  8, 22, 23, 14, 24, 26,  5,
       11, 28, 15, 16, 31, 10,  2, 17, 29,  6, 25,  1, 20, 19,  0, 18]), 'cur_cost': 50542.0}]
2025-06-26 17:41:15,775 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:41:15,775 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 167, 'skip_rate': 0.05389221556886228, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 158, 'cache_hits': 172, 'similarity_calculations': 3188, 'cache_hit_rate': 0.053952321204516936, 'cache_size': 3016}}
2025-06-26 17:41:15,775 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:41:15,775 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:41:15,775 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:41:15,775 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:41:15,777 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 45878.0
2025-06-26 17:41:16,279 - ExploitationExpert - INFO - res_population_num: 64
2025-06-26 17:41:16,279 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778, 8761, 8761, 8761]
2025-06-26 17:41:16,279 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-06-26 17:41:16,299 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:41:16,299 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 60240.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 9067.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32], 'cur_cost': 60630.0}, {'tour': [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17], 'cur_cost': 50317.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29], 'cur_cost': 58640.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': array([10, 21, 15,  4,  3,  0, 11, 12, 29,  7, 22, 26, 20,  9, 28, 31, 18,
        8, 19, 16, 25,  5,  2,  1, 27, 30, 24, 17, 14, 23, 32,  6, 13]), 'cur_cost': 50156.0}, {'tour': array([24, 11, 16, 26, 12, 23, 22, 30, 25,  8, 29, 21, 10, 17,  0, 28,  9,
        3,  5, 31,  1,  2, 14,  4, 15, 19, 18,  7, 27,  6, 32, 20, 13]), 'cur_cost': 45878.0}, {'tour': array([13,  9,  4, 12, 21, 30, 32,  7,  3, 27,  8, 22, 23, 14, 24, 26,  5,
       11, 28, 15, 16, 31, 10,  2, 17, 29,  6, 25,  1, 20, 19,  0, 18]), 'cur_cost': 50542.0}]
2025-06-26 17:41:16,301 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:41:16,301 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 168, 'skip_rate': 0.05357142857142857, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 159, 'cache_hits': 172, 'similarity_calculations': 3191, 'cache_hit_rate': 0.05390159824506424, 'cache_size': 3019}}
2025-06-26 17:41:16,301 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:41:16,301 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:41:16,301 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:41:16,301 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:41:16,301 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 57075.0
2025-06-26 17:41:16,802 - ExploitationExpert - INFO - res_population_num: 67
2025-06-26 17:41:16,803 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778, 8761, 8761, 8761, 8761, 8761, 8761]
2025-06-26 17:41:16,803 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64)]
2025-06-26 17:41:16,821 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:41:16,823 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 60240.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 9067.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32], 'cur_cost': 60630.0}, {'tour': [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17], 'cur_cost': 50317.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29], 'cur_cost': 58640.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': array([10, 21, 15,  4,  3,  0, 11, 12, 29,  7, 22, 26, 20,  9, 28, 31, 18,
        8, 19, 16, 25,  5,  2,  1, 27, 30, 24, 17, 14, 23, 32,  6, 13]), 'cur_cost': 50156.0}, {'tour': array([24, 11, 16, 26, 12, 23, 22, 30, 25,  8, 29, 21, 10, 17,  0, 28,  9,
        3,  5, 31,  1,  2, 14,  4, 15, 19, 18,  7, 27,  6, 32, 20, 13]), 'cur_cost': 45878.0}, {'tour': array([14, 29,  3, 27, 28, 15, 10,  7, 16,  2,  1,  5, 20,  4, 13, 24, 17,
        6, 12,  9,  8, 23, 11, 30, 25, 19, 32, 21,  0, 31, 22, 18, 26]), 'cur_cost': 57075.0}]
2025-06-26 17:41:16,824 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:41:16,824 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 169, 'skip_rate': 0.05325443786982249, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 160, 'cache_hits': 172, 'similarity_calculations': 3195, 'cache_hit_rate': 0.05383411580594679, 'cache_size': 3023}}
2025-06-26 17:41:16,825 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:41:16,825 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 11, 23, 10, 22, 9, 21, 8, 20, 7, 19, 6, 18, 5, 17, 4, 16, 3, 15, 2, 14, 1, 13, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 60240.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], 'cur_cost': 9067.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 24, 30, 1, 14, 27, 8, 20, 31, 4, 17, 25, 10, 21, 6, 28, 13, 2, 19, 29, 11, 23, 16, 26, 32], 'cur_cost': 60630.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 25, 30, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 27, 21, 14, 7, 2, 8, 15, 22, 28, 32, 29, 23, 16, 9, 3, 10, 17], 'cur_cost': 50317.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 31, 24, 17, 8, 2, 11, 25, 30, 20, 13, 6, 19, 23, 10, 4, 16, 28, 32, 21, 14, 7, 1, 26, 29], 'cur_cost': 58640.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}}]
2025-06-26 17:41:16,825 - __main__ - INFO - 进化阶段完成
2025-06-26 17:41:16,825 - __main__ - INFO - 开始评估阶段
2025-06-26 17:41:16,826 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:41:16,826 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:41:16,827 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 29091.0 → 9067.0 (Δ 20024.0)
- Mean cost: 40095.6 → 47597.7
- Diversity: 0.506 → 0.662 (Δ 0.1562)

#### Elite metrics
- Elite count: 61 → 67
- Best elite cost: 8761 → 8761 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -10510.57
- Exploit: 3 inds, success 33.3% , avg Δ -482.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [16252.0, 16252.0, 29091.0], diversity [0.8636363636363634, 0.8727272727272726, 0.7481481481481481]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:41:16,828 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:41:33,073 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-phase with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -10510.57,
      "contribution_to_elite": "moderate (led to significant cost reductions but low success rate)"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -482.33,
      "contribution_to_elite": "minimal (small improvements but maintains elite stability)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore is too aggressive (high failure rate), exploit is underutilized for refinement"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, add small mutation rate to exploit operators"
  }
}
```
2025-06-26 17:41:33,079 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:41:33,081 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-phase with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -10510.57,
      "contribution_to_elite": "moderate (led to significant cost reductions but low success rate)"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -482.33,
      "contribution_to_elite": "minimal (small improvements but maintains elite stability)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore is too aggressive (high failure rate), exploit is underutilized for refinement"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, add small mutation rate to exploit operators"
  }
}
```
2025-06-26 17:41:33,081 - __main__ - INFO - 评估阶段完成
2025-06-26 17:41:33,081 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-phase with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -10510.57,
      "contribution_to_elite": "moderate (led to significant cost reductions but low success rate)"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -482.33,
      "contribution_to_elite": "minimal (small improvements but maintains elite stability)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore is too aggressive (high failure rate), exploit is underutilized for refinement"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, add small mutation rate to exploit operators"
  }
}
```
2025-06-26 17:41:33,082 - __main__ - INFO - 当前最佳适应度: 9067.0
2025-06-26 17:41:33,084 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_route_1.pkl
2025-06-26 17:41:33,084 - __main__ - INFO - composite4_33 开始进化第 3 代
2025-06-26 17:41:33,084 - __main__ - INFO - 开始分析阶段
2025-06-26 17:41:33,084 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:41:33,090 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9067.0, 'max': 60630.0, 'mean': 47597.7, 'std': 14511.691176771921}, 'diversity': 0.8983164983164983, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 1, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:41:33,091 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9067.0, 'max': 60630.0, 'mean': 47597.7, 'std': 14511.691176771921}, 'diversity_level': 0.8983164983164983, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 1, 2, 1, 1, 1]}, 'coordinates': [[5248, 4841], [5246, 4845], [5240, 4845], [5238, 4841], [5240, 4837], [5246, 4837], [5251, 4849], [5235, 4849], [5235, 4833], [5251, 4833], [6102, 3756], [6097, 3765], [6087, 3765], [6082, 3756], [6087, 3747], [6097, 3747], [6095, 3760], [6089, 3760], [6089, 3752], [6095, 3752], [2386, 3819], [2386, 3860], [2425, 3832], [2410, 3786], [2362, 3786], [2347, 3832], [5121, 4369], [5161, 4369], [5141, 4404], [5101, 4404], [5081, 4369], [5101, 4334], [5141, 4334]], 'distance_matrix': array([[  0.,   4.,   9., ..., 501., 528., 518.],
       [  4.,   0.,   6., ..., 504., 531., 522.],
       [  9.,   6.,   0., ..., 502., 530., 521.],
       ...,
       [501., 504., 502., ...,   0.,  40.,  69.],
       [528., 531., 530., ...,  40.,   0.,  40.],
       [518., 522., 521., ...,  69.,  40.,   0.]])}
2025-06-26 17:41:33,091 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:41:33,091 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:41:33,092 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:41:33,093 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:41:33,093 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:41:33,093 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:41:33,093 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:41:33,093 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:41:33,093 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:41:33,125 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:41:33,125 - __main__ - INFO - 精英专家分析报告: {'elite_count': 67, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 306.0, 'avg_gap': 38835.40149253731}, 'structure_gap': {'unique_elite_edges': 65, 'unique_pop_edges': 200, 'common_edges': 45}}, 'elite_diversity': {'diversity_score': 0.2703150912106136}}
2025-06-26 17:41:33,125 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:41:33,125 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:41:33,129 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:41:33,129 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 9067.0, mean 47597.7, max 60630.0, std 14511.691176771921
- diversity: 0.8983164983164983
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [16252.0, 29091.0, 9067.0] (Δ 10012.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [2347.0, 3747.0, 6102.0, 4849.0]
- centroid/spread: [4954.939, 4226.303] | rms 1356.193
- density_grid(3x3): [[6, 0, 10], [0, 0, 7], [0, 0, 10]]
- edge_len_stats(min,q1,med,q3,max): [4.0, 449.75, 1191.5, 2812.25, 3756.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 4, 6, 1, 7, 1, 6, 1]
- nn_median_dist: 6.403

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:41:33,129 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:41:56,718 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 4.0 to 3756.0, with significant quartile differences)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Partial (density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (diversity high at 0.898, convergence at 0.0)",
    "quality_spread": "Wide (cost_stats show large min-max range with high std)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max length quartile (2812.25-3756.0)",
    "low_density_areas": "Empty grid cells (particularly center and bottom-left regions)"
  },
  "opportunity_regions": {
    "high_density_cells": "Top-right and bottom-right grid cells (containing 10 nodes each)",
    "short_edge_clusters": "Edges in min length quartile (4.0-449.75)"
  },
  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells",
    "Use edge recombination operators focusing on short edges",
    "Introduce local search around promising regions (top-right cluster)",
    "Maintain diversity through mutation in low-density areas",
    "Monitor long edges for potential path refinement"
  ]
}
```
2025-06-26 17:41:56,720 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:41:56,720 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 4.0 to 3756.0, with significant quartile differences)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Partial (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity high at 0.898, convergence at 0.0)', 'quality_spread': 'Wide (cost_stats show large min-max range with high std)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (2812.25-3756.0)', 'low_density_areas': 'Empty grid cells (particularly center and bottom-left regions)'}, 'opportunity_regions': {'high_density_cells': 'Top-right and bottom-right grid cells (containing 10 nodes each)', 'short_edge_clusters': 'Edges in min length quartile (4.0-449.75)'}, 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Prioritize exploitation in high-density cells', 'Use edge recombination operators focusing on short edges', 'Introduce local search around promising regions (top-right cluster)', 'Maintain diversity through mutation in low-density areas', 'Monitor long edges for potential path refinement']}
2025-06-26 17:41:56,720 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:41:56,720 - __main__ - INFO - 分析阶段完成
2025-06-26 17:41:56,720 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 4.0 to 3756.0, with significant quartile differences)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Partial (density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (diversity high at 0.898, convergence at 0.0)', 'quality_spread': 'Wide (cost_stats show large min-max range with high std)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (2812.25-3756.0)', 'low_density_areas': 'Empty grid cells (particularly center and bottom-left regions)'}, 'opportunity_regions': {'high_density_cells': 'Top-right and bottom-right grid cells (containing 10 nodes each)', 'short_edge_clusters': 'Edges in min length quartile (4.0-449.75)'}, 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Prioritize exploitation in high-density cells', 'Use edge recombination operators focusing on short edges', 'Introduce local search around promising regions (top-right cluster)', 'Maintain diversity through mutation in low-density areas', 'Monitor long edges for potential path refinement']}
2025-06-26 17:41:56,720 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:41:56,720 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:41:56,722 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9067.0
  • mean_cost: 47597.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 4.0 to 3756.0, with significant quartile differences), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges)
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_areas
- Opportunity regions (# 2): high_density_cells, short_edge_clusters
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-phase with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:41:56,722 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:41:56,722 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:42:07,983 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit opportunity regions (high_density_cells, short_edge_clusters)."
}
```
2025-06-26 17:42:07,983 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:42:07,983 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:42:07,983 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit opportunity regions (high_density_cells, short_edge_clusters)."
}
```
2025-06-26 17:42:07,983 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:42:07,983 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:42:07,983 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit opportunity regions (high_density_cells, short_edge_clusters)."
}
```
2025-06-26 17:42:07,985 - __main__ - INFO - 开始进化阶段
2025-06-26 17:42:07,985 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:42:07,985 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:42:07,985 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60240.0 (rank 80.0%)
- Population diversity: 0.6625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:42:07,985 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:42:07,985 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:42:18,605 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Alternating dense clusters with sparse adjacency"
}
```
2025-06-26 17:42:18,605 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:42:18,605 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16711.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:42:18,605 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16711.0}
2025-06-26 17:42:18,605 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:42:18,605 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:42:18,605 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9067.0 (rank 0.0%)
- Population diversity: 0.7125
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:42:18,605 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:42:18,605 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:42:29,796 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Cluster hops in dense regions, sparse adjacency links"
}
```
2025-06-26 17:42:29,800 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:42:29,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37901.0, 路径: [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32]
2025-06-26 17:42:29,800 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32], 'cur_cost': 37901.0}
2025-06-26 17:42:29,800 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:42:29,802 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:42:29,802 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60630.0 (rank 90.0%)
- Population diversity: 0.68125
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:42:29,802 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:42:29,802 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:42:40,997 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage grid pattern to explore sparse cells near opportunities"
}
```
2025-06-26 17:42:40,997 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:42:40,997 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41987.0, 路径: [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29]
2025-06-26 17:42:40,997 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}
2025-06-26 17:42:40,997 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:42:40,997 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:42:40,997 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 50317.0 (rank 70.0%)
- Population diversity: 0.625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:42:40,997 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:42:40,997 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:42:52,305 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, avoid long corridors"
}
```
2025-06-26 17:42:52,305 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:42:52,305 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27571.0, 路径: [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32]
2025-06-26 17:42:52,305 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32], 'cur_cost': 27571.0}
2025-06-26 17:42:52,305 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:42:52,305 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:42:52,305 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 58640.0 (rank 90.0%)
- Population diversity: 0.64375
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:42:52,305 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:42:52,305 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:43:03,438 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters, minimize long edges"
}
```
2025-06-26 17:43:03,438 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:43:03,438 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19286.0, 路径: [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1]
2025-06-26 17:43:03,438 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1], 'cur_cost': 19286.0}
2025-06-26 17:43:03,438 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:43:03,438 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:43:03,438 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41987.0 (rank 40.0%)
- Population diversity: 0.60625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:43:03,438 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:43:03,446 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:43:14,905 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters, avoid long edges, explore adjacent sparse cells"
}
```
2025-06-26 17:43:14,905 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:43:14,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52185.0, 路径: [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32]
2025-06-26 17:43:14,905 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32], 'cur_cost': 52185.0}
2025-06-26 17:43:14,905 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:43:14,905 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:43:14,905 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41987.0 (rank 40.0%)
- Population diversity: 0.665625
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:43:14,905 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:43:14,905 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:43:26,099 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters, avoid long edges, explore adjacent sparse cells"
}
```
2025-06-26 17:43:26,099 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:43:26,099 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54274.0, 路径: [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32]
2025-06-26 17:43:26,099 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32], 'cur_cost': 54274.0}
2025-06-26 17:43:26,099 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:43:26,103 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:43:26,103 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:43:26,103 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 46766.0
2025-06-26 17:43:26,605 - ExploitationExpert - INFO - res_population_num: 67
2025-06-26 17:43:26,605 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:43:26,605 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:43:26,624 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:43:26,624 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16711.0}, {'tour': [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32], 'cur_cost': 37901.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32], 'cur_cost': 27571.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1], 'cur_cost': 19286.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32], 'cur_cost': 52185.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32], 'cur_cost': 54274.0}, {'tour': array([ 4, 27, 22, 31, 11,  9, 29, 15, 17, 25,  1, 24, 21,  0,  8, 32,  6,
        5, 28, 16, 30,  2, 23, 26, 19, 13, 10,  3, 12, 14,  7, 18, 20]), 'cur_cost': 46766.0}, {'tour': array([24, 11, 16, 26, 12, 23, 22, 30, 25,  8, 29, 21, 10, 17,  0, 28,  9,
        3,  5, 31,  1,  2, 14,  4, 15, 19, 18,  7, 27,  6, 32, 20, 13]), 'cur_cost': 45878.0}, {'tour': array([14, 29,  3, 27, 28, 15, 10,  7, 16,  2,  1,  5, 20,  4, 13, 24, 17,
        6, 12,  9,  8, 23, 11, 30, 25, 19, 32, 21,  0, 31, 22, 18, 26]), 'cur_cost': 57075.0}]
2025-06-26 17:43:26,627 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:43:26,627 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 170, 'skip_rate': 0.052941176470588235, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 161, 'cache_hits': 172, 'similarity_calculations': 3200, 'cache_hit_rate': 0.05375, 'cache_size': 3028}}
2025-06-26 17:43:26,627 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:43:26,627 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:43:26,627 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:43:26,627 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:43:26,629 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 45533.0
2025-06-26 17:43:27,130 - ExploitationExpert - INFO - res_population_num: 68
2025-06-26 17:43:27,131 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778, 8761]
2025-06-26 17:43:27,132 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-06-26 17:43:27,155 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:43:27,155 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16711.0}, {'tour': [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32], 'cur_cost': 37901.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32], 'cur_cost': 27571.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1], 'cur_cost': 19286.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32], 'cur_cost': 52185.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32], 'cur_cost': 54274.0}, {'tour': array([ 4, 27, 22, 31, 11,  9, 29, 15, 17, 25,  1, 24, 21,  0,  8, 32,  6,
        5, 28, 16, 30,  2, 23, 26, 19, 13, 10,  3, 12, 14,  7, 18, 20]), 'cur_cost': 46766.0}, {'tour': array([29, 16,  0, 14,  2, 30, 10, 13, 22, 23, 21,  4, 18,  8,  9, 32,  5,
        7, 28, 25, 12,  3, 27, 31, 15, 11,  6, 20,  1, 26, 17, 24, 19]), 'cur_cost': 45533.0}, {'tour': array([14, 29,  3, 27, 28, 15, 10,  7, 16,  2,  1,  5, 20,  4, 13, 24, 17,
        6, 12,  9,  8, 23, 11, 30, 25, 19, 32, 21,  0, 31, 22, 18, 26]), 'cur_cost': 57075.0}]
2025-06-26 17:43:27,156 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:43:27,157 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 171, 'skip_rate': 0.05263157894736842, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 162, 'cache_hits': 172, 'similarity_calculations': 3206, 'cache_hit_rate': 0.053649407361197755, 'cache_size': 3034}}
2025-06-26 17:43:27,157 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:43:27,158 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:43:27,158 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:43:27,158 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:43:27,159 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 45740.0
2025-06-26 17:43:27,660 - root - WARNING - 无法找到足够的不重叠段 (找到 1/3)，使用退化策略
2025-06-26 17:43:27,661 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:43:27,661 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778, 8761, 8761, 8761]
2025-06-26 17:43:27,662 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64)]
2025-06-26 17:43:27,681 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:43:27,681 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16711.0}, {'tour': [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32], 'cur_cost': 37901.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32], 'cur_cost': 27571.0}, {'tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1], 'cur_cost': 19286.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32], 'cur_cost': 52185.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32], 'cur_cost': 54274.0}, {'tour': array([ 4, 27, 22, 31, 11,  9, 29, 15, 17, 25,  1, 24, 21,  0,  8, 32,  6,
        5, 28, 16, 30,  2, 23, 26, 19, 13, 10,  3, 12, 14,  7, 18, 20]), 'cur_cost': 46766.0}, {'tour': array([29, 16,  0, 14,  2, 30, 10, 13, 22, 23, 21,  4, 18,  8,  9, 32,  5,
        7, 28, 25, 12,  3, 27, 31, 15, 11,  6, 20,  1, 26, 17, 24, 19]), 'cur_cost': 45533.0}, {'tour': array([ 6,  4, 12, 21, 20,  9, 14, 19, 27, 23,  1, 13,  7, 31,  0,  5, 16,
        8, 28, 32, 17, 11, 10, 30,  2, 22, 15, 18, 25, 26, 24, 29,  3]), 'cur_cost': 45740.0}]
2025-06-26 17:43:27,683 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:43:27,684 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 172, 'skip_rate': 0.05232558139534884, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 163, 'cache_hits': 172, 'similarity_calculations': 3213, 'cache_hit_rate': 0.053532524120759414, 'cache_size': 3041}}
2025-06-26 17:43:27,684 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:43:27,684 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16711.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 17, 21, 25, 29, 15, 10, 6, 1, 4, 9, 13, 18, 22, 26, 30, 16, 11, 7, 2, 14, 19, 23, 27, 31, 20, 24, 28, 32], 'cur_cost': 37901.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 1, 6, 11, 16, 21, 26, 31, 2, 7, 12, 17, 22, 27, 32, 3, 8, 13, 18, 23, 28, 4, 9, 14, 19, 24, 29], 'cur_cost': 41987.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 31, 29, 27, 24, 21, 18, 14, 10, 7, 3, 1, 2, 4, 6, 9, 11, 13, 16, 17, 20, 23, 26, 32], 'cur_cost': 27571.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 19, 22, 25, 28, 30, 32, 31, 29, 27, 26, 24, 23, 21, 20, 18, 17, 16, 14, 13, 11, 10, 9, 7, 6, 4, 3, 2, 1], 'cur_cost': 19286.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 22, 11, 25, 7, 17, 29, 1, 9, 14, 20, 24, 6, 16, 28, 2, 10, 13, 21, 26, 4, 18, 23, 27, 30, 31, 32], 'cur_cost': 52185.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 22, 7, 10, 25, 13, 17, 20, 2, 9, 24, 16, 1, 6, 21, 14, 4, 23, 11, 18, 26, 29, 27, 30, 31, 28, 32], 'cur_cost': 54274.0}}]
2025-06-26 17:43:27,684 - __main__ - INFO - 进化阶段完成
2025-06-26 17:43:27,684 - __main__ - INFO - 开始评估阶段
2025-06-26 17:43:27,684 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:43:27,686 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:43:27,686 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 9067.0 → 16711.0 (Δ -7644.0)
- Mean cost: 47597.7 → 38795.4
- Diversity: 0.662 → 0.681 (Δ 0.0188)

#### Elite metrics
- Elite count: 67 → 70
- Best elite cost: 8761 → 8761 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ 10421.86
- Exploit: 3 inds, success 100.0% , avg Δ 5023.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [16252.0, 29091.0, 9067.0], diversity [0.8727272727272726, 0.7481481481481481, 0.8983164983164983]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:43:27,686 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:43:41,012 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 10421.86,
      "contribution": "significant improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_delta": 5023.33,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "7:3 (explore:exploit)",
    "assessment": "explore is effective but could benefit from slightly more exploitation for stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing exploit attempts slightly to balance high explore variance"
  }
}
```
2025-06-26 17:43:41,021 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:43:41,021 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 10421.86,
      "contribution": "significant improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_delta": 5023.33,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "7:3 (explore:exploit)",
    "assessment": "explore is effective but could benefit from slightly more exploitation for stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing exploit attempts slightly to balance high explore variance"
  }
}
```
2025-06-26 17:43:41,022 - __main__ - INFO - 评估阶段完成
2025-06-26 17:43:41,022 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 10421.86,
      "contribution": "significant improvement but inconsistent"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_delta": 5023.33,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "7:3 (explore:exploit)",
    "assessment": "explore is effective but could benefit from slightly more exploitation for stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing exploit attempts slightly to balance high explore variance"
  }
}
```
2025-06-26 17:43:41,022 - __main__ - INFO - 当前最佳适应度: 16711.0
2025-06-26 17:43:41,024 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_route_2.pkl
2025-06-26 17:43:41,024 - __main__ - INFO - composite4_33 开始进化第 4 代
2025-06-26 17:43:41,024 - __main__ - INFO - 开始分析阶段
2025-06-26 17:43:41,025 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:43:41,030 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 16711.0, 'max': 54274.0, 'mean': 38795.4, 'std': 12575.10422382256}, 'diversity': 0.8882154882154882, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:43:41,031 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 16711.0, 'max': 54274.0, 'mean': 38795.4, 'std': 12575.10422382256}, 'diversity_level': 0.8882154882154882, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[5248, 4841], [5246, 4845], [5240, 4845], [5238, 4841], [5240, 4837], [5246, 4837], [5251, 4849], [5235, 4849], [5235, 4833], [5251, 4833], [6102, 3756], [6097, 3765], [6087, 3765], [6082, 3756], [6087, 3747], [6097, 3747], [6095, 3760], [6089, 3760], [6089, 3752], [6095, 3752], [2386, 3819], [2386, 3860], [2425, 3832], [2410, 3786], [2362, 3786], [2347, 3832], [5121, 4369], [5161, 4369], [5141, 4404], [5101, 4404], [5081, 4369], [5101, 4334], [5141, 4334]], 'distance_matrix': array([[  0.,   4.,   9., ..., 501., 528., 518.],
       [  4.,   0.,   6., ..., 504., 531., 522.],
       [  9.,   6.,   0., ..., 502., 530., 521.],
       ...,
       [501., 504., 502., ...,   0.,  40.,  69.],
       [528., 531., 530., ...,  40.,   0.,  40.],
       [518., 522., 521., ...,  69.,  40.,   0.]])}
2025-06-26 17:43:41,031 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:43:41,031 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:43:41,032 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:43:41,034 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:43:41,034 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:43:41,034 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:43:41,034 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:43:41,034 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:43:41,034 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:43:41,070 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:43:41,072 - __main__ - INFO - 精英专家分析报告: {'elite_count': 70, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 7950.0, 'avg_gap': 30033.157142857144}, 'structure_gap': {'unique_elite_edges': 67, 'unique_pop_edges': 214, 'common_edges': 43}}, 'elite_diversity': {'diversity_score': 0.2690758516845473}}
2025-06-26 17:43:41,072 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:43:41,072 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:43:41,073 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:43:41,073 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 16711.0, mean 38795.4, max 54274.0, std 12575.10422382256
- diversity: 0.8882154882154882
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [29091.0, 9067.0, 16711.0] (Δ 10012.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [2347.0, 3747.0, 6102.0, 4849.0]
- centroid/spread: [4954.939, 4226.303] | rms 1356.193
- density_grid(3x3): [[6, 0, 10], [0, 0, 7], [0, 0, 10]]
- edge_len_stats(min,q1,med,q3,max): [4.0, 449.75, 1191.5, 2812.25, 3756.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 4, 6, 1, 7, 1, 6, 1]
- nn_median_dist: 6.403

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:43:41,073 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:44:05,380 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 4.0 to 3756.0, Q3 at 2812.25 suggests significant jumps)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations, no clear dominance in any sector)",
    "deceptiveness": "Possible deceptive attractors (long_edge_ratio of 0.25 indicates some regions may mislead local search)"
  },
  "population_state": {
    "coverage": "Incomplete (density_grid shows empty cells, particularly in center-left and bottom-left regions)",
    "convergence": "Early-stage (diversity 0.89, convergence 0.0, recent best costs show large Δ but no stagnation)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in top-right density_grid cell with 10 nodes)",
    "Sparse regions (empty cells in grid, especially center-left)"
  ],
  "opportunity_regions": [
    "High-density clusters (top-left and bottom-right grid cells with 6 and 10 nodes respectively)",
    "Short-edge neighborhoods (low edge_len_stats quartiles suggest exploitable local structures)"
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Prioritize crossover operators that preserve high-density clusters (e.g. edge-assembly crossover)",
    "Apply local search (2-opt) selectively in regions with short median edge lengths",
    "Introduce directed mutations to penetrate empty grid cells (e.g. node insertion near centroid)",
    "Monitor direction histogram for emerging alignment patterns to guide search"
  ]
}
```
2025-06-26 17:44:05,380 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:44:05,380 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0, Q3 at 2812.25 suggests significant jumps)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive attractors (long_edge_ratio of 0.25 indicates some regions may mislead local search)'}, 'population_state': {'coverage': 'Incomplete (density_grid shows empty cells, particularly in center-left and bottom-left regions)', 'convergence': 'Early-stage (diversity 0.89, convergence 0.0, recent best costs show large Δ but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in top-right density_grid cell with 10 nodes)', 'Sparse regions (empty cells in grid, especially center-left)'], 'opportunity_regions': ['High-density clusters (top-left and bottom-right grid cells with 6 and 10 nodes respectively)', 'Short-edge neighborhoods (low edge_len_stats quartiles suggest exploitable local structures)'], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Prioritize crossover operators that preserve high-density clusters (e.g. edge-assembly crossover)', 'Apply local search (2-opt) selectively in regions with short median edge lengths', 'Introduce directed mutations to penetrate empty grid cells (e.g. node insertion near centroid)', 'Monitor direction histogram for emerging alignment patterns to guide search']}
2025-06-26 17:44:05,380 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:44:05,380 - __main__ - INFO - 分析阶段完成
2025-06-26 17:44:05,380 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0, Q3 at 2812.25 suggests significant jumps)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive attractors (long_edge_ratio of 0.25 indicates some regions may mislead local search)'}, 'population_state': {'coverage': 'Incomplete (density_grid shows empty cells, particularly in center-left and bottom-left regions)', 'convergence': 'Early-stage (diversity 0.89, convergence 0.0, recent best costs show large Δ but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in top-right density_grid cell with 10 nodes)', 'Sparse regions (empty cells in grid, especially center-left)'], 'opportunity_regions': ['High-density clusters (top-left and bottom-right grid cells with 6 and 10 nodes respectively)', 'Short-edge neighborhoods (low edge_len_stats quartiles suggest exploitable local structures)'], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Prioritize crossover operators that preserve high-density clusters (e.g. edge-assembly crossover)', 'Apply local search (2-opt) selectively in regions with short median edge lengths', 'Introduce directed mutations to penetrate empty grid cells (e.g. node insertion near centroid)', 'Monitor direction histogram for emerging alignment patterns to guide search']}
2025-06-26 17:44:05,380 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:44:05,382 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:44:05,382 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 16711.0
  • mean_cost: 38795.4
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 4.0 to 3756.0, Q3 at 2812.25 suggests significant jumps), modality Potentially multimodal (direction histogram shows varied edge orientations, no clear dominance in any sector), deceptiveness Possible deceptive attractors (long_edge_ratio of 0.25 indicates some regions may mislead local search)
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Sparse regions (empt
- Opportunity regions (# 2): High-density cluster, Short-edge neighborh
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:44:05,382 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:44:05,382 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:44:16,538 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 17:44:16,538 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:44:16,538 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:44:16,538 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 17:44:16,543 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:44:16,543 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:44:16,543 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 17:44:16,543 - __main__ - INFO - 开始进化阶段
2025-06-26 17:44:16,544 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:44:16,544 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:44:16,544 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16711.0 (rank 0.0%)
- Population diversity: 0.68125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:44:16,544 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:44:16,544 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:44:27,971 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32],
  "targeted_regions": "High-density cl, Short-edge neig, adjacent sparse",
  "strategy_comment": "Cluster hops in dense regions, short edges, sparse adjacency"
}
```
2025-06-26 17:44:27,980 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:44:27,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52258.0, 路径: [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32]
2025-06-26 17:44:27,981 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}
2025-06-26 17:44:27,982 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:44:27,982 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:44:27,982 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 37901.0 (rank 20.0%)
- Population diversity: 0.69375
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:44:27,982 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:44:27,982 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:44:39,272 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32],
  "targeted_regions": "High-density cl, Short-edge neig",
  "strategy_comment": "Prioritize dense clusters with short edges, skip sparse regions"
}
```
2025-06-26 17:44:39,272 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:44:39,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52851.0, 路径: [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32]
2025-06-26 17:44:39,277 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}
2025-06-26 17:44:39,277 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:44:39,277 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:44:39,277 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41987.0 (rank 20.0%)
- Population diversity: 0.678125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:44:39,277 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:44:39,277 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:44:50,538 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3],
  "targeted_regions": "High-density cl, Short-edge neig, adjacent low-density",
  "strategy_comment": "Cluster hops in dense regions, short edges, explore adjacent sparse"
}
```
2025-06-26 17:44:50,538 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:44:50,546 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34731.0, 路径: [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3]
2025-06-26 17:44:50,546 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}
2025-06-26 17:44:50,547 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:44:50,547 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:44:50,547 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27571.0 (rank 10.0%)
- Population diversity: 0.65625
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:44:50,547 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:44:50,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:45:01,496 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32],
  "targeted_regions": "High-density cl, Short-edge neig, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 17:45:01,496 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:45:01,496 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58412.0, 路径: [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32]
2025-06-26 17:45:01,496 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}
2025-06-26 17:45:01,496 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:45:01,504 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:45:01,505 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19286.0 (rank 0.0%)
- Population diversity: 0.678125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:45:01,505 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:45:01,505 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:45:12,958 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1],
  "targeted_regions": "High-density cl, Short-edge neig, adjacent sparse",
  "strategy_comment": "Cluster hops in dense regions, short edges, explore sparse adjacents"
}
```
2025-06-26 17:45:12,961 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:45:12,961 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29571.0, 路径: [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1]
2025-06-26 17:45:12,961 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}
2025-06-26 17:45:12,964 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:45:12,973 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:45:12,974 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 52185.0 (rank 50.0%)
- Population diversity: 0.665625
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density cl, Short-edge neig
- Difficult regions to avoid (sample): Long-edge corri, Sparse regions 
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:45:12,974 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:45:12,975 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:45:24,622 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32],
  "targeted_regions": "High-density cl, Short-edge neig, adjacent sparse cells",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse areas"
}
```
2025-06-26 17:45:24,628 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:45:24,628 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60404.0, 路径: [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32]
2025-06-26 17:45:24,628 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}
2025-06-26 17:45:24,629 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 17:45:24,629 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:45:24,629 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:45:24,630 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 47552.0
2025-06-26 17:45:25,131 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:45:25,131 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:45:25,131 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:45:25,151 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:45:25,152 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}, {'tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}, {'tour': array([ 0, 12, 15,  1,  2, 11, 14, 32, 25,  8, 31, 23, 22, 18,  6, 24,  5,
       26, 29,  4,  3, 30, 28, 13,  7, 20, 17, 16, 27, 10, 21, 19,  9]), 'cur_cost': 47552.0}, {'tour': array([ 4, 27, 22, 31, 11,  9, 29, 15, 17, 25,  1, 24, 21,  0,  8, 32,  6,
        5, 28, 16, 30,  2, 23, 26, 19, 13, 10,  3, 12, 14,  7, 18, 20]), 'cur_cost': 46766.0}, {'tour': array([29, 16,  0, 14,  2, 30, 10, 13, 22, 23, 21,  4, 18,  8,  9, 32,  5,
        7, 28, 25, 12,  3, 27, 31, 15, 11,  6, 20,  1, 26, 17, 24, 19]), 'cur_cost': 45533.0}, {'tour': array([ 6,  4, 12, 21, 20,  9, 14, 19, 27, 23,  1, 13,  7, 31,  0,  5, 16,
        8, 28, 32, 17, 11, 10, 30,  2, 22, 15, 18, 25, 26, 24, 29,  3]), 'cur_cost': 45740.0}]
2025-06-26 17:45:25,153 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:45:25,154 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 173, 'skip_rate': 0.05202312138728324, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 164, 'cache_hits': 172, 'similarity_calculations': 3221, 'cache_hit_rate': 0.05339956535237504, 'cache_size': 3049}}
2025-06-26 17:45:25,154 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 17:45:25,154 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:45:25,154 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:45:25,154 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:45:25,156 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 36291.0
2025-06-26 17:45:25,657 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:45:25,659 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:45:25,659 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:45:25,678 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:45:25,678 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}, {'tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}, {'tour': array([ 0, 12, 15,  1,  2, 11, 14, 32, 25,  8, 31, 23, 22, 18,  6, 24,  5,
       26, 29,  4,  3, 30, 28, 13,  7, 20, 17, 16, 27, 10, 21, 19,  9]), 'cur_cost': 47552.0}, {'tour': array([ 6,  4, 25, 29, 26, 27, 32, 19,  0,  3, 28, 30, 20, 23,  1, 24, 22,
       12, 17,  8, 18, 31, 13, 14, 11, 16, 15,  5, 10, 21,  7,  2,  9]), 'cur_cost': 36291.0}, {'tour': array([29, 16,  0, 14,  2, 30, 10, 13, 22, 23, 21,  4, 18,  8,  9, 32,  5,
        7, 28, 25, 12,  3, 27, 31, 15, 11,  6, 20,  1, 26, 17, 24, 19]), 'cur_cost': 45533.0}, {'tour': array([ 6,  4, 12, 21, 20,  9, 14, 19, 27, 23,  1, 13,  7, 31,  0,  5, 16,
        8, 28, 32, 17, 11, 10, 30,  2, 22, 15, 18, 25, 26, 24, 29,  3]), 'cur_cost': 45740.0}]
2025-06-26 17:45:25,681 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:45:25,681 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 174, 'skip_rate': 0.05172413793103448, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 165, 'cache_hits': 172, 'similarity_calculations': 3230, 'cache_hit_rate': 0.05325077399380805, 'cache_size': 3058}}
2025-06-26 17:45:25,682 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:45:25,682 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:45:25,682 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:45:25,682 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:45:25,682 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 55063.0
2025-06-26 17:45:26,186 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:45:26,187 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:45:26,187 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:45:26,205 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:45:26,206 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}, {'tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}, {'tour': array([ 0, 12, 15,  1,  2, 11, 14, 32, 25,  8, 31, 23, 22, 18,  6, 24,  5,
       26, 29,  4,  3, 30, 28, 13,  7, 20, 17, 16, 27, 10, 21, 19,  9]), 'cur_cost': 47552.0}, {'tour': array([ 6,  4, 25, 29, 26, 27, 32, 19,  0,  3, 28, 30, 20, 23,  1, 24, 22,
       12, 17,  8, 18, 31, 13, 14, 11, 16, 15,  5, 10, 21,  7,  2,  9]), 'cur_cost': 36291.0}, {'tour': array([21, 12, 22, 15, 28, 29,  5, 32,  3,  2, 26, 23, 16, 31, 13, 10,  0,
       24,  4, 17, 14,  6,  7, 27, 19,  9,  8, 18,  1, 25, 11, 20, 30]), 'cur_cost': 55063.0}, {'tour': array([ 6,  4, 12, 21, 20,  9, 14, 19, 27, 23,  1, 13,  7, 31,  0,  5, 16,
        8, 28, 32, 17, 11, 10, 30,  2, 22, 15, 18, 25, 26, 24, 29,  3]), 'cur_cost': 45740.0}]
2025-06-26 17:45:26,208 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:45:26,208 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 175, 'skip_rate': 0.05142857142857143, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 166, 'cache_hits': 172, 'similarity_calculations': 3240, 'cache_hit_rate': 0.05308641975308642, 'cache_size': 3068}}
2025-06-26 17:45:26,208 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:45:26,208 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:45:26,209 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:45:26,209 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:45:26,209 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 52986.0
2025-06-26 17:45:26,712 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:45:26,712 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:45:26,713 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:45:26,732 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:45:26,733 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}, {'tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}, {'tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}, {'tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}, {'tour': array([ 0, 12, 15,  1,  2, 11, 14, 32, 25,  8, 31, 23, 22, 18,  6, 24,  5,
       26, 29,  4,  3, 30, 28, 13,  7, 20, 17, 16, 27, 10, 21, 19,  9]), 'cur_cost': 47552.0}, {'tour': array([ 6,  4, 25, 29, 26, 27, 32, 19,  0,  3, 28, 30, 20, 23,  1, 24, 22,
       12, 17,  8, 18, 31, 13, 14, 11, 16, 15,  5, 10, 21,  7,  2,  9]), 'cur_cost': 36291.0}, {'tour': array([21, 12, 22, 15, 28, 29,  5, 32,  3,  2, 26, 23, 16, 31, 13, 10,  0,
       24,  4, 17, 14,  6,  7, 27, 19,  9,  8, 18,  1, 25, 11, 20, 30]), 'cur_cost': 55063.0}, {'tour': array([14, 18, 13, 30,  5,  6, 15,  8, 17,  4, 26,  3, 31, 20, 32, 29, 28,
       12, 16,  9,  2, 24,  7, 27,  1,  0, 21, 10, 23, 19, 25, 11, 22]), 'cur_cost': 52986.0}]
2025-06-26 17:45:26,734 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:45:26,734 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 176, 'skip_rate': 0.05113636363636364, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 167, 'cache_hits': 172, 'similarity_calculations': 3251, 'cache_hit_rate': 0.0529067979083359, 'cache_size': 3079}}
2025-06-26 17:45:26,734 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:45:26,734 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 1, 6, 19, 23, 10, 4, 16, 28, 31, 7, 21, 26, 13, 29, 32], 'cur_cost': 52258.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 27, 30, 8, 20, 24, 11, 2, 14, 17, 25, 7, 19, 4, 13, 21, 28, 6, 16, 23, 10, 26, 1, 29, 31, 32], 'cur_cost': 52851.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 30, 31, 27, 23, 19, 15, 11, 7, 3], 'cur_cost': 34731.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 15, 3, 9, 27, 14, 20, 8, 24, 1, 17, 30, 6, 10, 25, 2, 19, 13, 28, 4, 21, 16, 29, 11, 23, 26, 31, 32], 'cur_cost': 58412.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 19, 22, 25, 28, 31, 30, 27, 24, 21, 18, 15, 10, 3, 6, 9, 13, 16, 20, 23, 26, 29, 32, 17, 14, 11, 7, 4, 2, 1], 'cur_cost': 29571.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 9, 3, 15, 21, 7, 24, 11, 2, 17, 28, 6, 13, 25, 1, 20, 8, 26, 4, 19, 29, 10, 23, 14, 27, 16, 30, 31, 32], 'cur_cost': 60404.0}}]
2025-06-26 17:45:26,736 - __main__ - INFO - 进化阶段完成
2025-06-26 17:45:26,736 - __main__ - INFO - 开始评估阶段
2025-06-26 17:45:26,736 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:45:26,737 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:45:26,737 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 16711.0 → 29571.0 (Δ -12860.0)
- Mean cost: 38795.4 → 48011.9
- Diversity: 0.681 → 0.681 (Δ 0.0)

#### Elite metrics
- Elite count: 70 → 70
- Best elite cost: 8761 → 8761 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -15431.0
- Exploit: 4 inds, success 50.0% , avg Δ 105.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [29091.0, 9067.0, 16711.0], diversity [0.7481481481481481, 0.8983164983164983, 0.8882154882154882]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:45:26,738 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:45:42,766 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -15431.0,
      "contribution": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 105.25,
      "contribution": "stable but limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore strategy is underperforming despite high potential, exploit is stable but not driving significant improvements"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_suggestions": "consider increasing mutation intensity for explore strategy to improve success rate"
  }
}
```
2025-06-26 17:45:42,778 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:45:42,778 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -15431.0,
      "contribution": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 105.25,
      "contribution": "stable but limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore strategy is underperforming despite high potential, exploit is stable but not driving significant improvements"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_suggestions": "consider increasing mutation intensity for explore strategy to improve success rate"
  }
}
```
2025-06-26 17:45:42,778 - __main__ - INFO - 评估阶段完成
2025-06-26 17:45:42,778 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -15431.0,
      "contribution": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 105.25,
      "contribution": "stable but limited improvement"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore strategy is underperforming despite high potential, exploit is stable but not driving significant improvements"
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_suggestions": "consider increasing mutation intensity for explore strategy to improve success rate"
  }
}
```
2025-06-26 17:45:42,779 - __main__ - INFO - 当前最佳适应度: 29571.0
2025-06-26 17:45:42,780 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_route_3.pkl
2025-06-26 17:45:42,780 - __main__ - INFO - composite4_33 开始进化第 5 代
2025-06-26 17:45:42,780 - __main__ - INFO - 开始分析阶段
2025-06-26 17:45:42,780 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:45:42,786 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 29571.0, 'max': 60404.0, 'mean': 48011.9, 'std': 10157.126861962492}, 'diversity': 0.8787878787878787, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:45:42,787 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 29571.0, 'max': 60404.0, 'mean': 48011.9, 'std': 10157.126861962492}, 'diversity_level': 0.8787878787878787, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[5248, 4841], [5246, 4845], [5240, 4845], [5238, 4841], [5240, 4837], [5246, 4837], [5251, 4849], [5235, 4849], [5235, 4833], [5251, 4833], [6102, 3756], [6097, 3765], [6087, 3765], [6082, 3756], [6087, 3747], [6097, 3747], [6095, 3760], [6089, 3760], [6089, 3752], [6095, 3752], [2386, 3819], [2386, 3860], [2425, 3832], [2410, 3786], [2362, 3786], [2347, 3832], [5121, 4369], [5161, 4369], [5141, 4404], [5101, 4404], [5081, 4369], [5101, 4334], [5141, 4334]], 'distance_matrix': array([[  0.,   4.,   9., ..., 501., 528., 518.],
       [  4.,   0.,   6., ..., 504., 531., 522.],
       [  9.,   6.,   0., ..., 502., 530., 521.],
       ...,
       [501., 504., 502., ...,   0.,  40.,  69.],
       [528., 531., 530., ...,  40.,   0.,  40.],
       [518., 522., 521., ...,  69.,  40.,   0.]])}
2025-06-26 17:45:42,787 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:45:42,787 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:45:42,787 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:45:42,788 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:45:42,789 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:45:42,789 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:45:42,789 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:45:42,789 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:45:42,789 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:45:42,824 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:45:42,824 - __main__ - INFO - 精英专家分析报告: {'elite_count': 70, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 20810.0, 'avg_gap': 39249.65714285715}, 'structure_gap': {'unique_elite_edges': 76, 'unique_pop_edges': 228, 'common_edges': 34}}, 'elite_diversity': {'diversity_score': 0.2690758516845473}}
2025-06-26 17:45:42,824 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:45:42,824 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:45:42,828 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:45:42,828 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 29571.0, mean 48011.9, max 60404.0, std 10157.126861962492
- diversity: 0.8787878787878787
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [9067.0, 16711.0, 29571.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [2347.0, 3747.0, 6102.0, 4849.0]
- centroid/spread: [4954.939, 4226.303] | rms 1356.193
- density_grid(3x3): [[6, 0, 10], [0, 0, 7], [0, 0, 10]]
- edge_len_stats(min,q1,med,q3,max): [4.0, 449.75, 1191.5, 2812.25, 3756.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 4, 6, 1, 7, 1, 6, 1]
- nn_median_dist: 6.403

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:45:42,828 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:46:13,019 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 1/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 17:46:13,019 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 17:46:15,021 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-06-26 17:46:45,283 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 2/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 17:46:45,283 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 17:46:47,286 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 3/3)
2025-06-26 17:47:12,716 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 4.0 to 3756.0 with Q3 at 2812.25)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations with peaks in sectors 0,2,4,6)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long connections)"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows uneven distribution with empty cells and concentrations in top-right/bottom-right)",
    "convergence_status": "Early-stage (diversity 0.88, convergence 0.0, recent best costs show improvement without stagnation)"
  },
  "difficult_regions": [
    {"type": "Sparse corridor", "location": "Central column (density_grid [*,0,*] cells)"},
    {"type": "Long-edge zone", "location": "Regions with edges >2812.25 units (Q3 threshold)"}
  ],
  "opportunity_regions": [
    {"type": "Node cluster", "location": "Top-right cell (density 10 nodes)"},
    {"type": "Directional bias", "location": "Sectors 0/2/4/6 (direction_hist peaks)"}
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Intensify exploitation in high-density cells via 2-opt local search",
    "Target long edges with edge-recombination crossover",
    "Maintain diversity through restart mechanisms in sparse regions",
    "Guide mutations using direction histogram biases"
  ]
}
```
2025-06-26 17:47:12,719 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:47:12,719 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0 with Q3 at 2812.25)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations with peaks in sectors 0,2,4,6)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long connections)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with empty cells and concentrations in top-right/bottom-right)', 'convergence_status': 'Early-stage (diversity 0.88, convergence 0.0, recent best costs show improvement without stagnation)'}, 'difficult_regions': [{'type': 'Sparse corridor', 'location': 'Central column (density_grid [*,0,*] cells)'}, {'type': 'Long-edge zone', 'location': 'Regions with edges >2812.25 units (Q3 threshold)'}], 'opportunity_regions': [{'type': 'Node cluster', 'location': 'Top-right cell (density 10 nodes)'}, {'type': 'Directional bias', 'location': 'Sectors 0/2/4/6 (direction_hist peaks)'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Intensify exploitation in high-density cells via 2-opt local search', 'Target long edges with edge-recombination crossover', 'Maintain diversity through restart mechanisms in sparse regions', 'Guide mutations using direction histogram biases']}
2025-06-26 17:47:12,719 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:47:12,719 - __main__ - INFO - 分析阶段完成
2025-06-26 17:47:12,719 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 4.0 to 3756.0 with Q3 at 2812.25)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations with peaks in sectors 0,2,4,6)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long connections)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with empty cells and concentrations in top-right/bottom-right)', 'convergence_status': 'Early-stage (diversity 0.88, convergence 0.0, recent best costs show improvement without stagnation)'}, 'difficult_regions': [{'type': 'Sparse corridor', 'location': 'Central column (density_grid [*,0,*] cells)'}, {'type': 'Long-edge zone', 'location': 'Regions with edges >2812.25 units (Q3 threshold)'}], 'opportunity_regions': [{'type': 'Node cluster', 'location': 'Top-right cell (density 10 nodes)'}, {'type': 'Directional bias', 'location': 'Sectors 0/2/4/6 (direction_hist peaks)'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Intensify exploitation in high-density cells via 2-opt local search', 'Target long edges with edge-recombination crossover', 'Maintain diversity through restart mechanisms in sparse regions', 'Guide mutations using direction histogram biases']}
2025-06-26 17:47:12,721 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:47:12,721 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:47:12,721 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 29571.0
  • mean_cost: 48011.9
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 4.0 to 3756.0 with Q3 at 2812.25), modality Potentially multimodal (direction histogram shows varied edge orientations with peaks in sectors 0,2,4,6), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long connections)
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Sparse cor, {'type': 'Long-edge 
- Opportunity regions (# 2): {'type': 'Node clust, {'type': 'Directiona
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:47:12,722 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:47:12,722 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:47:23,223 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:47:23,223 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:47:23,223 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:47:23,223 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:47:23,223 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:47:23,223 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:47:23,223 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:47:23,223 - __main__ - INFO - 开始进化阶段
2025-06-26 17:47:23,223 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:47:23,223 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:47:23,227 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 52258.0 (rank 40.0%)
- Population diversity: 0.68125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:47:23,227 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:47:23,227 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:47:34,532 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14],
  "targeted_regions": "Node clusters near sparse cells",
  "strategy_comment": "Balanced exploration of low-density nodes while avoiding long edges"
}
```
2025-06-26 17:47:34,532 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:47:34,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37481.0, 路径: [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14]
2025-06-26 17:47:34,532 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37481.0}
2025-06-26 17:47:34,540 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:47:34,540 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:47:34,540 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 52851.0 (rank 50.0%)
- Population diversity: 0.659375
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:47:34,540 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:47:34,540 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:47:45,626 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23],
  "targeted_regions": "Node-based opportunities and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of low-density nodes near opportunities"
}
```
2025-06-26 17:47:45,626 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:47:45,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55733.0, 路径: [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23]
2025-06-26 17:47:45,628 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23], 'cur_cost': 55733.0}
2025-06-26 17:47:45,628 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:47:45,628 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:47:45,628 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34731.0 (rank 10.0%)
- Population diversity: 0.68125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:47:45,628 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:47:45,628 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:47:56,604 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28],
  "targeted_regions": "Node-based opportunities, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of low-density nodes near opportunities"
}
```
2025-06-26 17:47:56,606 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:47:56,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59284.0, 路径: [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28]
2025-06-26 17:47:56,606 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28], 'cur_cost': 59284.0}
2025-06-26 17:47:56,607 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:47:56,607 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:47:56,607 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 58412.0 (rank 70.0%)
- Population diversity: 0.70625
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:47:56,607 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:47:56,607 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:48:07,756 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32],
  "targeted_regions": "Node-based opportunities, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of low-density nodes near opportunities"
}
```
2025-06-26 17:48:07,756 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:48:07,756 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57023.0, 路径: [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32]
2025-06-26 17:48:07,756 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32], 'cur_cost': 57023.0}
2025-06-26 17:48:07,756 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:48:07,756 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:48:07,762 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29571.0 (rank 0.0%)
- Population diversity: 0.690625
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:48:07,762 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:48:07,762 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:48:19,039 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2],
  "targeted_regions": "Low-density nodes near opportunity clusters",
  "strategy_comment": "Balanced leapfrogging between sparse/opportunity zones"
}
```
2025-06-26 17:48:19,039 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:48:19,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42153.0, 路径: [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2]
2025-06-26 17:48:19,039 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2], 'cur_cost': 42153.0}
2025-06-26 17:48:19,039 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:48:19,039 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:48:19,039 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60404.0 (rank 90.0%)
- Population diversity: 0.70625
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:48:19,039 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:48:19,039 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:48:30,142 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32],
  "targeted_regions": "Node-based opportunities and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of low-density nodes near opportunities"
}
```
2025-06-26 17:48:30,145 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:48:30,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59903.0, 路径: [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32]
2025-06-26 17:48:30,145 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32], 'cur_cost': 59903.0}
2025-06-26 17:48:30,145 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:48:30,145 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:48:30,147 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 47552.0 (rank 30.0%)
- Population diversity: 0.703125
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'Node , {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Spars, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 32]  
• Path length == 33 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:48:30,147 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:48:30,148 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:48:41,139 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14],
  "targeted_regions": "Node clusters in NW/SE quadrants",
  "strategy_comment": "Spiral through sparse cells, avoid long edges"
}
```
2025-06-26 17:48:41,139 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:48:41,139 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37453.0, 路径: [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14]
2025-06-26 17:48:41,139 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37453.0}
2025-06-26 17:48:41,139 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:48:41,139 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:48:41,139 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:48:41,139 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 46920.0
2025-06-26 17:48:41,639 - root - WARNING - 无法找到足够的不重叠段 (找到 1/3)，使用退化策略
2025-06-26 17:48:41,641 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:48:41,641 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:48:41,641 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:48:41,660 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:48:41,660 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37481.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23], 'cur_cost': 55733.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28], 'cur_cost': 59284.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32], 'cur_cost': 57023.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2], 'cur_cost': 42153.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32], 'cur_cost': 59903.0}, {'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37453.0}, {'tour': array([18, 30, 16,  6,  0, 12, 13,  4, 23, 10, 28, 17, 11, 15, 22, 14, 31,
        1,  2, 25,  3,  9, 24, 19,  8, 29, 27,  7,  5, 20, 21, 26, 32]), 'cur_cost': 46920.0}, {'tour': array([21, 12, 22, 15, 28, 29,  5, 32,  3,  2, 26, 23, 16, 31, 13, 10,  0,
       24,  4, 17, 14,  6,  7, 27, 19,  9,  8, 18,  1, 25, 11, 20, 30]), 'cur_cost': 55063.0}, {'tour': array([14, 18, 13, 30,  5,  6, 15,  8, 17,  4, 26,  3, 31, 20, 32, 29, 28,
       12, 16,  9,  2, 24,  7, 27,  1,  0, 21, 10, 23, 19, 25, 11, 22]), 'cur_cost': 52986.0}]
2025-06-26 17:48:41,662 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:48:41,662 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 177, 'skip_rate': 0.05084745762711865, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 168, 'cache_hits': 172, 'similarity_calculations': 3263, 'cache_hit_rate': 0.05271222801103279, 'cache_size': 3091}}
2025-06-26 17:48:41,662 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:48:41,663 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:48:41,663 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:48:41,663 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:48:41,664 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 53005.0
2025-06-26 17:48:42,167 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:48:42,167 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:48:42,167 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:48:42,188 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:48:42,188 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37481.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23], 'cur_cost': 55733.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28], 'cur_cost': 59284.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32], 'cur_cost': 57023.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2], 'cur_cost': 42153.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32], 'cur_cost': 59903.0}, {'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37453.0}, {'tour': array([18, 30, 16,  6,  0, 12, 13,  4, 23, 10, 28, 17, 11, 15, 22, 14, 31,
        1,  2, 25,  3,  9, 24, 19,  8, 29, 27,  7,  5, 20, 21, 26, 32]), 'cur_cost': 46920.0}, {'tour': array([ 2, 17, 18, 26, 31,  7, 15, 24, 11,  4, 22,  6, 16, 30,  5, 29,  8,
        3, 13, 20, 27, 23, 19, 28, 32, 21,  1,  0,  9, 25, 14, 12, 10]), 'cur_cost': 53005.0}, {'tour': array([14, 18, 13, 30,  5,  6, 15,  8, 17,  4, 26,  3, 31, 20, 32, 29, 28,
       12, 16,  9,  2, 24,  7, 27,  1,  0, 21, 10, 23, 19, 25, 11, 22]), 'cur_cost': 52986.0}]
2025-06-26 17:48:42,190 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:48:42,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 178, 'skip_rate': 0.05056179775280899, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 169, 'cache_hits': 172, 'similarity_calculations': 3276, 'cache_hit_rate': 0.052503052503052504, 'cache_size': 3104}}
2025-06-26 17:48:42,190 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:48:42,190 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:48:42,190 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:48:42,190 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:48:42,192 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 48408.0
2025-06-26 17:48:42,694 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:48:42,694 - ExploitationExpert - INFO - res_population_costs: [8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8761, 8771, 8776, 8776, 8776, 8776, 8778]
2025-06-26 17:48:42,694 - ExploitationExpert - INFO - res_populations: [array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       20, 25, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 20,
       21, 25, 24, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 25, 20, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 20, 24, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 23, 20, 24, 25,
       21, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 31, 26, 27,
       28, 29, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 20,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  1,  6,  7,  2,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 29, 26, 27, 32, 31, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 20, 25, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 20, 24, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 24, 20, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 20, 25, 21, 22,  8,  4,  3,  2,  7,  6,  1],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 20, 21, 25, 24, 23, 30, 29, 28,
       27, 26, 31, 32, 13, 18, 14, 15, 19, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 25, 21,
       20, 22, 30, 31, 32, 27, 26, 29, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 23, 24, 20, 25,
       21, 22, 30, 29, 26, 31, 32, 27, 28,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 22, 21, 25, 20, 24, 23, 30, 31, 26,
       29, 28, 27, 32, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  9, 12, 17, 16, 11, 10, 19, 15, 14, 18, 13, 32, 27, 28, 29,
       26, 31, 30, 23, 24, 25, 21, 20, 22,  8,  4,  3,  7,  2,  1,  6],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 28, 27, 32, 31, 26, 29, 30, 22, 21,
       25, 20, 24, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  6,  1,  2,  7,  3,  4,  8, 29, 28, 27, 32, 26, 31, 30, 22, 21,
       25, 24, 20, 23, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9,  5],
      dtype=int64), array([ 0,  5,  4,  8,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31,
       26, 27, 28, 29, 30, 23, 24, 25, 21, 20, 22,  7,  3,  2,  1,  6],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 31, 26, 27, 28,
       29, 30, 23, 24, 25, 21, 20, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 24, 25, 20, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  9, 12, 17, 16, 11, 10, 15, 19, 18, 14, 13, 32, 27, 28, 29, 26,
       31, 30, 23, 20, 24, 25, 21, 22,  7,  6,  1,  2,  3,  8,  4,  5],
      dtype=int64), array([ 0,  5,  4,  8,  3,  2,  1,  6,  7, 22, 20, 21, 25, 24, 23, 30, 31,
       32, 26, 29, 28, 27, 13, 14, 18, 19, 15, 10, 11, 16, 17, 12,  9],
      dtype=int64)]
2025-06-26 17:48:42,713 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:48:42,713 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37481.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23], 'cur_cost': 55733.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28], 'cur_cost': 59284.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32], 'cur_cost': 57023.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2], 'cur_cost': 42153.0}, {'tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32], 'cur_cost': 59903.0}, {'tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37453.0}, {'tour': array([18, 30, 16,  6,  0, 12, 13,  4, 23, 10, 28, 17, 11, 15, 22, 14, 31,
        1,  2, 25,  3,  9, 24, 19,  8, 29, 27,  7,  5, 20, 21, 26, 32]), 'cur_cost': 46920.0}, {'tour': array([ 2, 17, 18, 26, 31,  7, 15, 24, 11,  4, 22,  6, 16, 30,  5, 29,  8,
        3, 13, 20, 27, 23, 19, 28, 32, 21,  1,  0,  9, 25, 14, 12, 10]), 'cur_cost': 53005.0}, {'tour': array([29, 25,  7, 27,  9,  2, 21, 28, 31, 23,  6,  5, 30,  3,  8, 17, 14,
       20, 18, 16,  4,  1, 19, 13, 22, 12, 11, 15, 24, 32,  0, 26, 10]), 'cur_cost': 48408.0}]
2025-06-26 17:48:42,716 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:48:42,716 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 179, 'skip_rate': 0.05027932960893855, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 170, 'cache_hits': 172, 'similarity_calculations': 3290, 'cache_hit_rate': 0.052279635258358666, 'cache_size': 3118}}
2025-06-26 17:48:42,716 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:48:42,716 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 30, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37481.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 10, 17, 28, 7, 14, 21, 31, 6, 13, 24, 1, 9, 16, 27, 4, 11, 18, 29, 2, 20, 26, 32, 23], 'cur_cost': 55733.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 14, 31, 1, 17, 6, 25, 10, 21, 4, 16, 30, 2, 19, 13, 27, 23, 32, 28], 'cur_cost': 59284.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 29, 3, 15, 9, 24, 11, 26, 8, 20, 2, 14, 17, 30, 6, 21, 13, 25, 1, 16, 10, 27, 4, 19, 23, 31, 28, 32], 'cur_cost': 57023.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 3, 8, 13, 18, 23, 28, 31, 6, 11, 16, 21, 26, 1, 4, 9, 14, 19, 24, 29, 32, 7, 12, 17, 22, 27, 2], 'cur_cost': 42153.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 19, 25, 30, 15, 22, 3, 9, 17, 28, 11, 24, 7, 20, 2, 14, 27, 6, 21, 1, 13, 26, 4, 16, 29, 10, 23, 18, 31, 32], 'cur_cost': 59903.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 32, 28, 24, 20, 16, 11, 7, 3, 1, 4, 9, 13, 18, 22, 26, 30, 31, 27, 23, 19, 15, 10, 6, 2, 14], 'cur_cost': 37453.0}}]
2025-06-26 17:48:42,716 - __main__ - INFO - 进化阶段完成
2025-06-26 17:48:42,716 - __main__ - INFO - 开始评估阶段
2025-06-26 17:48:42,718 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:48:42,718 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:48:42,718 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 29571.0 → 37453.0 (Δ -7882.0)
- Mean cost: 48011.9 → 49736.3
- Diversity: 0.681 → 0.622 (Δ -0.0594)

#### Elite metrics
- Elite count: 70 → 70
- Best elite cost: 8761 → 8761 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ -1893.0
- Exploit: 3 inds, success 66.7% , avg Δ -1331.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [9067.0, 16711.0, 29571.0], diversity [0.8983164983164983, 0.8882154882154882, 0.8787878787878787]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:48:42,719 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:48:56,365 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 57.1,
      "average_improvement": -1893.0,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 66.7,
      "average_improvement": -1331.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "Explore slightly more effective but both strategies have room for improvement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost diversity and prevent stagnation"
  }
}
```
2025-06-26 17:48:56,373 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:48:56,373 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 57.1,
      "average_improvement": -1893.0,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 66.7,
      "average_improvement": -1331.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "Explore slightly more effective but both strategies have room for improvement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost diversity and prevent stagnation"
  }
}
```
2025-06-26 17:48:56,373 - __main__ - INFO - 评估阶段完成
2025-06-26 17:48:56,378 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 57.1,
      "average_improvement": -1893.0,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 66.7,
      "average_improvement": -1331.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "Explore slightly more effective but both strategies have room for improvement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost diversity and prevent stagnation"
  }
}
```
2025-06-26 17:48:56,378 - __main__ - INFO - 当前最佳适应度: 37453.0
2025-06-26 17:48:56,380 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_route_4.pkl
2025-06-26 17:48:56,396 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite4_33_solution.json
2025-06-26 17:48:56,396 - __main__ - INFO - 实例 composite4_33 处理完成
