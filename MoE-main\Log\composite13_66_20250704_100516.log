2025-07-04 10:05:16,756 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 10:05:16,756 - __main__ - INFO - 开始分析阶段
2025-07-04 10:05:16,757 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:05:16,776 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9859.0, 'max': 121924.0, 'mean': 75467.1, 'std': 44224.41583887796}, 'diversity': 0.9067340067340067, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-04 10:05:16,777 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9859.0, 'max': 121924.0, 'mean': 75467.1, 'std': 44224.41583887796}, 'diversity_level': 0.9067340067340067, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:05:16,777 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:05:16,777 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:05:16,778 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:05:16,785 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:05:16,785 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (58, 60), 'frequency': 0.5, 'avg_cost': 27.0}, {'edge': (50, 51), 'frequency': 0.5, 'avg_cost': 16.0}], 'common_subpaths': [{'subpath': (53, 62, 59), 'frequency': 0.4}, {'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (9, 11, 7), 'frequency': 0.3}, {'subpath': (11, 7, 3), 'frequency': 0.3}, {'subpath': (7, 3, 1), 'frequency': 0.3}, {'subpath': (3, 1, 0), 'frequency': 0.3}, {'subpath': (1, 0, 10), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(9, 11)', 'frequency': 0.4}, {'edge': '(0, 1)', 'frequency': 0.4}, {'edge': '(0, 10)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(53, 62)', 'frequency': 0.4}, {'edge': '(59, 62)', 'frequency': 0.4}, {'edge': '(58, 60)', 'frequency': 0.5}, {'edge': '(60, 64)', 'frequency': 0.4}, {'edge': '(52, 63)', 'frequency': 0.4}, {'edge': '(14, 15)', 'frequency': 0.4}, {'edge': '(12, 17)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(28, 30)', 'frequency': 0.4}, {'edge': '(24, 29)', 'frequency': 0.4}, {'edge': '(29, 32)', 'frequency': 0.4}, {'edge': '(44, 45)', 'frequency': 0.4}, {'edge': '(38, 51)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.5}, {'edge': '(41, 50)', 'frequency': 0.4}, {'edge': '(10, 48)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(35, 36)', 'frequency': 0.2}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.2}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(26, 34)', 'frequency': 0.2}, {'edge': '(25, 31)', 'frequency': 0.2}, {'edge': '(36, 37)', 'frequency': 0.2}, {'edge': '(18, 27)', 'frequency': 0.2}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(14, 20)', 'frequency': 0.2}, {'edge': '(13, 21)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.3}, {'edge': '(9, 19)', 'frequency': 0.3}, {'edge': '(8, 10)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(3, 33)', 'frequency': 0.3}, {'edge': '(33, 49)', 'frequency': 0.2}, {'edge': '(28, 54)', 'frequency': 0.2}, {'edge': '(0, 35)', 'frequency': 0.2}, {'edge': '(36, 47)', 'frequency': 0.2}, {'edge': '(41, 43)', 'frequency': 0.2}, {'edge': '(8, 45)', 'frequency': 0.2}, {'edge': '(8, 17)', 'frequency': 0.2}, {'edge': '(17, 40)', 'frequency': 0.3}, {'edge': '(11, 51)', 'frequency': 0.2}, {'edge': '(20, 56)', 'frequency': 0.2}, {'edge': '(25, 60)', 'frequency': 0.2}, {'edge': '(17, 23)', 'frequency': 0.2}, {'edge': '(44, 47)', 'frequency': 0.2}, {'edge': '(16, 44)', 'frequency': 0.2}, {'edge': '(42, 64)', 'frequency': 0.3}, {'edge': '(1, 62)', 'frequency': 0.2}, {'edge': '(9, 54)', 'frequency': 0.2}, {'edge': '(16, 54)', 'frequency': 0.2}, {'edge': '(5, 59)', 'frequency': 0.2}, {'edge': '(28, 61)', 'frequency': 0.2}, {'edge': '(43, 53)', 'frequency': 0.2}, {'edge': '(15, 44)', 'frequency': 0.2}, {'edge': '(49, 52)', 'frequency': 0.2}, {'edge': '(26, 55)', 'frequency': 0.2}, {'edge': '(9, 50)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(41, 64)', 'frequency': 0.2}, {'edge': '(36, 56)', 'frequency': 0.2}, {'edge': '(21, 40)', 'frequency': 0.2}, {'edge': '(27, 29)', 'frequency': 0.2}, {'edge': '(18, 19)', 'frequency': 0.2}, {'edge': '(1, 18)', 'frequency': 0.2}, {'edge': '(45, 46)', 'frequency': 0.2}, {'edge': '(26, 35)', 'frequency': 0.2}, {'edge': '(14, 31)', 'frequency': 0.2}, {'edge': '(14, 53)', 'frequency': 0.2}, {'edge': '(49, 57)', 'frequency': 0.2}, {'edge': '(36, 60)', 'frequency': 0.2}, {'edge': '(13, 17)', 'frequency': 0.2}, {'edge': '(12, 19)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [36, 60, 25, 41, 64, 42, 4], 'cost': 16265.0, 'size': 7}, {'region': [54, 44, 2, 39, 36, 56, 34], 'cost': 15719.0, 'size': 7}, {'region': [28, 61, 36, 53, 43, 63], 'cost': 14442.0, 'size': 6}, {'region': [7, 46, 0, 39, 5, 49], 'cost': 12501.0, 'size': 6}, {'region': [58, 31, 61, 39, 27], 'cost': 10721.0, 'size': 5}]}
2025-07-04 10:05:16,786 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:05:16,786 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:05:16,786 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:05:16,786 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 10:05:16,787 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-04 10:05:16,787 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:05:16,787 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:05:17,254 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:05:17,255 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9859.0, mean 75467.1, max 121924.0, std 44224.41583887796
- diversity: 0.9067340067340067
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (58, 60), 'frequency': 0.5, 'avg_cost': 27.0}, {'edge': (50, 51), 'frequency': 0.5, 'avg_cost': 16.0}]
- common_subpaths_sample: [{'subpath': (53, 62, 59), 'frequency': 0.4}, {'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (9, 11, 7), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [36, 60, 25, 41, 64, 42, 4], 'cost': 16265.0, 'size': 7}, {'region': [54, 44, 2, 39, 36, 56, 34], 'cost': 15719.0, 'size': 7}, {'region': [28, 61, 36, 53, 43, 63], 'cost': 14442.0, 'size': 6}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:05:17,256 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:23,516 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.45,
    "modality": "moderate",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9067340067340067,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "region": [
        36,
        60,
        25,
        41,
        64,
        42,
        4
      ],
      "cost": 16265.0,
      "size": 7,
      "spatial_context": "Located within the bounding box.  This difficult region seems clustered."
    },
    {
      "region": [
        54,
        44,
        2,
        39,
        36,
        56,
        34
      ],
      "cost": 15719.0,
      "size": 7,
      "spatial_context": "Located within the bounding box, potential corridor based on spread."
    },
    {
      "region": [
        28,
        61,
        36,
        53,
        43,
        63
      ],
      "cost": 14442.0,
      "size": 6,
      "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."
    }
  ],
  "opportunity_regions": [
    {
      "grid_cell": "(0,0)",
      "nodes": 14,
      "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."
    },
    {
      "grid_cell": "(2,2)",
      "nodes": 14,
      "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."
    },
    {
      "grid_cell": "(1,1)",
      "nodes": 12,
      "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."
    }
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore and exploit",
    "operators": [
      "Apply local search operators (2-opt, 3-opt) to nodes in high-density regions (e.g. grid cells with high node counts) to refine potential solution sub-paths.",
      "Employ crossover operators that favor the high-quality edges identified in `high_quality_edges_sample`.",
      "Use mutation operators focused on exploring edges in the difficult regions or combining subpaths from high-quality edges and common subpaths.",
      "Consider a diversification strategy to maintain high diversity by introducing solutions that differ significantly from the current population."
    ]
  }
}
```
2025-07-04 10:05:23,518 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:05:23,518 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'moderate', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9067340067340067, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': [36, 60, 25, 41, 64, 42, 4], 'cost': 16265.0, 'size': 7, 'spatial_context': 'Located within the bounding box.  This difficult region seems clustered.'}, {'region': [54, 44, 2, 39, 36, 56, 34], 'cost': 15719.0, 'size': 7, 'spatial_context': 'Located within the bounding box, potential corridor based on spread.'}, {'region': [28, 61, 36, 53, 43, 63], 'cost': 14442.0, 'size': 6, 'spatial_context': 'Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes.'}], 'opportunity_regions': [{'grid_cell': '(0,0)', 'nodes': 14, 'spatial_context': 'High density in the bottom left corner. Potential for local optimization or exploiting local structure.'}, {'grid_cell': '(2,2)', 'nodes': 14, 'spatial_context': 'High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization.'}, {'grid_cell': '(1,1)', 'nodes': 12, 'spatial_context': 'Nodes clustered, potentially a good target for refinement through local search.'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Apply local search operators (2-opt, 3-opt) to nodes in high-density regions (e.g. grid cells with high node counts) to refine potential solution sub-paths.', 'Employ crossover operators that favor the high-quality edges identified in `high_quality_edges_sample`.', 'Use mutation operators focused on exploring edges in the difficult regions or combining subpaths from high-quality edges and common subpaths.', 'Consider a diversification strategy to maintain high diversity by introducing solutions that differ significantly from the current population.']}}
2025-07-04 10:05:23,518 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:05:23,518 - __main__ - INFO - 分析阶段完成
2025-07-04 10:05:23,518 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.45, 'modality': 'moderate', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9067340067340067, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': [36, 60, 25, 41, 64, 42, 4], 'cost': 16265.0, 'size': 7, 'spatial_context': 'Located within the bounding box.  This difficult region seems clustered.'}, {'region': [54, 44, 2, 39, 36, 56, 34], 'cost': 15719.0, 'size': 7, 'spatial_context': 'Located within the bounding box, potential corridor based on spread.'}, {'region': [28, 61, 36, 53, 43, 63], 'cost': 14442.0, 'size': 6, 'spatial_context': 'Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes.'}], 'opportunity_regions': [{'grid_cell': '(0,0)', 'nodes': 14, 'spatial_context': 'High density in the bottom left corner. Potential for local optimization or exploiting local structure.'}, {'grid_cell': '(2,2)', 'nodes': 14, 'spatial_context': 'High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization.'}, {'grid_cell': '(1,1)', 'nodes': 12, 'spatial_context': 'Nodes clustered, potentially a good target for refinement through local search.'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Apply local search operators (2-opt, 3-opt) to nodes in high-density regions (e.g. grid cells with high node counts) to refine potential solution sub-paths.', 'Employ crossover operators that favor the high-quality edges identified in `high_quality_edges_sample`.', 'Use mutation operators focused on exploring edges in the difficult regions or combining subpaths from high-quality edges and common subpaths.', 'Consider a diversification strategy to maintain high diversity by introducing solutions that differ significantly from the current population.']}}
2025-07-04 10:05:23,518 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:05:23,518 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:05:23,518 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.907
  • best_cost: 9859.0
  • mean_cost: 75467.1
- Search-space: ruggedness 0.45, modality moderate, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [36, 60, , {'region': [54, 44, ...
- Opportunity regions (# 3): {'grid_cell': '(0,0), {'grid_cell': '(2,2)...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:05:23,518 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:05:23,518 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:25,834 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and the exploration phase suggest a strong bias towards exploration.  A higher exploration ratio promotes broader search."
}
```
2025-07-04 10:05:25,834 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:05:25,834 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:05:25,834 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and the exploration phase suggest a strong bias towards exploration.  A higher exploration ratio promotes broader search."
}
```
2025-07-04 10:05:25,837 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:05:25,837 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 10:05:25,837 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and the exploration phase suggest a strong bias towards exploration.  A higher exploration ratio promotes broader search."
}
```
2025-07-04 10:05:25,838 - __main__ - INFO - 开始进化阶段
2025-07-04 10:05:25,838 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:05:25,838 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:25,840 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10241.0 (rank 20.0%)
- Population diversity: 0.7246153846153847
  • Edge uniqueness: 0.725
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.873
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7246153846153847, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8734375}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:25,840 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:25,840 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:28,980 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
  "targeted_regions": "Opportunity regions (0,0), (2,2), and (1,1) and detours around difficult regions.",
  "strategy_comment": "Exploited opportunity regions, avoided difficult areas through careful ordering to maintain diversity."
}
```
2025-07-04 10:05:28,980 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:28,980 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10243.0, 路径: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
2025-07-04 10:05:28,982 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}
2025-07-04 10:05:28,982 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:05:28,982 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:28,983 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9859.0 (rank 0.0%)
- Population diversity: 0.7261538461538461
  • Edge uniqueness: 0.726
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.875
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 36, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7261538461538461, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.875}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:28,983 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:28,984 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:32,074 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36],
  "targeted_regions": "Opportunity regions (0,0), (2,2), (1,1)",
  "strategy_comment": "Prioritized opportunity region traversal while avoiding difficult regions for diversity and cost trade-off."
}
```
2025-07-04 10:05:32,074 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:32,074 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10022.0, 路径: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
2025-07-04 10:05:32,074 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}
2025-07-04 10:05:32,074 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:05:32,074 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:32,078 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9890.0 (rank 0.0%)
- Population diversity: 0.7292307692307692
  • Edge uniqueness: 0.729
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.883
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [25, 26, 36, 37, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7292307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8828125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:32,078 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:32,078 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:35,160 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54],
  "targeted_regions": "Opportunity regions (0,0), (2,2), and (1,1). Avoids difficult regions.",
  "strategy_comment": "Reordered nodes to increase diversity while avoiding problematic regions and incorporating opportunity regions."
}
```
2025-07-04 10:05:35,160 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:35,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13665.0, 路径: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
2025-07-04 10:05:35,160 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}
2025-07-04 10:05:35,160 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:05:35,160 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:35,160 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106947.0 (rank 70.0%)
- Population diversity: 0.7323076923076923
  • Edge uniqueness: 0.732
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.886
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 36, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7323076923076923, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8859375}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:35,160 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:35,160 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:38,227 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17],
  "targeted_regions": "Explore (0,0), (2,2), (1,1) via existing path, Avoid difficult regions",
  "strategy_comment": "Maintain current path, explore opportunity regions via existing nodes, avoid difficult regions with detours."
}
```
2025-07-04 10:05:38,227 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:38,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108728.0, 路径: [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36]
2025-07-04 10:05:38,227 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}
2025-07-04 10:05:38,227 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:05:38,227 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:38,227 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97629.0 (rank 50.0%)
- Population diversity: 0.7338461538461538
  • Edge uniqueness: 0.734
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.886
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 28, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 36, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7338461538461538, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8859375}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:38,227 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:38,227 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:41,134 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65],
  "targeted_regions": "grid_cell(0,0), grid_cell(2,2)",
  "strategy_comment": "Explores opportunity regions and maintains distance from difficult regions to encourage diversity and maintain path quality."
}
```
2025-07-04 10:05:41,134 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:41,134 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99445.0, 路径: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36]
2025-07-04 10:05:41,134 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}
2025-07-04 10:05:41,134 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:05:41,134 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:05:41,134 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 121785.0 (rank 80.0%)
- Population diversity: 0.7338461538461538
  • Edge uniqueness: 0.734
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.886
- Evolution phase: exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: {'grid_cell': ', {'grid_cell': '...
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'region': [36,, {'region': [54,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 36, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"grid_cell": "(0,0)", "nodes": 14, "spatial_context": "High density in the bottom left corner. Potential for local optimization or exploiting local structure."}, {"grid_cell": "(2,2)", "nodes": 14, "spatial_context": "High density in the bottom right corner. Similar to the bottom left corner, a candidate for optimization."}, {"grid_cell": "(1,1)", "nodes": 12, "spatial_context": "Nodes clustered, potentially a good target for refinement through local search."}]
  • Difficult regions (full): [{"region": [36, 60, 25, 41, 64, 42, 4], "cost": 16265.0, "size": 7, "spatial_context": "Located within the bounding box.  This difficult region seems clustered."}, {"region": [54, 44, 2, 39, 36, 56, 34], "cost": 15719.0, "size": 7, "spatial_context": "Located within the bounding box, potential corridor based on spread."}, {"region": [28, 61, 36, 53, 43, 63], "cost": 14442.0, "size": 6, "spatial_context": "Clustered in the bounding box. Suggests a sub-optimal arrangement of nodes."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7338461538461538, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8859375}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:05:41,134 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:05:41,134 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:44,313 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36],
  "targeted_regions": "grid_cell(0,0), grid_cell(2,2), avoid difficult regions",
  "strategy_comment": "Prioritizes opportunity regions while carefully avoiding and detouring around identified difficult areas for cost-effectiveness."
}
```
2025-07-04 10:05:44,313 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:05:44,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118171.0, 路径: [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36]
2025-07-04 10:05:44,313 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}
2025-07-04 10:05:44,313 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 10:05:44,313 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:05:44,313 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:05:44,313 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 111721.0
2025-07-04 10:05:46,644 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 10:05:46,644 - ExploitationExpert - INFO - res_population_costs: [79773]
2025-07-04 10:05:46,644 - ExploitationExpert - INFO - res_populations: [array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}, {'tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}, {'tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': [61, 37, 14, 53, 50, 23, 26, 55, 49, 57, 15, 44, 35, 65, 39, 51, 30, 62, 22, 59, 38, 29, 18, 1, 13, 6, 19, 36, 60, 25, 41, 64, 42, 4, 8, 45, 16, 54, 28, 20, 56, 43, 9, 2, 52, 58, 46, 12, 10, 0, 32, 63, 7, 34, 24, 17, 21, 40, 31, 11, 47, 3, 5, 27, 48, 33], 'cur_cost': 121924.0}, {'tour': [6, 1, 16, 44, 47, 36, 60, 27, 28, 22, 25, 20, 7, 38, 26, 45, 42, 64, 24, 29, 14, 30, 50, 51, 32, 15, 13, 17, 40, 3, 62, 10, 48, 35, 21, 4, 37, 65, 33, 58, 23, 31, 55, 52, 63, 18, 39, 53, 43, 41, 0, 57, 49, 54, 61, 46, 8, 5, 59, 2, 56, 11, 9, 19, 12, 34], 'cur_cost': 99097.0}, {'tour': [32, 1, 62, 61, 28, 44, 40, 43, 42, 13, 17, 23, 24, 6, 10, 30, 41, 38, 52, 8, 16, 55, 60, 58, 21, 46, 45, 57, 53, 47, 2, 11, 34, 25, 64, 48, 65, 14, 31, 26, 35, 22, 63, 36, 56, 18, 19, 12, 27, 29, 49, 33, 3, 59, 20, 39, 15, 37, 7, 5, 0, 51, 50, 9, 54, 4], 'cur_cost': 86763.0}]
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - 局部搜索耗时: 2.33秒
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 10:05:46,645 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 10:05:46,645 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:05:46,645 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:05:46,648 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108868.0
2025-07-04 10:05:47,542 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 10:05:47,542 - ExploitationExpert - INFO - res_population_costs: [79773, 9582]
2025-07-04 10:05:47,542 - ExploitationExpert - INFO - res_populations: [array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64)]
2025-07-04 10:05:47,543 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:05:47,543 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}, {'tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}, {'tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': array([34, 25, 52, 33, 16, 18, 46, 30, 27, 13, 23,  6, 36, 21, 19, 56, 37,
        7,  3, 54, 28, 43, 32, 49,  5, 58, 20, 41, 65, 53, 57, 62, 39, 29,
        9,  4, 14,  2, 42, 44, 11,  0, 24, 47, 10, 63, 64, 45, 61, 48, 22,
        1, 40, 15, 38, 35, 55, 50, 31, 51,  8, 17, 12, 59, 26, 60]), 'cur_cost': 108868.0}, {'tour': [6, 1, 16, 44, 47, 36, 60, 27, 28, 22, 25, 20, 7, 38, 26, 45, 42, 64, 24, 29, 14, 30, 50, 51, 32, 15, 13, 17, 40, 3, 62, 10, 48, 35, 21, 4, 37, 65, 33, 58, 23, 31, 55, 52, 63, 18, 39, 53, 43, 41, 0, 57, 49, 54, 61, 46, 8, 5, 59, 2, 56, 11, 9, 19, 12, 34], 'cur_cost': 99097.0}, {'tour': [32, 1, 62, 61, 28, 44, 40, 43, 42, 13, 17, 23, 24, 6, 10, 30, 41, 38, 52, 8, 16, 55, 60, 58, 21, 46, 45, 57, 53, 47, 2, 11, 34, 25, 64, 48, 65, 14, 31, 26, 35, 22, 63, 36, 56, 18, 19, 12, 27, 29, 49, 33, 3, 59, 20, 39, 15, 37, 7, 5, 0, 51, 50, 9, 54, 4], 'cur_cost': 86763.0}]
2025-07-04 10:05:47,545 - ExploitationExpert - INFO - 局部搜索耗时: 0.90秒
2025-07-04 10:05:47,546 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 10:05:47,546 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:05:47,547 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:05:47,547 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:05:47,547 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:05:47,547 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108774.0
2025-07-04 10:05:48,048 - ExploitationExpert - INFO - res_population_num: 7
2025-07-04 10:05:48,049 - ExploitationExpert - INFO - res_population_costs: [79773, 9582, 9555, 9545, 9526, 9526, 9526]
2025-07-04 10:05:48,049 - ExploitationExpert - INFO - res_populations: [array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:05:48,053 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:05:48,053 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}, {'tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}, {'tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': array([34, 25, 52, 33, 16, 18, 46, 30, 27, 13, 23,  6, 36, 21, 19, 56, 37,
        7,  3, 54, 28, 43, 32, 49,  5, 58, 20, 41, 65, 53, 57, 62, 39, 29,
        9,  4, 14,  2, 42, 44, 11,  0, 24, 47, 10, 63, 64, 45, 61, 48, 22,
        1, 40, 15, 38, 35, 55, 50, 31, 51,  8, 17, 12, 59, 26, 60]), 'cur_cost': 108868.0}, {'tour': array([49, 58, 15, 57, 51, 10, 14, 13, 34, 29,  3, 27, 11, 22,  1, 42,  2,
        6,  8, 65, 64, 24,  0, 56, 48, 40, 32, 46, 28, 59, 23,  7,  9, 16,
       12, 45, 38, 61, 39,  5, 55, 26, 25, 52, 37,  4, 44, 17, 60, 43, 63,
       47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]), 'cur_cost': 108774.0}, {'tour': [32, 1, 62, 61, 28, 44, 40, 43, 42, 13, 17, 23, 24, 6, 10, 30, 41, 38, 52, 8, 16, 55, 60, 58, 21, 46, 45, 57, 53, 47, 2, 11, 34, 25, 64, 48, 65, 14, 31, 26, 35, 22, 63, 36, 56, 18, 19, 12, 27, 29, 49, 33, 3, 59, 20, 39, 15, 37, 7, 5, 0, 51, 50, 9, 54, 4], 'cur_cost': 86763.0}]
2025-07-04 10:05:48,054 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:05:48,055 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 10:05:48,055 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:05:48,055 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:05:48,055 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:05:48,055 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:05:48,056 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110161.0
2025-07-04 10:05:48,557 - ExploitationExpert - INFO - res_population_num: 12
2025-07-04 10:05:48,558 - ExploitationExpert - INFO - res_population_costs: [79773, 9582, 9555, 9545, 9526, 9526, 9526, 9526, 9521, 9521, 9521, 9521]
2025-07-04 10:05:48,558 - ExploitationExpert - INFO - res_populations: [array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 10:05:48,562 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:05:48,562 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}, {'tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}, {'tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': array([34, 25, 52, 33, 16, 18, 46, 30, 27, 13, 23,  6, 36, 21, 19, 56, 37,
        7,  3, 54, 28, 43, 32, 49,  5, 58, 20, 41, 65, 53, 57, 62, 39, 29,
        9,  4, 14,  2, 42, 44, 11,  0, 24, 47, 10, 63, 64, 45, 61, 48, 22,
        1, 40, 15, 38, 35, 55, 50, 31, 51,  8, 17, 12, 59, 26, 60]), 'cur_cost': 108868.0}, {'tour': array([49, 58, 15, 57, 51, 10, 14, 13, 34, 29,  3, 27, 11, 22,  1, 42,  2,
        6,  8, 65, 64, 24,  0, 56, 48, 40, 32, 46, 28, 59, 23,  7,  9, 16,
       12, 45, 38, 61, 39,  5, 55, 26, 25, 52, 37,  4, 44, 17, 60, 43, 63,
       47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]), 'cur_cost': 108774.0}, {'tour': array([57, 10, 52, 33, 37,  4, 48,  5,  2, 35, 14,  6, 39, 26, 54, 22, 47,
       44, 28, 41,  3,  0, 18, 65, 61, 64, 34, 15, 38, 19, 63,  7, 25, 13,
       60, 32, 30,  9, 40,  8, 36, 58, 46, 49, 20, 59, 53, 24, 31, 23,  1,
       17, 11, 51, 50, 56, 29, 55, 21, 45, 12, 62, 27, 43, 16, 42]), 'cur_cost': 110161.0}]
2025-07-04 10:05:48,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:05:48,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 10:05:48,565 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:05:48,566 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10243.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10022.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13665.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [40, 55, 18, 22, 32, 3, 33, 49, 9, 30, 6, 28, 54, 2, 15, 1, 42, 37, 23, 53, 59, 24, 62, 60, 58, 31, 61, 39, 27, 0, 35, 47, 4, 48, 10, 51, 38, 46, 19, 25, 43, 41, 63, 13, 56, 5, 50, 14, 52, 16, 11, 29, 12, 7, 26, 34, 21, 20, 64, 65, 57, 44, 45, 8, 17, 36], 'cur_cost': 108728.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}}]
2025-07-04 10:05:48,566 - __main__ - INFO - 进化阶段完成
2025-07-04 10:05:48,567 - __main__ - INFO - 开始评估阶段
2025-07-04 10:05:48,567 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-04 10:05:48,568 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:05:48,568 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9859.0 → 10022.0 (Δ -163.0)
- Mean cost: 75467.1 → 79979.8
- Diversity: 0.725 → 0.737 (Δ 0.0123)

#### Elite metrics
- Elite count: 0 → 12
- Best elite cost: 9859.0 → 9521 (Δ 338.0)
- Elite diversity: 0.000 → 0.214 (Δ 0.2141)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -653.83
- Exploit: 4 inds, success 25.0% , avg Δ -10301.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 8
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:05:48,568 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:52,032 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "slightly_explore_biased",
  "recommendations": [
    "Increase explore_ratio slightly. Suggest setting explore_ratio to 0.7.",
    "Monitor the regression individuals. High number of regression individuals might indicate instability in the search space or the need to adjust the mutation parameters. Further analysis needed in the next iteration."
  ]
}
```
2025-07-04 10:05:52,063 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:05:52,063 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "slightly_explore_biased",
  "recommendations": [
    "Increase explore_ratio slightly. Suggest setting explore_ratio to 0.7.",
    "Monitor the regression individuals. High number of regression individuals might indicate instability in the search space or the need to adjust the mutation parameters. Further analysis needed in the next iteration."
  ]
}
```
2025-07-04 10:05:52,064 - __main__ - INFO - 评估阶段完成
2025-07-04 10:05:52,064 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "slightly_explore_biased",
  "recommendations": [
    "Increase explore_ratio slightly. Suggest setting explore_ratio to 0.7.",
    "Monitor the regression individuals. High number of regression individuals might indicate instability in the search space or the need to adjust the mutation parameters. Further analysis needed in the next iteration."
  ]
}
```
2025-07-04 10:05:52,064 - __main__ - INFO - 当前最佳适应度: 10022.0
2025-07-04 10:05:52,066 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-04 10:05:52,066 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-04 10:05:52,066 - __main__ - INFO - 开始分析阶段
2025-07-04 10:05:52,067 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:05:52,085 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10022.0, 'max': 118171.0, 'mean': 79979.8, 'std': 45167.38999499528}, 'diversity': 0.9148148148148147, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-04 10:05:52,085 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10022.0, 'max': 118171.0, 'mean': 79979.8, 'std': 45167.38999499528}, 'diversity_level': 0.9148148148148147, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:05:52,086 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:05:52,086 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:05:52,086 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:05:52,089 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:05:52,090 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:05:52,090 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-04 10:05:52,090 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:05:52,090 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:05:52,090 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:05:52,094 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:05:52,094 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(18, 16)': 0.75}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 501.0, 'avg_gap': 64592.88333333334}, 'structure_gap': {'unique_elite_edges': 154, 'unique_pop_edges': 441, 'common_edges': 95}}, 'elite_diversity': {'diversity_score': 0.3292011019283746}}
2025-07-04 10:05:52,094 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:05:52,094 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:05:52,097 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:05:52,097 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 10022.0, mean 79979.8, max 118171.0, std 45167.38999499528
- diversity: 0.9148148148148147
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10022.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.3292011019283746}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:05:52,097 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:05:58,160 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.6,
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.91,
    "convergence": 0.0,
    "clustering": "very weak"
  },
  "difficult_regions": [
    {
      "description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.",
      "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].",
      "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."
    }
  ],
  "evolution_phase": "early_exploration",
  "evolution_direction": {
    "recommended_focus": "explore_exploit",
    "operators": [
      "Apply a diversified crossover operator to encourage exploration and generate new solution structures. Given the high diversity, this is a good time to explore. The diversity may allow for early escape from poor local optima. However, the operators should be tuned to minimize the chance of disruption to good partial solutions (given the few clusterings).",
      "Use a local search operator, such as 2-opt, on promising solutions, to exploit the opportunity regions identified by the density grid. This exploits the high density areas by applying localized search.",
      "Implement edge-focused mutation that prioritizes adding/removing/swapping edges in the difficult regions. Focus the search by making small perturbations near these 'difficult' edges.",
      "Consider the fixed_nodes_sample and see if the best solution is stuck there, and use operators that are unlikely to move these nodes.",
      "Monitor the elite_common_edge_percentage: it is currently 0, so any operator should work in theory, but there is no evidence of local optimization and that may need to change."
    ]
  }
}
```
2025-07-04 10:05:58,160 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:05:58,160 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.91, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': [{'description': 'Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.', 'analysis': 'Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions.'}], 'opportunity_regions': [{'description': 'High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].', 'analysis': 'The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress.'}], 'evolution_phase': 'early_exploration', 'evolution_direction': {'recommended_focus': 'explore_exploit', 'operators': ['Apply a diversified crossover operator to encourage exploration and generate new solution structures. Given the high diversity, this is a good time to explore. The diversity may allow for early escape from poor local optima. However, the operators should be tuned to minimize the chance of disruption to good partial solutions (given the few clusterings).', 'Use a local search operator, such as 2-opt, on promising solutions, to exploit the opportunity regions identified by the density grid. This exploits the high density areas by applying localized search.', "Implement edge-focused mutation that prioritizes adding/removing/swapping edges in the difficult regions. Focus the search by making small perturbations near these 'difficult' edges.", 'Consider the fixed_nodes_sample and see if the best solution is stuck there, and use operators that are unlikely to move these nodes.', 'Monitor the elite_common_edge_percentage: it is currently 0, so any operator should work in theory, but there is no evidence of local optimization and that may need to change.']}}
2025-07-04 10:05:58,160 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:05:58,160 - __main__ - INFO - 分析阶段完成
2025-07-04 10:05:58,160 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.91, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': [{'description': 'Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.', 'analysis': 'Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions.'}], 'opportunity_regions': [{'description': 'High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].', 'analysis': 'The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress.'}], 'evolution_phase': 'early_exploration', 'evolution_direction': {'recommended_focus': 'explore_exploit', 'operators': ['Apply a diversified crossover operator to encourage exploration and generate new solution structures. Given the high diversity, this is a good time to explore. The diversity may allow for early escape from poor local optima. However, the operators should be tuned to minimize the chance of disruption to good partial solutions (given the few clusterings).', 'Use a local search operator, such as 2-opt, on promising solutions, to exploit the opportunity regions identified by the density grid. This exploits the high density areas by applying localized search.', "Implement edge-focused mutation that prioritizes adding/removing/swapping edges in the difficult regions. Focus the search by making small perturbations near these 'difficult' edges.", 'Consider the fixed_nodes_sample and see if the best solution is stuck there, and use operators that are unlikely to move these nodes.', 'Monitor the elite_common_edge_percentage: it is currently 0, so any operator should work in theory, but there is no evidence of local optimization and that may need to change.']}}
2025-07-04 10:05:58,160 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:05:58,160 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:05:58,160 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.91
  • best_cost: 10022.0
  • mean_cost: 79979.8
- Search-space: ruggedness 0.6, modality multi, deceptiveness moderate
- Evolution phase: early_exploration
- Landscape focus suggestion: explore_exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Pot
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:05:58,160 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:05:58,160 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:01,284 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, and focus on explore. Higher explore bias."
}
```
2025-07-04 10:06:01,284 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:06:01,284 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:06:01,284 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, and focus on explore. Higher explore bias."
}
```
2025-07-04 10:06:01,284 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:06:01,284 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:06:01,284 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity, and focus on explore. Higher explore bias."
}
```
2025-07-04 10:06:01,284 - __main__ - INFO - 开始进化阶段
2025-07-04 10:06:01,289 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:06:01,289 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:01,289 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10243.0 (rank 10.0%)
- Population diversity: 0.7369230769230769
  • Edge uniqueness: 0.737
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.887
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7369230769230769, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8875}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:01,289 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:01,289 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:04,682 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61],
  "targeted_regions": "Explored high-density nodes: [14, 12, 15, 22]. Modified path to include and sequence efficiently.",
  "strategy_comment": "Prioritized opportunity regions within the path while maintaining a balance between cost and diversity."
}
```
2025-07-04 10:06:04,683 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:04,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10061.0, 路径: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
2025-07-04 10:06:04,684 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}
2025-07-04 10:06:04,684 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:06:04,685 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:04,686 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10022.0 (rank 0.0%)
- Population diversity: 0.7415384615384616
  • Edge uniqueness: 0.742
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7415384615384616, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:04,686 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:04,686 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:07,665 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27],
  "targeted_regions": "High-density node clusters, avoid long edges.",
  "strategy_comment": "Prioritize visiting high-density clusters, re-order path to minimize edges identified as difficult."
}
```
2025-07-04 10:06:07,665 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:07,665 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10092.0, 路径: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
2025-07-04 10:06:07,665 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}
2025-07-04 10:06:07,665 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:06:07,665 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:07,668 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13665.0 (rank 20.0%)
- Population diversity: 0.7430769230769231
  • Edge uniqueness: 0.743
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.914
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7430769230769231, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9140625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:07,668 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:07,668 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:10,830 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54],
  "targeted_regions": "High-density cells [14, 12, 12, 14]",
  "strategy_comment": "Explored high-density regions and maintained edge diversity while attempting to reduce long edges."
}
```
2025-07-04 10:06:10,830 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:10,830 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13735.0, 路径: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
2025-07-04 10:06:10,830 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}
2025-07-04 10:06:10,830 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-04 10:06:10,830 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:10,830 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:10,834 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 102634.0
2025-07-04 10:06:11,337 - ExploitationExpert - INFO - res_population_num: 15
2025-07-04 10:06:11,337 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521, 9521]
2025-07-04 10:06:11,337 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 10:06:11,348 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:11,348 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([61,  3,  6, 52, 64, 34, 13, 20, 62, 28, 63, 37, 53, 40, 48, 46, 30,
       47, 60, 59, 49, 65, 24, 11, 10, 27, 42, 36,  8, 54,  4,  0,  2, 33,
       38, 58, 22,  1, 23,  9, 12, 51, 39, 21, 17, 43, 29, 26, 50, 19, 44,
       18, 35, 25, 45, 14, 15, 56,  7, 57, 31, 55, 16,  5, 32, 41]), 'cur_cost': 102634.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36], 'cur_cost': 99445.0}, {'tour': [20, 51, 11, 65, 13, 3, 29, 57, 1, 0, 35, 17, 40, 9, 54, 16, 39, 38, 31, 46, 5, 59, 37, 18, 23, 19, 28, 61, 53, 43, 63, 12, 2, 42, 56, 4, 22, 60, 47, 30, 24, 45, 15, 44, 7, 52, 49, 27, 10, 48, 6, 62, 34, 14, 32, 55, 26, 8, 50, 64, 21, 41, 33, 25, 58, 36], 'cur_cost': 118171.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': array([34, 25, 52, 33, 16, 18, 46, 30, 27, 13, 23,  6, 36, 21, 19, 56, 37,
        7,  3, 54, 28, 43, 32, 49,  5, 58, 20, 41, 65, 53, 57, 62, 39, 29,
        9,  4, 14,  2, 42, 44, 11,  0, 24, 47, 10, 63, 64, 45, 61, 48, 22,
        1, 40, 15, 38, 35, 55, 50, 31, 51,  8, 17, 12, 59, 26, 60]), 'cur_cost': 108868.0}, {'tour': array([49, 58, 15, 57, 51, 10, 14, 13, 34, 29,  3, 27, 11, 22,  1, 42,  2,
        6,  8, 65, 64, 24,  0, 56, 48, 40, 32, 46, 28, 59, 23,  7,  9, 16,
       12, 45, 38, 61, 39,  5, 55, 26, 25, 52, 37,  4, 44, 17, 60, 43, 63,
       47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]), 'cur_cost': 108774.0}, {'tour': array([57, 10, 52, 33, 37,  4, 48,  5,  2, 35, 14,  6, 39, 26, 54, 22, 47,
       44, 28, 41,  3,  0, 18, 65, 61, 64, 34, 15, 38, 19, 63,  7, 25, 13,
       60, 32, 30,  9, 40,  8, 36, 58, 46, 49, 20, 59, 53, 24, 31, 23,  1,
       17, 11, 51, 50, 56, 29, 55, 21, 45, 12, 62, 27, 43, 16, 42]), 'cur_cost': 110161.0}]
2025-07-04 10:06:11,348 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:11,348 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 10:06:11,348 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-04 10:06:11,348 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:06:11,348 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:11,352 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99445.0 (rank 30.0%)
- Population diversity: 0.7415384615384616
  • Edge uniqueness: 0.742
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 40, 51, 11, 53, 12, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7415384615384616, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:11,353 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:11,353 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:14,341 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36],
  "targeted_regions": "Nodes around [14, 12, 53] in high-density area.",
  "strategy_comment": "Focusing on opportunity region and maintaining current node order to balance diversity and cost."
}
```
2025-07-04 10:06:14,341 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:14,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98258.0, 路径: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11]
2025-07-04 10:06:14,341 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}
2025-07-04 10:06:14,341 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 10:06:14,341 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:14,341 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:14,345 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 94860.0
2025-07-04 10:06:14,847 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:06:14,847 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:06:14,847 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:14,853 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:14,853 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([61,  3,  6, 52, 64, 34, 13, 20, 62, 28, 63, 37, 53, 40, 48, 46, 30,
       47, 60, 59, 49, 65, 24, 11, 10, 27, 42, 36,  8, 54,  4,  0,  2, 33,
       38, 58, 22,  1, 23,  9, 12, 51, 39, 21, 17, 43, 29, 26, 50, 19, 44,
       18, 35, 25, 45, 14, 15, 56,  7, 57, 31, 55, 16,  5, 32, 41]), 'cur_cost': 102634.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([23,  1,  5,  6, 64, 53, 21, 27, 15, 32, 11, 41, 43, 48, 40, 25, 28,
       42, 33, 46, 10, 16,  2, 47, 31, 57,  9, 63, 29, 26, 20,  3, 44, 12,
       50, 51, 35, 14,  7, 24, 30, 59, 18, 36, 37,  0, 55, 65, 52, 39, 45,
       56, 38, 61, 60, 17, 54, 34,  4, 19, 22, 49,  8, 62, 13, 58]), 'cur_cost': 94860.0}, {'tour': array([26, 39, 29, 62,  1, 20,  3, 31, 42, 18, 60, 30, 35, 12,  5, 22, 21,
       27, 44, 24, 59, 25, 51, 50, 53, 38, 37,  8, 64, 46, 34,  0, 45,  6,
       55, 58, 16, 61,  2, 14, 56, 52, 48, 40, 54, 13,  4, 63,  9, 19, 43,
       57, 32, 28, 41, 36, 47, 11, 10, 15,  7, 33, 17, 65, 49, 23]), 'cur_cost': 111721.0}, {'tour': array([34, 25, 52, 33, 16, 18, 46, 30, 27, 13, 23,  6, 36, 21, 19, 56, 37,
        7,  3, 54, 28, 43, 32, 49,  5, 58, 20, 41, 65, 53, 57, 62, 39, 29,
        9,  4, 14,  2, 42, 44, 11,  0, 24, 47, 10, 63, 64, 45, 61, 48, 22,
        1, 40, 15, 38, 35, 55, 50, 31, 51,  8, 17, 12, 59, 26, 60]), 'cur_cost': 108868.0}, {'tour': array([49, 58, 15, 57, 51, 10, 14, 13, 34, 29,  3, 27, 11, 22,  1, 42,  2,
        6,  8, 65, 64, 24,  0, 56, 48, 40, 32, 46, 28, 59, 23,  7,  9, 16,
       12, 45, 38, 61, 39,  5, 55, 26, 25, 52, 37,  4, 44, 17, 60, 43, 63,
       47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]), 'cur_cost': 108774.0}, {'tour': array([57, 10, 52, 33, 37,  4, 48,  5,  2, 35, 14,  6, 39, 26, 54, 22, 47,
       44, 28, 41,  3,  0, 18, 65, 61, 64, 34, 15, 38, 19, 63,  7, 25, 13,
       60, 32, 30,  9, 40,  8, 36, 58, 46, 49, 20, 59, 53, 24, 31, 23,  1,
       17, 11, 51, 50, 56, 29, 55, 21, 45, 12, 62, 27, 43, 16, 42]), 'cur_cost': 110161.0}]
2025-07-04 10:06:14,856 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:14,856 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-04 10:06:14,856 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 10:06:14,856 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:06:14,856 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:14,858 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111721.0 (rank 90.0%)
- Population diversity: 0.7369230769230769
  • Edge uniqueness: 0.737
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7369230769230769, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:14,858 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:14,858 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:18,695 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23],
  "targeted_regions": "Opportunity regions around nodes 14, 12.  Sparse cells explored by slight adjustments.",
  "strategy_comment": "Minor path perturbations focusing on potential local optimization around opportunity regions to improve cost."
}
```
2025-07-04 10:06:18,696 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:18,696 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111721.0, 路径: [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23]
2025-07-04 10:06:18,696 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}
2025-07-04 10:06:18,697 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:06:18,697 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:18,697 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:18,698 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109257.0
2025-07-04 10:06:19,201 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:06:19,201 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:06:19,201 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:19,207 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:19,207 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([61,  3,  6, 52, 64, 34, 13, 20, 62, 28, 63, 37, 53, 40, 48, 46, 30,
       47, 60, 59, 49, 65, 24, 11, 10, 27, 42, 36,  8, 54,  4,  0,  2, 33,
       38, 58, 22,  1, 23,  9, 12, 51, 39, 21, 17, 43, 29, 26, 50, 19, 44,
       18, 35, 25, 45, 14, 15, 56,  7, 57, 31, 55, 16,  5, 32, 41]), 'cur_cost': 102634.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([23,  1,  5,  6, 64, 53, 21, 27, 15, 32, 11, 41, 43, 48, 40, 25, 28,
       42, 33, 46, 10, 16,  2, 47, 31, 57,  9, 63, 29, 26, 20,  3, 44, 12,
       50, 51, 35, 14,  7, 24, 30, 59, 18, 36, 37,  0, 55, 65, 52, 39, 45,
       56, 38, 61, 60, 17, 54, 34,  4, 19, 22, 49,  8, 62, 13, 58]), 'cur_cost': 94860.0}, {'tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}, {'tour': array([56, 24, 58, 11, 30,  4, 13, 51,  5, 40, 19, 21, 27, 42, 45, 38, 10,
       26,  6, 16, 32,  0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20,  3,
       46, 31, 36, 55, 54, 35, 34,  9, 25,  7, 33, 23,  2, 18, 47, 59, 60,
        8, 15, 57,  1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]), 'cur_cost': 109257.0}, {'tour': array([49, 58, 15, 57, 51, 10, 14, 13, 34, 29,  3, 27, 11, 22,  1, 42,  2,
        6,  8, 65, 64, 24,  0, 56, 48, 40, 32, 46, 28, 59, 23,  7,  9, 16,
       12, 45, 38, 61, 39,  5, 55, 26, 25, 52, 37,  4, 44, 17, 60, 43, 63,
       47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]), 'cur_cost': 108774.0}, {'tour': array([57, 10, 52, 33, 37,  4, 48,  5,  2, 35, 14,  6, 39, 26, 54, 22, 47,
       44, 28, 41,  3,  0, 18, 65, 61, 64, 34, 15, 38, 19, 63,  7, 25, 13,
       60, 32, 30,  9, 40,  8, 36, 58, 46, 49, 20, 59, 53, 24, 31, 23,  1,
       17, 11, 51, 50, 56, 29, 55, 21, 45, 12, 62, 27, 43, 16, 42]), 'cur_cost': 110161.0}]
2025-07-04 10:06:19,209 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:19,209 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-04 10:06:19,209 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:06:19,209 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-04 10:06:19,209 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:19,211 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108774.0 (rank 60.0%)
- Population diversity: 0.7369230769230769
  • Edge uniqueness: 0.737
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early_exploration
- Landscape recommends focus: explore_exploit

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [49, 58, 15, 57, 51, 10, 14, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 12, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [{"description": "High-density cells in the density grid provide potential opportunity regions. Focus on nodes clustered in the higher density cells; i.e., [14, 12, 12, 14].", "analysis": "The density grid highlights areas with a higher concentration of nodes, where the search can focus on local optimization or edge exploitation. The distribution may be non-uniform, so focusing on these regions may yield faster optimization progress."}]
  • Difficult regions (full): [{"description": "Potential difficult regions along long-edge corridors or low-density areas. Specifically, the long edge ratio of 0.249 suggests the presence of difficult edges, and the edge length statistics indicate a significant spread. Further investigation is required to pinpoint exact locations, likely involving the edges in the edge_len_stats.", "analysis": "Long edge ratio and edge length spread imply challenging areas where the search struggles.  The clustering information is minimal, suggesting that there are not any obvious sub-problems to exploit, so the long edge areas are the first place to look. Also, given the relatively high bounding box, nodes may be spread out, which could increase long edges. The direction hist information shows some bias, but given the few samples available, it is difficult to make definite conclusions."}]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7369230769230769, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:19,212 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:19,212 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:22,407 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35],
  "targeted_regions": "Exploiting cluster near nodes [14, 12, 13]. Avoiding potentially difficult edges.",
  "strategy_comment": "Slightly altered path through opportunity region, aims to balance cost and diversity by keeping existing path structure."
}
```
2025-07-04 10:06:22,407 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:22,410 - ExplorationExpert - INFO - 探索路径生成完成，成本: 108767.0, 路径: [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35]
2025-07-04 10:06:22,410 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}
2025-07-04 10:06:22,410 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:06:22,410 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:22,410 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:22,410 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 93114.0
2025-07-04 10:06:22,911 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:06:22,911 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:06:22,911 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:22,918 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:22,918 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([61,  3,  6, 52, 64, 34, 13, 20, 62, 28, 63, 37, 53, 40, 48, 46, 30,
       47, 60, 59, 49, 65, 24, 11, 10, 27, 42, 36,  8, 54,  4,  0,  2, 33,
       38, 58, 22,  1, 23,  9, 12, 51, 39, 21, 17, 43, 29, 26, 50, 19, 44,
       18, 35, 25, 45, 14, 15, 56,  7, 57, 31, 55, 16,  5, 32, 41]), 'cur_cost': 102634.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([23,  1,  5,  6, 64, 53, 21, 27, 15, 32, 11, 41, 43, 48, 40, 25, 28,
       42, 33, 46, 10, 16,  2, 47, 31, 57,  9, 63, 29, 26, 20,  3, 44, 12,
       50, 51, 35, 14,  7, 24, 30, 59, 18, 36, 37,  0, 55, 65, 52, 39, 45,
       56, 38, 61, 60, 17, 54, 34,  4, 19, 22, 49,  8, 62, 13, 58]), 'cur_cost': 94860.0}, {'tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}, {'tour': array([56, 24, 58, 11, 30,  4, 13, 51,  5, 40, 19, 21, 27, 42, 45, 38, 10,
       26,  6, 16, 32,  0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20,  3,
       46, 31, 36, 55, 54, 35, 34,  9, 25,  7, 33, 23,  2, 18, 47, 59, 60,
        8, 15, 57,  1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]), 'cur_cost': 109257.0}, {'tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}, {'tour': array([39, 31, 36, 60, 24, 20, 45, 63, 19, 53, 57, 40, 38, 61, 26, 22, 10,
       11,  2,  3, 46, 16, 14, 28, 30,  1, 51, 13, 29, 33, 35,  4, 41, 50,
       12, 15, 42, 44, 64, 56,  8, 62, 65,  0,  9, 58,  5, 23, 49, 52, 43,
        6, 18, 47,  7, 55, 32, 37, 21, 17, 34, 25, 59, 48, 54, 27]), 'cur_cost': 93114.0}]
2025-07-04 10:06:22,920 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:22,920 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-04 10:06:22,920 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:06:22,920 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}}]
2025-07-04 10:06:22,920 - __main__ - INFO - 进化阶段完成
2025-07-04 10:06:22,920 - __main__ - INFO - 开始评估阶段
2025-07-04 10:06:22,920 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-04 10:06:22,920 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:06:22,920 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 10022.0 → 10061.0 (Δ -39.0)
- Mean cost: 79979.8 → 75249.9
- Diversity: 0.737 → 0.729 (Δ -0.0077)

#### Elite metrics
- Elite count: 12 → 17
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.214 → 0.153 (Δ -0.0612)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ 206.0
- Exploit: 4 inds, success 75.0% , avg Δ 11515.75

#### Other indicators
- No-change individuals: 1
- Regression individuals: 3
- Historical trends: Last 1 iterations: costs [10022.0], diversity [0.9148148148148147]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:06:22,920 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:26,213 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.75
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Increase explore ratio slightly. Since exploit is more successful, focus on maintaining or improving elite quality. Explore might be finding new areas.",
    "Suggest setting explore_ratio to 0.4",
    "Monitor diversity closely as it is decreasing in elites. Consider adjusting diversity weight in subsequent iterations if this trend continues."
  ]
}
```
2025-07-04 10:06:26,242 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:06:26,242 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.75
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Increase explore ratio slightly. Since exploit is more successful, focus on maintaining or improving elite quality. Explore might be finding new areas.",
    "Suggest setting explore_ratio to 0.4",
    "Monitor diversity closely as it is decreasing in elites. Consider adjusting diversity weight in subsequent iterations if this trend continues."
  ]
}
```
2025-07-04 10:06:26,242 - __main__ - INFO - 评估阶段完成
2025-07-04 10:06:26,244 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.75
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Increase explore ratio slightly. Since exploit is more successful, focus on maintaining or improving elite quality. Explore might be finding new areas.",
    "Suggest setting explore_ratio to 0.4",
    "Monitor diversity closely as it is decreasing in elites. Consider adjusting diversity weight in subsequent iterations if this trend continues."
  ]
}
```
2025-07-04 10:06:26,245 - __main__ - INFO - 当前最佳适应度: 10061.0
2025-07-04 10:06:26,245 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-04 10:06:26,245 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-04 10:06:26,247 - __main__ - INFO - 开始分析阶段
2025-07-04 10:06:26,247 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:06:26,265 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 111721.0, 'mean': 75249.9, 'std': 42275.51443199716}, 'diversity': 0.9087542087542086, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-04 10:06:26,265 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 111721.0, 'mean': 75249.9, 'std': 42275.51443199716}, 'diversity_level': 0.9087542087542086, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:06:26,266 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:06:26,266 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:06:26,266 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:06:26,269 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:06:26,269 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:06:26,269 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-04 10:06:26,269 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:06:26,271 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:06:26,271 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:06:26,277 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:06:26,278 - __main__ - INFO - 精英专家分析报告: {'elite_count': 17, 'elite_common_features': {'common_edges': {'(18, 16)': 0.7058823529411765}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 540.0, 'avg_gap': 61588.25294117646}, 'structure_gap': {'unique_elite_edges': 163, 'unique_pop_edges': 441, 'common_edges': 95}}, 'elite_diversity': {'diversity_score': 0.2825311942959001}}
2025-07-04 10:06:26,278 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:06:26,278 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:06:26,280 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:06:26,280 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 10061.0, mean 75249.9, max 111721.0, std 42275.51443199716
- diversity: 0.9087542087542086
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10022.0, 10061.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2825311942959001}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:06:26,280 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:30,340 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.75,
    "modality": "likely multi-modal",
    "deceptiveness": "potentially deceptive"
  },
  "population_state": {
    "diversity": 0.9087542087542086,
    "convergence": 0.0,
    "clustering": "weak, but present (9 clusters)"
  },
  "difficult_regions": [
    "Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).",
    "Potential long-edge corridors may exist"
  ],
  "opportunity_regions": [
    "High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.",
	"The 17.455 NN median dist suggests potential short edges within the opportunity regions."
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore and exploit",
    "operators": [
      "Apply edge recombination operators, focusing on the opportunity regions (high-density cells).",
      "Use 2-opt or 3-opt to locally optimize routes within the high-density clusters.",
      "Employ a diversification strategy to prevent premature convergence (e.g., increase mutation rate or introduce new solutions).",
      "Consider guided exploration: Incorporate spatial information (bounding box, centroid, density grid) into the generation/mutation process to bias toward the opportunity regions and avoid long edges."
    ]
  }
}
```
2025-07-04 10:06:30,340 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:06:30,340 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.75, 'modality': 'likely multi-modal', 'deceptiveness': 'potentially deceptive'}, 'population_state': {'diversity': 0.9087542087542086, 'convergence': 0.0, 'clustering': 'weak, but present (9 clusters)'}, 'difficult_regions': ['Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).', 'Potential long-edge corridors may exist'], 'opportunity_regions': ['High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.', 'The 17.455 NN median dist suggests potential short edges within the opportunity regions.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Apply edge recombination operators, focusing on the opportunity regions (high-density cells).', 'Use 2-opt or 3-opt to locally optimize routes within the high-density clusters.', 'Employ a diversification strategy to prevent premature convergence (e.g., increase mutation rate or introduce new solutions).', 'Consider guided exploration: Incorporate spatial information (bounding box, centroid, density grid) into the generation/mutation process to bias toward the opportunity regions and avoid long edges.']}}
2025-07-04 10:06:30,340 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:06:30,340 - __main__ - INFO - 分析阶段完成
2025-07-04 10:06:30,340 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.75, 'modality': 'likely multi-modal', 'deceptiveness': 'potentially deceptive'}, 'population_state': {'diversity': 0.9087542087542086, 'convergence': 0.0, 'clustering': 'weak, but present (9 clusters)'}, 'difficult_regions': ['Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).', 'Potential long-edge corridors may exist'], 'opportunity_regions': ['High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.', 'The 17.455 NN median dist suggests potential short edges within the opportunity regions.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Apply edge recombination operators, focusing on the opportunity regions (high-density cells).', 'Use 2-opt or 3-opt to locally optimize routes within the high-density clusters.', 'Employ a diversification strategy to prevent premature convergence (e.g., increase mutation rate or introduce new solutions).', 'Consider guided exploration: Incorporate spatial information (bounding box, centroid, density grid) into the generation/mutation process to bias toward the opportunity regions and avoid long edges.']}}
2025-07-04 10:06:30,340 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:06:30,340 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:06:30,340 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.909
  • best_cost: 10061.0
  • mean_cost: 75249.9
- Search-space: ruggedness 0.75, modality likely multi-modal, deceptiveness potentially deceptive
- Evolution phase: early exploration
- Landscape focus suggestion: explore and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): Regions between long, Potential long-edge 
- Opportunity regions (# 2): High-density regions, The 17.455 NN median
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.75
  },
  "bala...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:06:30,340 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:06:30,340 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:32,603 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Focus on discovering new potential solutions, favoring explore."
}
```
2025-07-04 10:06:32,603 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:06:32,603 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:06:32,603 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Focus on discovering new potential solutions, favoring explore."
}
```
2025-07-04 10:06:32,603 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:06:32,603 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:06:32,603 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Focus on discovering new potential solutions, favoring explore."
}
```
2025-07-04 10:06:32,603 - __main__ - INFO - 开始进化阶段
2025-07-04 10:06:32,603 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:06:32,603 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:32,603 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10061.0 (rank 0.0%)
- Population diversity: 0.7292307692307692
  • Edge uniqueness: 0.729
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.902
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: High-density re, The 17.455 NN m
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Regions between, Potential long-
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.", "The 17.455 NN median dist suggests potential short edges within the opportunity regions."]
  • Difficult regions (full): ["Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).", "Potential long-edge corridors may exist"]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7292307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9015625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:32,603 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:32,603 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:35,613 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55],
  "targeted_regions": "Exploits high-density cells in opportunity regions.",
  "strategy_comment": "Focus on established path, optimizing short edges and avoiding known problematic long edge areas."
}
```
2025-07-04 10:06:35,613 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:35,613 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10061.0, 路径: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
2025-07-04 10:06:35,613 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}
2025-07-04 10:06:35,613 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:06:35,613 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:35,613 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10092.0 (rank 10.0%)
- Population diversity: 0.7292307692307692
  • Edge uniqueness: 0.729
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.902
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: High-density re, The 17.455 NN m
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Regions between, Potential long-
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.", "The 17.455 NN median dist suggests potential short edges within the opportunity regions."]
  • Difficult regions (full): ["Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).", "Potential long-edge corridors may exist"]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7292307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9015625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:35,613 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:35,613 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:38,759 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27],
  "targeted_regions": "Exploiting high-density regions (0,0),(2,0),(1,1),(2,2) and aiming for short edges within those areas.",
  "strategy_comment": "Maintain path structure but explore alternative connections within high-density clusters to reduce edge lengths."
}
```
2025-07-04 10:06:38,759 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:38,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10092.0, 路径: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
2025-07-04 10:06:38,759 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}
2025-07-04 10:06:38,759 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:06:38,759 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:38,759 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13735.0 (rank 20.0%)
- Population diversity: 0.7292307692307692
  • Edge uniqueness: 0.729
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.902
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: High-density re, The 17.455 NN m
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Regions between, Potential long-
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.", "The 17.455 NN median dist suggests potential short edges within the opportunity regions."]
  • Difficult regions (full): ["Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).", "Potential long-edge corridors may exist"]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7292307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9015625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:38,759 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:38,759 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:41,816 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54],
  "targeted_regions": "Prioritizing cell (0,0) and (2,0), explore connections within.",
  "strategy_comment": "Focused on exploring opportunity regions and maintaining current node order as a base, while aiming for better edge utilization."
}
```
2025-07-04 10:06:41,817 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:41,817 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13735.0, 路径: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
2025-07-04 10:06:41,817 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}
2025-07-04 10:06:41,818 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-04 10:06:41,818 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:41,818 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:41,818 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 114065.0
2025-07-04 10:06:42,322 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:06:42,322 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:06:42,322 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:06:42,328 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:42,328 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([28, 47, 59, 54, 23, 10, 15, 12, 31,  2, 32, 62, 24, 37,  3, 16, 34,
       51, 60, 49, 55, 52, 46,  8, 30, 41,  5, 50, 65, 18, 64, 26, 25,  0,
       22, 57, 53, 45, 27, 17, 42,  9, 36, 61,  4, 20, 29, 13, 43, 21, 14,
        6, 33, 58, 38, 48, 44, 39, 11,  7, 63, 19,  1, 40, 35, 56]), 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([23,  1,  5,  6, 64, 53, 21, 27, 15, 32, 11, 41, 43, 48, 40, 25, 28,
       42, 33, 46, 10, 16,  2, 47, 31, 57,  9, 63, 29, 26, 20,  3, 44, 12,
       50, 51, 35, 14,  7, 24, 30, 59, 18, 36, 37,  0, 55, 65, 52, 39, 45,
       56, 38, 61, 60, 17, 54, 34,  4, 19, 22, 49,  8, 62, 13, 58]), 'cur_cost': 94860.0}, {'tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}, {'tour': array([56, 24, 58, 11, 30,  4, 13, 51,  5, 40, 19, 21, 27, 42, 45, 38, 10,
       26,  6, 16, 32,  0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20,  3,
       46, 31, 36, 55, 54, 35, 34,  9, 25,  7, 33, 23,  2, 18, 47, 59, 60,
        8, 15, 57,  1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]), 'cur_cost': 109257.0}, {'tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}, {'tour': array([39, 31, 36, 60, 24, 20, 45, 63, 19, 53, 57, 40, 38, 61, 26, 22, 10,
       11,  2,  3, 46, 16, 14, 28, 30,  1, 51, 13, 29, 33, 35,  4, 41, 50,
       12, 15, 42, 44, 64, 56,  8, 62, 65,  0,  9, 58,  5, 23, 49, 52, 43,
        6, 18, 47,  7, 55, 32, 37, 21, 17, 34, 25, 59, 48, 54, 27]), 'cur_cost': 93114.0}]
2025-07-04 10:06:42,330 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:42,330 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-04 10:06:42,331 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-04 10:06:42,331 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:06:42,332 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:42,332 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98258.0 (rank 50.0%)
- Population diversity: 0.7369230769230769
  • Edge uniqueness: 0.737
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.902
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: High-density re, The 17.455 NN m
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Regions between, Potential long-
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.", "The 17.455 NN median dist suggests potential short edges within the opportunity regions."]
  • Difficult regions (full): ["Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).", "Potential long-edge corridors may exist"]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7369230769230769, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9015625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:42,332 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:42,332 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:45,236 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11],
  "targeted_regions": "High-density regions and adjacent low-density cells.",
  "strategy_comment": "Prioritize exploration of opportunities while maintaining edge uniqueness and path diversity."
}
```
2025-07-04 10:06:45,237 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:45,238 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98258.0, 路径: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11]
2025-07-04 10:06:45,238 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}
2025-07-04 10:06:45,238 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 10:06:45,238 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:45,239 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:45,239 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 103583.0
2025-07-04 10:06:45,743 - ExploitationExpert - INFO - res_population_num: 18
2025-07-04 10:06:45,743 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521]
2025-07-04 10:06:45,743 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:45,749 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:45,751 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([28, 47, 59, 54, 23, 10, 15, 12, 31,  2, 32, 62, 24, 37,  3, 16, 34,
       51, 60, 49, 55, 52, 46,  8, 30, 41,  5, 50, 65, 18, 64, 26, 25,  0,
       22, 57, 53, 45, 27, 17, 42,  9, 36, 61,  4, 20, 29, 13, 43, 21, 14,
        6, 33, 58, 38, 48, 44, 39, 11,  7, 63, 19,  1, 40, 35, 56]), 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([40, 55,  8, 59, 13,  1, 22, 30, 18, 20,  3, 44, 57, 43, 51, 16,  7,
       28, 15, 62, 17, 31, 14,  4, 38, 29, 61, 63,  5, 39, 46, 52, 32, 53,
        2, 12, 58,  9, 19, 37, 24, 42, 10, 11, 36,  6,  0, 33, 26, 47, 41,
       64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 25, 48, 49]), 'cur_cost': 103583.0}, {'tour': [26, 39, 29, 62, 1, 20, 3, 31, 42, 18, 60, 30, 35, 12, 5, 22, 21, 27, 44, 24, 59, 25, 51, 50, 53, 38, 37, 8, 64, 46, 34, 0, 45, 6, 55, 58, 16, 61, 2, 14, 56, 52, 48, 40, 54, 13, 4, 63, 9, 19, 43, 57, 32, 28, 41, 36, 47, 11, 10, 15, 7, 33, 17, 65, 49, 23], 'cur_cost': 111721.0}, {'tour': array([56, 24, 58, 11, 30,  4, 13, 51,  5, 40, 19, 21, 27, 42, 45, 38, 10,
       26,  6, 16, 32,  0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20,  3,
       46, 31, 36, 55, 54, 35, 34,  9, 25,  7, 33, 23,  2, 18, 47, 59, 60,
        8, 15, 57,  1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]), 'cur_cost': 109257.0}, {'tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}, {'tour': array([39, 31, 36, 60, 24, 20, 45, 63, 19, 53, 57, 40, 38, 61, 26, 22, 10,
       11,  2,  3, 46, 16, 14, 28, 30,  1, 51, 13, 29, 33, 35,  4, 41, 50,
       12, 15, 42, 44, 64, 56,  8, 62, 65,  0,  9, 58,  5, 23, 49, 52, 43,
        6, 18, 47,  7, 55, 32, 37, 21, 17, 34, 25, 59, 48, 54, 27]), 'cur_cost': 93114.0}]
2025-07-04 10:06:45,753 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:45,753 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-04 10:06:45,753 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 10:06:45,753 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 10:06:45,754 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:45,754 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:45,754 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 116011.0
2025-07-04 10:06:46,255 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-07-04 10:06:46,257 - ExploitationExpert - INFO - res_population_num: 18
2025-07-04 10:06:46,257 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521]
2025-07-04 10:06:46,258 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:46,264 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:46,265 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([28, 47, 59, 54, 23, 10, 15, 12, 31,  2, 32, 62, 24, 37,  3, 16, 34,
       51, 60, 49, 55, 52, 46,  8, 30, 41,  5, 50, 65, 18, 64, 26, 25,  0,
       22, 57, 53, 45, 27, 17, 42,  9, 36, 61,  4, 20, 29, 13, 43, 21, 14,
        6, 33, 58, 38, 48, 44, 39, 11,  7, 63, 19,  1, 40, 35, 56]), 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([40, 55,  8, 59, 13,  1, 22, 30, 18, 20,  3, 44, 57, 43, 51, 16,  7,
       28, 15, 62, 17, 31, 14,  4, 38, 29, 61, 63,  5, 39, 46, 52, 32, 53,
        2, 12, 58,  9, 19, 37, 24, 42, 10, 11, 36,  6,  0, 33, 26, 47, 41,
       64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 25, 48, 49]), 'cur_cost': 103583.0}, {'tour': array([45, 52, 18, 27, 57,  3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37,
       22, 26, 33,  4, 42,  7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46,
       15, 65, 60, 20, 64, 10, 29,  2, 25, 61, 24,  8, 21, 54, 49, 48, 19,
       34, 17, 41, 32, 63,  6, 12, 62,  5,  1,  9,  0, 50, 13, 31]), 'cur_cost': 116011.0}, {'tour': array([56, 24, 58, 11, 30,  4, 13, 51,  5, 40, 19, 21, 27, 42, 45, 38, 10,
       26,  6, 16, 32,  0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20,  3,
       46, 31, 36, 55, 54, 35, 34,  9, 25,  7, 33, 23,  2, 18, 47, 59, 60,
        8, 15, 57,  1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]), 'cur_cost': 109257.0}, {'tour': [49, 58, 15, 57, 51, 10, 14, 12, 13, 34, 29, 3, 27, 11, 22, 1, 42, 2, 6, 8, 65, 64, 24, 0, 56, 48, 40, 32, 46, 28, 59, 23, 7, 9, 16, 45, 38, 61, 39, 5, 55, 26, 25, 52, 37, 4, 44, 17, 60, 43, 63, 47, 31, 18, 62, 21, 36, 19, 41, 50, 33, 30, 20, 53, 54, 35], 'cur_cost': 108767.0}, {'tour': array([39, 31, 36, 60, 24, 20, 45, 63, 19, 53, 57, 40, 38, 61, 26, 22, 10,
       11,  2,  3, 46, 16, 14, 28, 30,  1, 51, 13, 29, 33, 35,  4, 41, 50,
       12, 15, 42, 44, 64, 56,  8, 62, 65,  0,  9, 58,  5, 23, 49, 52, 43,
        6, 18, 47,  7, 55, 32, 37, 21, 17, 34, 25, 59, 48, 54, 27]), 'cur_cost': 93114.0}]
2025-07-04 10:06:46,267 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:46,267 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-04 10:06:46,268 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 10:06:46,268 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 10:06:46,268 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:46,269 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109257.0 (rank 70.0%)
- Population diversity: 0.7476923076923077
  • Edge uniqueness: 0.748
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.902
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit

### Opportunity Regions
- Sample regions: High-density re, The 17.455 NN m
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Regions between, Potential long-
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High-density regions identified by the density grid (3x3): Cell coordinates: (0,0) with 14 nodes; (2,0) with 14 nodes; (1,1) with 12 nodes; (2,2) with 14 nodes - potential areas to exploit for efficient edge connections.", "The 17.455 NN median dist suggests potential short edges within the opportunity regions."]
  • Difficult regions (full): ["Regions between long edges (edge_len_stats: med=1900.0, max=3032.0, long_edge_ratio: 0.249).", "Potential long-edge corridors may exist"]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7476923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9015625}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:46,269 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:46,269 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:49,200 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41],
  "targeted_regions": "Exploiting the 17.455 NN median distance within potential short edge clusters.",
  "strategy_comment": "Refine existing path, focusing on short edges within high-density areas and maintain diversity."
}
```
2025-07-04 10:06:49,201 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:06:49,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109257.0, 路径: [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41]
2025-07-04 10:06:49,201 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41], 'cur_cost': 109257.0}
2025-07-04 10:06:49,201 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:06:49,201 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:49,201 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:49,201 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114063.0
2025-07-04 10:06:49,709 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 10:06:49,709 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521]
2025-07-04 10:06:49,710 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:49,716 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:49,716 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([28, 47, 59, 54, 23, 10, 15, 12, 31,  2, 32, 62, 24, 37,  3, 16, 34,
       51, 60, 49, 55, 52, 46,  8, 30, 41,  5, 50, 65, 18, 64, 26, 25,  0,
       22, 57, 53, 45, 27, 17, 42,  9, 36, 61,  4, 20, 29, 13, 43, 21, 14,
        6, 33, 58, 38, 48, 44, 39, 11,  7, 63, 19,  1, 40, 35, 56]), 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([40, 55,  8, 59, 13,  1, 22, 30, 18, 20,  3, 44, 57, 43, 51, 16,  7,
       28, 15, 62, 17, 31, 14,  4, 38, 29, 61, 63,  5, 39, 46, 52, 32, 53,
        2, 12, 58,  9, 19, 37, 24, 42, 10, 11, 36,  6,  0, 33, 26, 47, 41,
       64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 25, 48, 49]), 'cur_cost': 103583.0}, {'tour': array([45, 52, 18, 27, 57,  3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37,
       22, 26, 33,  4, 42,  7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46,
       15, 65, 60, 20, 64, 10, 29,  2, 25, 61, 24,  8, 21, 54, 49, 48, 19,
       34, 17, 41, 32, 63,  6, 12, 62,  5,  1,  9,  0, 50, 13, 31]), 'cur_cost': 116011.0}, {'tour': [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41], 'cur_cost': 109257.0}, {'tour': array([44, 20,  6, 10, 60, 51, 33, 30, 40,  4, 58,  7, 36, 17, 56,  9, 32,
        3, 46, 21, 59, 31, 65, 15,  5, 57, 39, 26,  0, 14, 42, 54, 12, 47,
       43, 52, 64, 41, 29, 62, 49,  2, 63,  8, 23, 35, 61, 45, 28, 11, 38,
       27, 25, 18, 24, 22, 34, 55, 48, 53,  1, 13, 16, 37, 50, 19]), 'cur_cost': 114063.0}, {'tour': array([39, 31, 36, 60, 24, 20, 45, 63, 19, 53, 57, 40, 38, 61, 26, 22, 10,
       11,  2,  3, 46, 16, 14, 28, 30,  1, 51, 13, 29, 33, 35,  4, 41, 50,
       12, 15, 42, 44, 64, 56,  8, 62, 65,  0,  9, 58,  5, 23, 49, 52, 43,
        6, 18, 47,  7, 55, 32, 37, 21, 17, 34, 25, 59, 48, 54, 27]), 'cur_cost': 93114.0}]
2025-07-04 10:06:49,719 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:06:49,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-04 10:06:49,720 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:06:49,720 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:06:49,720 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:06:49,720 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:06:49,720 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101508.0
2025-07-04 10:06:50,222 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 10:06:50,223 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521]
2025-07-04 10:06:50,223 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:06:50,230 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:06:50,230 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([28, 47, 59, 54, 23, 10, 15, 12, 31,  2, 32, 62, 24, 37,  3, 16, 34,
       51, 60, 49, 55, 52, 46,  8, 30, 41,  5, 50, 65, 18, 64, 26, 25,  0,
       22, 57, 53, 45, 27, 17, 42,  9, 36, 61,  4, 20, 29, 13, 43, 21, 14,
        6, 33, 58, 38, 48, 44, 39, 11,  7, 63, 19,  1, 40, 35, 56]), 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': array([40, 55,  8, 59, 13,  1, 22, 30, 18, 20,  3, 44, 57, 43, 51, 16,  7,
       28, 15, 62, 17, 31, 14,  4, 38, 29, 61, 63,  5, 39, 46, 52, 32, 53,
        2, 12, 58,  9, 19, 37, 24, 42, 10, 11, 36,  6,  0, 33, 26, 47, 41,
       64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 25, 48, 49]), 'cur_cost': 103583.0}, {'tour': array([45, 52, 18, 27, 57,  3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37,
       22, 26, 33,  4, 42,  7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46,
       15, 65, 60, 20, 64, 10, 29,  2, 25, 61, 24,  8, 21, 54, 49, 48, 19,
       34, 17, 41, 32, 63,  6, 12, 62,  5,  1,  9,  0, 50, 13, 31]), 'cur_cost': 116011.0}, {'tour': [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41], 'cur_cost': 109257.0}, {'tour': array([44, 20,  6, 10, 60, 51, 33, 30, 40,  4, 58,  7, 36, 17, 56,  9, 32,
        3, 46, 21, 59, 31, 65, 15,  5, 57, 39, 26,  0, 14, 42, 54, 12, 47,
       43, 52, 64, 41, 29, 62, 49,  2, 63,  8, 23, 35, 61, 45, 28, 11, 38,
       27, 25, 18, 24, 22, 34, 55, 48, 53,  1, 13, 16, 37, 50, 19]), 'cur_cost': 114063.0}, {'tour': array([35, 16,  4, 19, 20, 10, 37, 55, 44, 39, 57, 62, 43, 26,  2,  7,  3,
       49, 42, 40, 13, 28, 21, 47, 15, 53, 36, 51, 50, 60, 31,  0, 45,  5,
       27, 29, 12, 48, 58, 32, 46, 30, 52, 63, 64,  6, 65, 11, 33, 56, 25,
       34,  9, 61, 22, 54,  1, 14, 41, 38, 17, 18, 59, 23, 24,  8]), 'cur_cost': 101508.0}]
2025-07-04 10:06:50,232 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:06:50,233 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-04 10:06:50,233 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:06:50,233 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27], 'cur_cost': 10092.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [56, 24, 58, 11, 30, 4, 13, 51, 5, 40, 19, 21, 27, 42, 45, 38, 10, 26, 6, 16, 32, 0, 29, 44, 39, 64, 48, 62, 12, 28, 61, 63, 20, 3, 46, 31, 36, 55, 54, 35, 34, 9, 25, 7, 33, 23, 2, 18, 47, 59, 60, 8, 15, 57, 1, 50, 65, 37, 22, 53, 17, 14, 52, 43, 49, 41], 'cur_cost': 109257.0}}]
2025-07-04 10:06:50,233 - __main__ - INFO - 进化阶段完成
2025-07-04 10:06:50,233 - __main__ - INFO - 开始评估阶段
2025-07-04 10:06:50,234 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-04 10:06:50,236 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:06:50,236 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 10061.0 → 10061.0 (Δ 0.0)
- Mean cost: 75249.9 → 79063.3
- Diversity: 0.729 → 0.769 (Δ 0.04)

#### Elite metrics
- Elite count: 17 → 19
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.153 → 0.139 (Δ -0.0137)

#### Strategy performance
- Explore: 5 inds, success 0.0% , avg Δ 0.0
- Exploit: 5 inds, success 0.0% , avg Δ -7626.8

#### Other indicators
- No-change individuals: 5
- Regression individuals: 5
- Historical trends: Last 2 iterations: costs [10022.0, 10061.0], diversity [0.9148148148148147, 0.9087542087542086]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:06:50,236 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:52,349 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": -7626.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Reduce exploit intensity and increase explore intensity.",
    "Increase diversity weight.",
    "Consider increasing population size to help escape local optima."
  ]
}
```
2025-07-04 10:06:52,375 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:06:52,375 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": -7626.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Reduce exploit intensity and increase explore intensity.",
    "Increase diversity weight.",
    "Consider increasing population size to help escape local optima."
  ]
}
```
2025-07-04 10:06:52,375 - __main__ - INFO - 评估阶段完成
2025-07-04 10:06:52,375 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": -7626.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Reduce exploit intensity and increase explore intensity.",
    "Increase diversity weight.",
    "Consider increasing population size to help escape local optima."
  ]
}
```
2025-07-04 10:06:52,375 - __main__ - INFO - 当前最佳适应度: 10061.0
2025-07-04 10:06:52,375 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_2.pkl
2025-07-04 10:06:52,375 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-04 10:06:52,375 - __main__ - INFO - 开始分析阶段
2025-07-04 10:06:52,375 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:06:52,402 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 116011.0, 'mean': 79063.3, 'std': 44706.20898946812}, 'diversity': 0.9245791245791245, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-04 10:06:52,402 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 116011.0, 'mean': 79063.3, 'std': 44706.20898946812}, 'diversity_level': 0.9245791245791245, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:06:52,402 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:06:52,404 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:06:52,404 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:06:52,405 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:06:52,405 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:06:52,405 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-04 10:06:52,405 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:06:52,410 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:06:52,410 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:06:52,416 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:06:52,416 - __main__ - INFO - 精英专家分析报告: {'elite_count': 19, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 540.0, 'avg_gap': 65837.51052631579}, 'structure_gap': {'unique_elite_edges': 166, 'unique_pop_edges': 463, 'common_edges': 96}}, 'elite_diversity': {'diversity_score': 0.2819422293106504}}
2025-07-04 10:06:52,416 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:06:52,417 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:06:52,418 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:06:52,419 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 10061.0, mean 79063.3, max 116011.0, std 44706.20898946812
- diversity: 0.9245791245791245
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10022.0, 10061.0, 10061.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2819422293106504}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:06:52,419 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:55,913 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.6,
    "modality": "multi",
    "deceptiveness": "likely"
  },
  "population_state": {
    "diversity": 0.92,
    "convergence": 0.0,
    "clustering": "very weak"
  },
  "difficult_regions": [
    "Edges with length > 2469.0 (Q3) appear to be difficult.",
    "Corridors suggested by low density areas in the density_grid may contain difficult sections."
  ],
  "opportunity_regions": [
    "High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.",
    "Investigate nodes within the high density cells for edge improvements."
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "exploration & exploitation",
    "operators": [
      "Apply a diverse range of crossover operators with a high mutation rate to promote exploration, given the high diversity and low convergence.",
      "Consider using edge-based recombination operators focusing on high density areas as identified in opportunity regions.",
      "Implement operators that attempt to avoid long edges and exploit short edges, to address the likely deceptive nature of the landscape.",
      "Use local search operators to fine tune solutions, focusing on the identified opportunity regions."
    ]
  }
}
```
2025-07-04 10:06:55,913 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:06:55,913 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'likely'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': ['Edges with length > 2469.0 (Q3) appear to be difficult.', 'Corridors suggested by low density areas in the density_grid may contain difficult sections.'], 'opportunity_regions': ['High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.', 'Investigate nodes within the high density cells for edge improvements.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'exploration & exploitation', 'operators': ['Apply a diverse range of crossover operators with a high mutation rate to promote exploration, given the high diversity and low convergence.', 'Consider using edge-based recombination operators focusing on high density areas as identified in opportunity regions.', 'Implement operators that attempt to avoid long edges and exploit short edges, to address the likely deceptive nature of the landscape.', 'Use local search operators to fine tune solutions, focusing on the identified opportunity regions.']}}
2025-07-04 10:06:55,913 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:06:55,915 - __main__ - INFO - 分析阶段完成
2025-07-04 10:06:55,915 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'likely'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': ['Edges with length > 2469.0 (Q3) appear to be difficult.', 'Corridors suggested by low density areas in the density_grid may contain difficult sections.'], 'opportunity_regions': ['High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.', 'Investigate nodes within the high density cells for edge improvements.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'exploration & exploitation', 'operators': ['Apply a diverse range of crossover operators with a high mutation rate to promote exploration, given the high diversity and low convergence.', 'Consider using edge-based recombination operators focusing on high density areas as identified in opportunity regions.', 'Implement operators that attempt to avoid long edges and exploit short edges, to address the likely deceptive nature of the landscape.', 'Use local search operators to fine tune solutions, focusing on the identified opportunity regions.']}}
2025-07-04 10:06:55,915 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:06:55,915 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:06:55,915 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 10061.0
  • mean_cost: 79063.3
- Search-space: ruggedness 0.6, modality multi, deceptiveness likely
- Evolution phase: early exploration
- Landscape focus suggestion: exploration & exploitation
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): Edges with length > , Corridors suggested 
- Opportunity regions (# 2): High density cells i, Investigate nodes wi
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": -7626.8
  },
  "b...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:06:55,915 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:06:55,915 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:06:58,107 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Strategy effectiveness feedback shows exploration ineffectual, so a slight exploitation is assigned."
}
```
2025-07-04 10:06:58,107 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:06:58,107 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:06:58,107 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Strategy effectiveness feedback shows exploration ineffectual, so a slight exploitation is assigned."
}
```
2025-07-04 10:06:58,107 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:06:58,107 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:06:58,107 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Strategy effectiveness feedback shows exploration ineffectual, so a slight exploitation is assigned."
}
```
2025-07-04 10:06:58,107 - __main__ - INFO - 开始进化阶段
2025-07-04 10:06:58,107 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:06:58,107 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:06:58,107 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10061.0 (rank 0.0%)
- Population diversity: 0.7692307692307693
  • Edge uniqueness: 0.769
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7692307692307693, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:06:58,107 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:06:58,107 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:01,263 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55],
  "targeted_regions": "Exploiting high-density cells, avoiding long edges.",
  "strategy_comment": "Maintain the base path and apply slight alterations near the opportunity and difficult regions."
}
```
2025-07-04 10:07:01,263 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:01,263 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10061.0, 路径: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
2025-07-04 10:07:01,263 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}
2025-07-04 10:07:01,263 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:07:01,263 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:01,263 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10092.0 (rank 10.0%)
- Population diversity: 0.7692307692307693
  • Edge uniqueness: 0.769
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.903
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7692307692307693, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.903125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:01,268 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:01,268 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:04,014 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27],
  "targeted_regions": "Exploiting opportunity regions and avoiding difficult edges.",
  "strategy_comment": "Focus on exploring low-density cells adjacent to opportunity regions while maintaining diversity."
}
```
2025-07-04 10:07:04,014 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:04,014 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15781.0, 路径: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55]
2025-07-04 10:07:04,014 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}
2025-07-04 10:07:04,014 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:07:04,014 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:04,014 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13735.0 (rank 10.0%)
- Population diversity: 0.7723076923076924
  • Edge uniqueness: 0.772
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7723076923076924, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:04,019 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:04,019 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:06,988 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54],
  "targeted_regions": "High density cells, explore sparse areas",
  "strategy_comment": "Minor reordering around high-density clusters for exploration while avoiding long edges."
}
```
2025-07-04 10:07:06,988 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:06,988 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13735.0, 路径: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
2025-07-04 10:07:06,988 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}
2025-07-04 10:07:06,988 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:07:06,988 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:06,988 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114065.0 (rank 80.0%)
- Population diversity: 0.7723076923076924
  • Edge uniqueness: 0.772
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7723076923076924, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:06,988 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:06,988 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:10,045 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56],
  "targeted_regions": "Exploiting opportunity regions; avoiding long edges.",
  "strategy_comment": "Focused on visiting opportunities while maintaining distance from identified difficult areas, maximizing exploration in early phase."
}
```
2025-07-04 10:07:10,047 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:10,047 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114065.0, 路径: [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56]
2025-07-04 10:07:10,047 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56], 'cur_cost': 114065.0}
2025-07-04 10:07:10,047 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 10:07:10,047 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:10,047 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98258.0 (rank 30.0%)
- Population diversity: 0.7723076923076924
  • Edge uniqueness: 0.772
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7723076923076924, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:10,047 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:10,047 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:12,984 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11],
  "targeted_regions": "Explore high-density cells and avoid long edges.",
  "strategy_comment": "Prioritize exploration of suggested cells and maintain node adjacency diversity."
}
```
2025-07-04 10:07:12,984 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:12,984 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98258.0, 路径: [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11]
2025-07-04 10:07:12,984 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}
2025-07-04 10:07:12,984 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:07:12,984 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:12,984 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103583.0 (rank 50.0%)
- Population diversity: 0.7723076923076924
  • Edge uniqueness: 0.772
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 25, 48, 49]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7723076923076924, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:12,984 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:12,984 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:15,979 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49],
  "targeted_regions": "Prioritize exploration of high-density cells, while maintaining distance to difficult edges.",
  "strategy_comment": "Maintain diversity by re-evaluating edge choices within opportunity regions, and avoids difficult edges through node reordering."
}
```
2025-07-04 10:07:15,979 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:15,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103555.0, 路径: [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25]
2025-07-04 10:07:15,981 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}
2025-07-04 10:07:15,981 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:07:15,981 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:15,981 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116011.0 (rank 90.0%)
- Population diversity: 0.7723076923076924
  • Edge uniqueness: 0.772
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration
- Landscape recommends focus: exploration & exploitation

### Opportunity Regions
- Sample regions: High density ce, Investigate nod
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: Edges with leng, Corridors sugge
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): ["High density cells in the density_grid: [14, 12, 14] suggest potential areas to exploit.", "Investigate nodes within the high density cells for edge improvements."]
  • Difficult regions (full): ["Edges with length > 2469.0 (Q3) appear to be difficult.", "Corridors suggested by low density areas in the density_grid may contain difficult sections."]
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7723076923076924, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:15,981 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:15,981 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:18,974 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31],
  "targeted_regions": "High density cells (14,12,14). Avoided edges > 2469.",
  "strategy_comment": "Maintained node order while exploring opportunity regions and detouring difficult edges for diversification."
}
```
2025-07-04 10:07:18,976 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:18,976 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116011.0, 路径: [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31]
2025-07-04 10:07:18,976 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}
2025-07-04 10:07:18,976 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:07:18,978 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:18,978 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:18,978 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106447.0
2025-07-04 10:07:19,480 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-07-04 10:07:19,482 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 10:07:19,482 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:19,483 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:19,489 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:19,490 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56], 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([44, 20,  6, 10, 60, 51, 33, 30, 40,  4, 58,  7, 36, 17, 56,  9, 32,
        3, 46, 21, 59, 31, 65, 15,  5, 57, 39, 26,  0, 14, 42, 54, 12, 47,
       43, 52, 64, 41, 29, 62, 49,  2, 63,  8, 23, 35, 61, 45, 28, 11, 38,
       27, 25, 18, 24, 22, 34, 55, 48, 53,  1, 13, 16, 37, 50, 19]), 'cur_cost': 114063.0}, {'tour': array([35, 16,  4, 19, 20, 10, 37, 55, 44, 39, 57, 62, 43, 26,  2,  7,  3,
       49, 42, 40, 13, 28, 21, 47, 15, 53, 36, 51, 50, 60, 31,  0, 45,  5,
       27, 29, 12, 48, 58, 32, 46, 30, 52, 63, 64,  6, 65, 11, 33, 56, 25,
       34,  9, 61, 22, 54,  1, 14, 41, 38, 17, 18, 59, 23, 24,  8]), 'cur_cost': 101508.0}]
2025-07-04 10:07:19,492 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:19,492 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-04 10:07:19,492 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:07:19,492 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:07:19,492 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:19,492 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:19,492 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106035.0
2025-07-04 10:07:19,999 - ExploitationExpert - INFO - res_population_num: 20
2025-07-04 10:07:19,999 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521]
2025-07-04 10:07:19,999 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:07:20,006 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:20,006 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56], 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([15, 64, 44, 54, 20, 21, 56, 50, 13,  9, 19, 18, 65,  7, 32, 45, 35,
       14, 26, 52, 12, 17, 11,  5, 24,  8, 31, 61, 37, 57, 46, 41, 53, 63,
       34, 28,  1,  3, 43, 16, 58, 10, 22,  0, 59, 38, 47, 39, 33, 49, 62,
        6, 48, 29, 27,  4,  2, 40, 23, 25, 51, 60, 55, 42, 36, 30]), 'cur_cost': 106035.0}, {'tour': array([35, 16,  4, 19, 20, 10, 37, 55, 44, 39, 57, 62, 43, 26,  2,  7,  3,
       49, 42, 40, 13, 28, 21, 47, 15, 53, 36, 51, 50, 60, 31,  0, 45,  5,
       27, 29, 12, 48, 58, 32, 46, 30, 52, 63, 64,  6, 65, 11, 33, 56, 25,
       34,  9, 61, 22, 54,  1, 14, 41, 38, 17, 18, 59, 23, 24,  8]), 'cur_cost': 101508.0}]
2025-07-04 10:07:20,008 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:07:20,008 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-04 10:07:20,009 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:07:20,009 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:07:20,010 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:20,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:20,010 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104270.0
2025-07-04 10:07:21,359 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:21,359 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773, 9521, 9521]
2025-07-04 10:07:21,359 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:07:21,366 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:21,367 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56], 'cur_cost': 114065.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([15, 64, 44, 54, 20, 21, 56, 50, 13,  9, 19, 18, 65,  7, 32, 45, 35,
       14, 26, 52, 12, 17, 11,  5, 24,  8, 31, 61, 37, 57, 46, 41, 53, 63,
       34, 28,  1,  3, 43, 16, 58, 10, 22,  0, 59, 38, 47, 39, 33, 49, 62,
        6, 48, 29, 27,  4,  2, 40, 23, 25, 51, 60, 55, 42, 36, 30]), 'cur_cost': 106035.0}, {'tour': array([ 1, 27, 48, 50, 14, 38, 56, 29, 18, 64, 53, 26, 55,  5, 61, 62, 12,
       44,  9, 41, 57, 49, 47, 40, 39, 63, 21,  8, 51,  4, 33, 45, 16, 46,
        0, 23, 35, 22, 65, 36, 25, 19, 52, 31, 30, 10, 59, 20, 24, 37, 28,
       42,  3, 13, 34,  2, 32,  7, 43, 15, 11, 60,  6, 17, 58, 54]), 'cur_cost': 104270.0}]
2025-07-04 10:07:21,367 - ExploitationExpert - INFO - 局部搜索耗时: 1.36秒
2025-07-04 10:07:21,367 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-04 10:07:21,369 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:07:21,369 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [28, 47, 59, 54, 23, 10, 15, 12, 31, 2, 32, 62, 24, 37, 3, 16, 34, 51, 60, 49, 55, 52, 46, 8, 30, 41, 5, 50, 65, 18, 64, 26, 25, 0, 22, 57, 53, 45, 27, 17, 42, 9, 36, 61, 4, 20, 29, 13, 43, 21, 14, 6, 33, 58, 38, 48, 44, 39, 11, 7, 63, 19, 1, 40, 35, 56], 'cur_cost': 114065.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}}]
2025-07-04 10:07:21,369 - __main__ - INFO - 进化阶段完成
2025-07-04 10:07:21,369 - __main__ - INFO - 开始评估阶段
2025-07-04 10:07:21,369 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-04 10:07:21,372 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:07:21,372 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 10061.0 → 10061.0 (Δ 0.0)
- Mean cost: 79063.3 → 78821.8
- Diversity: 0.769 → 0.749 (Δ -0.02)

#### Elite metrics
- Elite count: 19 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.139 → 0.126 (Δ -0.0133)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ -808.71
- Exploit: 3 inds, success 66.7% , avg Δ 2692.0

#### Other indicators
- No-change individuals: 5
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [10022.0, 10061.0, 10061.0], diversity [0.9148148148148147, 0.9087542087542086, 0.9245791245791245]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:07:21,373 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:23,760 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {
    "explore": 0.2,
    "exploit": 0.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Decrease exploit ratio. The elite is not improving, and the best elite cost has not changed. The explore success rate is low while the exploit success is much higher.",
    "Increase the number of individuals exploring, potentially by increasing the explore ratio by 0.1-0.2.",
    "Monitor elite diversity closely. If it decreases further, consider increasing diversity weight."
  ]
}
```
2025-07-04 10:07:23,791 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:07:23,791 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {
    "explore": 0.2,
    "exploit": 0.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Decrease exploit ratio. The elite is not improving, and the best elite cost has not changed. The explore success rate is low while the exploit success is much higher.",
    "Increase the number of individuals exploring, potentially by increasing the explore ratio by 0.1-0.2.",
    "Monitor elite diversity closely. If it decreases further, consider increasing diversity weight."
  ]
}
```
2025-07-04 10:07:23,791 - __main__ - INFO - 评估阶段完成
2025-07-04 10:07:23,792 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {
    "explore": 0.2,
    "exploit": 0.8
  },
  "balance_state": "unbalanced",
  "recommendations": [
    "Decrease exploit ratio. The elite is not improving, and the best elite cost has not changed. The explore success rate is low while the exploit success is much higher.",
    "Increase the number of individuals exploring, potentially by increasing the explore ratio by 0.1-0.2.",
    "Monitor elite diversity closely. If it decreases further, consider increasing diversity weight."
  ]
}
```
2025-07-04 10:07:23,792 - __main__ - INFO - 当前最佳适应度: 10061.0
2025-07-04 10:07:23,795 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_3.pkl
2025-07-04 10:07:23,795 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-04 10:07:23,795 - __main__ - INFO - 开始分析阶段
2025-07-04 10:07:23,795 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:07:23,813 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 116011.0, 'mean': 78821.8, 'std': 43248.6060163793}, 'diversity': 0.9212121212121214, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-04 10:07:23,814 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10061.0, 'max': 116011.0, 'mean': 78821.8, 'std': 43248.6060163793}, 'diversity_level': 0.9212121212121214, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:07:23,815 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:07:23,815 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:07:23,815 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:07:23,818 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 10:07:23,818 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:07:23,819 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-04 10:07:23,819 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:07:23,819 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:07:23,819 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:07:23,828 - EliteExpert - INFO - 精英解分析完成
2025-07-04 10:07:23,828 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 540.0, 'avg_gap': 65948.84761904762}, 'structure_gap': {'unique_elite_edges': 162, 'unique_pop_edges': 454, 'common_edges': 103}}, 'elite_diversity': {'diversity_score': 0.2829004329004329}}
2025-07-04 10:07:23,828 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:07:23,828 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:07:23,831 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:07:23,831 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 10061.0, mean 78821.8, max 116011.0, std 43248.6060163793
- diversity: 0.9212121212121214
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10061.0, 10061.0, 10061.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2829004329004329}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 10:07:23,832 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:26,429 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {"ruggedness": 0.7, "modality": "multi", "deceptiveness": "likely"},
  "population_state": {"diversity": 0.92, "convergence": 0.0, "clustering": "very weak"},
  "difficult_regions": [],
  "opportunity_regions": [[1,1, "center"]],
  "evolution_phase": "early exploration/stagnation",
  "evolution_direction": {
    "recommended_focus": "diversify and exploit",
    "operators": ["diversification operators (e.g., large neighborhood search, random restarts) to escape stagnation and maintain diversity, edge swap operators focusing in the center."]
  }
}
```
2025-07-04 10:07:26,429 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:07:26,429 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.7, 'modality': 'multi', 'deceptiveness': 'likely'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': [], 'opportunity_regions': [[1, 1, 'center']], 'evolution_phase': 'early exploration/stagnation', 'evolution_direction': {'recommended_focus': 'diversify and exploit', 'operators': ['diversification operators (e.g., large neighborhood search, random restarts) to escape stagnation and maintain diversity, edge swap operators focusing in the center.']}}
2025-07-04 10:07:26,431 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:07:26,431 - __main__ - INFO - 分析阶段完成
2025-07-04 10:07:26,431 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7, 'modality': 'multi', 'deceptiveness': 'likely'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'very weak'}, 'difficult_regions': [], 'opportunity_regions': [[1, 1, 'center']], 'evolution_phase': 'early exploration/stagnation', 'evolution_direction': {'recommended_focus': 'diversify and exploit', 'operators': ['diversification operators (e.g., large neighborhood search, random restarts) to escape stagnation and maintain diversity, edge swap operators focusing in the center.']}}
2025-07-04 10:07:26,431 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:07:26,432 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:07:26,432 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 10061.0
  • mean_cost: 78821.8
- Search-space: ruggedness 0.7, modality multi, deceptiveness likely
- Evolution phase: early exploration/stagnation
- Landscape focus suggestion: diversify and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 1): [1, 1, 'center']
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {
    "explore": 0.2,
    "exploit": 0.8
...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:07:26,432 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:07:26,432 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:28,678 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration/stagnation and high diversity. Population is exploring, opportunity region present."
}
```
2025-07-04 10:07:28,680 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:07:28,680 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:07:28,680 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration/stagnation and high diversity. Population is exploring, opportunity region present."
}
```
2025-07-04 10:07:28,680 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:07:28,680 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit']
2025-07-04 10:07:28,680 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration/stagnation and high diversity. Population is exploring, opportunity region present."
}
```
2025-07-04 10:07:28,680 - __main__ - INFO - 开始进化阶段
2025-07-04 10:07:28,680 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:07:28,680 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:28,680 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10061.0 (rank 0.0%)
- Population diversity: 0.7492307692307693
  • Edge uniqueness: 0.749
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration/stagnation
- Landscape recommends focus: diversify and exploit

### Opportunity Regions
- Sample regions: [1, 1, 'center'
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: None
- Characteristics: No specific difficult regions identified
- Avoidance patterns: Focus on general path quality

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [[1, 1, "center"]]
  • Difficult regions (full): []
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7492307692307693, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:28,680 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:28,680 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:31,759 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55],
  "targeted_regions": "Maintain order, explore/diversify",
  "strategy_comment": "Slightly reordered nodes in opportunity region; aim to diversify edge choices."
}
```
2025-07-04 10:07:31,759 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:31,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10061.0, 路径: [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55]
2025-07-04 10:07:31,759 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}
2025-07-04 10:07:31,759 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:07:31,759 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:31,759 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15781.0 (rank 20.0%)
- Population diversity: 0.7492307692307693
  • Edge uniqueness: 0.749
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration/stagnation
- Landscape recommends focus: diversify and exploit

### Opportunity Regions
- Sample regions: [1, 1, 'center'
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: None
- Characteristics: No specific difficult regions identified
- Avoidance patterns: Focus on general path quality

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [[1, 1, "center"]]
  • Difficult regions (full): []
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7492307692307693, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:31,759 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:31,763 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:35,152 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55],
  "targeted_regions": "Opportunity regions and sparse cells exploration",
  "strategy_comment": "Initial exploration focuses on existing path structure; diversifying by starting at different positions and inserting new edges."
}
```
2025-07-04 10:07:35,152 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:35,152 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15781.0, 路径: [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55]
2025-07-04 10:07:35,152 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}
2025-07-04 10:07:35,152 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:07:35,152 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:35,152 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13735.0 (rank 10.0%)
- Population diversity: 0.7492307692307693
  • Edge uniqueness: 0.749
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration/stagnation
- Landscape recommends focus: diversify and exploit

### Opportunity Regions
- Sample regions: [1, 1, 'center'
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: None
- Characteristics: No specific difficult regions identified
- Avoidance patterns: Focus on general path quality

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [[1, 1, "center"]]
  • Difficult regions (full): []
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7492307692307693, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:35,152 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:35,152 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:38,025 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54],
  "targeted_regions": "center area",
  "strategy_comment": "Minor modification in the center region for diversity; leverages existing high-quality path structure."
}
```
2025-07-04 10:07:38,025 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:38,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13735.0, 路径: [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54]
2025-07-04 10:07:38,025 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}
2025-07-04 10:07:38,025 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-04 10:07:38,025 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:38,025 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:38,025 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 102647.0
2025-07-04 10:07:38,526 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:38,526 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:38,526 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:38,534 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:38,535 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([36,  1, 15, 32, 14, 11, 13, 56, 20, 52, 23, 58, 30, 21, 26, 18, 53,
        7,  4, 41,  2, 38, 35, 46,  3,  6, 29, 34, 12, 22, 25, 24, 49, 33,
       16, 60, 55, 59, 37,  5,  0, 40, 43, 10, 63, 27, 61, 48, 47, 39,  8,
       62, 17,  9, 44, 50, 57, 54, 42, 28, 51, 45, 65, 19, 64, 31]), 'cur_cost': 102647.0}, {'tour': [2, 3, 33, 34, 27, 61, 22, 10, 7, 46, 0, 39, 5, 49, 43, 37, 52, 29, 48, 15, 14, 12, 53, 51, 40, 56, 20, 57, 55, 45, 30, 31, 25, 60, 54, 19, 13, 23, 17, 8, 9, 63, 47, 44, 16, 4, 24, 18, 21, 6, 64, 42, 38, 59, 41, 50, 26, 62, 1, 35, 58, 32, 65, 28, 36, 11], 'cur_cost': 98258.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([15, 64, 44, 54, 20, 21, 56, 50, 13,  9, 19, 18, 65,  7, 32, 45, 35,
       14, 26, 52, 12, 17, 11,  5, 24,  8, 31, 61, 37, 57, 46, 41, 53, 63,
       34, 28,  1,  3, 43, 16, 58, 10, 22,  0, 59, 38, 47, 39, 33, 49, 62,
        6, 48, 29, 27,  4,  2, 40, 23, 25, 51, 60, 55, 42, 36, 30]), 'cur_cost': 106035.0}, {'tour': array([ 1, 27, 48, 50, 14, 38, 56, 29, 18, 64, 53, 26, 55,  5, 61, 62, 12,
       44,  9, 41, 57, 49, 47, 40, 39, 63, 21,  8, 51,  4, 33, 45, 16, 46,
        0, 23, 35, 22, 65, 36, 25, 19, 52, 31, 30, 10, 59, 20, 24, 37, 28,
       42,  3, 13, 34,  2, 32,  7, 43, 15, 11, 60,  6, 17, 58, 54]), 'cur_cost': 104270.0}]
2025-07-04 10:07:38,536 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:38,536 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-04 10:07:38,537 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-04 10:07:38,537 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 10:07:38,537 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:38,537 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:38,538 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 95710.0
2025-07-04 10:07:39,041 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:39,041 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:39,041 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:39,049 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:39,049 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([36,  1, 15, 32, 14, 11, 13, 56, 20, 52, 23, 58, 30, 21, 26, 18, 53,
        7,  4, 41,  2, 38, 35, 46,  3,  6, 29, 34, 12, 22, 25, 24, 49, 33,
       16, 60, 55, 59, 37,  5,  0, 40, 43, 10, 63, 27, 61, 48, 47, 39,  8,
       62, 17,  9, 44, 50, 57, 54, 42, 28, 51, 45, 65, 19, 64, 31]), 'cur_cost': 102647.0}, {'tour': array([36, 42, 32, 49, 47, 62, 57, 44, 35,  3, 16, 40, 10, 13, 48,  7, 15,
       30, 28, 39, 21, 26, 46, 20,  1, 19, 63, 53, 61, 59, 56, 45, 23, 22,
       11, 38,  6,  4, 25, 37, 55, 34, 29, 12, 52, 14, 51,  2, 31, 17, 64,
       18, 27, 33, 58,  5, 60,  0,  8,  9, 24, 43, 50, 65, 54, 41]), 'cur_cost': 95710.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': [45, 52, 18, 27, 57, 3, 40, 58, 14, 44, 23, 30, 36, 39, 16, 11, 37, 22, 26, 33, 4, 42, 7, 38, 56, 51, 28, 35, 55, 47, 59, 43, 53, 46, 15, 65, 60, 20, 64, 10, 29, 2, 25, 61, 24, 8, 21, 54, 49, 48, 19, 34, 17, 41, 32, 63, 6, 12, 62, 5, 1, 9, 0, 50, 13, 31], 'cur_cost': 116011.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([15, 64, 44, 54, 20, 21, 56, 50, 13,  9, 19, 18, 65,  7, 32, 45, 35,
       14, 26, 52, 12, 17, 11,  5, 24,  8, 31, 61, 37, 57, 46, 41, 53, 63,
       34, 28,  1,  3, 43, 16, 58, 10, 22,  0, 59, 38, 47, 39, 33, 49, 62,
        6, 48, 29, 27,  4,  2, 40, 23, 25, 51, 60, 55, 42, 36, 30]), 'cur_cost': 106035.0}, {'tour': array([ 1, 27, 48, 50, 14, 38, 56, 29, 18, 64, 53, 26, 55,  5, 61, 62, 12,
       44,  9, 41, 57, 49, 47, 40, 39, 63, 21,  8, 51,  4, 33, 45, 16, 46,
        0, 23, 35, 22, 65, 36, 25, 19, 52, 31, 30, 10, 59, 20, 24, 37, 28,
       42,  3, 13, 34,  2, 32,  7, 43, 15, 11, 60,  6, 17, 58, 54]), 'cur_cost': 104270.0}]
2025-07-04 10:07:39,052 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:39,052 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-04 10:07:39,053 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-04 10:07:39,053 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:07:39,053 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:39,054 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103555.0 (rank 50.0%)
- Population diversity: 0.7446153846153846
  • Edge uniqueness: 0.745
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration/stagnation
- Landscape recommends focus: diversify and exploit

### Opportunity Regions
- Sample regions: [1, 1, 'center'
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: None
- Characteristics: No specific difficult regions identified
- Avoidance patterns: Focus on general path quality

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [[1, 1, "center"]]
  • Difficult regions (full): []
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7446153846153846, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:39,054 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:39,054 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:42,081 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25],
  "targeted_regions": "Leveraging opportunity region via node 1",
  "strategy_comment": "Maintain existing path structure and slightly modify for diversity near opportunity region 1."
}
```
2025-07-04 10:07:42,081 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:42,081 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103555.0, 路径: [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25]
2025-07-04 10:07:42,081 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}
2025-07-04 10:07:42,081 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 10:07:42,081 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:42,081 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:42,081 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 94797.0
2025-07-04 10:07:42,584 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:42,584 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:42,584 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:42,591 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:42,592 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([36,  1, 15, 32, 14, 11, 13, 56, 20, 52, 23, 58, 30, 21, 26, 18, 53,
        7,  4, 41,  2, 38, 35, 46,  3,  6, 29, 34, 12, 22, 25, 24, 49, 33,
       16, 60, 55, 59, 37,  5,  0, 40, 43, 10, 63, 27, 61, 48, 47, 39,  8,
       62, 17,  9, 44, 50, 57, 54, 42, 28, 51, 45, 65, 19, 64, 31]), 'cur_cost': 102647.0}, {'tour': array([36, 42, 32, 49, 47, 62, 57, 44, 35,  3, 16, 40, 10, 13, 48,  7, 15,
       30, 28, 39, 21, 26, 46, 20,  1, 19, 63, 53, 61, 59, 56, 45, 23, 22,
       11, 38,  6,  4, 25, 37, 55, 34, 29, 12, 52, 14, 51,  2, 31, 17, 64,
       18, 27, 33, 58,  5, 60,  0,  8,  9, 24, 43, 50, 65, 54, 41]), 'cur_cost': 95710.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': array([45, 44, 60, 54, 22,  1, 11, 30, 57,  7, 48, 47, 37, 32,  4, 29, 56,
       14, 53, 61, 15, 16,  0,  9,  3,  5, 31, 28, 26, 42, 58, 62, 10, 36,
       50,  8, 20, 12, 33, 52, 55,  2, 19, 59, 49, 18, 38, 63, 13, 39, 64,
       17, 27, 51, 41, 40, 25, 46, 24, 43, 21,  6, 23, 34, 35, 65]), 'cur_cost': 94797.0}, {'tour': array([ 5, 29,  7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49,  6, 61, 54, 57,
       46, 20, 37,  0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11,
       38, 15, 12, 13,  9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22,  1, 51,
        2,  8,  3, 10, 59, 25, 35,  4, 42, 17, 34, 23, 32, 64, 27]), 'cur_cost': 106447.0}, {'tour': array([15, 64, 44, 54, 20, 21, 56, 50, 13,  9, 19, 18, 65,  7, 32, 45, 35,
       14, 26, 52, 12, 17, 11,  5, 24,  8, 31, 61, 37, 57, 46, 41, 53, 63,
       34, 28,  1,  3, 43, 16, 58, 10, 22,  0, 59, 38, 47, 39, 33, 49, 62,
        6, 48, 29, 27,  4,  2, 40, 23, 25, 51, 60, 55, 42, 36, 30]), 'cur_cost': 106035.0}, {'tour': array([ 1, 27, 48, 50, 14, 38, 56, 29, 18, 64, 53, 26, 55,  5, 61, 62, 12,
       44,  9, 41, 57, 49, 47, 40, 39, 63, 21,  8, 51,  4, 33, 45, 16, 46,
        0, 23, 35, 22, 65, 36, 25, 19, 52, 31, 30, 10, 59, 20, 24, 37, 28,
       42,  3, 13, 34,  2, 32,  7, 43, 15, 11, 60,  6, 17, 58, 54]), 'cur_cost': 104270.0}]
2025-07-04 10:07:42,594 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:42,594 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-04 10:07:42,594 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 10:07:42,594 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-04 10:07:42,594 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:07:42,596 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106447.0 (rank 90.0%)
- Population diversity: 0.7507692307692307
  • Edge uniqueness: 0.751
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.908
- Evolution phase: early exploration/stagnation
- Landscape recommends focus: diversify and exploit

### Opportunity Regions
- Sample regions: [1, 1, 'center'
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: None
- Characteristics: No specific difficult regions identified
- Avoidance patterns: Focus on general path quality

### Spatial & Additional Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution
  • Full path sequence: [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27]
  • Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
  • Opportunity regions (full): [[1, 1, "center"]]
  • Difficult regions (full): []
  • Diversity metrics (detailed): {"edge_uniqueness": 0.7507692307692307, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9078125}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:07:42,596 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:07:42,596 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:45,791 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27],
  "targeted_regions": "Opportunity region [1,1,'center'] explored through node sequence.",
  "strategy_comment": "Maintains existing order, diversifying by subtle adjustments around the center, to improve path shape. Risk-averse."
}
```
2025-07-04 10:07:45,791 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:07:45,791 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106447.0, 路径: [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27]
2025-07-04 10:07:45,791 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27], 'cur_cost': 106447.0}
2025-07-04 10:07:45,791 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:07:45,791 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:45,791 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:45,791 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 99397.0
2025-07-04 10:07:46,293 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:46,293 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:46,293 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:46,299 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:46,301 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([36,  1, 15, 32, 14, 11, 13, 56, 20, 52, 23, 58, 30, 21, 26, 18, 53,
        7,  4, 41,  2, 38, 35, 46,  3,  6, 29, 34, 12, 22, 25, 24, 49, 33,
       16, 60, 55, 59, 37,  5,  0, 40, 43, 10, 63, 27, 61, 48, 47, 39,  8,
       62, 17,  9, 44, 50, 57, 54, 42, 28, 51, 45, 65, 19, 64, 31]), 'cur_cost': 102647.0}, {'tour': array([36, 42, 32, 49, 47, 62, 57, 44, 35,  3, 16, 40, 10, 13, 48,  7, 15,
       30, 28, 39, 21, 26, 46, 20,  1, 19, 63, 53, 61, 59, 56, 45, 23, 22,
       11, 38,  6,  4, 25, 37, 55, 34, 29, 12, 52, 14, 51,  2, 31, 17, 64,
       18, 27, 33, 58,  5, 60,  0,  8,  9, 24, 43, 50, 65, 54, 41]), 'cur_cost': 95710.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': array([45, 44, 60, 54, 22,  1, 11, 30, 57,  7, 48, 47, 37, 32,  4, 29, 56,
       14, 53, 61, 15, 16,  0,  9,  3,  5, 31, 28, 26, 42, 58, 62, 10, 36,
       50,  8, 20, 12, 33, 52, 55,  2, 19, 59, 49, 18, 38, 63, 13, 39, 64,
       17, 27, 51, 41, 40, 25, 46, 24, 43, 21,  6, 23, 34, 35, 65]), 'cur_cost': 94797.0}, {'tour': [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27], 'cur_cost': 106447.0}, {'tour': array([ 7, 62, 65,  5, 55, 37, 53, 44, 60, 14, 49, 17, 21, 23,  2, 52, 25,
        8, 33,  0, 31, 40,  4, 27, 35, 29, 39, 46, 12, 45, 13, 38, 54, 24,
       43, 22, 56, 57, 61, 36,  3, 20, 47, 18, 59, 16, 32, 28, 34, 41,  1,
        9, 50, 10, 63, 58, 15, 30, 26, 11,  6, 51, 42, 64, 48, 19]), 'cur_cost': 99397.0}, {'tour': array([ 1, 27, 48, 50, 14, 38, 56, 29, 18, 64, 53, 26, 55,  5, 61, 62, 12,
       44,  9, 41, 57, 49, 47, 40, 39, 63, 21,  8, 51,  4, 33, 45, 16, 46,
        0, 23, 35, 22, 65, 36, 25, 19, 52, 31, 30, 10, 59, 20, 24, 37, 28,
       42,  3, 13, 34,  2, 32,  7, 43, 15, 11, 60,  6, 17, 58, 54]), 'cur_cost': 104270.0}]
2025-07-04 10:07:46,303 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:46,303 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-07-04 10:07:46,303 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:07:46,303 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:07:46,304 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:07:46,304 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:07:46,304 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105608.0
2025-07-04 10:07:46,806 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 10:07:46,806 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9526, 9526, 9545, 9555, 9582, 79773]
2025-07-04 10:07:46,806 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 17, 12, 22, 15, 14, 20, 21, 13, 23, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33,
       25, 26, 36, 19, 18, 16, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40,
       49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 59, 62, 64, 57, 54, 60, 58, 56, 14,
       15, 17, 12, 22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 33, 25, 26,
       36, 37, 31, 24, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 33,  8,  5, 57, 11,  3,  9, 65,  4, 49, 52, 59, 62, 53, 14, 31,
       42, 23, 63, 35, 26, 24, 28, 15, 46, 45,  1, 18, 19, 32, 29, 27, 21,
       40, 34, 37, 47, 22, 43, 30,  7,  6, 20, 10, 48, 50, 38, 16, 25, 13,
       58, 54, 44,  2, 39, 36, 56, 60, 64, 41, 51, 55, 61, 12, 17],
      dtype=int64)]
2025-07-04 10:07:46,814 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:07:46,814 - ExploitationExpert - INFO - populations: [{'tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}, {'tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}, {'tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}, {'tour': array([36,  1, 15, 32, 14, 11, 13, 56, 20, 52, 23, 58, 30, 21, 26, 18, 53,
        7,  4, 41,  2, 38, 35, 46,  3,  6, 29, 34, 12, 22, 25, 24, 49, 33,
       16, 60, 55, 59, 37,  5,  0, 40, 43, 10, 63, 27, 61, 48, 47, 39,  8,
       62, 17,  9, 44, 50, 57, 54, 42, 28, 51, 45, 65, 19, 64, 31]), 'cur_cost': 102647.0}, {'tour': array([36, 42, 32, 49, 47, 62, 57, 44, 35,  3, 16, 40, 10, 13, 48,  7, 15,
       30, 28, 39, 21, 26, 46, 20,  1, 19, 63, 53, 61, 59, 56, 45, 23, 22,
       11, 38,  6,  4, 25, 37, 55, 34, 29, 12, 52, 14, 51,  2, 31, 17, 64,
       18, 27, 33, 58,  5, 60,  0,  8,  9, 24, 43, 50, 65, 54, 41]), 'cur_cost': 95710.0}, {'tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}, {'tour': array([45, 44, 60, 54, 22,  1, 11, 30, 57,  7, 48, 47, 37, 32,  4, 29, 56,
       14, 53, 61, 15, 16,  0,  9,  3,  5, 31, 28, 26, 42, 58, 62, 10, 36,
       50,  8, 20, 12, 33, 52, 55,  2, 19, 59, 49, 18, 38, 63, 13, 39, 64,
       17, 27, 51, 41, 40, 25, 46, 24, 43, 21,  6, 23, 34, 35, 65]), 'cur_cost': 94797.0}, {'tour': [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27], 'cur_cost': 106447.0}, {'tour': array([ 7, 62, 65,  5, 55, 37, 53, 44, 60, 14, 49, 17, 21, 23,  2, 52, 25,
        8, 33,  0, 31, 40,  4, 27, 35, 29, 39, 46, 12, 45, 13, 38, 54, 24,
       43, 22, 56, 57, 61, 36,  3, 20, 47, 18, 59, 16, 32, 28, 34, 41,  1,
        9, 50, 10, 63, 58, 15, 30, 26, 11,  6, 51, 42, 64, 48, 19]), 'cur_cost': 99397.0}, {'tour': array([65, 22,  3, 24, 64, 34, 52, 14, 13, 31,  1, 58, 63, 41, 23, 10, 48,
       54, 16, 26, 18,  0, 38, 43, 44, 29, 60, 45, 28, 55,  5,  4, 56, 35,
       17, 30,  7,  8, 39, 53,  9, 20, 11, 59, 12, 19, 33,  6, 57, 40, 36,
       25, 61, 49, 51, 15,  2, 27, 50, 32, 37, 47, 21, 46, 42, 62]), 'cur_cost': 105608.0}]
2025-07-04 10:07:46,816 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:07:46,816 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-07-04 10:07:46,816 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:07:46,816 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 2, 6, 9, 11, 7, 3, 1, 0, 10, 14, 12, 15, 22, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 36, 26, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42, 52, 63, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55], 'cur_cost': 10061.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [35, 28, 30, 34, 26, 25, 31, 33, 32, 29, 24, 37, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 54, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 36, 27, 55], 'cur_cost': 15781.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [25, 26, 37, 36, 31, 33, 28, 30, 35, 34, 32, 29, 24, 27, 18, 16, 23, 22, 12, 14, 15, 17, 21, 20, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42, 54], 'cur_cost': 13735.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [40, 55, 8, 59, 13, 1, 22, 30, 18, 20, 3, 44, 57, 43, 51, 16, 7, 28, 15, 62, 17, 31, 14, 4, 38, 29, 61, 63, 5, 39, 46, 52, 32, 53, 2, 12, 58, 9, 19, 37, 24, 42, 10, 11, 36, 6, 0, 33, 26, 47, 41, 64, 23, 54, 45, 65, 34, 56, 60, 27, 35, 21, 50, 48, 49, 25], 'cur_cost': 103555.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [5, 29, 7, 28, 63, 62, 24, 39, 56, 19, 52, 58, 49, 6, 61, 54, 57, 46, 20, 37, 0, 47, 65, 41, 44, 45, 30, 53, 18, 55, 31, 48, 60, 11, 38, 15, 12, 13, 9, 43, 50, 26, 40, 33, 14, 36, 16, 21, 22, 1, 51, 2, 8, 3, 10, 59, 25, 35, 4, 42, 17, 34, 23, 32, 64, 27], 'cur_cost': 106447.0}}]
2025-07-04 10:07:46,816 - __main__ - INFO - 进化阶段完成
2025-07-04 10:07:46,816 - __main__ - INFO - 开始评估阶段
2025-07-04 10:07:46,819 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-04 10:07:46,821 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 10:07:46,821 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 10061.0 → 10061.0 (Δ 0.0)
- Mean cost: 78821.8 → 74773.8
- Diversity: 0.749 → 0.762 (Δ 0.0123)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.126 → 0.126 (Δ 0.0)

#### Strategy performance
- Explore: 5 inds, success 0.0% , avg Δ 0.0
- Exploit: 5 inds, success 80.0% , avg Δ 8096.0

#### Other indicators
- No-change individuals: 5
- Regression individuals: 1
- Historical trends: Last 3 iterations: costs [10061.0, 10061.0, 10061.0], diversity [0.9087542087542086, 0.9245791245791245, 0.9212121212121214]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 10:07:46,821 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:07:48,831 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.8
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Decrease explore ratio to 0.1. The explore strategy is currently ineffective and not contributing to improvements.",
    "No changes to diversity_weight, since it is stable."
  ]
}
```
2025-07-04 10:07:48,858 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 10:07:48,858 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.8
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Decrease explore ratio to 0.1. The explore strategy is currently ineffective and not contributing to improvements.",
    "No changes to diversity_weight, since it is stable."
  ]
}
```
2025-07-04 10:07:48,858 - __main__ - INFO - 评估阶段完成
2025-07-04 10:07:48,858 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": 0.0,
    "exploit": 0.8
  },
  "balance_state": "exploit_dominant",
  "recommendations": [
    "Decrease explore ratio to 0.1. The explore strategy is currently ineffective and not contributing to improvements.",
    "No changes to diversity_weight, since it is stable."
  ]
}
```
2025-07-04 10:07:48,858 - __main__ - INFO - 当前最佳适应度: 10061.0
2025-07-04 10:07:48,858 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_4.pkl
2025-07-04 10:07:48,875 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-04 10:07:48,875 - __main__ - INFO - 实例 composite13_66 处理完成
