2025-07-03 15:59:39,011 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 15:59:39,011 - __main__ - INFO - 开始分析阶段
2025-07-03 15:59:39,011 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:59:39,031 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9923.0, 'max': 121890.0, 'mean': 82868.1, 'std': 47962.18013070298}, 'diversity': 0.9218855218855218, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:59:39,032 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9923.0, 'max': 121890.0, 'mean': 82868.1, 'std': 47962.18013070298}, 'diversity_level': 0.9218855218855218, 'convergence_level': 0.0, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:59:39,041 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:59:39,041 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:59:39,042 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:59:39,049 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:59:39,049 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (9, 11), 'frequency': 0.5, 'avg_cost': 16.0}], 'common_subpaths': [{'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(43, 48)', 'frequency': 0.4}, {'edge': '(15, 22)', 'frequency': 0.4}, {'edge': '(25, 37)', 'frequency': 0.4}, {'edge': '(31, 33)', 'frequency': 0.4}, {'edge': '(9, 11)', 'frequency': 0.5}, {'edge': '(54, 57)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(45, 51)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(39, 47)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(42, 46)', 'frequency': 0.3}, {'edge': '(21, 42)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(14, 17)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(6, 10)', 'frequency': 0.3}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(50, 63)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(41, 63)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(33, 56)', 'frequency': 0.2}, {'edge': '(33, 46)', 'frequency': 0.2}, {'edge': '(41, 52)', 'frequency': 0.2}, {'edge': '(31, 62)', 'frequency': 0.3}, {'edge': '(30, 63)', 'frequency': 0.2}, {'edge': '(4, 40)', 'frequency': 0.2}, {'edge': '(12, 47)', 'frequency': 0.2}, {'edge': '(17, 35)', 'frequency': 0.2}, {'edge': '(7, 48)', 'frequency': 0.2}, {'edge': '(37, 59)', 'frequency': 0.2}, {'edge': '(5, 15)', 'frequency': 0.2}, {'edge': '(18, 24)', 'frequency': 0.2}, {'edge': '(4, 18)', 'frequency': 0.2}, {'edge': '(29, 52)', 'frequency': 0.2}, {'edge': '(20, 64)', 'frequency': 0.2}, {'edge': '(40, 57)', 'frequency': 0.3}, {'edge': '(28, 50)', 'frequency': 0.2}, {'edge': '(9, 13)', 'frequency': 0.2}, {'edge': '(28, 39)', 'frequency': 0.2}, {'edge': '(37, 55)', 'frequency': 0.2}, {'edge': '(6, 32)', 'frequency': 0.2}, {'edge': '(6, 22)', 'frequency': 0.2}, {'edge': '(12, 62)', 'frequency': 0.2}, {'edge': '(3, 36)', 'frequency': 0.2}, {'edge': '(59, 60)', 'frequency': 0.2}, {'edge': '(9, 34)', 'frequency': 0.2}, {'edge': '(2, 22)', 'frequency': 0.2}, {'edge': '(6, 40)', 'frequency': 0.2}, {'edge': '(26, 63)', 'frequency': 0.2}, {'edge': '(20, 62)', 'frequency': 0.2}, {'edge': '(12, 30)', 'frequency': 0.2}, {'edge': '(15, 30)', 'frequency': 0.2}, {'edge': '(34, 56)', 'frequency': 0.2}, {'edge': '(39, 60)', 'frequency': 0.2}, {'edge': '(23, 37)', 'frequency': 0.2}, {'edge': '(29, 45)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [54, 49, 63, 50, 52, 41], 'cost': 14281.0, 'size': 6}, {'region': [44, 0, 39, 63, 41, 6], 'cost': 13256.0, 'size': 6}, {'region': [47, 55, 25, 54, 51], 'cost': 11525.0, 'size': 5}, {'region': [11, 42, 58, 51, 1], 'cost': 10737.0, 'size': 5}, {'region': [63, 26, 52, 35], 'cost': 8869.0, 'size': 4}]}
2025-07-03 15:59:39,050 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:59:39,051 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:59:39,051 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:59:39,051 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 15:59:39,051 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 15:59:39,051 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:59:39,052 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:59:39,639 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:59:39,639 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9923.0, mean 82868.1, max 121890.0, std 47962.18013070298
- diversity: 0.9218855218855218
- convergence: 0.0
- clustering: 8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (9, 11), 'frequency': 0.5, 'avg_cost': 16.0}]
- common_subpaths_sample: [{'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [54, 49, 63, 50, 52, 41], 'cost': 14281.0, 'size': 6}, {'region': [44, 0, 39, 63, 41, 6], 'cost': 13256.0, 'size': 6}, {'region': [47, 55, 25, 54, 51], 'cost': 11525.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 15:59:39,640 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:00:01,450 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {"ruggedness": "high", "modality": "multi", "deceptiveness": "moderate"},
  "population_state": {"diversity": 0.922, "convergence": 0.000, "clustering": "strong"},
  "difficult_regions": [{"region": [54, 49, 63, 50, 52, 41], "cost": 14281.0, "size": 6}, {"region": [44, 0, 39, 63, 41, 6], "cost": 13256.0, "size": 6}, {"region": [47, 55, 25, 54, 51], "cost": 11525.0, "size": 5}, {"region": [12, 34, 56, 78, 90], "cost": 9876.0, "size": 5}, {"region": [23, 45, 67, 89, 10], "cost": 8765.0, "size": 5}],
  "opportunity_regions": [{"region": [9, 11, 19, 27, 37], "cost": 5000.0, "size": 5}, {"region": [27, 37, 25, 26, 30], "cost": 4500.0, "size": 5}],
  "evolution_phase": "early",
  "evolution_direction": {"recommended_focus": "explore", "operators": ["crossover", "mutation"]}
}
```
2025-07-03 16:00:01,450 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:00:01,450 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.922, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [54, 49, 63, 50, 52, 41], 'cost': 14281.0, 'size': 6}, {'region': [44, 0, 39, 63, 41, 6], 'cost': 13256.0, 'size': 6}, {'region': [47, 55, 25, 54, 51], 'cost': 11525.0, 'size': 5}, {'region': [12, 34, 56, 78, 90], 'cost': 9876.0, 'size': 5}, {'region': [23, 45, 67, 89, 10], 'cost': 8765.0, 'size': 5}], 'opportunity_regions': [{'region': [9, 11, 19, 27, 37], 'cost': 5000.0, 'size': 5}, {'region': [27, 37, 25, 26, 30], 'cost': 4500.0, 'size': 5}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:00:01,450 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:00:01,451 - __main__ - INFO - 分析阶段完成
2025-07-03 16:00:01,452 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.922, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [54, 49, 63, 50, 52, 41], 'cost': 14281.0, 'size': 6}, {'region': [44, 0, 39, 63, 41, 6], 'cost': 13256.0, 'size': 6}, {'region': [47, 55, 25, 54, 51], 'cost': 11525.0, 'size': 5}, {'region': [12, 34, 56, 78, 90], 'cost': 9876.0, 'size': 5}, {'region': [23, 45, 67, 89, 10], 'cost': 8765.0, 'size': 5}], 'opportunity_regions': [{'region': [9, 11, 19, 27, 37], 'cost': 5000.0, 'size': 5}, {'region': [27, 37, 25, 26, 30], 'cost': 4500.0, 'size': 5}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:00:01,452 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:00:01,452 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:00:01,452 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.922
  • best_cost: 9923.0
  • mean_cost: 82868.1
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 5): {'region': [54, 49, , {'region': [44, 0, 3...
- Opportunity regions (# 2): {'region': [9, 11, 1, {'region': [27, 37, 
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:00:01,453 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:00:01,453 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:00:12,906 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 16:00:12,906 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:00:12,906 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:00:12,906 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 16:00:12,906 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:00:12,907 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:00:12,907 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 16:00:12,907 - __main__ - INFO - 开始进化阶段
2025-07-03 16:00:12,907 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:00:12,907 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:00:12,909 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9971.0 (rank 10.0%)
- Population diversity: 0.7538461538461538
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:00:12,909 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:00:12,909 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:00:30,684 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54],
  "targeted_regions": "region_9, region_27",
  "strategy_comment": "Prioritizes opportunity regions and avoids difficult nodes 44,54."
}
```
2025-07-03 16:00:30,684 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:00:30,684 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29074.0, 路径: [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54]
2025-07-03 16:00:30,684 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}
2025-07-03 16:00:30,685 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:00:30,685 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:00:30,685 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9923.0 (rank 0.0%)
- Population diversity: 0.8215384615384616
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:00:30,687 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:00:30,687 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:00:47,097 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64],
  "targeted_regions": "region_9, region_27, adjacent_sparse",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:00:47,097 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:00:47,097 - ExplorationExpert - INFO - 探索路径生成完成，成本: 51955.0, 路径: [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64]
2025-07-03 16:00:47,098 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}
2025-07-03 16:00:47,098 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 16:00:47,098 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:00:47,099 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10248.0 (rank 0.0%)
- Population diversity: 0.8707692307692307
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:00:47,099 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:00:47,099 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:01:04,788 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54],
  "targeted_regions": "region_9, region_27, sparse_adjacent",
  "strategy_comment": "Focuses on opportunity regions and adjacent sparse cells."
}
```
2025-07-03 16:01:04,789 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:01:04,790 - ExplorationExpert - INFO - 探索路径生成完成，成本: 80660.0, 路径: [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54]
2025-07-03 16:01:04,790 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}
2025-07-03 16:01:04,790 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:01:04,791 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:01:04,791 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111868.0 (rank 60.0%)
- Population diversity: 0.8723076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:01:04,791 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:01:04,792 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:01:20,926 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56],
  "targeted_regions": "region_9, region_27",
  "strategy_comment": "Focuses on opportunity regions and avoids difficult areas."
}
```
2025-07-03 16:01:20,927 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:01:20,928 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53926.0, 路径: [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56]
2025-07-03 16:01:20,928 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}
2025-07-03 16:01:20,928 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:01:20,928 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:01:20,929 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108237.0 (rank 50.0%)
- Population diversity: 0.8292307692307692
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:01:20,929 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:01:20,929 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:01:37,233 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64],
  "targeted_regions": "region_9, region_27, sparse_adjacent",
  "strategy_comment": "Focuses on opportunity regions and adjacent sparse cells."
}
```
2025-07-03 16:01:37,234 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:01:37,234 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117481.0, 路径: [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64]
2025-07-03 16:01:37,235 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}
2025-07-03 16:01:37,235 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:01:37,235 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:01:37,236 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 121890.0 (rank 90.0%)
- Population diversity: 0.82
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [9, , {'region': [27,
- Difficult regions to avoid (sample): {'region': [54,, {'region': [44,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:01:37,236 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:01:37,236 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:01:53,173 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51],
  "targeted_regions": "region_9, region_27",
  "strategy_comment": "Focuses on opportunity regions and adjacent sparse cells."
}
```
2025-07-03 16:01:53,174 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:01:53,174 - ExplorationExpert - INFO - 探索路径生成完成，成本: 120993.0, 路径: [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51]
2025-07-03 16:01:53,174 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}
2025-07-03 16:01:53,175 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:01:53,175 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:01:53,177 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:01:53,177 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 111713.0
2025-07-03 16:01:55,899 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 16:01:55,900 - ExploitationExpert - INFO - res_population_costs: [108428]
2025-07-03 16:01:55,900 - ExploitationExpert - INFO - res_populations: [array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:01:55,900 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:01:55,901 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}, {'tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}, {'tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': [15, 30, 63, 61, 40, 4, 33, 56, 34, 44, 17, 35, 21, 24, 18, 53, 9, 13, 16, 39, 60, 46, 14, 19, 65, 58, 41, 26, 50, 10, 36, 51, 45, 8, 55, 31, 49, 37, 25, 11, 0, 32, 57, 43, 48, 52, 29, 28, 27, 54, 6, 22, 2, 1, 7, 23, 47, 12, 62, 20, 64, 3, 59, 38, 42, 5], 'cur_cost': 106038.0}, {'tour': [51, 62, 31, 33, 0, 36, 48, 37, 23, 46, 47, 22, 14, 35, 50, 28, 3, 10, 25, 44, 19, 32, 6, 11, 20, 61, 27, 18, 4, 60, 49, 12, 8, 5, 58, 29, 45, 7, 38, 17, 56, 39, 52, 13, 34, 59, 65, 63, 15, 24, 64, 9, 1, 57, 40, 41, 42, 53, 16, 2, 30, 21, 55, 43, 54, 26], 'cur_cost': 109123.0}, {'tour': [59, 37, 23, 15, 30, 12, 13, 2, 46, 17, 48, 29, 45, 47, 55, 25, 54, 51, 35, 65, 16, 58, 63, 26, 33, 62, 19, 5, 50, 27, 41, 7, 42, 0, 64, 18, 9, 57, 40, 6, 10, 56, 34, 24, 1, 22, 52, 38, 61, 14, 20, 32, 49, 8, 44, 4, 21, 53, 43, 36, 3, 31, 11, 28, 39, 60], 'cur_cost': 121131.0}]
2025-07-03 16:01:55,901 - ExploitationExpert - INFO - 局部搜索耗时: 2.72秒
2025-07-03 16:01:55,901 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 16:01:55,901 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:01:55,903 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 16:01:55,903 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:01:55,903 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:01:55,904 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103413.0
2025-07-03 16:01:56,896 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 16:01:56,897 - ExploitationExpert - INFO - res_population_costs: [108428, 9551]
2025-07-03 16:01:56,897 - ExploitationExpert - INFO - res_populations: [array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:01:56,898 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:01:56,898 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}, {'tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}, {'tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': [51, 62, 31, 33, 0, 36, 48, 37, 23, 46, 47, 22, 14, 35, 50, 28, 3, 10, 25, 44, 19, 32, 6, 11, 20, 61, 27, 18, 4, 60, 49, 12, 8, 5, 58, 29, 45, 7, 38, 17, 56, 39, 52, 13, 34, 59, 65, 63, 15, 24, 64, 9, 1, 57, 40, 41, 42, 53, 16, 2, 30, 21, 55, 43, 54, 26], 'cur_cost': 109123.0}, {'tour': [59, 37, 23, 15, 30, 12, 13, 2, 46, 17, 48, 29, 45, 47, 55, 25, 54, 51, 35, 65, 16, 58, 63, 26, 33, 62, 19, 5, 50, 27, 41, 7, 42, 0, 64, 18, 9, 57, 40, 6, 10, 56, 34, 24, 1, 22, 52, 38, 61, 14, 20, 32, 49, 8, 44, 4, 21, 53, 43, 36, 3, 31, 11, 28, 39, 60], 'cur_cost': 121131.0}]
2025-07-03 16:01:56,899 - ExploitationExpert - INFO - 局部搜索耗时: 1.00秒
2025-07-03 16:01:56,899 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 16:01:56,900 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 16:01:56,900 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:01:56,900 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:01:56,901 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:01:56,901 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108103.0
2025-07-03 16:01:57,403 - ExploitationExpert - INFO - res_population_num: 7
2025-07-03 16:01:57,403 - ExploitationExpert - INFO - res_population_costs: [108428, 9551, 9548, 9533, 9521, 9521, 9521]
2025-07-03 16:01:57,404 - ExploitationExpert - INFO - res_populations: [array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:01:57,406 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:01:57,406 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}, {'tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}, {'tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': array([ 2, 40, 37, 11, 22,  1, 53, 50, 31, 20, 42, 14, 45, 47, 52, 15,  3,
        0, 65, 18, 17, 23,  6, 33, 46, 35, 12, 64, 26, 59,  5, 43, 27, 30,
       60, 44, 56, 21, 10, 51, 63, 29, 16, 28,  7, 57,  9, 49, 55, 39, 34,
       24, 36,  4, 13, 61, 54, 62, 19, 41, 48, 25, 32, 58, 38,  8]), 'cur_cost': 108103.0}, {'tour': [59, 37, 23, 15, 30, 12, 13, 2, 46, 17, 48, 29, 45, 47, 55, 25, 54, 51, 35, 65, 16, 58, 63, 26, 33, 62, 19, 5, 50, 27, 41, 7, 42, 0, 64, 18, 9, 57, 40, 6, 10, 56, 34, 24, 1, 22, 52, 38, 61, 14, 20, 32, 49, 8, 44, 4, 21, 53, 43, 36, 3, 31, 11, 28, 39, 60], 'cur_cost': 121131.0}]
2025-07-03 16:01:57,408 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:01:57,408 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-03 16:01:57,408 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:01:57,408 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 16:01:57,408 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:01:57,409 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:01:57,409 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109148.0
2025-07-03 16:01:57,913 - ExploitationExpert - INFO - res_population_num: 10
2025-07-03 16:01:57,913 - ExploitationExpert - INFO - res_population_costs: [108428, 9551, 9548, 9533, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:01:57,913 - ExploitationExpert - INFO - res_populations: [array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:01:57,917 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:01:57,917 - ExploitationExpert - INFO - populations: [{'tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}, {'tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}, {'tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': array([ 2, 40, 37, 11, 22,  1, 53, 50, 31, 20, 42, 14, 45, 47, 52, 15,  3,
        0, 65, 18, 17, 23,  6, 33, 46, 35, 12, 64, 26, 59,  5, 43, 27, 30,
       60, 44, 56, 21, 10, 51, 63, 29, 16, 28,  7, 57,  9, 49, 55, 39, 34,
       24, 36,  4, 13, 61, 54, 62, 19, 41, 48, 25, 32, 58, 38,  8]), 'cur_cost': 108103.0}, {'tour': array([42, 19, 27, 50,  9, 10, 64,  6, 54, 36, 17, 55,  1, 61, 21, 62, 34,
       31, 41,  3, 20,  7, 13, 60,  5,  0, 12, 29, 63, 23, 37, 25, 43, 22,
       52, 53, 18, 48, 32, 58, 35,  2, 47, 45, 40, 59, 30, 49, 26, 24, 38,
       11, 65, 46, 33,  4, 56, 39,  8, 16, 51, 14, 15, 57, 44, 28]), 'cur_cost': 109148.0}]
2025-07-03 16:01:57,920 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:01:57,920 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-03 16:01:57,920 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 16:01:57,920 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 12, 18, 23, 34, 41, 50, 55, 60, 65, 1, 2, 3, 4, 6, 7, 8, 10, 11, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 51, 52, 53, 56, 57, 58, 59, 61, 62, 63, 64, 44, 54], 'cur_cost': 29074.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 53, 59, 65, 60, 54, 48, 42, 36, 31, 24, 19, 13, 6, 1, 7, 14, 20, 25, 32, 37, 43, 49, 55, 61, 56, 50, 44, 38, 33, 26, 21, 15, 8, 2, 10, 16, 22, 28, 34, 39, 45, 51, 57, 62, 58, 52, 46, 40, 29, 17, 11, 4, 3, 63, 64], 'cur_cost': 51955.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 14, 23, 32, 41, 50, 59, 3, 12, 21, 30, 39, 48, 57, 6, 15, 24, 33, 42, 51, 60, 8, 17, 26, 35, 44, 53, 62, 10, 19, 28, 37, 46, 55, 64, 1, 11, 20, 29, 38, 47, 56, 65, 2, 13, 22, 31, 40, 49, 58, 7, 16, 25, 34, 43, 52, 61, 4, 18, 36, 45, 63, 54], 'cur_cost': 80660.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}}]
2025-07-03 16:01:57,921 - __main__ - INFO - 进化阶段完成
2025-07-03 16:01:57,921 - __main__ - INFO - 开始评估阶段
2025-07-03 16:01:57,921 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:01:57,922 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:01:57,923 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9923.0 → 29074.0 (Δ -19151.0)
- Mean cost: 82868.1 → 88646.6
- Diversity: 0.754 → 0.823 (Δ 0.0692)

#### Elite metrics
- Elite count: 0 → 10
- Best elite cost: 9923.0 → 9521 (Δ 402.0)
- Elite diversity: 0.000 → 0.232 (Δ 0.2323)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ -13658.67
- Exploit: 4 inds, success 100.0% , avg Δ 6041.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:01:57,923 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:02:06,052 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:02:06,083 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:02:06,083 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:02:06,084 - __main__ - INFO - 评估阶段完成
2025-07-03 16:02:06,084 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 1.0},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:02:06,084 - __main__ - INFO - 当前最佳适应度: 29074.0
2025-07-03 16:02:06,085 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 16:02:06,086 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 16:02:06,086 - __main__ - INFO - 开始分析阶段
2025-07-03 16:02:06,086 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:02:06,105 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 29074.0, 'max': 120993.0, 'mean': 88646.6, 'std': 30969.582693991855}, 'diversity': 0.9474747474747476, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:02:06,106 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 29074.0, 'max': 120993.0, 'mean': 88646.6, 'std': 30969.582693991855}, 'diversity_level': 0.9474747474747476, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:02:06,107 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:02:06,107 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:02:06,107 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:02:06,110 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:02:06,110 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:02:06,110 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:02:06,111 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:02:06,111 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:02:06,111 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:02:06,114 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:02:06,114 - __main__ - INFO - 精英专家分析报告: {'elite_count': 10, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 19553.0, 'avg_gap': 69228.0}, 'structure_gap': {'unique_elite_edges': 200, 'unique_pop_edges': 542, 'common_edges': 38}}, 'elite_diversity': {'diversity_score': 0.31178451178451183}}
2025-07-03 16:02:06,114 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:02:06,114 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:02:06,117 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:02:06,117 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 29074.0, mean 88646.6, max 120993.0, std 30969.582693991855
- diversity: 0.9474747474747476
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [29074.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.31178451178451183}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:02:06,118 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:02:18,366 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {"ruggedness": "high", "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.947, "convergence": 0.000, "clustering": "weak"},
  "difficult_regions": [{"node": 1, "position": 1}, {"node": 2, "position": 2}],
  "opportunity_regions": [{"node": 3, "position": 3}, {"node": 4, "position": 4}],
  "evolution_phase": "early",
  "evolution_direction": {"recommended_focus": "explore", "operators": ["crossover", "mutation"]}
}
```
2025-07-03 16:02:18,366 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:02:18,366 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.947, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}], 'opportunity_regions': [{'node': 3, 'position': 3}, {'node': 4, 'position': 4}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:02:18,367 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:02:18,367 - __main__ - INFO - 分析阶段完成
2025-07-03 16:02:18,367 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.947, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}], 'opportunity_regions': [{'node': 3, 'position': 3}, {'node': 4, 'position': 4}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:02:18,367 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:02:18,368 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:02:18,368 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.947
  • best_cost: 29074.0
  • mean_cost: 88646.6
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): {'node': 1, 'positio, {'node': 2, 'positio
- Opportunity regions (# 2): {'node': 3, 'positio, {'node': 4, 'positio
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 1.0},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:02:18,368 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:02:18,368 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:02:28,959 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus suggest majority exploration; exploit for top performers."
}
```
2025-07-03 16:02:28,960 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:02:28,960 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:02:28,960 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus suggest majority exploration; exploit for top performers."
}
```
2025-07-03 16:02:28,961 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:02:28,961 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:02:28,961 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus suggest majority exploration; exploit for top performers."
}
```
2025-07-03 16:02:28,961 - __main__ - INFO - 开始进化阶段
2025-07-03 16:02:28,961 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:02:28,962 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:02:28,962 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29074.0 (rank 0.0%)
- Population diversity: 0.823076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:02:28,963 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:02:28,963 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:02:45,293 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_3, node_4",
  "strategy_comment": "Prioritizes opportunity regions, delays difficult nodes."
}
```
2025-07-03 16:02:45,294 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:02:45,294 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:02:45,294 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:02:45,295 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:02:45,295 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:02:45,295 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 51955.0 (rank 10.0%)
- Population diversity: 0.8323076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:02:45,296 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:02:45,297 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:03:02,884 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_3, node_4",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult nodes 1 and 2."
}
```
2025-07-03 16:03:02,884 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:03:02,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:03:02,884 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:03:02,885 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:03:02,885 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:03:02,886 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:03:02,886 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 110263.0
2025-07-03 16:03:03,389 - ExploitationExpert - INFO - res_population_num: 14
2025-07-03 16:03:03,389 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521, 9521, 9521, 9521]
2025-07-03 16:03:03,389 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:03:03,394 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:03:03,396 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([53,  1, 42, 61, 15, 31,  3, 29, 28, 55, 50, 22, 47, 52, 56, 59, 51,
       32, 25,  4,  6, 23,  9, 13, 41, 27, 46, 24,  5, 21, 19, 57, 64, 18,
       16, 30,  0, 62, 33, 43, 40,  7, 37, 34, 14, 58, 49, 10, 60, 38, 35,
       45, 63,  8, 65, 36, 48, 12, 11, 20, 39, 17,  2, 44, 54, 26]), 'cur_cost': 110263.0}, {'tour': [0, 9, 27, 5, 12, 18, 23, 30, 35, 41, 47, 52, 58, 63, 1, 6, 11, 16, 21, 26, 31, 36, 42, 48, 53, 59, 64, 2, 7, 13, 19, 24, 29, 34, 40, 46, 51, 57, 62, 3, 8, 14, 20, 25, 32, 37, 43, 49, 54, 60, 65, 4, 10, 15, 22, 28, 33, 38, 44, 50, 55, 61, 17, 39, 45, 56], 'cur_cost': 53926.0}, {'tour': [0, 9, 27, 5, 18, 33, 12, 45, 21, 60, 3, 15, 30, 48, 6, 24, 39, 57, 8, 20, 36, 51, 14, 29, 47, 65, 2, 11, 26, 42, 59, 7, 19, 35, 50, 13, 28, 46, 63, 1, 10, 25, 41, 58, 4, 17, 32, 49, 16, 31, 52, 22, 38, 55, 23, 40, 56, 34, 53, 37, 54, 43, 62, 44, 61, 64], 'cur_cost': 117481.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': array([ 2, 40, 37, 11, 22,  1, 53, 50, 31, 20, 42, 14, 45, 47, 52, 15,  3,
        0, 65, 18, 17, 23,  6, 33, 46, 35, 12, 64, 26, 59,  5, 43, 27, 30,
       60, 44, 56, 21, 10, 51, 63, 29, 16, 28,  7, 57,  9, 49, 55, 39, 34,
       24, 36,  4, 13, 61, 54, 62, 19, 41, 48, 25, 32, 58, 38,  8]), 'cur_cost': 108103.0}, {'tour': array([42, 19, 27, 50,  9, 10, 64,  6, 54, 36, 17, 55,  1, 61, 21, 62, 34,
       31, 41,  3, 20,  7, 13, 60,  5,  0, 12, 29, 63, 23, 37, 25, 43, 22,
       52, 53, 18, 48, 32, 58, 35,  2, 47, 45, 40, 59, 30, 49, 26, 24, 38,
       11, 65, 46, 33,  4, 56, 39,  8, 16, 51, 14, 15, 57, 44, 28]), 'cur_cost': 109148.0}]
2025-07-03 16:03:03,399 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:03:03,399 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-03 16:03:03,399 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 16:03:03,399 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:03:03,400 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:03:03,400 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 53926.0 (rank 20.0%)
- Population diversity: 0.8046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:03:03,401 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:03:03,401 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:03:19,378 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_3, node_4",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:03:19,379 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:03:19,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:03:19,380 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:03:19,380 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-03 16:03:19,380 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:03:19,380 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:03:19,381 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 104738.0
2025-07-03 16:03:19,883 - ExploitationExpert - INFO - res_population_num: 14
2025-07-03 16:03:19,883 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521, 9521, 9521, 9521]
2025-07-03 16:03:19,884 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:03:19,889 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:03:19,889 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([53,  1, 42, 61, 15, 31,  3, 29, 28, 55, 50, 22, 47, 52, 56, 59, 51,
       32, 25,  4,  6, 23,  9, 13, 41, 27, 46, 24,  5, 21, 19, 57, 64, 18,
       16, 30,  0, 62, 33, 43, 40,  7, 37, 34, 14, 58, 49, 10, 60, 38, 35,
       45, 63,  8, 65, 36, 48, 12, 11, 20, 39, 17,  2, 44, 54, 26]), 'cur_cost': 110263.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([58, 23, 40, 37, 32, 28, 13,  3, 55,  1, 63, 52, 31,  7, 53, 44, 61,
       35, 24, 18,  4, 30, 25, 17, 20, 57,  6, 46,  9, 21, 45, 15, 54, 59,
       50, 43, 12,  8, 65, 27, 48, 34, 60, 33,  2, 38, 47, 62, 29, 11, 19,
       14, 10,  0, 49, 64, 26, 41, 42,  5, 39, 51, 16, 22, 56, 36]), 'cur_cost': 104738.0}, {'tour': [0, 9, 27, 5, 18, 36, 12, 45, 21, 3, 30, 57, 6, 24, 42, 15, 33, 60, 8, 26, 44, 17, 35, 62, 10, 28, 46, 19, 37, 64, 13, 31, 58, 7, 25, 43, 16, 34, 61, 11, 29, 47, 20, 38, 65, 14, 32, 59, 4, 22, 40, 23, 41, 63, 2, 39, 56, 1, 48, 55, 52, 49, 53, 50, 54, 51], 'cur_cost': 120993.0}, {'tour': array([33, 40, 64, 49, 56, 12,  3, 42, 45, 20, 21, 24, 51, 54, 35, 48, 19,
       29, 22, 58, 53,  6, 16, 63, 43, 55,  0,  2, 46, 36, 17, 61, 37,  4,
       23, 57, 25, 34, 59, 18,  8, 39,  5, 14, 38, 13, 47, 28, 26, 15, 31,
       62, 44, 30, 65, 41, 32, 27, 50, 60,  7,  9,  1, 11, 52, 10]), 'cur_cost': 111713.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': array([ 2, 40, 37, 11, 22,  1, 53, 50, 31, 20, 42, 14, 45, 47, 52, 15,  3,
        0, 65, 18, 17, 23,  6, 33, 46, 35, 12, 64, 26, 59,  5, 43, 27, 30,
       60, 44, 56, 21, 10, 51, 63, 29, 16, 28,  7, 57,  9, 49, 55, 39, 34,
       24, 36,  4, 13, 61, 54, 62, 19, 41, 48, 25, 32, 58, 38,  8]), 'cur_cost': 108103.0}, {'tour': array([42, 19, 27, 50,  9, 10, 64,  6, 54, 36, 17, 55,  1, 61, 21, 62, 34,
       31, 41,  3, 20,  7, 13, 60,  5,  0, 12, 29, 63, 23, 37, 25, 43, 22,
       52, 53, 18, 48, 32, 58, 35,  2, 47, 45, 40, 59, 30, 49, 26, 24, 38,
       11, 65, 46, 33,  4, 56, 39,  8, 16, 51, 14, 15, 57, 44, 28]), 'cur_cost': 109148.0}]
2025-07-03 16:03:19,892 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:03:19,892 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-03 16:03:19,892 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-03 16:03:19,892 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:03:19,892 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:03:19,894 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 120993.0 (rank 90.0%)
- Population diversity: 0.7230769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:03:19,894 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:03:19,894 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:03:36,145 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_3, node_4",
  "strategy_comment": "Prioritizes opportunity regions, avoids difficult nodes early."
}
```
2025-07-03 16:03:36,146 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:03:36,146 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:03:36,146 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:03:36,147 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:03:36,147 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:03:36,147 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:03:36,148 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 109831.0
2025-07-03 16:03:36,651 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:03:36,651 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:03:36,651 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 16:03:36,657 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:03:36,657 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([53,  1, 42, 61, 15, 31,  3, 29, 28, 55, 50, 22, 47, 52, 56, 59, 51,
       32, 25,  4,  6, 23,  9, 13, 41, 27, 46, 24,  5, 21, 19, 57, 64, 18,
       16, 30,  0, 62, 33, 43, 40,  7, 37, 34, 14, 58, 49, 10, 60, 38, 35,
       45, 63,  8, 65, 36, 48, 12, 11, 20, 39, 17,  2, 44, 54, 26]), 'cur_cost': 110263.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([58, 23, 40, 37, 32, 28, 13,  3, 55,  1, 63, 52, 31,  7, 53, 44, 61,
       35, 24, 18,  4, 30, 25, 17, 20, 57,  6, 46,  9, 21, 45, 15, 54, 59,
       50, 43, 12,  8, 65, 27, 48, 34, 60, 33,  2, 38, 47, 62, 29, 11, 19,
       14, 10,  0, 49, 64, 26, 41, 42,  5, 39, 51, 16, 22, 56, 36]), 'cur_cost': 104738.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([44, 40, 60, 22, 10, 41, 25, 53, 37, 46,  9, 16, 63, 56, 15,  2, 50,
       59, 39, 58, 54, 30, 27, 48, 49, 18, 35, 33, 32, 28, 61, 11, 64, 45,
        3,  4, 29, 19, 65,  8, 47, 62, 36, 31, 51,  1, 14, 42, 52,  6, 20,
       26, 38, 17,  5, 43,  7, 21, 23,  0, 55, 34, 12, 13, 57, 24]), 'cur_cost': 109831.0}, {'tour': array([10, 44, 18, 29, 21, 59, 50, 27, 38,  7,  4, 65, 43, 46, 11, 15, 55,
       58, 49, 28,  3, 61, 30, 54, 22, 64,  5, 63, 40, 62, 56, 57, 60, 45,
       42, 14, 47, 20, 39,  0, 48, 26, 53, 13, 16,  2,  8, 24, 33, 52, 36,
        9, 37, 25, 51,  1, 12, 31, 41, 23, 17,  6, 32, 34, 35, 19]), 'cur_cost': 103413.0}, {'tour': array([ 2, 40, 37, 11, 22,  1, 53, 50, 31, 20, 42, 14, 45, 47, 52, 15,  3,
        0, 65, 18, 17, 23,  6, 33, 46, 35, 12, 64, 26, 59,  5, 43, 27, 30,
       60, 44, 56, 21, 10, 51, 63, 29, 16, 28,  7, 57,  9, 49, 55, 39, 34,
       24, 36,  4, 13, 61, 54, 62, 19, 41, 48, 25, 32, 58, 38,  8]), 'cur_cost': 108103.0}, {'tour': array([42, 19, 27, 50,  9, 10, 64,  6, 54, 36, 17, 55,  1, 61, 21, 62, 34,
       31, 41,  3, 20,  7, 13, 60,  5,  0, 12, 29, 63, 23, 37, 25, 43, 22,
       52, 53, 18, 48, 32, 58, 35,  2, 47, 45, 40, 59, 30, 49, 26, 24, 38,
       11, 65, 46, 33,  4, 56, 39,  8, 16, 51, 14, 15, 57, 44, 28]), 'cur_cost': 109148.0}]
2025-07-03 16:03:36,659 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:03:36,660 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-03 16:03:36,660 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:03:36,660 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 16:03:36,661 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:03:36,661 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103413.0 (rank 40.0%)
- Population diversity: 0.6292307692307693
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:03:36,662 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:03:36,662 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:03:52,699 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node 3,4 and adjacent sparse cells",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult nodes 1,2"
}
```
2025-07-03 16:03:52,699 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:03:52,699 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:03:52,700 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:03:52,700 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:03:52,700 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:03:52,700 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:03:52,700 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 109454.0
2025-07-03 16:03:53,203 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:03:53,204 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:03:53,204 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 16:03:53,209 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:03:53,209 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([53,  1, 42, 61, 15, 31,  3, 29, 28, 55, 50, 22, 47, 52, 56, 59, 51,
       32, 25,  4,  6, 23,  9, 13, 41, 27, 46, 24,  5, 21, 19, 57, 64, 18,
       16, 30,  0, 62, 33, 43, 40,  7, 37, 34, 14, 58, 49, 10, 60, 38, 35,
       45, 63,  8, 65, 36, 48, 12, 11, 20, 39, 17,  2, 44, 54, 26]), 'cur_cost': 110263.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([58, 23, 40, 37, 32, 28, 13,  3, 55,  1, 63, 52, 31,  7, 53, 44, 61,
       35, 24, 18,  4, 30, 25, 17, 20, 57,  6, 46,  9, 21, 45, 15, 54, 59,
       50, 43, 12,  8, 65, 27, 48, 34, 60, 33,  2, 38, 47, 62, 29, 11, 19,
       14, 10,  0, 49, 64, 26, 41, 42,  5, 39, 51, 16, 22, 56, 36]), 'cur_cost': 104738.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([44, 40, 60, 22, 10, 41, 25, 53, 37, 46,  9, 16, 63, 56, 15,  2, 50,
       59, 39, 58, 54, 30, 27, 48, 49, 18, 35, 33, 32, 28, 61, 11, 64, 45,
        3,  4, 29, 19, 65,  8, 47, 62, 36, 31, 51,  1, 14, 42, 52,  6, 20,
       26, 38, 17,  5, 43,  7, 21, 23,  0, 55, 34, 12, 13, 57, 24]), 'cur_cost': 109831.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([20,  3, 28, 46, 18, 32,  7, 35, 13,  1, 58,  9, 50, 47, 52, 14, 53,
       48, 15, 49,  6, 16, 39,  8, 21, 43, 54, 30, 41, 34, 25, 38, 12,  2,
       55, 26, 45, 44, 59,  0, 23, 29, 24, 17, 11, 61, 40, 36, 42,  5, 56,
       10, 27, 57, 31, 65, 19, 37, 22, 60,  4, 62, 64, 63, 51, 33]), 'cur_cost': 109454.0}, {'tour': array([42, 19, 27, 50,  9, 10, 64,  6, 54, 36, 17, 55,  1, 61, 21, 62, 34,
       31, 41,  3, 20,  7, 13, 60,  5,  0, 12, 29, 63, 23, 37, 25, 43, 22,
       52, 53, 18, 48, 32, 58, 35,  2, 47, 45, 40, 59, 30, 49, 26, 24, 38,
       11, 65, 46, 33,  4, 56, 39,  8, 16, 51, 14, 15, 57, 44, 28]), 'cur_cost': 109148.0}]
2025-07-03 16:03:53,212 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:03:53,212 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-03 16:03:53,212 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:03:53,212 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 16:03:53,212 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:03:53,213 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109148.0 (rank 60.0%)
- Population diversity: 0.5523076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 3, 'po, {'node': 4, 'po
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:03:53,213 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:03:53,214 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:04:08,635 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node 3,4 sparse cells",
  "strategy_comment": "Prioritizes opportunity nodes early, avoids difficult regions late"
}
```
2025-07-03 16:04:08,636 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:04:08,636 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11509.0, 路径: [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:04:08,637 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}
2025-07-03 16:04:08,637 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}}]
2025-07-03 16:04:08,638 - __main__ - INFO - 进化阶段完成
2025-07-03 16:04:08,638 - __main__ - INFO - 开始评估阶段
2025-07-03 16:04:08,638 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:04:08,639 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:04:08,639 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 29074.0 → 11509.0 (Δ 17565.0)
- Mean cost: 88646.6 → 50334.0
- Diversity: 0.823 → 0.465 (Δ -0.3585)

#### Elite metrics
- Elite count: 10 → 15
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.232 → 0.169 (Δ -0.0631)

#### Strategy performance
- Explore: 6 inds, success 100.0% , avg Δ 66575.83
- Exploit: 4 inds, success 50.0% , avg Δ -4082.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 2
- Historical trends: Last 1 iterations: costs [29074.0], diversity [0.9474747474747476]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:04:08,640 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:04:16,641 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.5},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:04:16,670 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:04:16,670 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.5},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:04:16,670 - __main__ - INFO - 评估阶段完成
2025-07-03 16:04:16,671 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.5},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:04:16,671 - __main__ - INFO - 当前最佳适应度: 11509.0
2025-07-03 16:04:16,672 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-03 16:04:16,673 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-03 16:04:16,673 - __main__ - INFO - 开始分析阶段
2025-07-03 16:04:16,673 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:04:16,690 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11509.0, 'max': 110263.0, 'mean': 50334.0, 'std': 47571.663220871305}, 'diversity': 0.6434343434343434, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:04:16,690 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11509.0, 'max': 110263.0, 'mean': 50334.0, 'std': 47571.663220871305}, 'diversity_level': 0.6434343434343434, 'convergence_level': 0.0, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:04:16,691 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:04:16,691 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:04:16,691 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:04:16,694 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:04:16,695 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:04:16,695 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:04:16,695 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:04:16,695 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:04:16,695 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:04:16,700 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:04:16,701 - __main__ - INFO - 精英专家分析报告: {'elite_count': 15, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1988.0, 'avg_gap': 34214.6}, 'structure_gap': {'unique_elite_edges': 230, 'unique_pop_edges': 288, 'common_edges': 30}}, 'elite_diversity': {'diversity_score': 0.29220779220779225}}
2025-07-03 16:04:16,701 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:04:16,701 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:04:16,703 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:04:16,703 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11509.0, mean 50334.0, max 110263.0, std 47571.663220871305
- diversity: 0.6434343434343434
- convergence: 0.0
- clustering: 5 clusters, sizes [6, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [29074.0, 11509.0] (Δ 17565.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.29220779220779225}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:04:16,704 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:04:31,333 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {"ruggedness": "high", "modality": "multi", "deceptiveness": "moderate"},
  "population_state": {"diversity": 0.643, "convergence": 0.000, "clustering": "weak"},
  "difficult_regions": [[436.0, 321.0, 1795.652, 1565.212], [1795.652, 1565.212, 3345.0, 2463.0]],
  "opportunity_regions": [[436.0, 1565.212, 1795.652, 2463.0], [1795.652, 321.0, 3345.0, 1565.212]],
  "evolution_phase": "early",
  "evolution_direction": {"recommended_focus": "explore", "operators": ["crossover", "mutation"]}
}
```
2025-07-03 16:04:31,334 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:04:31,334 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.643, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [[436.0, 321.0, 1795.652, 1565.212], [1795.652, 1565.212, 3345.0, 2463.0]], 'opportunity_regions': [[436.0, 1565.212, 1795.652, 2463.0], [1795.652, 321.0, 3345.0, 1565.212]], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:04:31,334 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:04:31,334 - __main__ - INFO - 分析阶段完成
2025-07-03 16:04:31,334 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.643, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [[436.0, 321.0, 1795.652, 1565.212], [1795.652, 1565.212, 3345.0, 2463.0]], 'opportunity_regions': [[436.0, 1565.212, 1795.652, 2463.0], [1795.652, 321.0, 3345.0, 1565.212]], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:04:31,335 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:04:31,335 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:04:31,335 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.643
  • best_cost: 11509.0
  • mean_cost: 50334.0
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): [436.0, 321.0, 1795., [1795.652, 1565.212,
- Opportunity regions (# 2): [436.0, 1565.212, 17, [1795.652, 321.0, 33
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.5},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:04:31,336 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:04:31,336 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:04:43,121 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest strong exploration bias with minor exploitation."
}
```
2025-07-03 16:04:43,122 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-03 16:04:43,122 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-03 16:04:43,122 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest strong exploration bias with minor exploitation."
}
```
2025-07-03 16:04:43,123 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:04:43,123 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'explore']
2025-07-03 16:04:43,123 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest strong exploration bias with minor exploitation."
}
```
2025-07-03 16:04:43,124 - __main__ - INFO - 开始进化阶段
2025-07-03 16:04:43,124 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:04:43,124 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:04:43,124 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11509.0 (rank 0.0%)
- Population diversity: 0.4646153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:04:43,125 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:04:43,125 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:05:00,276 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "436.0, 1565.21, 1795.652, 321.0",
  "strategy_comment": "Prioritizes opportunity regions and adjacent sparse cells."
}
```
2025-07-03 16:05:00,277 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:05:00,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20339.0, 路径: [0, 50, 37, 1, 3, 12, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 63, 62, 61, 64]
2025-07-03 16:05:00,278 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 50, 37, 1, 3, 12, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 63, 62, 61, 64], 'cur_cost': 20339.0}
2025-07-03 16:05:00,278 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:05:00,278 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:05:00,279 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11509.0 (rank 0.0%)
- Population diversity: 0.48307692307692307
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:05:00,279 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:05:00,279 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:05:18,029 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "436.0, 1565.21, 1795.652, 321.0",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult areas."
}
```
2025-07-03 16:05:18,029 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:05:18,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18763.0, 路径: [0, 32, 28, 14, 51, 18, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 61, 63, 64, 62]
2025-07-03 16:05:18,030 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 32, 28, 14, 51, 18, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 61, 63, 64, 62], 'cur_cost': 18763.0}
2025-07-03 16:05:18,030 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 16:05:18,030 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:05:18,031 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 110263.0 (rank 90.0%)
- Population diversity: 0.5030769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:05:18,031 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:05:18,031 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:05:35,166 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "436.0, 1565.21, 1795.652, 321.0",
  "strategy_comment": "Focuses on opportunity regions and sparse cells."
}
```
2025-07-03 16:05:35,166 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:05:35,166 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19042.0, 路径: [0, 59, 39, 49, 65, 57, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58, 60, 62, 63, 64, 61]
2025-07-03 16:05:35,167 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 59, 39, 49, 65, 57, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58, 60, 62, 63, 64, 61], 'cur_cost': 19042.0}
2025-07-03 16:05:35,167 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:05:35,167 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:05:35,168 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11509.0 (rank 0.0%)
- Population diversity: 0.4230769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:05:35,168 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:05:35,168 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:05:52,554 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 1795, 321, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "436.0, 1565.21, 1795.652",
  "strategy_comment": "Prioritizes opportunity regions, avoids difficult areas."
}
```
2025-07-03 16:05:52,554 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:05:52,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17202.0, 路径: [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4]
2025-07-03 16:05:52,556 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4], 'cur_cost': 17202.0}
2025-07-03 16:05:52,556 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:05:52,556 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:05:52,557 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104738.0 (rank 70.0%)
- Population diversity: 0.4338461538461538
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:05:52,557 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:05:52,557 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:06:10,084 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "opportunity_regions(436,1565,321,1795,652)",
  "strategy_comment": "Focuses on opportunity regions and sparse cells."
}
```
2025-07-03 16:06:10,085 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:06:10,085 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19268.0, 路径: [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62]
2025-07-03 16:06:10,086 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62], 'cur_cost': 19268.0}
2025-07-03 16:06:10,086 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 16:06:10,086 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:06:10,086 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:06:10,087 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 96382.0
2025-07-03 16:06:10,590 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:06:10,591 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:06:10,591 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:06:10,596 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:06:10,597 - ExploitationExpert - INFO - populations: [{'tour': [0, 50, 37, 1, 3, 12, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 63, 62, 61, 64], 'cur_cost': 20339.0}, {'tour': [0, 32, 28, 14, 51, 18, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 61, 63, 64, 62], 'cur_cost': 18763.0}, {'tour': [0, 59, 39, 49, 65, 57, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58, 60, 62, 63, 64, 61], 'cur_cost': 19042.0}, {'tour': [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4], 'cur_cost': 17202.0}, {'tour': [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62], 'cur_cost': 19268.0}, {'tour': array([59, 63, 45, 15, 13, 41, 40, 49, 24,  5, 19, 27, 35, 36, 25, 62, 42,
        6, 57, 65, 46,  7, 55, 64,  8,  0, 44, 33, 17, 52, 32, 61, 39, 23,
       16, 51,  2, 48, 53, 60, 20, 29, 54,  9,  4, 12, 47, 28, 11, 10, 18,
       43, 30, 22,  1, 56,  3, 14, 50, 21, 34, 26, 31, 58, 38, 37]), 'cur_cost': 96382.0}, {'tour': array([44, 40, 60, 22, 10, 41, 25, 53, 37, 46,  9, 16, 63, 56, 15,  2, 50,
       59, 39, 58, 54, 30, 27, 48, 49, 18, 35, 33, 32, 28, 61, 11, 64, 45,
        3,  4, 29, 19, 65,  8, 47, 62, 36, 31, 51,  1, 14, 42, 52,  6, 20,
       26, 38, 17,  5, 43,  7, 21, 23,  0, 55, 34, 12, 13, 57, 24]), 'cur_cost': 109831.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([20,  3, 28, 46, 18, 32,  7, 35, 13,  1, 58,  9, 50, 47, 52, 14, 53,
       48, 15, 49,  6, 16, 39,  8, 21, 43, 54, 30, 41, 34, 25, 38, 12,  2,
       55, 26, 45, 44, 59,  0, 23, 29, 24, 17, 11, 61, 40, 36, 42,  5, 56,
       10, 27, 57, 31, 65, 19, 37, 22, 60,  4, 62, 64, 63, 51, 33]), 'cur_cost': 109454.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}]
2025-07-03 16:06:10,598 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:06:10,599 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-03 16:06:10,599 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-03 16:06:10,599 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:06:10,599 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:06:10,600 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:06:10,600 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 97472.0
2025-07-03 16:06:11,103 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:06:11,103 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:06:11,103 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:06:11,110 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:06:11,110 - ExploitationExpert - INFO - populations: [{'tour': [0, 50, 37, 1, 3, 12, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 63, 62, 61, 64], 'cur_cost': 20339.0}, {'tour': [0, 32, 28, 14, 51, 18, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 61, 63, 64, 62], 'cur_cost': 18763.0}, {'tour': [0, 59, 39, 49, 65, 57, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58, 60, 62, 63, 64, 61], 'cur_cost': 19042.0}, {'tour': [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4], 'cur_cost': 17202.0}, {'tour': [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62], 'cur_cost': 19268.0}, {'tour': array([59, 63, 45, 15, 13, 41, 40, 49, 24,  5, 19, 27, 35, 36, 25, 62, 42,
        6, 57, 65, 46,  7, 55, 64,  8,  0, 44, 33, 17, 52, 32, 61, 39, 23,
       16, 51,  2, 48, 53, 60, 20, 29, 54,  9,  4, 12, 47, 28, 11, 10, 18,
       43, 30, 22,  1, 56,  3, 14, 50, 21, 34, 26, 31, 58, 38, 37]), 'cur_cost': 96382.0}, {'tour': array([34, 32, 54,  9, 35, 23, 27,  0, 18, 28, 16, 57,  6, 41, 40, 20, 64,
       52, 60, 63, 45, 29,  3, 39, 12, 62, 46, 19, 22,  5, 24, 48, 50, 17,
       53, 11, 47, 31,  2,  7, 25, 36, 38,  1, 13, 21, 37, 33, 42, 30, 49,
       14,  8, 58, 15, 43, 26, 55,  4, 51, 65, 10, 61, 59, 56, 44]), 'cur_cost': 97472.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}, {'tour': array([20,  3, 28, 46, 18, 32,  7, 35, 13,  1, 58,  9, 50, 47, 52, 14, 53,
       48, 15, 49,  6, 16, 39,  8, 21, 43, 54, 30, 41, 34, 25, 38, 12,  2,
       55, 26, 45, 44, 59,  0, 23, 29, 24, 17, 11, 61, 40, 36, 42,  5, 56,
       10, 27, 57, 31, 65, 19, 37, 22, 60,  4, 62, 64, 63, 51, 33]), 'cur_cost': 109454.0}, {'tour': [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11509.0}]
2025-07-03 16:06:11,112 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:06:11,112 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-03 16:06:11,112 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:06:11,113 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 16:06:11,113 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:06:11,113 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:06:11,114 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 90370.0
2025-07-03 16:06:11,115 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：8,跳过局部搜索
2025-07-03 16:06:11,115 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:06:11,115 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([32, 46,  6, 50,  7, 38, 13, 44, 45, 31,  2, 60, 57, 59,  4,  1,  3,
       51, 47, 21, 63, 24, 52, 43, 56, 35, 61, 65, 36, 55, 16, 27, 34, 25,
       11, 15, 17,  0, 22, 14, 29, 26, 28, 23,  9, 62, 53, 58, 10, 20, 48,
       41, 12, 40, 19, 64, 42, 39, 30,  5, 18, 54,  8, 49, 33, 37]), 'cur_cost': 90370.0}
2025-07-03 16:06:11,115 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 16:06:11,115 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:06:11,115 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109454.0 (rank 90.0%)
- Population diversity: 0.54
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:06:11,116 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:06:11,116 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:06:28,160 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "436.0, 1565.21, 1795.652, 321.0",
  "strategy_comment": "Focuses on opportunity regions and adjacent sparse cells."
}
```
2025-07-03 16:06:28,160 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:06:28,160 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17434.0, 路径: [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63]
2025-07-03 16:06:28,161 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63], 'cur_cost': 17434.0}
2025-07-03 16:06:28,161 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 16:06:28,161 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:06:28,163 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11509.0 (rank 0.0%)
- Population diversity: 0.4646153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): [436.0, 1565.21, [1795.652, 321.
- Difficult regions to avoid (sample): [436.0, 321.0, , [1795.652, 1565
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:06:28,163 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:06:28,164 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:06:45,229 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 436, 1565, 321, 1795, 652, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "436.0, 1565.21, 1795.652, 321.0",
  "strategy_comment": "Targets opportunity regions while avoiding difficult areas."
}
```
2025-07-03 16:06:45,230 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:06:45,230 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18412.0, 路径: [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61]
2025-07-03 16:06:45,231 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61], 'cur_cost': 18412.0}
2025-07-03 16:06:45,231 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 50, 37, 1, 3, 12, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 63, 62, 61, 64], 'cur_cost': 20339.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 32, 28, 14, 51, 18, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 65, 61, 63, 64, 62], 'cur_cost': 18763.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 59, 39, 49, 65, 57, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 58, 60, 62, 63, 64, 61], 'cur_cost': 19042.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4], 'cur_cost': 17202.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62], 'cur_cost': 19268.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 46,  6, 50,  7, 38, 13, 44, 45, 31,  2, 60, 57, 59,  4,  1,  3,
       51, 47, 21, 63, 24, 52, 43, 56, 35, 61, 65, 36, 55, 16, 27, 34, 25,
       11, 15, 17,  0, 22, 14, 29, 26, 28, 23,  9, 62, 53, 58, 10, 20, 48,
       41, 12, 40, 19, 64, 42, 39, 30,  5, 18, 54,  8, 49, 33, 37]), 'cur_cost': 90370.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63], 'cur_cost': 17434.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61], 'cur_cost': 18412.0}}]
2025-07-03 16:06:45,231 - __main__ - INFO - 进化阶段完成
2025-07-03 16:06:45,232 - __main__ - INFO - 开始评估阶段
2025-07-03 16:06:45,232 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:06:45,233 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:06:45,234 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 11509.0 → 17202.0 (Δ -5693.0)
- Mean cost: 50334.0 → 41468.4
- Diversity: 0.465 → 0.465 (Δ 0.0)

#### Elite metrics
- Elite count: 15 → 15
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.169 → 0.169 (Δ 0.0)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ 34290.14
- Exploit: 3 inds, success 33.3% , avg Δ -50458.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 2 iterations: costs [29074.0, 11509.0], diversity [0.9474747474747476, 0.6434343434343434]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:06:45,234 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:06:54,984 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.429,
    "exploit": 0.333
  },
  "balance_state": "explore_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "maintain_diversity_weight_at_current_level",
    "monitor_elite_diversity_for_signs_of_stagnation"
  ]
}
```
2025-07-03 16:06:55,011 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:06:55,011 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.429,
    "exploit": 0.333
  },
  "balance_state": "explore_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "maintain_diversity_weight_at_current_level",
    "monitor_elite_diversity_for_signs_of_stagnation"
  ]
}
```
2025-07-03 16:06:55,012 - __main__ - INFO - 评估阶段完成
2025-07-03 16:06:55,012 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.429,
    "exploit": 0.333
  },
  "balance_state": "explore_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "maintain_diversity_weight_at_current_level",
    "monitor_elite_diversity_for_signs_of_stagnation"
  ]
}
```
2025-07-03 16:06:55,012 - __main__ - INFO - 当前最佳适应度: 17202.0
2025-07-03 16:06:55,014 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_2.pkl
2025-07-03 16:06:55,014 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-03 16:06:55,014 - __main__ - INFO - 开始分析阶段
2025-07-03 16:06:55,014 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:06:55,031 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 17202.0, 'max': 97472.0, 'mean': 41468.4, 'std': 34927.30909245658}, 'diversity': 0.6558922558922559, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:06:55,032 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 17202.0, 'max': 97472.0, 'mean': 41468.4, 'std': 34927.30909245658}, 'diversity_level': 0.6558922558922559, 'convergence_level': 0.0, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1], 'summary': '7 clusters, sizes [4, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1], 'summary': '7 clusters, sizes [4, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:06:55,033 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:06:55,033 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:06:55,033 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:06:55,038 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:06:55,039 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:06:55,039 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:06:55,039 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:06:55,040 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:06:55,040 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:06:55,044 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:06:55,045 - __main__ - INFO - 精英专家分析报告: {'elite_count': 15, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 7681.0, 'avg_gap': 25349.0}, 'structure_gap': {'unique_elite_edges': 208, 'unique_pop_edges': 326, 'common_edges': 52}}, 'elite_diversity': {'diversity_score': 0.29220779220779225}}
2025-07-03 16:06:55,045 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:06:55,045 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:06:55,048 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:06:55,048 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 17202.0, mean 41468.4, max 97472.0, std 34927.30909245658
- diversity: 0.6558922558922559
- convergence: 0.0
- clustering: 7 clusters, sizes [4, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [29074.0, 11509.0, 17202.0] (Δ 8782.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.29220779220779225}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:06:55,049 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:07:11,677 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.656,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation"]
  }
}
```
2025-07-03 16:07:11,678 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:07:11,678 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.656, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:07:11,678 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:07:11,678 - __main__ - INFO - 分析阶段完成
2025-07-03 16:07:11,679 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.656, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:07:11,679 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:07:11,679 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:07:11,679 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.656
  • best_cost: 17202.0
  • mean_cost: 41468.4
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'node': 1, 'positio, {'node': 2, 'positio...
- Opportunity regions (# 3): {'node': 4, 'positio, {'node': 5, 'positio...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.429,
    "exploit": 0.333
  },
  "b...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:07:11,679 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:07:11,680 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:07:22,464 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover opportunity regions."
}
```
2025-07-03 16:07:22,464 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 16:07:22,464 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 16:07:22,464 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover opportunity regions."
}
```
2025-07-03 16:07:22,464 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:07:22,465 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 16:07:22,465 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 70% explore to discover opportunity regions."
}
```
2025-07-03 16:07:22,465 - __main__ - INFO - 开始进化阶段
2025-07-03 16:07:22,465 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:07:22,465 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:07:22,467 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 20339.0 (rank 60.0%)
- Population diversity: 0.4646153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:07:22,467 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:07:22,467 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:07:38,243 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes 1 and 2."
}
```
2025-07-03 16:07:38,243 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:07:38,243 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:07:38,243 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:07:38,244 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:07:38,244 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:07:38,244 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 18763.0 (rank 40.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:07:38,245 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:07:38,245 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:07:55,413 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:07:55,413 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:07:55,413 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:07:55,413 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:07:55,414 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:07:55,414 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:07:55,414 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:07:55,414 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 113353.0
2025-07-03 16:07:55,919 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:07:55,919 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:07:55,919 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:07:55,925 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:07:55,925 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 4, 21, 11, 52, 15, 46, 37, 60, 33, 19, 47, 65,  2, 14,  9, 32, 54,
       18, 10, 26, 40,  0, 62, 28, 44, 45, 39, 64, 13, 59, 27, 17, 56, 51,
       41, 25, 63, 48, 29,  3, 31, 30, 58, 57, 49,  1, 36, 38, 61, 53,  6,
       55, 24, 50, 35, 43, 12,  7, 20,  8,  5, 34, 16, 42, 23, 22]), 'cur_cost': 113353.0}, {'tour': [0, 60, 31, 27, 65, 64, 63, 62, 61, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 30, 29, 28, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 2, 1, 3, 4], 'cur_cost': 17202.0}, {'tour': [0, 12, 57, 54, 35, 7, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 64, 63, 65, 61, 62], 'cur_cost': 19268.0}, {'tour': array([59, 63, 45, 15, 13, 41, 40, 49, 24,  5, 19, 27, 35, 36, 25, 62, 42,
        6, 57, 65, 46,  7, 55, 64,  8,  0, 44, 33, 17, 52, 32, 61, 39, 23,
       16, 51,  2, 48, 53, 60, 20, 29, 54,  9,  4, 12, 47, 28, 11, 10, 18,
       43, 30, 22,  1, 56,  3, 14, 50, 21, 34, 26, 31, 58, 38, 37]), 'cur_cost': 96382.0}, {'tour': array([34, 32, 54,  9, 35, 23, 27,  0, 18, 28, 16, 57,  6, 41, 40, 20, 64,
       52, 60, 63, 45, 29,  3, 39, 12, 62, 46, 19, 22,  5, 24, 48, 50, 17,
       53, 11, 47, 31,  2,  7, 25, 36, 38,  1, 13, 21, 37, 33, 42, 30, 49,
       14,  8, 58, 15, 43, 26, 55,  4, 51, 65, 10, 61, 59, 56, 44]), 'cur_cost': 97472.0}, {'tour': [32, 46, 6, 50, 7, 38, 13, 44, 45, 31, 2, 60, 57, 59, 4, 1, 3, 51, 47, 21, 63, 24, 52, 43, 56, 35, 61, 65, 36, 55, 16, 27, 34, 25, 11, 15, 17, 0, 22, 14, 29, 26, 28, 23, 9, 62, 53, 58, 10, 20, 48, 41, 12, 40, 19, 64, 42, 39, 30, 5, 18, 54, 8, 49, 33, 37], 'cur_cost': 90370.0}, {'tour': [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63], 'cur_cost': 17434.0}, {'tour': [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61], 'cur_cost': 18412.0}]
2025-07-03 16:07:55,927 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:07:55,927 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 12, 'skip_rate': 0.08333333333333333, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 11, 'cache_hits': 8, 'similarity_calculations': 64, 'cache_hit_rate': 0.125, 'cache_size': 56}}
2025-07-03 16:07:55,927 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 16:07:55,927 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:07:55,927 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:07:55,928 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17202.0 (rank 20.0%)
- Population diversity: 0.5153846153846153
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:07:55,928 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:07:55,929 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:08:11,976 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:08:11,977 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:08:11,977 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:08:11,978 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:08:11,978 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-03 16:08:11,978 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:08:11,978 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:08:11,979 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99091.0
2025-07-03 16:08:12,482 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:08:12,482 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:08:12,482 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:08:12,487 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:08:12,488 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 4, 21, 11, 52, 15, 46, 37, 60, 33, 19, 47, 65,  2, 14,  9, 32, 54,
       18, 10, 26, 40,  0, 62, 28, 44, 45, 39, 64, 13, 59, 27, 17, 56, 51,
       41, 25, 63, 48, 29,  3, 31, 30, 58, 57, 49,  1, 36, 38, 61, 53,  6,
       55, 24, 50, 35, 43, 12,  7, 20,  8,  5, 34, 16, 42, 23, 22]), 'cur_cost': 113353.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([38, 26,  3, 56, 29, 30, 23, 39,  9, 58, 10, 21, 19, 54, 59, 61, 50,
       25, 12,  0, 18, 40, 47, 41, 53, 48, 55, 28, 11, 15, 17, 42,  1,  7,
       64, 51, 63, 22, 35, 14, 13, 62, 37, 34, 31,  4, 52, 33, 57, 49, 44,
       60, 32,  8, 24, 36, 16, 46, 43, 45, 27, 65, 20,  6,  2,  5]), 'cur_cost': 99091.0}, {'tour': array([59, 63, 45, 15, 13, 41, 40, 49, 24,  5, 19, 27, 35, 36, 25, 62, 42,
        6, 57, 65, 46,  7, 55, 64,  8,  0, 44, 33, 17, 52, 32, 61, 39, 23,
       16, 51,  2, 48, 53, 60, 20, 29, 54,  9,  4, 12, 47, 28, 11, 10, 18,
       43, 30, 22,  1, 56,  3, 14, 50, 21, 34, 26, 31, 58, 38, 37]), 'cur_cost': 96382.0}, {'tour': array([34, 32, 54,  9, 35, 23, 27,  0, 18, 28, 16, 57,  6, 41, 40, 20, 64,
       52, 60, 63, 45, 29,  3, 39, 12, 62, 46, 19, 22,  5, 24, 48, 50, 17,
       53, 11, 47, 31,  2,  7, 25, 36, 38,  1, 13, 21, 37, 33, 42, 30, 49,
       14,  8, 58, 15, 43, 26, 55,  4, 51, 65, 10, 61, 59, 56, 44]), 'cur_cost': 97472.0}, {'tour': [32, 46, 6, 50, 7, 38, 13, 44, 45, 31, 2, 60, 57, 59, 4, 1, 3, 51, 47, 21, 63, 24, 52, 43, 56, 35, 61, 65, 36, 55, 16, 27, 34, 25, 11, 15, 17, 0, 22, 14, 29, 26, 28, 23, 9, 62, 53, 58, 10, 20, 48, 41, 12, 40, 19, 64, 42, 39, 30, 5, 18, 54, 8, 49, 33, 37], 'cur_cost': 90370.0}, {'tour': [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63], 'cur_cost': 17434.0}, {'tour': [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61], 'cur_cost': 18412.0}]
2025-07-03 16:08:12,489 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:08:12,490 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 13, 'skip_rate': 0.07692307692307693, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 12, 'cache_hits': 8, 'similarity_calculations': 75, 'cache_hit_rate': 0.10666666666666667, 'cache_size': 67}}
2025-07-03 16:08:12,490 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-03 16:08:12,490 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:08:12,490 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:08:12,491 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96382.0 (rank 60.0%)
- Population diversity: 0.5723076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:08:12,492 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:08:12,492 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:08:28,870 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions early"
}
```
2025-07-03 16:08:28,871 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:08:28,871 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:08:28,872 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:08:28,872 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:08:28,872 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:08:28,872 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:08:28,872 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104583.0
2025-07-03 16:08:29,375 - ExploitationExpert - INFO - res_population_num: 15
2025-07-03 16:08:29,375 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:08:29,375 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:08:29,382 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:08:29,382 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 4, 21, 11, 52, 15, 46, 37, 60, 33, 19, 47, 65,  2, 14,  9, 32, 54,
       18, 10, 26, 40,  0, 62, 28, 44, 45, 39, 64, 13, 59, 27, 17, 56, 51,
       41, 25, 63, 48, 29,  3, 31, 30, 58, 57, 49,  1, 36, 38, 61, 53,  6,
       55, 24, 50, 35, 43, 12,  7, 20,  8,  5, 34, 16, 42, 23, 22]), 'cur_cost': 113353.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([38, 26,  3, 56, 29, 30, 23, 39,  9, 58, 10, 21, 19, 54, 59, 61, 50,
       25, 12,  0, 18, 40, 47, 41, 53, 48, 55, 28, 11, 15, 17, 42,  1,  7,
       64, 51, 63, 22, 35, 14, 13, 62, 37, 34, 31,  4, 52, 33, 57, 49, 44,
       60, 32,  8, 24, 36, 16, 46, 43, 45, 27, 65, 20,  6,  2,  5]), 'cur_cost': 99091.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([32, 44, 14, 43, 37,  2, 36, 48, 40, 23, 57, 42, 21, 33,  8, 29, 61,
       30, 15, 16, 31, 38, 27, 46, 24, 18, 19,  3, 62, 60, 22,  6, 39,  5,
       52, 41,  4,  1, 45,  7, 65, 12, 17, 28, 25, 20, 58, 35, 47, 54, 56,
       53, 11, 51, 49, 13, 55, 26, 63, 59, 10, 50,  9,  0, 64, 34]), 'cur_cost': 104583.0}, {'tour': [32, 46, 6, 50, 7, 38, 13, 44, 45, 31, 2, 60, 57, 59, 4, 1, 3, 51, 47, 21, 63, 24, 52, 43, 56, 35, 61, 65, 36, 55, 16, 27, 34, 25, 11, 15, 17, 0, 22, 14, 29, 26, 28, 23, 9, 62, 53, 58, 10, 20, 48, 41, 12, 40, 19, 64, 42, 39, 30, 5, 18, 54, 8, 49, 33, 37], 'cur_cost': 90370.0}, {'tour': [0, 60, 2, 64, 12, 5, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 65, 63], 'cur_cost': 17434.0}, {'tour': [0, 18, 37, 33, 42, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 64, 65, 63, 62, 61], 'cur_cost': 18412.0}]
2025-07-03 16:08:29,385 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:08:29,385 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 14, 'skip_rate': 0.07142857142857142, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 13, 'cache_hits': 8, 'similarity_calculations': 87, 'cache_hit_rate': 0.09195402298850575, 'cache_size': 79}}
2025-07-03 16:08:29,385 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:08:29,386 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 16:08:29,386 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:08:29,386 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90370.0 (rank 60.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:08:29,387 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:08:29,387 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:08:45,146 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:08:45,147 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:08:45,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:08:45,147 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:08:45,148 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 16:08:45,148 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:08:45,149 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17434.0 (rank 50.0%)
- Population diversity: 0.41846153846153844
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:08:45,149 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:08:45,149 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:09:02,104 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:09:02,105 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:09:02,105 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:09:02,105 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:09:02,105 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 16:09:02,105 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:02,106 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:02,106 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109498.0
2025-07-03 16:09:02,609 - ExploitationExpert - INFO - res_population_num: 16
2025-07-03 16:09:02,609 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521]
2025-07-03 16:09:02,609 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:09:02,616 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:09:02,617 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 4, 21, 11, 52, 15, 46, 37, 60, 33, 19, 47, 65,  2, 14,  9, 32, 54,
       18, 10, 26, 40,  0, 62, 28, 44, 45, 39, 64, 13, 59, 27, 17, 56, 51,
       41, 25, 63, 48, 29,  3, 31, 30, 58, 57, 49,  1, 36, 38, 61, 53,  6,
       55, 24, 50, 35, 43, 12,  7, 20,  8,  5, 34, 16, 42, 23, 22]), 'cur_cost': 113353.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([38, 26,  3, 56, 29, 30, 23, 39,  9, 58, 10, 21, 19, 54, 59, 61, 50,
       25, 12,  0, 18, 40, 47, 41, 53, 48, 55, 28, 11, 15, 17, 42,  1,  7,
       64, 51, 63, 22, 35, 14, 13, 62, 37, 34, 31,  4, 52, 33, 57, 49, 44,
       60, 32,  8, 24, 36, 16, 46, 43, 45, 27, 65, 20,  6,  2,  5]), 'cur_cost': 99091.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([32, 44, 14, 43, 37,  2, 36, 48, 40, 23, 57, 42, 21, 33,  8, 29, 61,
       30, 15, 16, 31, 38, 27, 46, 24, 18, 19,  3, 62, 60, 22,  6, 39,  5,
       52, 41,  4,  1, 45,  7, 65, 12, 17, 28, 25, 20, 58, 35, 47, 54, 56,
       53, 11, 51, 49, 13, 55, 26, 63, 59, 10, 50,  9,  0, 64, 34]), 'cur_cost': 104583.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 58, 65, 26, 37, 42, 52,  7, 36, 43, 56, 32, 38,  8, 49, 47, 30,
       39,  2, 19, 17, 63, 27, 12, 51,  4, 34, 53, 22, 46, 11, 35, 24, 21,
       48, 62, 13, 18,  6, 10, 40, 15, 28, 50, 57, 54, 16, 25,  1, 55,  3,
       29, 41,  9, 60, 33, 45, 31, 20, 44, 64,  0,  5, 61, 59, 14]), 'cur_cost': 109498.0}]
2025-07-03 16:09:02,619 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:09:02,619 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 15, 'skip_rate': 0.06666666666666667, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 14, 'cache_hits': 8, 'similarity_calculations': 100, 'cache_hit_rate': 0.08, 'cache_size': 92}}
2025-07-03 16:09:02,620 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 16:09:02,620 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}]
2025-07-03 16:09:02,621 - __main__ - INFO - 进化阶段完成
2025-07-03 16:09:02,621 - __main__ - INFO - 开始评估阶段
2025-07-03 16:09:02,621 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:09:02,622 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:09:02,622 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 17202.0 → 11573.0 (Δ 5629.0)
- Mean cost: 41468.4 → 49596.3
- Diversity: 0.465 → 0.478 (Δ 0.0138)

#### Elite metrics
- Elite count: 15 → 16
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.169 → 0.163 (Δ -0.0067)

#### Strategy performance
- Explore: 6 inds, success 100.0% , avg Δ 31842.0
- Exploit: 4 inds, success 0.0% , avg Δ -68082.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [29074.0, 11509.0, 17202.0], diversity [0.9474747474747476, 0.6434343434343434, 0.6558922558922559]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:09:02,624 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:09:10,692 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.5", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:09:10,719 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:09:10,720 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.5", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:09:10,720 - __main__ - INFO - 评估阶段完成
2025-07-03 16:09:10,721 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["reduce_explore_ratio_to_0.5", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:09:10,721 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:09:10,723 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_3.pkl
2025-07-03 16:09:10,723 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-03 16:09:10,723 - __main__ - INFO - 开始分析阶段
2025-07-03 16:09:10,723 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:09:10,740 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 113353.0, 'mean': 49596.3, 'std': 46691.56349930895}, 'diversity': 0.6484848484848484, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:09:10,741 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 113353.0, 'mean': 49596.3, 'std': 46691.56349930895}, 'diversity_level': 0.6484848484848484, 'convergence_level': 0.0, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:09:10,742 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:09:10,742 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:09:10,742 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:09:10,748 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:09:10,750 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:09:10,750 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:09:10,750 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:09:10,750 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:09:10,750 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:09:10,756 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:09:10,756 - __main__ - INFO - 精英专家分析报告: {'elite_count': 16, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2052.0, 'avg_gap': 33889.3}, 'structure_gap': {'unique_elite_edges': 232, 'unique_pop_edges': 292, 'common_edges': 32}}, 'elite_diversity': {'diversity_score': 0.2941919191919191}}
2025-07-03 16:09:10,757 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:09:10,757 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:09:10,759 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:09:10,759 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11573.0, mean 49596.3, max 113353.0, std 46691.56349930895
- diversity: 0.6484848484848484
- convergence: 0.0
- clustering: 5 clusters, sizes [6, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [11509.0, 17202.0, 11573.0] (Δ 2814.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2941919191919191}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:09:10,760 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:09:26,597 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.648,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "mid",
  "evolution_direction": {
    "recommended_focus": "exploit",
    "operators": ["local_search", "edge_recombination"]
  }
}
```
2025-07-03 16:09:26,597 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:09:26,597 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.648, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'mid', 'evolution_direction': {'recommended_focus': 'exploit', 'operators': ['local_search', 'edge_recombination']}}
2025-07-03 16:09:26,598 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:09:26,598 - __main__ - INFO - 分析阶段完成
2025-07-03 16:09:26,598 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.648, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'mid', 'evolution_direction': {'recommended_focus': 'exploit', 'operators': ['local_search', 'edge_recombination']}}
2025-07-03 16:09:26,598 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:09:26,598 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:09:26,599 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.648
  • best_cost: 11573.0
  • mean_cost: 49596.3
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: mid
- Landscape focus suggestion: exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'node': 1, 'positio, {'node': 2, 'positio...
- Opportunity regions (# 3): {'node': 4, 'positio, {'node': 5, 'positio...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:09:26,599 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:09:26,599 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:09:37,004 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Recommended focus is exploit; diversity is moderate but convergence is low."
}
```
2025-07-03 16:09:37,005 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-03 16:09:37,005 - __main__ - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-03 16:09:37,005 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Recommended focus is exploit; diversity is moderate but convergence is low."
}
```
2025-07-03 16:09:37,006 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:09:37,006 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-03 16:09:37,006 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Recommended focus is exploit; diversity is moderate but convergence is low."
}
```
2025-07-03 16:09:37,006 - __main__ - INFO - 开始进化阶段
2025-07-03 16:09:37,007 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-03 16:09:37,007 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:37,007 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:37,007 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 98227.0
2025-07-03 16:09:37,511 - ExploitationExpert - INFO - res_population_num: 16
2025-07-03 16:09:37,511 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428]
2025-07-03 16:09:37,511 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64)]
2025-07-03 16:09:37,518 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:09:37,518 - ExploitationExpert - INFO - populations: [{'tour': array([13, 50,  2, 55, 60, 29,  3,  5, 61, 35, 65, 58,  1, 30, 41, 15, 27,
       31, 23, 24, 44, 26, 37, 10, 51, 42, 11, 46,  0, 36, 16, 18, 39, 40,
       17, 59, 49, 53, 28,  4,  6, 12, 32,  9, 54, 64, 56, 43, 63, 33, 14,
       25, 38, 22, 20, 47, 45, 52, 21, 48, 34, 19,  7, 57, 62,  8]), 'cur_cost': 98227.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 4, 21, 11, 52, 15, 46, 37, 60, 33, 19, 47, 65,  2, 14,  9, 32, 54,
       18, 10, 26, 40,  0, 62, 28, 44, 45, 39, 64, 13, 59, 27, 17, 56, 51,
       41, 25, 63, 48, 29,  3, 31, 30, 58, 57, 49,  1, 36, 38, 61, 53,  6,
       55, 24, 50, 35, 43, 12,  7, 20,  8,  5, 34, 16, 42, 23, 22]), 'cur_cost': 113353.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([38, 26,  3, 56, 29, 30, 23, 39,  9, 58, 10, 21, 19, 54, 59, 61, 50,
       25, 12,  0, 18, 40, 47, 41, 53, 48, 55, 28, 11, 15, 17, 42,  1,  7,
       64, 51, 63, 22, 35, 14, 13, 62, 37, 34, 31,  4, 52, 33, 57, 49, 44,
       60, 32,  8, 24, 36, 16, 46, 43, 45, 27, 65, 20,  6,  2,  5]), 'cur_cost': 99091.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([32, 44, 14, 43, 37,  2, 36, 48, 40, 23, 57, 42, 21, 33,  8, 29, 61,
       30, 15, 16, 31, 38, 27, 46, 24, 18, 19,  3, 62, 60, 22,  6, 39,  5,
       52, 41,  4,  1, 45,  7, 65, 12, 17, 28, 25, 20, 58, 35, 47, 54, 56,
       53, 11, 51, 49, 13, 55, 26, 63, 59, 10, 50,  9,  0, 64, 34]), 'cur_cost': 104583.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 58, 65, 26, 37, 42, 52,  7, 36, 43, 56, 32, 38,  8, 49, 47, 30,
       39,  2, 19, 17, 63, 27, 12, 51,  4, 34, 53, 22, 46, 11, 35, 24, 21,
       48, 62, 13, 18,  6, 10, 40, 15, 28, 50, 57, 54, 16, 25,  1, 55,  3,
       29, 41,  9, 60, 33, 45, 31, 20, 44, 64,  0,  5, 61, 59, 14]), 'cur_cost': 109498.0}]
2025-07-03 16:09:37,520 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:09:37,521 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 16, 'skip_rate': 0.0625, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 15, 'cache_hits': 8, 'similarity_calculations': 114, 'cache_hit_rate': 0.07017543859649122, 'cache_size': 106}}
2025-07-03 16:09:37,521 - __main__ - WARNING - 个体 0 的利用路径生成失败，保留原个体
2025-07-03 16:09:37,521 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-03 16:09:37,521 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:37,522 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:37,522 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108344.0
2025-07-03 16:09:37,522 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：14,跳过局部搜索
2025-07-03 16:09:37,523 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:09:37,523 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([52, 65, 22, 56, 21, 44, 57, 20, 16, 60, 55, 34, 49, 32,  8, 41, 26,
       47,  1, 35, 36, 13, 27, 19, 63, 11, 54, 45, 61,  4, 42, 64, 50, 24,
       53, 48, 29, 25, 59, 10,  5,  3, 58, 40, 12, 33, 37, 62, 39, 17,  2,
       31, 28, 38, 15,  9, 18, 51,  0,  6, 14, 23, 46, 43,  7, 30]), 'cur_cost': 108344.0}
2025-07-03 16:09:37,523 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:09:37,523 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:37,523 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:37,524 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 113848.0
2025-07-03 16:09:38,027 - ExploitationExpert - INFO - res_population_num: 17
2025-07-03 16:09:38,027 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521]
2025-07-03 16:09:38,028 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:09:38,034 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:09:38,035 - ExploitationExpert - INFO - populations: [{'tour': array([13, 50,  2, 55, 60, 29,  3,  5, 61, 35, 65, 58,  1, 30, 41, 15, 27,
       31, 23, 24, 44, 26, 37, 10, 51, 42, 11, 46,  0, 36, 16, 18, 39, 40,
       17, 59, 49, 53, 28,  4,  6, 12, 32,  9, 54, 64, 56, 43, 63, 33, 14,
       25, 38, 22, 20, 47, 45, 52, 21, 48, 34, 19,  7, 57, 62,  8]), 'cur_cost': 98227.0}, {'tour': array([52, 65, 22, 56, 21, 44, 57, 20, 16, 60, 55, 34, 49, 32,  8, 41, 26,
       47,  1, 35, 36, 13, 27, 19, 63, 11, 54, 45, 61,  4, 42, 64, 50, 24,
       53, 48, 29, 25, 59, 10,  5,  3, 58, 40, 12, 33, 37, 62, 39, 17,  2,
       31, 28, 38, 15,  9, 18, 51,  0,  6, 14, 23, 46, 43,  7, 30]), 'cur_cost': 108344.0}, {'tour': array([45, 35, 19, 37,  3, 15, 38, 52, 25, 58, 36, 49, 17, 18, 30, 11, 42,
       59, 10, 46, 33,  6,  2, 12, 63, 43, 47, 29,  7,  1, 23, 14,  8, 28,
       54,  0, 22, 50,  4, 40, 64, 41, 65, 48, 20, 39, 31, 27, 16, 24, 57,
       60, 44, 51, 13,  5,  9, 32, 21, 53, 56, 62, 26, 55, 34, 61]), 'cur_cost': 113848.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([38, 26,  3, 56, 29, 30, 23, 39,  9, 58, 10, 21, 19, 54, 59, 61, 50,
       25, 12,  0, 18, 40, 47, 41, 53, 48, 55, 28, 11, 15, 17, 42,  1,  7,
       64, 51, 63, 22, 35, 14, 13, 62, 37, 34, 31,  4, 52, 33, 57, 49, 44,
       60, 32,  8, 24, 36, 16, 46, 43, 45, 27, 65, 20,  6,  2,  5]), 'cur_cost': 99091.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([32, 44, 14, 43, 37,  2, 36, 48, 40, 23, 57, 42, 21, 33,  8, 29, 61,
       30, 15, 16, 31, 38, 27, 46, 24, 18, 19,  3, 62, 60, 22,  6, 39,  5,
       52, 41,  4,  1, 45,  7, 65, 12, 17, 28, 25, 20, 58, 35, 47, 54, 56,
       53, 11, 51, 49, 13, 55, 26, 63, 59, 10, 50,  9,  0, 64, 34]), 'cur_cost': 104583.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 58, 65, 26, 37, 42, 52,  7, 36, 43, 56, 32, 38,  8, 49, 47, 30,
       39,  2, 19, 17, 63, 27, 12, 51,  4, 34, 53, 22, 46, 11, 35, 24, 21,
       48, 62, 13, 18,  6, 10, 40, 15, 28, 50, 57, 54, 16, 25,  1, 55,  3,
       29, 41,  9, 60, 33, 45, 31, 20, 44, 64,  0,  5, 61, 59, 14]), 'cur_cost': 109498.0}]
2025-07-03 16:09:38,038 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:09:38,038 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 18, 'skip_rate': 0.1111111111111111, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 16, 'cache_hits': 22, 'similarity_calculations': 144, 'cache_hit_rate': 0.1527777777777778, 'cache_size': 122}}
2025-07-03 16:09:38,038 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 16:09:38,039 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 16:09:38,039 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:38,039 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:38,040 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 117218.0
2025-07-03 16:09:38,040 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：14,跳过局部搜索
2025-07-03 16:09:38,040 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:09:38,041 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 6, 46, 53, 33, 22, 49,  9, 63, 34, 58, 50,  5, 64, 42, 15, 52, 16,
       26, 36, 54, 30, 27, 23, 28, 55, 56, 51,  8, 59, 18, 10,  0, 47, 29,
       65, 40, 20, 43, 61, 48, 14,  7, 44,  3, 39,  2, 31, 12, 24, 35, 17,
       19, 37, 21, 45, 41, 13, 60, 11, 32, 62,  4, 38, 25, 57,  1]), 'cur_cost': 117218.0}
2025-07-03 16:09:38,041 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:09:38,041 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:09:38,042 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99091.0 (rank 40.0%)
- Population diversity: 0.7230769230769231
- Evolution phase: mid
- Landscape recommends focus: exploit
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:09:38,042 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:09:38,042 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:09:54,968 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5, sparse_adjacent",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, explores adjacent sparse cells."
}
```
2025-07-03 16:09:54,968 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:09:54,969 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:09:54,969 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:09:54,970 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 16:09:54,970 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:54,970 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:54,970 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 85461.0
2025-07-03 16:09:54,971 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：14,跳过局部搜索
2025-07-03 16:09:54,971 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:09:54,972 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([49, 12, 18, 36, 16,  7,  5, 35, 31, 30, 10, 44, 37, 15, 51, 38, 40,
       41,  3, 54, 63, 56, 24, 55, 64,  1, 50, 60,  0, 22, 32, 42, 45, 46,
       43, 27, 23, 48, 19, 65,  4, 62, 29, 26, 13, 17, 39, 57, 11, 53, 52,
       59, 33, 34,  6, 20, 21, 61, 25, 47, 58,  2, 28,  8, 14,  9]), 'cur_cost': 85461.0}
2025-07-03 16:09:54,972 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:09:54,972 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:54,972 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:54,973 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 111085.0
2025-07-03 16:09:55,475 - ExploitationExpert - INFO - res_population_num: 17
2025-07-03 16:09:55,475 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521]
2025-07-03 16:09:55,475 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:09:55,482 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:09:55,482 - ExploitationExpert - INFO - populations: [{'tour': array([13, 50,  2, 55, 60, 29,  3,  5, 61, 35, 65, 58,  1, 30, 41, 15, 27,
       31, 23, 24, 44, 26, 37, 10, 51, 42, 11, 46,  0, 36, 16, 18, 39, 40,
       17, 59, 49, 53, 28,  4,  6, 12, 32,  9, 54, 64, 56, 43, 63, 33, 14,
       25, 38, 22, 20, 47, 45, 52, 21, 48, 34, 19,  7, 57, 62,  8]), 'cur_cost': 98227.0}, {'tour': array([52, 65, 22, 56, 21, 44, 57, 20, 16, 60, 55, 34, 49, 32,  8, 41, 26,
       47,  1, 35, 36, 13, 27, 19, 63, 11, 54, 45, 61,  4, 42, 64, 50, 24,
       53, 48, 29, 25, 59, 10,  5,  3, 58, 40, 12, 33, 37, 62, 39, 17,  2,
       31, 28, 38, 15,  9, 18, 51,  0,  6, 14, 23, 46, 43,  7, 30]), 'cur_cost': 108344.0}, {'tour': array([45, 35, 19, 37,  3, 15, 38, 52, 25, 58, 36, 49, 17, 18, 30, 11, 42,
       59, 10, 46, 33,  6,  2, 12, 63, 43, 47, 29,  7,  1, 23, 14,  8, 28,
       54,  0, 22, 50,  4, 40, 64, 41, 65, 48, 20, 39, 31, 27, 16, 24, 57,
       60, 44, 51, 13,  5,  9, 32, 21, 53, 56, 62, 26, 55, 34, 61]), 'cur_cost': 113848.0}, {'tour': array([ 6, 46, 53, 33, 22, 49,  9, 63, 34, 58, 50,  5, 64, 42, 15, 52, 16,
       26, 36, 54, 30, 27, 23, 28, 55, 56, 51,  8, 59, 18, 10,  0, 47, 29,
       65, 40, 20, 43, 61, 48, 14,  7, 44,  3, 39,  2, 31, 12, 24, 35, 17,
       19, 37, 21, 45, 41, 13, 60, 11, 32, 62,  4, 38, 25, 57,  1]), 'cur_cost': 117218.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([49, 12, 18, 36, 16,  7,  5, 35, 31, 30, 10, 44, 37, 15, 51, 38, 40,
       41,  3, 54, 63, 56, 24, 55, 64,  1, 50, 60,  0, 22, 32, 42, 45, 46,
       43, 27, 23, 48, 19, 65,  4, 62, 29, 26, 13, 17, 39, 57, 11, 53, 52,
       59, 33, 34,  6, 20, 21, 61, 25, 47, 58,  2, 28,  8, 14,  9]), 'cur_cost': 85461.0}, {'tour': array([ 3, 63,  6,  9, 19, 62, 40,  5, 48, 47, 32,  2, 10, 42, 43, 30, 50,
       18, 16, 28, 60, 25, 23, 20, 56,  0, 34, 38, 39, 37, 58, 29, 52, 13,
       55, 12, 59, 57,  8, 51, 54, 44, 15, 61, 11, 26, 45, 33, 41, 36, 35,
       21, 14, 46, 53, 24,  1,  7, 49, 65, 17,  4, 22, 64, 27, 31]), 'cur_cost': 111085.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 58, 65, 26, 37, 42, 52,  7, 36, 43, 56, 32, 38,  8, 49, 47, 30,
       39,  2, 19, 17, 63, 27, 12, 51,  4, 34, 53, 22, 46, 11, 35, 24, 21,
       48, 62, 13, 18,  6, 10, 40, 15, 28, 50, 57, 54, 16, 25,  1, 55,  3,
       29, 41,  9, 60, 33, 45, 31, 20, 44, 64,  0,  5, 61, 59, 14]), 'cur_cost': 109498.0}]
2025-07-03 16:09:55,486 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:09:55,487 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 21, 'skip_rate': 0.19047619047619047, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 17, 'cache_hits': 52, 'similarity_calculations': 190, 'cache_hit_rate': 0.2736842105263158, 'cache_size': 138}}
2025-07-03 16:09:55,487 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:09:55,487 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 16:09:55,487 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:09:55,488 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:09:55,488 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105402.0
2025-07-03 16:09:55,488 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：14,跳过局部搜索
2025-07-03 16:09:55,488 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:09:55,489 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([35, 57, 65, 11, 39, 21, 55, 29,  7, 25, 20, 34, 16, 31, 12,  6,  1,
       40, 13, 51,  5, 56, 64, 52,  3, 36, 61, 45, 26, 50, 42, 41, 58,  0,
       33, 28, 10, 63, 32, 22,  8, 15, 44,  2, 14, 43, 53, 59, 48, 18, 19,
       47,  4, 27, 23, 24, 54, 38, 30,  9, 49, 46, 62, 17, 60, 37]), 'cur_cost': 105402.0}
2025-07-03 16:09:55,489 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 16:09:55,489 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:09:55,490 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.7923076923076923
- Evolution phase: mid
- Landscape recommends focus: exploit
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:09:55,490 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:09:55,491 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:10:13,016 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5, sparse_adjacent",
  "strategy_comment": "Focus on opportunity nodes, avoid difficult regions, maintain diversity."
}
```
2025-07-03 16:10:13,016 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:10:13,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:10:13,017 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:10:13,017 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 16:10:13,017 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:10:13,017 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:10:13,018 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99225.0
2025-07-03 16:10:13,522 - ExploitationExpert - INFO - res_population_num: 19
2025-07-03 16:10:13,522 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9548, 9551, 108428, 9521, 9521, 9521]
2025-07-03 16:10:13,522 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 51,  5, 23, 38, 65, 22,  2, 56, 40,  6, 25, 61, 33, 32, 29,  1,
       15, 28, 18, 39, 10, 44, 35, 26, 63, 52, 54, 60, 50, 17, 16, 31, 62,
       20, 12, 30, 36, 19, 37, 55, 41, 24, 48,  7, 46, 64,  8, 14, 42, 13,
       43, 45, 53, 11, 58, 27, 34,  9, 49, 59,  4,  3, 47, 57, 21],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:10:13,530 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:10:13,531 - ExploitationExpert - INFO - populations: [{'tour': array([13, 50,  2, 55, 60, 29,  3,  5, 61, 35, 65, 58,  1, 30, 41, 15, 27,
       31, 23, 24, 44, 26, 37, 10, 51, 42, 11, 46,  0, 36, 16, 18, 39, 40,
       17, 59, 49, 53, 28,  4,  6, 12, 32,  9, 54, 64, 56, 43, 63, 33, 14,
       25, 38, 22, 20, 47, 45, 52, 21, 48, 34, 19,  7, 57, 62,  8]), 'cur_cost': 98227.0}, {'tour': array([52, 65, 22, 56, 21, 44, 57, 20, 16, 60, 55, 34, 49, 32,  8, 41, 26,
       47,  1, 35, 36, 13, 27, 19, 63, 11, 54, 45, 61,  4, 42, 64, 50, 24,
       53, 48, 29, 25, 59, 10,  5,  3, 58, 40, 12, 33, 37, 62, 39, 17,  2,
       31, 28, 38, 15,  9, 18, 51,  0,  6, 14, 23, 46, 43,  7, 30]), 'cur_cost': 108344.0}, {'tour': array([45, 35, 19, 37,  3, 15, 38, 52, 25, 58, 36, 49, 17, 18, 30, 11, 42,
       59, 10, 46, 33,  6,  2, 12, 63, 43, 47, 29,  7,  1, 23, 14,  8, 28,
       54,  0, 22, 50,  4, 40, 64, 41, 65, 48, 20, 39, 31, 27, 16, 24, 57,
       60, 44, 51, 13,  5,  9, 32, 21, 53, 56, 62, 26, 55, 34, 61]), 'cur_cost': 113848.0}, {'tour': array([ 6, 46, 53, 33, 22, 49,  9, 63, 34, 58, 50,  5, 64, 42, 15, 52, 16,
       26, 36, 54, 30, 27, 23, 28, 55, 56, 51,  8, 59, 18, 10,  0, 47, 29,
       65, 40, 20, 43, 61, 48, 14,  7, 44,  3, 39,  2, 31, 12, 24, 35, 17,
       19, 37, 21, 45, 41, 13, 60, 11, 32, 62,  4, 38, 25, 57,  1]), 'cur_cost': 117218.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([49, 12, 18, 36, 16,  7,  5, 35, 31, 30, 10, 44, 37, 15, 51, 38, 40,
       41,  3, 54, 63, 56, 24, 55, 64,  1, 50, 60,  0, 22, 32, 42, 45, 46,
       43, 27, 23, 48, 19, 65,  4, 62, 29, 26, 13, 17, 39, 57, 11, 53, 52,
       59, 33, 34,  6, 20, 21, 61, 25, 47, 58,  2, 28,  8, 14,  9]), 'cur_cost': 85461.0}, {'tour': array([ 3, 63,  6,  9, 19, 62, 40,  5, 48, 47, 32,  2, 10, 42, 43, 30, 50,
       18, 16, 28, 60, 25, 23, 20, 56,  0, 34, 38, 39, 37, 58, 29, 52, 13,
       55, 12, 59, 57,  8, 51, 54, 44, 15, 61, 11, 26, 45, 33, 41, 36, 35,
       21, 14, 46, 53, 24,  1,  7, 49, 65, 17,  4, 22, 64, 27, 31]), 'cur_cost': 111085.0}, {'tour': array([35, 57, 65, 11, 39, 21, 55, 29,  7, 25, 20, 34, 16, 31, 12,  6,  1,
       40, 13, 51,  5, 56, 64, 52,  3, 36, 61, 45, 26, 50, 42, 41, 58,  0,
       33, 28, 10, 63, 32, 22,  8, 15, 44,  2, 14, 43, 53, 59, 48, 18, 19,
       47,  4, 27, 23, 24, 54, 38, 30,  9, 49, 46, 62, 17, 60, 37]), 'cur_cost': 105402.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([15, 48, 24,  6, 31,  1, 62, 39, 26, 22, 20, 57, 40, 35, 63, 46, 13,
       38, 61, 10,  3, 29,  4, 52, 16,  2, 21, 65,  5, 11, 28, 30, 55, 59,
       54, 41, 49, 51, 12, 42, 56, 60, 27, 19, 23, 34,  0, 43, 17, 64,  9,
       25, 36, 45, 53,  7,  8, 32, 33, 44, 47, 14, 58, 37, 18, 50]), 'cur_cost': 99225.0}]
2025-07-03 16:10:13,534 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:10:13,535 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 23, 'skip_rate': 0.21739130434782608, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 18, 'cache_hits': 67, 'similarity_calculations': 222, 'cache_hit_rate': 0.30180180180180183, 'cache_size': 155}}
2025-07-03 16:10:13,535 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 16:10:13,535 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([52, 65, 22, 56, 21, 44, 57, 20, 16, 60, 55, 34, 49, 32,  8, 41, 26,
       47,  1, 35, 36, 13, 27, 19, 63, 11, 54, 45, 61,  4, 42, 64, 50, 24,
       53, 48, 29, 25, 59, 10,  5,  3, 58, 40, 12, 33, 37, 62, 39, 17,  2,
       31, 28, 38, 15,  9, 18, 51,  0,  6, 14, 23, 46, 43,  7, 30]), 'cur_cost': 108344.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 6, 46, 53, 33, 22, 49,  9, 63, 34, 58, 50,  5, 64, 42, 15, 52, 16,
       26, 36, 54, 30, 27, 23, 28, 55, 56, 51,  8, 59, 18, 10,  0, 47, 29,
       65, 40, 20, 43, 61, 48, 14,  7, 44,  3, 39,  2, 31, 12, 24, 35, 17,
       19, 37, 21, 45, 41, 13, 60, 11, 32, 62,  4, 38, 25, 57,  1]), 'cur_cost': 117218.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([49, 12, 18, 36, 16,  7,  5, 35, 31, 30, 10, 44, 37, 15, 51, 38, 40,
       41,  3, 54, 63, 56, 24, 55, 64,  1, 50, 60,  0, 22, 32, 42, 45, 46,
       43, 27, 23, 48, 19, 65,  4, 62, 29, 26, 13, 17, 39, 57, 11, 53, 52,
       59, 33, 34,  6, 20, 21, 61, 25, 47, 58,  2, 28,  8, 14,  9]), 'cur_cost': 85461.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 57, 65, 11, 39, 21, 55, 29,  7, 25, 20, 34, 16, 31, 12,  6,  1,
       40, 13, 51,  5, 56, 64, 52,  3, 36, 61, 45, 26, 50, 42, 41, 58,  0,
       33, 28, 10, 63, 32, 22,  8, 15, 44,  2, 14, 43, 53, 59, 48, 18, 19,
       47,  4, 27, 23, 24, 54, 38, 30,  9, 49, 46, 62, 17, 60, 37]), 'cur_cost': 105402.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}]
2025-07-03 16:10:13,536 - __main__ - INFO - 进化阶段完成
2025-07-03 16:10:13,536 - __main__ - INFO - 开始评估阶段
2025-07-03 16:10:13,536 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:10:13,539 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:10:13,539 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 11573.0 → 11573.0 (Δ 0.0)
- Mean cost: 49596.3 → 86195.6
- Diversity: 0.478 → 0.791 (Δ 0.3123)

#### Elite metrics
- Elite count: 16 → 19
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.163 → 0.137 (Δ -0.0257)

#### Strategy performance
- Explore: 2 inds, success 50.0% , avg Δ 43759.0
- Exploit: 8 inds, success 12.5% , avg Δ -56688.88

#### Other indicators
- No-change individuals: 1
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [11509.0, 17202.0, 11573.0], diversity [0.6434343434343434, 0.6558922558922559, 0.6484848484848484]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:10:13,540 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:10:25,078 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-late",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.125
  },
  "balance_state": "exploration_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "maintain_elite_count_at_19",
    "monitor_exploit_success_rate_for_3_more_iterations"
  ]
}
```
2025-07-03 16:10:25,112 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:10:25,112 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-late",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.125
  },
  "balance_state": "exploration_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "maintain_elite_count_at_19",
    "monitor_exploit_success_rate_for_3_more_iterations"
  ]
}
```
2025-07-03 16:10:25,113 - __main__ - INFO - 评估阶段完成
2025-07-03 16:10:25,113 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-late",
  "strategy_effectiveness": {
    "explore": 0.5,
    "exploit": 0.125
  },
  "balance_state": "exploration_effective_but_underutilized",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "maintain_elite_count_at_19",
    "monitor_exploit_success_rate_for_3_more_iterations"
  ]
}
```
2025-07-03 16:10:25,113 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:10:25,114 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_4.pkl
2025-07-03 16:10:25,130 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-03 16:10:25,131 - __main__ - INFO - 实例 composite13_66 处理完成
