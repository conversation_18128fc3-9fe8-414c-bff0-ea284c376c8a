2025-07-09 20:33:06 - log_config - INFO - 日志系统已初始化，级别: INFO
2025-07-09 20:33:06 - log_config - INFO - 日志文件: moe_app.log
2025-07-09 20:33:06 - __main__ - INFO - 项目根目录: c:\Users\<USER>\Desktop\EoH-main - idea - 0701
2025-07-09 20:33:06 - __main__ - INFO - 输入路径: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\benchmark_MMTSP
2025-07-09 20:33:06 - __main__ - INFO - 输出路径: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\benchmark_MMTSP\instance_pkl
2025-07-09 20:33:06 - loadinstance - INFO - 当前工作目录: C:\Users\<USER>\Desktop\EoH-main - idea - 0701
2025-07-09 20:33:06 - loadinstance - INFO - 输入路径: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\benchmark_MMTSP
2025-07-09 20:33:06 - loadinstance - INFO - 输出路径: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\benchmark_MMTSP\instance_pkl
2025-07-09 20:33:06 - loadinstance - INFO - 尝试加载实例: composite13_66, 路径: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\benchmark_MMTSP
2025-07-09 20:33:06 - InterfaceAPI.gemini - INFO - 初始化 gemini API接口
2025-07-09 20:33:06 - InterfaceAPI.gemini - INFO - Gemini API配置完成，模型: gemini-2.0-flash-lite
2025-07-09 20:33:06 - improved_collaboration_manager - WARNING - 知识库模块不可用
2025-07-09 20:33:07 - improved_collaboration_manager - INFO - 专家模块初始化完成
2025-07-09 20:33:07 - __main__ - INFO - 已启用改进的专家协作模式
2025-07-09 20:33:07 - __main__ - INFO - 开始处理实例: composite13_66
2025-07-09 20:33:07 - initpop - INFO - 混合初始化种群，城市数量: 66, 种群大小: 10, 贪心比例: 0.3
2025-07-09 20:33:07 - initpop - INFO - 贪心初始化种群，城市数量: 66, 种群大小: 3
2025-07-09 20:33:07 - initpop - INFO - 随机初始化种群，城市数量: 66, 种群大小: 7
2025-07-09 20:33:07 - __main__ - INFO - 初始化种群完成，当前最佳适应度: 9946.0
2025-07-09 20:33:07 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-09 20:33:07 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-09 20:33:07 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 0/5
2025-07-09 20:33:07 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:07 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 107726.0, 'mean': 77202.9, 'std': np.float64(44045.002759564)}, 'diversity': np.float64(0.9057239057239057), 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:07 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:33:07 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 0}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 0}}
2025-07-09 20:33:07 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:33:07 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:07 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 107726.0, 'mean': 77202.9, 'std': np.float64(44045.002759564)}, 'diversity': np.float64(0.9057239057239057), 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:07 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:33:07 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-09 20:33:07 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:33:07 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:33:07 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:33:07 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:33:07 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:33:07 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 0)
- Problem: 0 nodes, best cost: 9946.0
- Population diversity: 0.906
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:33:07 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:33:09 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:33:09 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:33:09 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:33:09 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:33:09 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:33:09 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:33:09 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:33:09 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:09 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:33:09 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:09 - __main__ - INFO - 开始进化阶段
2025-07-09 20:33:09 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:33:09 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:09 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:12 - ExploitationExpert - INFO - 自适应开发成功，改进: 381.00
2025-07-09 20:33:12 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(381.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:12 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:33:12 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:12 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:12 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9958.0 -> 9944.0
2025-07-09 20:33:15 - ExploitationExpert - INFO - 自适应开发成功，改进: 413.00
2025-07-09 20:33:15 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(413.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:15 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:33:15 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:15 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:19 - ExploitationExpert - INFO - 自适应开发成功，改进: 401.00
2025-07-09 20:33:19 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(401.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:19 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:33:19 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:19 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:19 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 105691.0 -> 99911.0
2025-07-09 20:33:24 - ExploitationExpert - INFO - 自适应开发成功，改进: 67377.00
2025-07-09 20:33:24 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 12,  5, 54, 52,  1,  0, 63, 53, 57, 64, 62, 22, 25, 20, 16,
       58, 56, 61, 21, 15, 46, 41, 50, 34, 30, 33, 14,  4,  3, 60, 47, 40,
       49, 43, 42, 51, 44, 55, 65, 59,  6, 31, 24, 29, 26, 28, 32, 35, 18,
       17, 23, 13, 48, 39, 38, 45, 19, 11,  7,  8, 10,  2,  9, 27]), 'cur_cost': np.float64(38314.0), 'improvement': np.float64(67377.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:24 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:33:24 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:24 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:24 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 105180.0 -> 99303.0
2025-07-09 20:33:28 - ExploitationExpert - INFO - 自适应开发成功，改进: 95651.00
2025-07-09 20:33:28 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(95651.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:28 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:33:28 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:28 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:29 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107141.0 -> 101312.0
2025-07-09 20:33:31 - ExploitationExpert - INFO - 自适应开发成功，改进: 67794.00
2025-07-09 20:33:31 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 29, 28, 24, 34, 23, 27, 26, 31, 21,  6,  3, 11, 59, 64, 57, 52,
       60,  5, 10,  9, 51, 45, 20, 49, 48, 40, 39, 15,  2, 17, 53, 65, 63,
       61,  1,  7, 22, 14, 38, 43, 42, 50, 47, 44, 12, 30, 13, 16, 19, 18,
       33, 37, 32, 41, 46,  8, 54, 62, 55, 58,  0,  4, 56, 35, 36]), 'cur_cost': np.float64(39347.0), 'improvement': np.float64(67794.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:31 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:33:31 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:31 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:31 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 102874.0 -> 97148.0
2025-07-09 20:33:37 - ExploitationExpert - INFO - 自适应开发成功，改进: 93295.00
2025-07-09 20:33:37 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 35, 34, 30, 28, 33, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9579.0), 'improvement': np.float64(93295.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:37 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:33:37 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:37 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:37 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107726.0 -> 101904.0
2025-07-09 20:33:43 - ExploitationExpert - INFO - 自适应开发成功，改进: 98179.00
2025-07-09 20:33:43 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(98179.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:43 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:33:43 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:43 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:43 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 106299.0 -> 100391.0
2025-07-09 20:33:46 - ExploitationExpert - INFO - 自适应开发成功，改进: 96728.00
2025-07-09 20:33:46 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(96728.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:46 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:33:46 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:46 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:46 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107263.0 -> 101386.0
2025-07-09 20:33:49 - ExploitationExpert - INFO - 自适应开发成功，改进: 64030.00
2025-07-09 20:33:49 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 34,  3, 17, 15, 50, 38, 10,  6, 20, 23, 18, 43, 49, 40, 48,
       42, 51, 14, 28, 25,  4, 58, 56, 60, 64, 44, 41, 45, 16, 21, 13, 61,
       65,  8,  2,  0,  7, 24, 29, 27, 37, 31, 33, 26, 22,  9, 11,  5, 53,
       63, 52, 54, 62, 59, 55, 12, 39, 47, 46, 57, 36, 30,  1, 19]), 'cur_cost': np.float64(43233.0), 'improvement': np.float64(64030.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:49 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(381.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(413.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(401.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 12,  5, 54, 52,  1,  0, 63, 53, 57, 64, 62, 22, 25, 20, 16,
       58, 56, 61, 21, 15, 46, 41, 50, 34, 30, 33, 14,  4,  3, 60, 47, 40,
       49, 43, 42, 51, 44, 55, 65, 59,  6, 31, 24, 29, 26, 28, 32, 35, 18,
       17, 23, 13, 48, 39, 38, 45, 19, 11,  7,  8, 10,  2,  9, 27]), 'cur_cost': np.float64(38314.0), 'improvement': np.float64(67377.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(95651.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 29, 28, 24, 34, 23, 27, 26, 31, 21,  6,  3, 11, 59, 64, 57, 52,
       60,  5, 10,  9, 51, 45, 20, 49, 48, 40, 39, 15,  2, 17, 53, 65, 63,
       61,  1,  7, 22, 14, 38, 43, 42, 50, 47, 44, 12, 30, 13, 16, 19, 18,
       33, 37, 32, 41, 46,  8, 54, 62, 55, 58,  0,  4, 56, 35, 36]), 'cur_cost': np.float64(39347.0), 'improvement': np.float64(67794.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 35, 34, 30, 28, 33, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9579.0), 'improvement': np.float64(93295.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(98179.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(96728.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 34,  3, 17, 15, 50, 38, 10,  6, 20, 23, 18, 43, 49, 40, 48,
       42, 51, 14, 28, 25,  4, 58, 56, 60, 64, 44, 41, 45, 16, 21, 13, 61,
       65,  8,  2,  0,  7, 24, 29, 27, 37, 31, 33, 26, 22,  9, 11,  5, 53,
       63, 52, 54, 62, 59, 55, 12, 39, 47, 46, 57, 36, 30,  1, 19]), 'cur_cost': np.float64(43233.0), 'improvement': np.float64(64030.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:33:49 - __main__ - INFO - 进化阶段完成
2025-07-09 20:33:49 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-09 20:33:49 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-09 20:33:49 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 0/5
2025-07-09 20:33:49 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:33:49 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:33:49 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-09 20:33:49 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-09 20:33:49 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-09 20:33:49 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 1/5
2025-07-09 20:33:49 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:33:49 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 1}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 1}}
2025-07-09 20:33:49 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:33:49 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:33:49 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:33:49 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:33:49 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:33:49 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:33:49 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:33:49 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 1)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.601
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:33:49 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:33:52 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:33:52 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:33:52 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:33:52 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:33:52 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:33:52 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:33:52 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:33:52 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:52 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:33:52 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:52 - __main__ - INFO - 开始进化阶段
2025-07-09 20:33:52 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:33:52 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:52 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:53 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:53 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:53 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:33:53 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:53 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:54 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:54 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:54 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:33:54 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:54 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:55 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:55 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:55 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:33:55 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:55 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:55 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 38314.0 -> 34535.0
2025-07-09 20:33:57 - ExploitationExpert - INFO - 自适应开发成功，改进: 19291.00
2025-07-09 20:33:57 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 27, 11,  7,  9,  3,  1,  0, 63, 53, 57, 64, 62, 22, 16, 21, 20,
       14, 15, 34, 30, 33, 25, 47, 40, 49, 43, 42, 51, 44, 58, 56, 61, 55,
       60, 54, 52, 65, 59,  6,  4,  2, 10,  8,  5, 12, 19, 46, 41, 50, 45,
       38, 39, 48, 13, 23, 17, 18, 35, 32, 28, 26, 29, 24, 31, 37]), 'cur_cost': np.float64(19023.0), 'improvement': np.float64(19291.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:57 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:33:57 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:57 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:57 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:57 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:57 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:33:57 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:57 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:57 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 39347.0 -> 36556.0
2025-07-09 20:34:04 - ExploitationExpert - INFO - 自适应开发成功，改进: 28356.00
2025-07-09 20:34:04 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 29, 28, 34, 24, 27, 26, 31, 21, 20, 13, 16, 19, 18, 23, 12, 15,
       17, 14, 22,  7,  1,  9,  5, 10,  0,  4,  6,  3, 11,  8,  2, 59, 64,
       57, 52, 60, 56, 58, 55, 62, 54, 53, 65, 63, 61, 49, 48, 40, 39, 44,
       47, 50, 42, 43, 38, 45, 51, 46, 41, 32, 37, 33, 30, 35, 36]), 'cur_cost': np.float64(10991.0), 'improvement': np.float64(28356.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:04 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:34:04 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:04 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:05 - ExploitationExpert - INFO - 自适应开发成功，改进: 1.00
2025-07-09 20:34:05 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9578.0), 'improvement': np.float64(1.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:05 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:34:05 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:05 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:06 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:06 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:06 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:34:06 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:06 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:07 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:07 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:07 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:34:07 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:07 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:07 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 43233.0 -> 38926.0
2025-07-09 20:34:11 - ExploitationExpert - INFO - 自适应开发成功，改进: 28092.00
2025-07-09 20:34:11 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 34,  3,  4, 58, 56, 60, 64, 55, 59, 62, 54, 52, 63, 53, 57,
       65, 61, 10,  6,  5, 11,  9,  1, 26, 33, 31, 37, 27, 29, 24,  7,  0,
        2,  8, 14, 13, 21, 20, 23, 16, 43, 49, 40, 48, 42, 51, 45, 41, 44,
       50, 38, 46, 47, 39, 19, 22, 12, 15, 17, 18, 28, 25, 36, 30]), 'cur_cost': np.float64(15141.0), 'improvement': np.float64(28092.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:11 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 27, 11,  7,  9,  3,  1,  0, 63, 53, 57, 64, 62, 22, 16, 21, 20,
       14, 15, 34, 30, 33, 25, 47, 40, 49, 43, 42, 51, 44, 58, 56, 61, 55,
       60, 54, 52, 65, 59,  6,  4,  2, 10,  8,  5, 12, 19, 46, 41, 50, 45,
       38, 39, 48, 13, 23, 17, 18, 35, 32, 28, 26, 29, 24, 31, 37]), 'cur_cost': np.float64(19023.0), 'improvement': np.float64(19291.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 29, 28, 34, 24, 27, 26, 31, 21, 20, 13, 16, 19, 18, 23, 12, 15,
       17, 14, 22,  7,  1,  9,  5, 10,  0,  4,  6,  3, 11,  8,  2, 59, 64,
       57, 52, 60, 56, 58, 55, 62, 54, 53, 65, 63, 61, 49, 48, 40, 39, 44,
       47, 50, 42, 43, 38, 45, 51, 46, 41, 32, 37, 33, 30, 35, 36]), 'cur_cost': np.float64(10991.0), 'improvement': np.float64(28356.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9578.0), 'improvement': np.float64(1.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 34,  3,  4, 58, 56, 60, 64, 55, 59, 62, 54, 52, 63, 53, 57,
       65, 61, 10,  6,  5, 11,  9,  1, 26, 33, 31, 37, 27, 29, 24,  7,  0,
        2,  8, 14, 13, 21, 20, 23, 16, 43, 49, 40, 48, 42, 51, 45, 41, 44,
       50, 38, 46, 47, 39, 19, 22, 12, 15, 17, 18, 28, 25, 36, 30]), 'cur_cost': np.float64(15141.0), 'improvement': np.float64(28092.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:34:11 - __main__ - INFO - 进化阶段完成
2025-07-09 20:34:11 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-09 20:34:11 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-09 20:34:11 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 1/5
2025-07-09 20:34:11 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:11 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:11 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-09 20:34:11 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-09 20:34:11 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-09 20:34:11 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 2/5
2025-07-09 20:34:11 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:34:11 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 2}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 2}}
2025-07-09 20:34:11 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:34:11 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:34:11 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:34:11 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:34:11 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:34:11 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:34:11 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:34:11 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 2)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.550
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:34:11 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:34:13 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:34:13 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:34:13 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:34:13 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:34:13 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:34:13 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:34:13 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:34:13 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:13 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:34:13 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:13 - __main__ - INFO - 开始进化阶段
2025-07-09 20:34:13 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:34:13 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:13 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:16 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:16 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:16 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:34:16 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:16 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:16 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:16 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:16 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:34:16 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:16 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:17 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:17 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:17 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:34:17 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:17 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:17 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 19023.0 -> 16711.0
2025-07-09 20:34:22 - ExploitationExpert - INFO - 自适应开发成功，改进: 9423.00
2025-07-09 20:34:22 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  7,  9, 11,  1,  0, 10,  2,  8,  5,  4,  6, 55, 61,
       56, 59, 62, 53, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 47, 48, 46, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 30, 28, 35, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9600.0), 'improvement': np.float64(9423.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:22 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:34:22 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:22 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:23 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:23 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:23 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:34:23 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:23 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:23 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 10991.0 -> 10890.0
2025-07-09 20:34:28 - ExploitationExpert - INFO - 自适应开发成功，改进: 1362.00
2025-07-09 20:34:28 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 35, 28, 30, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 36, 37, 26]), 'cur_cost': np.float64(9629.0), 'improvement': np.float64(1362.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:28 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:34:28 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:28 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:33 - ExploitationExpert - INFO - 自适应开发成功，改进: 40.00
2025-07-09 20:34:33 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(40.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:33 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:34:33 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:33 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:33 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:33 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:33 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:34:33 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:33 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:34 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:34 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:34 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:34:34 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:34 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:34 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 15141.0 -> 11348.0
2025-07-09 20:34:44 - ExploitationExpert - INFO - 自适应开发成功，改进: 5217.00
2025-07-09 20:34:44 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 29, 24, 18, 17, 15, 12, 22, 14, 23, 16, 19, 13, 21, 20, 43,
       49, 40, 48, 42, 51, 45, 41, 44, 50, 38, 46, 47, 39, 58, 56, 60, 53,
       55, 59, 62, 64, 57, 54, 52, 63, 65, 61, 10,  8,  4,  6,  2,  5, 11,
        9,  1,  0,  7,  3, 27, 37, 31, 33, 26, 34, 36, 25, 28, 30]), 'cur_cost': np.float64(9924.0), 'improvement': np.float64(5217.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:44 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  7,  9, 11,  1,  0, 10,  2,  8,  5,  4,  6, 55, 61,
       56, 59, 62, 53, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 47, 48, 46, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 30, 28, 35, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9600.0), 'improvement': np.float64(9423.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 35, 28, 30, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 36, 37, 26]), 'cur_cost': np.float64(9629.0), 'improvement': np.float64(1362.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(40.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 29, 24, 18, 17, 15, 12, 22, 14, 23, 16, 19, 13, 21, 20, 43,
       49, 40, 48, 42, 51, 45, 41, 44, 50, 38, 46, 47, 39, 58, 56, 60, 53,
       55, 59, 62, 64, 57, 54, 52, 63, 65, 61, 10,  8,  4,  6,  2,  5, 11,
        9,  1,  0,  7,  3, 27, 37, 31, 33, 26, 34, 36, 25, 28, 30]), 'cur_cost': np.float64(9924.0), 'improvement': np.float64(5217.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:34:44 - __main__ - INFO - 进化阶段完成
2025-07-09 20:34:44 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-09 20:34:44 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-09 20:34:44 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 2/5
2025-07-09 20:34:44 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:44 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:44 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-09 20:34:44 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-09 20:34:44 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-09 20:34:44 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 3/5
2025-07-09 20:34:44 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:34:44 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 3}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 3}}
2025-07-09 20:34:44 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:34:44 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:34:44 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:34:44 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:34:44 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:34:44 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:34:44 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:34:44 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 3)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.392
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:34:44 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:34:54 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-09 20:34:56 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-09 20:34:58 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:34:58 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:34:58 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:34:58 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:34:58 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:34:58 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:34:58 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:34:58 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:58 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:34:58 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:58 - __main__ - INFO - 开始进化阶段
2025-07-09 20:34:58 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:34:58 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:58 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:01 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:01 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:01 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:35:01 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:01 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:02 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:02 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:02 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:35:02 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:02 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:04 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:04 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:04 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:35:04 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:04 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06 - ExploitationExpert - INFO - 自适应开发成功，改进: 51.00
2025-07-09 20:35:06 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(51.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:06 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:35:06 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:06 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:06 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:06 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:35:06 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:06 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9629.0 -> 9622.0
2025-07-09 20:35:07 - ExploitationExpert - INFO - 自适应开发成功，改进: 21.00
2025-07-09 20:35:07 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9608.0), 'improvement': np.float64(21.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:07 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:35:07 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:07 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:08 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:08 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:08 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:35:08 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:08 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:09 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:09 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:09 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:35:09 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:09 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:11 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:11 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:11 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:35:11 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:11 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:11 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9924.0 -> 9895.0
2025-07-09 20:35:14 - ExploitationExpert - INFO - 自适应开发成功，改进: 362.00
2025-07-09 20:35:14 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       45, 44, 41, 38, 51, 50, 42, 47, 46, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9562.0), 'improvement': np.float64(362.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:14 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(51.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9608.0), 'improvement': np.float64(21.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       45, 44, 41, 38, 51, 50, 42, 47, 46, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9562.0), 'improvement': np.float64(362.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:35:14 - __main__ - INFO - 进化阶段完成
2025-07-09 20:35:14 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-09 20:35:14 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-09 20:35:14 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 3/5
2025-07-09 20:35:14 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:14 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:14 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-09 20:35:14 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-09 20:35:14 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-09 20:35:14 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 4/5
2025-07-09 20:35:14 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:35:14 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 4}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 4}}
2025-07-09 20:35:14 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:35:14 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:35:14 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:35:14 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:35:14 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:35:14 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:35:14 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:35:14 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 4)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.318
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:35:14 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:35:16 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:35:16 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:35:16 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:35:16 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:35:16 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:35:16 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:35:16 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:35:16 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:35:16 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:35:16 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:35:16 - __main__ - INFO - 开始进化阶段
2025-07-09 20:35:16 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:35:16 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:16 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:17 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:17 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:17 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:35:17 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:17 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:18 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:18 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:18 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:35:18 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:18 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:18 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:18 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:18 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:35:18 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:18 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:19 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:19 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:19 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:35:19 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:19 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:20 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:20 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:20 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:35:20 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:20 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:22 - ExploitationExpert - INFO - 自适应开发成功，改进: 81.00
2025-07-09 20:35:22 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65,
       52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9527.0), 'improvement': np.float64(81.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:22 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:35:22 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:22 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:23 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:23 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:23 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:35:23 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:23 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:23 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:23 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:23 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:35:23 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:23 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:24 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:24 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:24 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:35:24 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:24 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:24 - ExploitationExpert - INFO - 自适应开发成功，改进: 14.00
2025-07-09 20:35:24 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9548.0), 'improvement': np.float64(14.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:24 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65,
       52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9527.0), 'improvement': np.float64(81.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9548.0), 'improvement': np.float64(14.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:35:24 - __main__ - INFO - 进化阶段完成
2025-07-09 20:35:24 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:24 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9527.0, 'max': 9571.0, 'mean': 9546.9, 'std': np.float64(13.866866985732573)}, 'diversity': np.float64(0.3016835016835017), 'clusters': {'clusters': 4, 'cluster_sizes': [5, 2, 2, 1]}, 'convergence': 0.0}
2025-07-09 20:35:24 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-09 20:35:24 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-09 20:35:24 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 4/5
2025-07-09 20:35:24 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:24 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:24 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-09 20:35:24 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-09 20:35:24 - __main__ - INFO - 实例 composite13_66 处理完成
